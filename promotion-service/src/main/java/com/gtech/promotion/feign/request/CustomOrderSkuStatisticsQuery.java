package com.gtech.promotion.feign.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description: CustomOrderSkuStatisticsQuery
 * @date 2023/10/27
 */
@Data
public class CustomOrderSkuStatisticsQuery implements Serializable {

    private static final long serialVersionUID = -1134737539998567557L;

    @NotBlank(message = "tenantCode not blank")
    private String tenantCode;

    @NotBlank(message = "orgCode not blank")
    private String orgCode;

    @NotBlank(message = "memberCode not blank")
    private String memberCode;

    private String createTimeStart;

    private String createTimeEnd;

    private List<String> orderStatusList;

    private List<String> productCodes;

    private List<String> skuCodes;

    private List<String> excludeProductCodes;

    private List<String> excludeSkuCodes;


}
