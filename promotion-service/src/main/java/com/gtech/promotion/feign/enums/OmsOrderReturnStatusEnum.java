package com.gtech.promotion.feign.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * t_order_return表
 * 41: Received
 * 51: Processing
 * 61: Complete
 */
@AllArgsConstructor
@Getter
public enum OmsOrderReturnStatusEnum {
    /**
     * 41: 仓库已收到
     */
    RECEIVED("41"),
    /**
     * 51: 退款中
     */
    PROCESSING("51"),
    /**
     * 61: 退款完成
     */
    COMPLETE("61"),

    ;
    private final String code;

}
