package com.gtech.promotion.feign.request;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;


@EqualsAndHashCode(callSuper = true)
@Data
public class OrderReturnCommonIn extends TenantCodeIn implements Serializable {


    private String externalOrderCode;

    private String orderCode;

    private String externalReturnCode;

    private String returnCode;

    private String userName;

    private String userCode;

    private BigDecimal damageAmount;

    private String damageType;

    private String remark;

    private String cancelCode;

    private String auditStatus;

    private String isAdmin;
}
