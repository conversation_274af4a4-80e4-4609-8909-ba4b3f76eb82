package com.gtech.promotion.feign.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023/7/17 10:25
 */
@Data
public class MemberPurchaseStatisticsResp {
    /**
     * SPU编码
     */
    @ApiModelProperty(value = "productCode", notes = "SPU编码")
    private String productCode;
    /**
     * 总购买数量
     */
    @ApiModelProperty(value = "buyQty", notes = "总购买数量")
    private Long buyQty;
    /**
     * 总实付金额（折后）
     */
    @ApiModelProperty(value = "totalActualPayAmount", notes = "总实付金额（折后）")
    private BigDecimal totalActualPayAmount;
    /**
     * 取消的数量
     */
    @ApiModelProperty(value = "cancelQty", notes = "取消的数量")
    private Long cancelQty;
    /**
     * 取消的金额
     */
    @ApiModelProperty(value = "totalCancelAmount", notes = "取消的金额")
    private BigDecimal totalCancelAmount;
    /**
     * 退货完成数量
     */
    @ApiModelProperty(value = "returnedQty", notes = "退货完成数量")
    private Long returnedQty;
    /**
     * 退货完成金额
     */
    @ApiModelProperty(value = "totalReturnedAmount", notes = "退货完成金额")
    private BigDecimal totalReturnedAmount;

    public Long getEfficientQty() {
        return Optional.ofNullable(this.getBuyQty()).orElse(0L)
                - Optional.ofNullable(this.getCancelQty()).orElse(0L)
                - Optional.ofNullable(this.getReturnedQty()).orElse(0L);
    }
    public BigDecimal getEfficientAmount() {
        return Optional.ofNullable(this.getTotalActualPayAmount()).orElse(new BigDecimal("0"))
                .subtract(Optional.ofNullable(this.getTotalCancelAmount()).orElse(new BigDecimal("0")))
                .subtract(Optional.ofNullable(this.getTotalReturnedAmount()).orElse(new BigDecimal("0")));
    }

}
