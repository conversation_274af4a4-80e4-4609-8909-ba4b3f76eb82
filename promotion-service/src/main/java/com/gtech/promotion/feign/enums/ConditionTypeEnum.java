package com.gtech.promotion.feign.enums;


import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;

/**
 * condition type,
 * <p>
 * 0:=
 * 1:like
 * 2:!=
 * 3:in
 * 4:exist
 * 5:not exist
 */
@AllArgsConstructor
@Getter
public enum ConditionTypeEnum {

    EQ(0),
    LIKE(1),
    NQ(2),
    IN(3),
    EXIST(4),
    NOT_EXIST(5),

    ;
    private final Integer code;


    private static final Map<Integer, ConditionTypeEnum> OMS_ORDER_STATUS_MAP = Maps.newHashMap();

    static {
        Arrays.stream(values()).forEach(val -> OMS_ORDER_STATUS_MAP.put(val.getCode(), val));
    }

    public static ConditionTypeEnum getByCode(Integer code) {
        return OMS_ORDER_STATUS_MAP.get(code);
    }
}
