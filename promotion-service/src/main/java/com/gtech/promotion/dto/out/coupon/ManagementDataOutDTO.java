/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dto.out.coupon;

import com.gtech.promotion.api.ApiConstants;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
/**
 * 统一券码数据管理出参
 */
@Getter
@Setter
@ToString
@ApiModel("ManagementDataOut")
@NoArgsConstructor
@AllArgsConstructor
public class ManagementDataOutDTO implements Serializable{

    private static final long serialVersionUID = -6352609681467935228L;

    @ApiModelProperty(name = "couponCode", value = "券码编号", example = "611666644000134071")
    private String couponCode;
    
    @ApiModelProperty(name = "releaseTime", value = "投放时间", example = "20181213140606")
    private String releaseTime;
    
    @ApiModelProperty(name = "receiveTime", value = "领取时间", example = "20181213140606")
    private String receiveTime;
    
    @ApiModelProperty(name = "usedTime",value = "使用时间", example = "20181213140606")
    private String usedTime;
      
    @ApiModelProperty(name = "userCode",value = "会员编号",example = "18042000000058")
    private String userCode;
    
    @ApiModelProperty(name = "status",value = ApiConstants.COUPON_STATUS,example = "01")
    private String status;
    
    @ApiModelProperty(name = "couponType",value = ApiConstants.COUPON_TYPE, example = "02")
    private String couponType;
    
    @ApiModelProperty(name = "usedRefId",value = "订单编号",example = "20180426110666")
    private String usedRefId;
    
    @ApiModelProperty(name = "takeLabel",value =ApiConstants.TAKE_LABEL,example = "01")
    private String takeLabel;
    
    @ApiModelProperty(name = "activityCode",value = "活动编码", example = "20200690898932117670")
    private String activityCode;
    
    @ApiModelProperty(name = "activityName",value = "活动名称",example = "2018店铺")
    private String activityName;
    
    @ApiModelProperty(name = "frozenStatus",value =ApiConstants.FROZEN_STATUS ,example = "01")
    private String frozenStatus;
    
    
}
