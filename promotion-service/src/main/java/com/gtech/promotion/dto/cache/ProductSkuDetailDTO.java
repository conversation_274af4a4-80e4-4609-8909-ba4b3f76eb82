/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dto.cache;

import lombok.Data;
import lombok.ToString;
import org.apache.commons.collections4.MapUtils;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

/**   
 */
@Data
@ToString
public class ProductSkuDetailDTO implements Serializable{

    private static final long serialVersionUID = 4351759044918887950L;
    
    // 商品范围层级id:默认1
    private Integer seqNum;
    
    // 商品编码：0000-不限制
    private String productCode;

    private String activityCode;

    private Integer type = 1;

    // SKU编码
    private String skuCode;

    private String orgCode;

    // 套装SKU编码
    private String combineSkuCode;
    
    // 促销价格
    private BigDecimal promoPrice;
    private String priceType;
    private String priceDiscount;

    public static boolean moreThanOneSeq(Map<String, List<ProductSkuDetailDTO>> productDetails) {
        
        if (MapUtils.isEmpty(productDetails)) {
            return false;
        }
        
        for(Entry<String, List<ProductSkuDetailDTO>> entry : productDetails.entrySet()) {
            
            List<ProductSkuDetailDTO> list = entry.getValue();
            if (CollectionUtils.isEmpty(list)) {
                continue;
            }

            for(ProductSkuDetailDTO e : list) {
                if (e.seqNum > 1) {
                    return true;
                }
            }
        }

        return false;
    }

    public static boolean moreThanOneSeq(List<ProductSkuDetailDTO> productDetails) {

        if (CollectionUtils.isEmpty(productDetails)) {
            return false;
        }

        for(ProductSkuDetailDTO e : productDetails) {
            if (e.seqNum > 1) {
                return true;
            }
        }

        return false;
    }
}
  
