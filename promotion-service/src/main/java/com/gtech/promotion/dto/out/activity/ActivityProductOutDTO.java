/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dto.out.activity;

import com.gtech.promotion.api.ApiConstants;
import com.gtech.promotion.dto.out.product.ProductOutDTO;
import com.gtech.promotion.vo.bean.ActivityPeriod;
import com.gtech.promotion.vo.bean.Giveaway;
import com.gtech.promotion.vo.bean.ProductDetail;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 根据活动查商品 出参
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("ActivityProductOut")
public class ActivityProductOutDTO implements Serializable{

    private static final long serialVersionUID = 5570477485515736705L;

    @ApiModelProperty(ApiConstants.ACTIVITY_TYPE)
    private String activityType;

    @ApiModelProperty("活动名称")
    private String activityName;

    @ApiModelProperty("活动标签")
    private String activityLabel;

    @ApiModelProperty("活动描述")
    private String activityDesc;

    @ApiModelProperty("活动备注")
    private String activityRemark;

    @ApiModelProperty(ApiConstants.ACTIVITY_SORT)
    private String activitySort;//

    @ApiModelProperty("活动开始时间")
    private String activityBegin;

    @ApiModelProperty("结束时间")
    private String activityEnd;

    @ApiModelProperty(ApiConstants.PRODUCT_SELECTION_TYPE)
    private String productSelectionType;

    @ApiModelProperty("商品信息列表")
    private List<ProductOutDTO> products;

    @ApiModelProperty("商品池数量")
    private Integer seqCount;

    @ApiModelProperty("商品SPU总记录数")
    private long spuSkuTotal;

    @ApiModelProperty("商品明细列表")
    private List<ProductDetail> productDetils;

    @ApiModelProperty("赠品赠送最大限制数量")
    private String giftLimitMax;

    @ApiModelProperty("赠品列表")
    private List<Giveaway> giveaways;

    @ApiModelProperty(value = "Activity warm-up begin time.")
    private String warmBegin;

    @ApiModelProperty(value = "Activity cool down time.")
    private String coolDown;

    @ApiModelProperty(value = "ops create Activity type")
    private String opsType;

    @ApiModelProperty(value = "Ribbon image.")
    private String ribbonImage;

    @ApiModelProperty(value = "Position of Ribbon image .")
    private String ribbonPosition;

    @ApiModelProperty(value = "Ribbon text.")
    private String ribbonText;

    @ApiModelProperty(value = "Activity period.")
    private ActivityPeriod activityPeriod;

}
