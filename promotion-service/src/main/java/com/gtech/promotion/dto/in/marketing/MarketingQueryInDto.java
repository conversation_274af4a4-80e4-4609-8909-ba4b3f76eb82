/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WA<PERSON>RANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dto.in.marketing;

import com.gtech.commons.page.PageParam;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class MarketingQueryInDto extends PageParam implements Serializable {

    private static final long serialVersionUID = -2964779640591633440L;
    private String domainCode;

    private String tenantCode;

    private String orgCode;

    private String groupCode;

    private String activityType;

    private String activityCode;

    private String opsType;

    private String activityStatus;

    private String activityName;

    private String sponsors;

    private String activityBeginFrom;

    private String activityBeginTo;

    private String activityEndFrom;

    private String activityEndTo;

    private String warmBeginFrom;

    private String warmBeginTo;

    private String warmEndFrom;

    private String warmEndTo;

    private String luckyDrawRuleFlag;


    private Boolean defaultFlag;

}
