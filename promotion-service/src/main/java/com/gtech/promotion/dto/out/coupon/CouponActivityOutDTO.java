/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dto.out.coupon;

import com.gtech.promotion.dto.out.activity.TPromoActivityOutDTO;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Objects;

/**
 * 券活动查询出参
 */
@Getter
@Setter
@ToString
@ApiModel("CouponActivityOut")
public class CouponActivityOutDTO extends TPromoActivityOutDTO {

    private static final long serialVersionUID = 5239061180826348159L;

    // Coupon type: 01-优惠券 02-匿名券 03-优惠码
    private String couponType;


    // 通用优惠码券码
    private String promotionCode;

    private String promoPassword;


    // 券投放总数量：0-不限制
    private Integer totalQuantity;

    // 已投放量
    private Integer releasedQuantity;

    // 券码已领取的总量
    private Integer receivedQuantity;

    // 可用库存
    private Integer validQuantity;

    // 券码已使用的总量
    private Integer usedTotal;

    // 送券活动预留的券码库存
    private Integer reserveInventory;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        if (!super.equals(o)) return false;
        CouponActivityOutDTO that = (CouponActivityOutDTO) o;
        return Objects.equals(couponType, that.couponType) && Objects.equals(promotionCode, that.promotionCode) && Objects.equals(totalQuantity, that.totalQuantity) && Objects.equals(releasedQuantity, that.releasedQuantity) && Objects.equals(receivedQuantity, that.receivedQuantity) && Objects.equals(validQuantity, that.validQuantity) && Objects.equals(usedTotal, that.usedTotal) && Objects.equals(reserveInventory, that.reserveInventory);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), couponType, promotionCode, totalQuantity, releasedQuantity, receivedQuantity, validQuantity, usedTotal, reserveInventory);
    }
}
