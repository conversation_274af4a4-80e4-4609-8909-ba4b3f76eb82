/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dto.out.activity;

import com.alibaba.fastjson.annotation.JSONField;
import com.gtech.promotion.api.ApiConstants;
import com.gtech.promotion.calc.CalcConstants;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

/**
 * 购物车商品项输出
 *
 */
@Getter
@Setter
@ToString
public class ShoppingCartItemOutDTO {

    // Product sku code
    private String skuCode;

    // 套装商品sku编码
    private String combineSkuCode;

    // 套装编码或者活动编码
    @JSONField(serialize = false)
    private String combineOrSkuCode;

    @ApiModelProperty(value = ApiConstants.SELECT_FLAG)
    private String selectionFlag;

    @ApiModelProperty(value = "商品原单价")
    private BigDecimal productPrice;

    @ApiModelProperty(value = "购买数量（该sku的总数量）")
    private Integer quantity;

    @ApiModelProperty(value = "参与促销数量（参与当前活动的数量）")
    private Integer promoQuantity;

    @ApiModelProperty(value = "促销后总价（参与所有活动后价格）")
    private BigDecimal promoAmount;

    @ApiModelProperty(value = "当前商品参与的促销活动列表")
    private List<ShoppingCartItemActivityOutDTO> shoppingCartItemActivitys;

    public void setLastScale() {

        if (null != promoAmount) {
            promoAmount = promoAmount.setScale(CalcConstants.LAST_PRECISION, CalcConstants.LAST_ROUND);
        }
    }
}
