package com.gtech.promotion.dto.out.activity;

import com.gtech.promotion.api.ApiConstants;
import com.gtech.promotion.dao.model.activity.ActivityModel;
import com.gtech.promotion.dao.model.activity.QualificationModel;
import com.gtech.promotion.vo.bean.ActivityStore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/5/8 15:12
 */
@Data
@ToString
@ApiModel("MallActivityListResult")
public class MallActivityListResult implements Serializable {

    private static final long serialVersionUID = -1885572801645997387L;

    @ApiModelProperty("活动编码")
    private String activityCode;

    @ApiModelProperty("活动名称")
    private String activityName;

    @ApiModelProperty("活动标签")
    private String activityLabel;

    @ApiModelProperty("活动描述")
    private String activityDesc;

    @ApiModelProperty("活动备注")
    private String activityRemark;

    @ApiModelProperty(ApiConstants.ACTIVITY_SORT)
    private String activitySort;

    @ApiModelProperty("活动开始时间")
    private String activityBegin;

    @ApiModelProperty("结束时间")
    private String activityEnd;

    @ApiModelProperty("店铺渠道列表")
    private List<ActivityStore> stores;

    @ApiModelProperty("资格列表")
    private List<QualificationModel> qualifications;

    @ApiModelProperty(value = "Activity warm-up begin time.")
    private String warmBegin;

    @ApiModelProperty(value = "Activity cool down time.")
    private String coolDown;

    @ApiModelProperty(value = "ops create Activity type")
    private String opsType;

    @ApiModelProperty(value = "Activity background image.")
    private String backgroundImage;

    //构造函数
    public MallActivityListResult(){
    }

    /**
     * Creates a new instance of MallActivityListResult.
     *
     * @param tPromoActivityVO
     *            活动信息
     * @param tagCode
     *            活动标签编码
     * @param stores
     *            店铺渠道列表
     */
//    List<Qualification> qualifications
    public MallActivityListResult(ActivityModel tPromoActivityVO, String tagCode, List<ActivityStore> stores, List<QualificationModel> qualificationModels){
        this.activityCode = tPromoActivityVO.getActivityCode();
        this.activityName = tPromoActivityVO.getActivityName();
        this.activityLabel = tPromoActivityVO.getActivityLabel();
        this.activityDesc = tPromoActivityVO.getActivityDesc();
        this.activityRemark = tPromoActivityVO.getActivityRemark();
        this.activityBegin = tPromoActivityVO.getActivityBegin();
        this.activityEnd = tPromoActivityVO.getActivityEnd();
        this.qualifications = qualificationModels;
        this.activitySort = tagCode;
        this.stores = stores;
        this.warmBegin = tPromoActivityVO.getWarmBegin();
        this.coolDown = tPromoActivityVO.getCoolDown();
        this.opsType = tPromoActivityVO.getOpsType();
        this.backgroundImage = tPromoActivityVO.getBackgroundImage();
    }

}
