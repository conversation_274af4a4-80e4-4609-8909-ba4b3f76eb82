/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dto.in.coupon;

import com.gtech.promotion.api.ApiConstants;
import com.gtech.promotion.dto.in.activity.TPromoActivityListInDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**   
 * 券活动列表入参参数
 */
@Getter
@Setter
@ToString
@ApiModel("CouponActivityListIn")
public class CouponActivityListInDTO extends TPromoActivityListInDTO {

    private static final long serialVersionUID = -8810227800759436521L;

    @ApiModelProperty(name = "couponType",value = ApiConstants.COUPON_TYPE, required = false)
    private String couponType;
    
    // Coupon code.
    private String couponCode;

    private String templateCodes;

    private String channelCode;
}
