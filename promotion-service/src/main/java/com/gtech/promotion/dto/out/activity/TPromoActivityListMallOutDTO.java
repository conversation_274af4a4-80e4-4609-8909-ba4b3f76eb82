/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dto.out.activity;

import com.gtech.promotion.api.ApiConstants;
import com.gtech.promotion.dao.model.activity.ActivityModel;
import com.gtech.promotion.vo.bean.ActivityStore;
import com.gtech.promotion.vo.bean.Qualification;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

@Data
@ToString
@ApiModel("TPromoActivityListMallOut")
public class TPromoActivityListMallOutDTO implements Serializable{

    private static final long serialVersionUID = 76669019761730225L;

    @ApiModelProperty("活动编码")
    private String activityCode;

    @ApiModelProperty("活动名称")
    private String activityName;

    @ApiModelProperty("活动标签")
    private String activityLabel;

    @ApiModelProperty("活动描述")
    private String activityDesc;

    @ApiModelProperty("活动备注")
    private String activityRemark;

    @ApiModelProperty(ApiConstants.ACTIVITY_SORT)
    private String activitySort;

    @ApiModelProperty("活动开始时间")
    private String activityBegin;

    @ApiModelProperty("结束时间")
    private String activityEnd;

    @ApiModelProperty("店铺渠道列表")
    private List<ActivityStore> stores;

    @ApiModelProperty("资格列表")
    private List<Qualification> qualifications;

    @ApiModelProperty(value = "Activity warm-up begin time.")
    private String warmBegin;

    @ApiModelProperty(value = "Activity cool down time.")
    private String coolDown;

    @ApiModelProperty(value = "ops create Activity type")
    private String opsType;

    @ApiModelProperty(value = "Activity background image.")
    private String backgroundImage;

    //构造函数
    public TPromoActivityListMallOutDTO(){
    }

    /**
     * Creates a new instance of TPromoActivityListOutDTO.
     * 
     * @param tPromoActivityVO
     *            活动信息
     * @param tagCode
     *            活动标签编码
     * @param stores
     *            店铺渠道列表
     */
    public TPromoActivityListMallOutDTO(ActivityModel tPromoActivityVO, String tagCode, List<ActivityStore> stores, List<Qualification> qualifications){
        this.activityCode = tPromoActivityVO.getActivityCode();
        this.activityName = tPromoActivityVO.getActivityName();
        this.activityLabel = tPromoActivityVO.getActivityLabel();
        this.activityDesc = tPromoActivityVO.getActivityDesc();
        this.activityRemark = tPromoActivityVO.getActivityRemark();
        this.activityBegin = tPromoActivityVO.getActivityBegin();
        this.activityEnd = tPromoActivityVO.getActivityEnd();
        this.qualifications = qualifications;
        this.activitySort = tagCode;
        this.stores = stores;
        this.warmBegin = tPromoActivityVO.getWarmBegin();
        this.coolDown = tPromoActivityVO.getCoolDown();
        this.opsType = tPromoActivityVO.getOpsType();
        this.backgroundImage = tPromoActivityVO.getBackgroundImage();
    }

}
