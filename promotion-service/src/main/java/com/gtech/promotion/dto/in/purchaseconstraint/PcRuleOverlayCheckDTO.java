package com.gtech.promotion.dto.in.purchaseconstraint;

import com.gtech.promotion.dao.model.purchaseconstraint.PcRuleCalculateModel;
import com.gtech.promotion.dto.cache.PurchaseConstraintCacheDTO;
import com.gtech.promotion.vo.bean.ActivityStore;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 限购规则累计维度校验
 */
@Accessors(chain = true)
@Data
public class PcRuleOverlayCheckDTO implements Serializable {

    private static final long serialVersionUID = -8774266367544684215L;
    private String userCode;
    private String tenantCode;
    private String orgCode;
    private PurchaseConstraintCacheDTO pc;
    private Date statisticsBegin;
    private Date statisticsEnd;
    private List<String> includeProductCodes;
    private List<String> excludeProductCodes;
    private String cacheKeyMd5;
    private PcRuleCalculateModel.IncrementProduct increment;
    /**
     * 限购渠道及店铺
     */
    private List<ActivityStore> channelStores;
}
