package com.gtech.promotion.dto.cache;

import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.promotion.code.PromotionConstants;
import com.gtech.promotion.code.activity.ProductTypeEnum;
import com.gtech.promotion.dto.out.purchaseconstraint.FindPurchaseConstraintOutDto;
import com.gtech.promotion.vo.bean.ProductDetail;
import com.gtech.promotion.vo.bean.ProductScope;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serializable;
import java.util.List;

/**
 * 限购缓存实体类，暂时保持跟详情实体类一致
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class PurchaseConstraintCacheDTO extends FindPurchaseConstraintOutDto  implements Serializable{

    public String getConditionProductType() {
        if (CollectionUtils.isNotEmpty(ProductDetail.getProductScopeBySeq(BeanCopyUtils.jsonCopyList(this.getProductDetails(), ProductDetail.class), 1))) {
            return ProductTypeEnum.CUSTOM_PRODUCT.code();
        } else {
            List<ProductScope> productScopeBySeq = ProductScope.getProductScopeBySeq(this.getProducts(), 1);
            if (CollectionUtils.isNotEmpty(productScopeBySeq)) {
                if (productScopeBySeq.size() == 1
                        && CollectionUtils.isEmpty(productScopeBySeq.get(0).getAttributes())
                        && CollectionUtils.isEmpty(productScopeBySeq.get(0).getSpuAttributes())
                        && PromotionConstants.UNLIMITED.equals(productScopeBySeq.get(0).getBrandCode())
                        && PromotionConstants.UNLIMITED.equals(productScopeBySeq.get(0).getCategoryCode()) ){
                    return ProductTypeEnum.ALL.code();
                }
                return ProductTypeEnum.CUSTOM_RANGE.code();
            } else {
                return ProductTypeEnum.ALL.code();
            }
        }
    }
}
