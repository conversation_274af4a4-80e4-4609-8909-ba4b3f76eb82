/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dto.out.marketing;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**   
 * 单品活动活动价出参对象
 */
@Data
@ToString
@ApiModel("FlashSaleSkuActivityPrice")
public class FlashSaleSkuActivityPriceDTO implements Serializable {

    private static final long serialVersionUID = -3253020777448360560L;
    @ApiModelProperty("商品sku编码")
    private String skuCode;
    
    @ApiModelProperty("套装商品sku编码（1.7版本新加）")
    private String combineSkuCode;
    
    @ApiModelProperty("商品spu编码")
    private String productCode;
    
    @ApiModelProperty("活动价对象数组（只有一个优先级最高的，为了兼容老版本）")
    private List<FlashSaleActivityPriceDTO> activity;

}