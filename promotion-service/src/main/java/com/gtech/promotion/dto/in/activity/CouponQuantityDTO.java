/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dto.in.activity;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.exception.ErrorCodes;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**   
 * 查询券数量
 */
@Data
@ToString(callSuper = true)
@ApiModel("CouponQuantityRequest")
public class CouponQuantityDTO implements Serializable{

    private static final long serialVersionUID = -5324661908110230277L;

    @ApiModelProperty(name = "tenantCode",value = "租户编码",required = true, example = "88000011")
    private String tenantCode;

    @ApiModelProperty(name = "activityCode",value = "活动编号",required = true, example = "20200690898932117670")
    private String activityCode;
    
    @ApiModelProperty(name = "releaseCode",value = "批次号",required = true)
    private String releaseCode;

    @ApiModelProperty(name = "status",value = "状态：01-未发放 02-已发放 03-已使用 04-已锁定05-已过期",required = true)
    private String status;

    @ApiModelProperty(name = "frozen status",value = "冻结状态：01-未冻结 02-已冻结",required = true)
    private String frozenStatus;

    public void validate() {
        CheckUtils.isNotBlank(this.tenantCode, ErrorCodes.PARAM_EMPTY, "tenantCode");
        CheckUtils.isNotBlank(this.activityCode, ErrorCodes.PARAM_EMPTY, "activityCode");
        CheckUtils.isNotBlank(this.releaseCode, ErrorCodes.PARAM_EMPTY, "releaseCode");
        CheckUtils.isNotBlank(this.status, ErrorCodes.PARAM_EMPTY, "status");
        CheckUtils.isNotBlank(this.frozenStatus, ErrorCodes.PARAM_EMPTY, "frozenStatus");
    }
}
