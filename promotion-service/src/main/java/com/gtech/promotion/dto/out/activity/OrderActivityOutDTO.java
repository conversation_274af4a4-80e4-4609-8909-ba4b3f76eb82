/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dto.out.activity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**   
 * 促销租户数据统计出参实体
 */
@Data
@ToString
@ApiModel("OrderActivityOut")
public class OrderActivityOutDTO implements Serializable{

    private static final long serialVersionUID = -5483551227443367592L;

    
    @ApiModelProperty(value = "订单总金额", example = "888888")
    private BigDecimal totalAmount;
    @ApiModelProperty(value = "使用订单id", example = "888888")
    private String orderId;
    @ApiModelProperty(value = "订单详情", example = "888888")
    private List<OrderDetail> orderDetails;

    @Data
    public static class OrderDetail{

        @ApiModelProperty(value = "活动id", example = "888888")
        private String activityCode;
        @ApiModelProperty(value = "活动名称", example = "888888")
        private String activityName;
        @ApiModelProperty(value = "活动类型", example = "888888")
        private String activityType;
        @ApiModelProperty(value = "活动减免金额", example = "888888")
        private BigDecimal incentiveAmount;


    }

}
  
