package com.gtech.promotion.dto.out.purchaseconstraint;

import com.gtech.promotion.dto.cache.BaseCacheDTO;
import com.gtech.promotion.dto.cache.CustomConditionAware;
import com.gtech.promotion.vo.bean.*;
import com.gtech.promotion.vo.param.activity.CustomCondition;
import lombok.Data;
import lombok.ToString;

import java.util.Date;
import java.util.List;

@Data
@ToString
public class FindPurchaseConstraintOutDto extends BaseCacheDTO implements CustomConditionAware {
    private Long id;

    // DOMAIN code
    private String domainCode;

    /**
     * 租户号
     */
    private String tenantCode;

    /**
     * 组织编号
     */
    private String orgCode;

    /**
     * 限购Code
     */
    private String purchaseConstraintCode;

    /**
     * 限购名称
     */
    private String purchaseConstraintName;

    /**
     * 限购开始时间
     */
    private Date purchaseConstraintStartTime;

    /**
     * 限购结束时间
     */
    private Date purchaseConstraintEndTime;

    /**
     * 状态
     * @see com.gtech.promotion.code.purchaseconstraint.PurchaseConstraintStatusEnum
     */
    private String purchaseConstraintStatus;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 更新人
     */
    private String updateUser;

    /**
     * 周期类型：00-全时段 01-自定义
     */
    private String periodType;

    /**
     * 商品范围正反选：01-正选；02-反选
     */
    private String productSelectionType;

    /**
     * Product item scope type: 1-All scope 2-By spu in scope
     */
    private Boolean itemScopeType;

    /**
     * 店铺范围：00-全店铺 01-自定义
     */
    private String storeType;

    /**
     * 审核人
     */
    private String auditUser;

    /**
     * 是否优先购买  0 否 1 是
     */
    private Integer firstRefusal;


    /**
     * 限购渠道及店铺
     */
    private List<ActivityStore> channelStores;

    /**
     * 商品列表
     */
    private List<ProductScope> products;

    /**
     * 商品明细列表
     */
    private List<ProductDetail> productDetails;

    /**
     * 商品黑名单列表
     */
    private List<ProductDetail> productDetailBlackList;

    /**
     * Qualification list. Empty means not limited.
     */
    private List<Qualification> qualifications;


    /**
     * Activity period.
     */
    private ActivityPeriod activityPeriod;

    /**
     * 限购规则列表
     */
    private List<PurchaseConstraintRule> purchaseConstraintRuleList;

    /**
     * 描述
     */
    private String description;

    /**
     * 优先级
     */
    private Integer priority;

    /**
     * 高级价格限购标识，默认0，非高级价格限购
     */
    private Integer priceSetting;


    private List<CustomCondition> customConditionsList;
    private List<CustomCondition> customRulesList;
    private String customCondition;
    private String customRule;


    public String getCustomCondition() {
        return this.customCondition;
    }

    public String getCustomRule() {
        return this.customRule;
    }

}
