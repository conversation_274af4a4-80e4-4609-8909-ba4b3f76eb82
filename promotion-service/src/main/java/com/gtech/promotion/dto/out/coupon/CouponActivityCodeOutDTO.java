/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dto.out.coupon;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 */
@Data
@ToString(callSuper = true)
@ApiModel("CouponActivityCodeOut")
public class CouponActivityCodeOutDTO implements Serializable{

    private static final long serialVersionUID = -5957875785887607852L;

    @ApiModelProperty("活动编码：为空时表示没有重复的优惠码")
    private String activityCode;

    @ApiModelProperty("活动状态:只有05和07状态下可以编辑，才会有promotionCode")
    private String status;

    @ApiModelProperty("为老活动生成的优惠码")
    private String promotionCode;

    @ApiModelProperty("优惠码指令")
    private String promoPassword;


}
