/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dto.in.coupon;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**   
 * 锁定优惠券入参
 */
@Data
@ToString
public class TCouponLockDTO implements Serializable{

    private static final long serialVersionUID = 2982355900825089978L;

    /***
     * 会员编号
     */
    private String userCode;
    
    /**  
     * 租户编码 
     */
    private String tenantCode;

    /***
     * 使用时业务编号
     */
    private String orderNo;

    /***
     * 订单锁定时的优惠券编码集合
     */
    private String couponCodes;

}
