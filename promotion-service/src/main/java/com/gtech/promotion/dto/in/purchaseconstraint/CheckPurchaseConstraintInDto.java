package com.gtech.promotion.dto.in.purchaseconstraint;

import com.gtech.promotion.code.purchaseconstraint.PurchaseConstraintRuleTypeEnum;
import lombok.*;

import java.util.List;
import java.util.Map;

@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CheckPurchaseConstraintInDto {
    private String tenantCode;

    private String domainCode;

    private String orgCode;

    private String memberCode;

    private String channelCode;

    private Map<String, List<String>> qualifications;

    List<CheckPurchaseConstraintProductDto> checkPurchaseConstraintProducts;

    private Integer touristFlag;

    private String orderCode;

    private Boolean useIncrement;
    /**
     * 校验规则,
     * 默认校验所有规则
     * @see PurchaseConstraintRuleTypeEnum
     */
    private List<Integer> pcRuleTypeList;
    private Map<String, String> customMap;

}