package com.gtech.promotion.dto.out.coupon;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ExportCouponOutDTO implements Serializable {

    private static final long serialVersionUID = -7034938288914181685L;
    private String id;

    private String couponCode;

    private String releaseCode;

    // Validity date begin: yyyyMMddhhmmss
    private String validStartTime;

    // Validity date end: yyyyMMddhhmmss
    private String validEndTime;

    private String status;
}
