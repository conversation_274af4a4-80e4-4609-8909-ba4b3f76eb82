package com.gtech.promotion.dto.in.activity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/7/16 16:26
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GroupPriorityVO {

    private String tenantCode;

    private List<ActivityGroup> groups;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ActivityGroup {

        private String groupCode;

        private Long priority;
    }

}
