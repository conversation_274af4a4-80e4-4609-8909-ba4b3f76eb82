/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dto.out.marketing;

import com.gtech.promotion.api.ApiConstants;
import com.gtech.promotion.vo.bean.ActivityPeriod;
import com.gtech.promotion.vo.bean.Qualification;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 根据商品查询促销列表出参实体(出参)
 * 
 */
@Data
@ToString
@ApiModel("FlashSaleActivityInfo")
public class FlashSaleActivityInfoDTO implements Serializable{

    private static final long serialVersionUID = -7626002447833164385L;

    @ApiModelProperty(ApiConstants.ACTIVITY_TYPE)
    private String activityType;//

    @ApiModelProperty("活动编码")
    private String activityCode;//

    @ApiModelProperty("活动名称")
    private String activityName;//

    @ApiModelProperty("活动描述")
    private String activityDesc;//
    
    @ApiModelProperty("活动备注")
    private String activityRemark;//

    @ApiModelProperty("活动标签")
    private String activityLabel;//
    
    @ApiModelProperty("开始时间")
    private String activityBegin;//

    @ApiModelProperty("结束时间")
    private String activityEnd;//

    @ApiModelProperty(value = "Activity warm-up begin time.")
    private String warmBegin;

    @ApiModelProperty(value = "Activity cool down time.")
    private String coolDown;

    @ApiModelProperty(value = "ops create Activity type")
    private String opsType;

    @ApiModelProperty("Qualification list")
    private List<Qualification> qualifications;//
    
    @ApiModelProperty("预热结束时间")
    private String warmEnd;//

    @ApiModelProperty("活动URL")
    private String activityUrl;//

    @ApiModelProperty(value = "Activity background image.")
    private String backgroundImage;

    private String ribbonImage;
    private String ribbonPosition;
    private String ribbonText;

    @ApiModelProperty(value = "sponsors")
    private String sponsors;

    @ApiModelProperty(value = "Flash sale price")
    private BigDecimal flashPrice;

    @ApiModelProperty(value = "sku inventory")
    private Integer skuInventory;

    @ApiModelProperty(value = "max pre user")
    private Integer maxPerUser;

    @ApiModelProperty(value = "Activity period.")
    private ActivityPeriod activityPeriod;

    @ApiModelProperty(value = "Shipping time.")
    private String shippingTime;

    @ApiModelProperty("团长价格")
    private BigDecimal groupLeaderPrice;
    @ApiModelProperty("时效字段")
    private Integer effectiveHour;
    @ApiModelProperty("成团人数")
    private Integer groupSize;
    @ApiModelProperty("单数限制（单/人）")
    private Integer orderCount;
    @ApiModelProperty("允许自动成团，默认开， 0开 1关闭")
    private Integer autoGroupFlag;
    @ApiModelProperty("允许超员成团，0开 1关闭")
    private Integer allowGroupFlag;
    @ApiModelProperty("团长福利")
    private String leaderBenefits;
    @ApiModelProperty("单数限制 1限制，0单数字段没有意义")
    private Integer orderCountFlag;

}
