/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WA<PERSON>RANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dto.in.coupon;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel("BindingCouponIn")
@NoArgsConstructor
@AllArgsConstructor
public class BindingCouponInDTO implements Serializable{
    
    private static final long serialVersionUID = 333410587599235393L;

    @ApiModelProperty(name = "tenantCode",value = "租户编号", required = true)
    private String tenantCode;

    @ApiModelProperty(name = "activityCode",value = "活动编码", required = true)
    private String activityCode;
    
    @ApiModelProperty(name = "validStartTime",value = "可用开始时间", required = true)
    private String validStartTime;

    @ApiModelProperty(name = "validEndTime",value = "可用结束时间", required = true)
    private String validEndTime;
    
    @ApiModelProperty(name = "userCoupons",value = "券和用户绑定", required = true)
    private List<UserAndCoupon> userCoupons;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UserAndCoupon implements Serializable{
        
        private static final long serialVersionUID = -4272863841511851740L;

        @ApiModelProperty(name = "userCode",value = "会员编号", required = true)
        private String userCode;

        @ApiModelProperty(name = "couponCode",value = "券编码编码", required = false)
        private String couponCode;
    }
}
