/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dto.in.activity;

import java.io.Serializable;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 查询活动详情 入参实体
 */
@Getter
@Setter
@ToString
// com.gtech.promotion.vo.param.activity.FindActivityParam
public class ActivityParamDTO  implements Serializable{

    private static final long serialVersionUID = 1048373266961290910L;

    @ApiModelProperty(name = "tenantCode",value = "租户编码", required = true)
    private String tenantCode;

    @ApiModelProperty(value = "Activity code", required = true)
    private String activityCode;

    // Store organization code. (Blank value means unlimited.)
    @ApiModelProperty(value="Store organization code. (Blank value means unlimited.)",required=false)
    private String orgCode;
    
    @ApiModelProperty(value="渠道编码",required=false)
    private String channelCode;

}
