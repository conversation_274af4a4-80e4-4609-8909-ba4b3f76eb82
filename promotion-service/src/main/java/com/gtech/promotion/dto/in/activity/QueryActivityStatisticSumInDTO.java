/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dto.in.activity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import com.gtech.promotion.checker.activity.ActivityStatisticChecker;
import com.gtech.promotion.exception.Check;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "查询活动统计总数入参实体类")
public class QueryActivityStatisticSumInDTO implements Serializable {

    private static final long serialVersionUID = -7863580111090629814L;

    @ApiModelProperty(value = "系统租铺编号",required = true)
    private String tenantCode;

    @ApiModelProperty(value = "活动编号",required = true)
    private String activityCode;

    public void checkParams(){

        Check.check(StringUtils.isBlank(tenantCode), ActivityStatisticChecker.NOT_NULL_TENANT_CODE);
        Check.check(StringUtils.isBlank(activityCode), ActivityStatisticChecker.NOT_NULL_ACTIVITY_CODE);
    }
}
