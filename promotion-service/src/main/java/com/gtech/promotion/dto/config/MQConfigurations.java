package com.gtech.promotion.dto.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.stereotype.Component;

@SpringBootConfiguration
@Component(value="MQConfigurations")
@Data
public class MQConfigurations {
	@Value("${rocketmq.consumer.namesrvAddr}")
    private String namesrvAddr;
	@Value("${rocketmq.consumer.consumeThreadMin}")
    private int consumeThreadMin;
	@Value("${rocketmq.consumer.consumeThreadMax}")
    private int consumeThreadMax;
	@Value("${rocketmq.consumer.topics:}")
    private String topics;
	@Value("${rocketmq.consumer.consumeMessageBatchMaxSize}")
    private int consumeMessageBatchMaxSize;
    /**
     * 消息最大大小，默认4M
     */
	@Value("${rocketmq.producer.maxMessageSize}")
    private Integer maxMessageSize ;
    /**
     * 消息发送超时时间，默认3秒
     */
	@Value("${rocketmq.producer.sendMsgTimeout}")
    private Integer sendMsgTimeout;
    /**
     * 消息发送失败重试次数，默认2次
     */
	@Value("${rocketmq.producer.retryTimesWhenSendFailed}")
    private Integer retryTimesWhenSendFailed;
}
