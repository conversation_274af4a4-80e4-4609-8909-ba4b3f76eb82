/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WA<PERSON>RANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dto.in.coupon;

import com.gtech.promotion.api.ApiConstants;
import com.gtech.promotion.dto.in.activity.ActivityUpdateDTO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**   
 * 
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class UpdateCouponActivityInDTO extends ActivityUpdateDTO{
    
    private static final long serialVersionUID = 107092560058297080L;

    @ApiModelProperty(name = "couponType",value = ApiConstants.COUPON_TYPE, required = true)
    private String couponType;
    
    @ApiModelProperty(name = "userLimitMax",value = "单用户领券上限，0默认无限", required = false)
    private Integer userLimitMax;

    @ApiModelProperty(name = "userLimitMaxDay",value = "单用户单日领券上限，0默认无限", required = false)
    private Integer userLimitMaxDay;

    @ApiModelProperty(name = "totalQuantity",value = "券投放总数量：当优惠券类型为02固定促销优惠码，数量固定为1", required = true)
    private Integer totalQuantity=1;

    @ApiModelProperty(name = "promotionCode",value = " 促销优惠码", required = false)
    private String promotionCode;

    @ApiModelProperty(name = "Promo password",value = "优惠码指令")
    private String promoPassword;


}
  
