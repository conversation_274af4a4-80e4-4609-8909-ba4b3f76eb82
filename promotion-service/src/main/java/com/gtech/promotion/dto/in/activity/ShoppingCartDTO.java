/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dto.in.activity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;

import com.google.common.base.Splitter;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.api.ApiConstants;
import com.gtech.promotion.callable.ErrorCouponAndReason;
import com.gtech.promotion.dao.model.coupon.TPromoCouponCodeUserVO;
import com.gtech.promotion.vo.bean.ShoppingCartStore;
import com.gtech.promotion.vo.result.activity.ActivityGroupCache;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 购物车查询计算的参数实体 属性有”租户信息“和”订单活动列表“以及“商品列表，商品里面包含其所属活动” 等
 */
@Getter
@Setter
@ToString
public class ShoppingCartDTO implements Serializable{

    private static final long serialVersionUID = -8669146458890802091L;

    // Tenant code.
    private String domainCode;

    // Tenant code.
    private String tenantCode;
    
    // Language id.
    private String language;

    //自定义选择查优惠券的 打折券，满减券，满减码，兑换券 其中的几种券
    private List<String> opsTypeList;

    // Shopping cart store list.
    private List<ShoppingCartStore> cartStoreList;
    public void setCartStoreList(List<ShoppingCartStore> cartStoreList) {

        if (CollectionUtils.isEmpty(cartStoreList)) {
            this.cartStoreList = null;
            return;
        }
        this.cartStoreList = cartStoreList;

        if (null == this.promoProducts) {
            this.promoProducts = new ArrayList<>();
            for(ShoppingCartStore cs : cartStoreList) {
                List<ShoppingCartItem> cartItems = BeanCopyUtils.jsonCopyList(cs.getCartItemList(), ShoppingCartItem.class);
                cartItems.forEach(x -> x.setOrgCode(cs.getOrgCode()));
                this.promoProducts.addAll(cartItems);
            }
        }
    }

    public List<String> getOrgCodes() {

        if (CollectionUtils.isEmpty(cartStoreList)) {
            return Collections.emptyList();
        }

        List<String> orgCodes = new ArrayList<>();
        for(ShoppingCartStore cs : this.cartStoreList) {
            orgCodes.add(cs.getOrgCode());
        }

        return orgCodes;
    }

    @ApiModelProperty(value = "渠道编码",required = false)
    private Integer channelCode;

    @ApiModelProperty(value = "会员编码",required = true)
    private String userCode;

    @ApiModelProperty(value = "邮费")
    private BigDecimal postage;

    @ApiModelProperty(value = ApiConstants.QUALIFICATIONS)
    private Map<String, List<String>> qualifications;

    @ApiModelProperty(value = "已使用的券编码（多个用逗号隔开）",required = false)
    private String couponCodes;

    // Promotion time.(yyyyMMddHHmmss)
    private String promotionTime;

    private List<ShoppingCartItem> promoProducts;
    public List<ShoppingCartItem> getPromoProducts() {
        return this.promoProducts;
    }

    // 为筛选优惠券专门添加的字段
    @ApiModelProperty(hidden = true)
    private transient List<ErrorCouponAndReason> errorCouponAndReasons;

    public void addErrorCouponAndReasons(ErrorCouponAndReason errorCouponAndReason){
        if (CollectionUtils.isEmpty(errorCouponAndReasons)){
            errorCouponAndReasons = new ArrayList<>();
        }
        errorCouponAndReasons.add(errorCouponAndReason);
    }

    private transient String activityExpr;

	private List<ActivityGroupCache> groupCacheList;

    private Set<String> highPrioriyActivities;
    
    public void autoHighPrioriyActivities(Map<String, TPromoCouponCodeUserVO> couponCodeMap) {

        if (StringUtil.isBlank(this.getCouponCodes()) || MapUtils.isEmpty(couponCodeMap)){
            return;
        }

        for(String cc : Splitter.on(",").trimResults().splitToList(this.getCouponCodes())) {
            TPromoCouponCodeUserVO couponCodeUser = couponCodeMap.get(cc);
            if (null != couponCodeUser) {
                if (null == highPrioriyActivities) {
                    highPrioriyActivities = new HashSet<>();
                }
                highPrioriyActivities.add(couponCodeUser.getActivityCode());
            }
        }
    }

    public boolean isHighPrioriyActivity(String activityCode) {

        if (CollectionUtils.isEmpty(highPrioriyActivities)) {
            return false;
        }

        return highPrioriyActivities.contains(activityCode);
    }
}
