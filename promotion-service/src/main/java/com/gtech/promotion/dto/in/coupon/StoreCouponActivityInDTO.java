/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dto.in.coupon;

import com.gtech.promotion.api.ApiConstants;
import com.gtech.promotion.page.RequestPage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**   
 * 商城可查询所有店铺下的优惠券活动信息入参参数
 */
@ApiModel("StoreCouponActivityIn")
@Data
@EqualsAndHashCode(callSuper = true)
// com.gtech.promotion.vo.param.coupon.QueryCouponActivityListByStoreParam
public class StoreCouponActivityInDTO extends RequestPage implements Serializable {

    private static final long serialVersionUID = 6798266147881904014L;

    @ApiModelProperty(name = "tenantCode",value = "租户编码", required = true)
    private String tenantCode;

    @ApiModelProperty(value = "Language id")
    private String language;

    @ApiModelProperty(name = "userCode",value = "会员编码", required = false)
    private String userCode;

    @ApiModelProperty(value = ApiConstants.QUALIFICATIONS)
    private Map<String, List<String>> qualifications;
    
    @ApiModelProperty(value = "Store organization code.", required = false)
    private String orgCode;
    
    @ApiModelProperty(name = "channelCode",value = "渠道编码", required = false)
    private String channelCode;

}
