/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dto.out.activity;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.gtech.promotion.api.ApiConstants;
import com.gtech.promotion.vo.bean.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 根据租户编码和活动编码查询促销活动
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Data
public class TPromoActivityOutDTO implements Serializable{

    private static final long serialVersionUID = 4548126123593607225L;
    private String orgCode;

    // Activity code.
    private String activityCode;

    private String externalActivityId;


    // Activity Name
    private String activityName;

    // 活动类型: 01-活动 02-券
    private String activityType;

    // Activity sponsors
    private String sponsors;

    private String opsType;

    // Activity tag codes. Separated by commas. 01-Simple promotion, 02-Order discount, 04-特价、05-包邮、06-赠品、07-捆绑、08-满送、10-Buy A get B discount
    private String tagCode;

    // Activity label.
    private String activityLabel;

    // Activity description.
    private String activityDesc;

    // Activity remark.
    private String activityRemark;

    // Multilingual attributes.
    private List<ActivityLanguage> activityLanguages;

    // Activity begin time.
    private String activityBegin;

    // Activity end time.
    private String activityEnd;

    private String promotionCategory;

    private String promotionCategoryName;

    @ApiModelProperty("预热开始时间")
    private String warmBegin;

    @ApiModelProperty("预热结束时间")
    private String warmEnd;

    @ApiModelProperty("周期类型：00-全时段 01-自定义")
    private String periodType;

    @ApiModelProperty("活动状态")
    private String activityStatus;

    @ApiModelProperty("活动主页地址")
    private String activityUrl;

    private String priceCondition;

    // Incentive limited mark: 00-unlimited 01-limited
    private String incentiveLimitedFlag;

    // Incentive limited list. It can't be empty while incentiveLimitedFlag is '01'.
    private List<IncentiveLimited> incentiveLimiteds;

    @ApiModelProperty(value = "Product item scope type: 1-All scope 2-By spu in scope")
    private String itemScopeType;

    @ApiModelProperty("店铺类型")
    private String storeType;

    @ApiModelProperty("活动优先级,1.15.0版本新增字段")
    private Integer priority;
    
    @ApiModelProperty(value = "是否对外显示：1显示；2不显示（1.21版本新加）")
    private Integer showFlag;
    
    @ApiModelProperty("活动渠道及店铺")
    private List<ActivityStore> channelStores;

    @ApiModelProperty("赠品列表")
    private List<Giveaway> giveaways;

    @ApiModelProperty("商品列表")
    private List<ProductScope> products;

    @ApiModelProperty("商品明细列表")
    private List<ProductDetail> productDetils;

    @ApiModelProperty("商品黑名单列表")
    private List<ProductDetail> productDetilBlackList;

    // Qualification list. Empty means not limited.
    private List<Qualification> qualifications;

    // Template code.
    private String templateCode;

    @ApiModelProperty(value = "Activity period.")
    private ActivityPeriod activityPeriod;

    @ApiModelProperty(value = "Custom conditions. json")
    private String customCondition;

    @ApiModelProperty(ApiConstants.PRODUCT_SELECTION_TYPE)
    private String productSelectionType;

    // Parameters list of the activity template function.
    private List<FunctionParam> funcParams;

    // Activity create time.
    @JSONField(format="yyyyMMddHHmmss")
    private Date createTime;

    @ApiModelProperty("promotion code")
    private String promotionCode;

    @ApiModelProperty("promo password")
    private String promoPassword;


    @ApiModelProperty("Coupon type: 01-General coupon 02-Anonymous coupon 03-Single fixed promotion code.")
    private String couponType;

    @ApiModelProperty(value = "Activity cool down time.")
    private String coolDown;

    @ApiModelProperty(value = "Activity background image.")
    private String backgroundImage;

    @ApiModelProperty(value = ApiConstants.PRODUCT_CONDITION)
    private String productCondition;

    @ApiModelProperty(value = "Ribbon image.")
    private String ribbonImage;

    @ApiModelProperty(value = "Position of Ribbon image .")
    private String ribbonPosition;

    @ApiModelProperty(value = "Ribbon text.")
    private String ribbonText;

    @ApiModelProperty(value = "Need Audit. 0:no 1:yes")
    private String needAudit = "0";

    @ApiModelProperty(value = "Need Different Operator.  0:no 1:yes")
    private String needDifferentOperator = "0";

    @ApiModelProperty(value = "audit user")
    private String auditUser;

    @ApiModelProperty(value = "commit user")
    private String commitUser;

    @ApiModelProperty(value = "coupon activity user limit max ")
    private  Integer userLimitMax;

    @ApiModelProperty(value = "coupon activity user limit max  day")
    private  Integer userLimitMaxDay;

    @ApiModelProperty(value = "extend params")
    private JSONObject extendParams;

    private List<ExtImage> extImages;
}
