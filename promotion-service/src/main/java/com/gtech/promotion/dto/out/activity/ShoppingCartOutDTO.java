/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dto.out.activity;

import com.alibaba.fastjson.JSONObject;
import com.gtech.commons.exception.ErrorCode;
import com.gtech.promotion.calc.CalcActivity;
import com.gtech.promotion.calc.CalcConstants;
import com.gtech.promotion.vo.bean.ActivityPeriod;
import com.gtech.promotion.vo.bean.Giveaway;
import com.gtech.promotion.vo.param.activity.CustomCondition;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.collections4.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;

/**
 * ShoppingCartOutDTO
 */
@Getter
@Setter
@ToString
// com.gtech.promotion.vo.result.activity.CalcShoppingCartResult
public class ShoppingCartOutDTO {

    // Activity code
    private String activityCode;

	private String groupCode;

    private String opsType;

    // Activity name
    private String activityName;

    private String promotionCategory;

    private String promotionCategoryName;

    // Activity information for calculate.
    private CalcActivity calcActivity;

    // Coupon code
    private String couponCode;

    // Promotion scope: 01-单品 , 02-商品范围 , 03-订单
    private String promoScope;

    // Activity label
    private String activityLabel;

    // Activity type：01-Activity 02-Coupon
    private String activityType;

    // 开始时间：yyyyMMddhhmmss
    private String activityBegin;

    // 结束时间：yyyyMMddhhmmss
    private String activityEnd;
    // 活动描述
    private String activityDesc;
    // 活动备注
    private String activityRemark;

    // Acitivity URL
    private String activityUrl;

    // Product selection type: 01-Selection, 02-Invert Selection
    private String productSelectionType;
    
    // 赠品赠送最大限制数量
    private String giveawayLimitMax;

    // Promotion effective flag.
    private boolean effectiveFlag;

    // Promotion use failed reason
    private ErrorCode failedReason;

    // 还差多少满足活动（捆绑和满送活动此值为空）
    private String needMoreAmount;
    
    // 数值单位：01元；02件（捆绑和满送活动此值为空）
    private String needMoreUnit;

    // 活动减免总金额
    private BigDecimal promoRewardAmount;

    // 活动减免邮费总金额
    private BigDecimal promoRewardPostage = BigDecimal.ZERO;

    // Incentive type: 01-减金额 02-打折扣 03-单件固定金额 04-组合固定金额 05-包邮 06-送赠品 07-买A送A 08-买A送B 12-买A减价B 13-买A打折B
    private String rewardType;

    // 单人限购次数
    private String userLimitation;

    private JSONObject extendParams;

    // 赠品列表
    private List<Giveaway> giveaways;
    
    // 商品项列表
    private List<ShoppingCartItemOutDTO> shoppingCartItems;

    // Activity template code
    private String templateCode;

    // Coupon use condition value unit: 01-金额 02-数量
    private String conditionUnit;

    // Coupon use condition value
    private BigDecimal conditionValue;

    // Amount before used activity
    private BigDecimal beforeAmount;

    // Amount after used activity
    private BigDecimal afterAmount;

    // Activity sponsors
    private String sponsors;

    @ApiModelProperty(value = "Activity period.")
    private ActivityPeriod activityPeriod;

    @ApiModelProperty(value = "Ribbon image.")
    private String ribbonImage;

    @ApiModelProperty(value = "Position of Ribbon image .")
    private String ribbonPosition;

    @ApiModelProperty(value = "Ribbon text.")
    private String ribbonText;

    // 自定义条件
    private String customCondition;

    private List<CustomCondition> customConditionsList;

    // 奖励次数 每的活动 触发多次算多次
    private int rewardTimes = 0;
    public void addRewardTimes(int rewardTimes) {

        this.rewardTimes += rewardTimes;
    }

    public void setLastScale() {
        
        if (null != promoRewardAmount) {
            promoRewardAmount = promoRewardAmount.setScale(CalcConstants.LAST_PRECISION, CalcConstants.LAST_ROUND);
        }
        if (CollectionUtils.isNotEmpty(shoppingCartItems)) {
            for(ShoppingCartItemOutDTO sci : shoppingCartItems) {
                sci.setLastScale();
            }
        }
    }
}
