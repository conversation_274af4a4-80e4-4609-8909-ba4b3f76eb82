/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dto.in.activity;

import java.io.Serializable;
import java.math.BigDecimal;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TPromoActivityStatisticInDTO implements Serializable {

    private static final long serialVersionUID = -7978993259209204640L;

    // 租铺号
    private String tenantCode;

    // 活动编码
    private String activityCode;

    // 活动参与次数 以付款订单计
    private Integer orderCount;

    // 参与人数
    private Integer memberCount;

    // 减免金额
    private BigDecimal reduceAmount;

    // 订单总金额
    private BigDecimal orderAmount;

    // 统计日期
    private String statisticDate;

    // 领取优惠券数量
    private Integer allocateCouponCount;

    // 使用优惠券数量
    private Integer useCouponCount;
}
