package com.gtech.promotion.dto.in.coupon;

import com.gtech.promotion.dto.out.activity.ShoppingCartOutDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ChooseCouponByCartDTO implements Comparable<ChooseCouponByCartDTO>{

    private BigDecimal discountAmount;
    private List<ChooseCouponByCartCouponInfoDTO> coupons;
    private List<ShoppingCartOutDTO> shoppingCartOutDTOS;

    @Override
    public int compareTo(ChooseCouponByCartDTO o) {
        int i = o.getDiscountAmount().compareTo(discountAmount);
        if (i == 0 && !CollectionUtils.isEmpty(coupons) && !CollectionUtils.isEmpty(o.getCoupons())){
            Collections.sort(coupons);
            List<ChooseCouponByCartCouponInfoDTO> coupons2 = o.getCoupons();
            Collections.sort(coupons2);
            i = coupons.get(0).compareTo(coupons2.get(0));
        }
        return i;
    }
}
