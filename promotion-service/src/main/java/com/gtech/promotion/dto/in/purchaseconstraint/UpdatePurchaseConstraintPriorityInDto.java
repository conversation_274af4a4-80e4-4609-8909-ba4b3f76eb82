package com.gtech.promotion.dto.in.purchaseconstraint;

import com.gtech.promotion.vo.bean.PurchaseConstraintPriority;
import lombok.Data;
import lombok.ToString;

import java.util.List;

@Data
@ToString
public class UpdatePurchaseConstraintPriorityInDto {
    private String tenantCode;

    private List<PurchaseConstraintPriority> purchaseConstraintPriorityList;

    private String operateUser;

    private String operateLastName;

    private String operateFirstName;
}
