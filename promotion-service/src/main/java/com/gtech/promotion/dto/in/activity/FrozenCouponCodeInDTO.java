/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dto.in.activity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * 冻结券码
 */
@Data
@ToString(callSuper = true)
@ApiModel("FrozenCouponCodeIn")
public class FrozenCouponCodeInDTO implements Serializable{

    private static final long serialVersionUID = 4147577440238454555L;

    @ApiModelProperty(name = "tenantCode",value = "租户编码",required = true)
    private String tenantCode;

    @ApiModelProperty(name = "couponCode",value = "券码",required = true)
    private String couponCode;

    @ApiModelProperty(name = "frozenStatus",value = "Frozen Status.1-UnFrozen,2-Frozened",example = "1",required = true)
    private Integer frozenStatus;

    //只有在发错券的时候才传这个参数
    @ApiModelProperty(name = "logicDelete",value = "Logic Delete",example = "0-unDelete,1-delete")
    private Integer logicDelete;
}
