/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dto.in.activity;

import java.io.Serializable;
import java.util.Date;

import org.apache.commons.lang3.StringUtils;

import com.gtech.commons.utils.DateUtil;
import com.gtech.promotion.checker.activity.ActivityStatisticChecker;
import com.gtech.promotion.exception.Check;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "查询活动统计入参实体类")
public class QueryActivityStatisticInDTO implements Serializable {

    private static final long serialVersionUID = -872253389827636737L;

    @ApiModelProperty(value = "系统租铺编号",required = true)
    private String tenantCode;

    @ApiModelProperty(value = "活动编号",required = true)
    private String activityCode;

    @ApiModelProperty(value = "查询起始时间(具体到天) 如:20181010",required = true)
    private String startTime;

    @ApiModelProperty(value = "查询结束时间(具体到天) 如:20181110",required = true)
    private String endTime;

    public void checkParams(){
        Check.check(StringUtils.isBlank(tenantCode), ActivityStatisticChecker.NOT_NULL_TENANT_CODE);
        Check.check(StringUtils.isBlank(activityCode), ActivityStatisticChecker.NOT_NULL_ACTIVITY_CODE);
        Check.check(StringUtils.isBlank(startTime), ActivityStatisticChecker.NOT_NULL_START_TIME);
        Check.check(StringUtils.isBlank(endTime), ActivityStatisticChecker.NOT_NULL_END_TIME);

        Date start = DateUtil.parseDate(startTime, DateUtil.FORMAT_YYYYMMDD);
        Date end = DateUtil.parseDate(endTime, DateUtil.FORMAT_YYYYMMDD);

        long times = end.getTime() - start.getTime();
        Check.check(times < 0, ActivityStatisticChecker.START_TIME_LOWER_END_TIME);

        long day = times / (24*60*60*1000);

        Check.check(day > 90, ActivityStatisticChecker.ONLY_SUPPORT_90_DAYS);
    }
}
