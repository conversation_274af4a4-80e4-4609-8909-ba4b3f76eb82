/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dto.in.activity;

import java.io.Serializable;
import java.util.List;

import com.gtech.promotion.code.activity.ItemScopeTypeEnum;
import com.gtech.promotion.dao.model.activity.ActivityModel;
import com.gtech.promotion.helper.ActivityCodeComparator;

import lombok.Data;
import lombok.ToString;

/**
 * 购物车活动对象
 *
 */
@Data
@ToString
public class ShoppingCartActivity implements Serializable{

    private static final long serialVersionUID = -6914516888695217194L;

    private ActivityModel activityModel;
    public String getActivityCode() {
        return null == this.activityModel ? null : this.activityModel.getActivityCode();
    }
    public String getActivityType() {
        return null == this.activityModel ? null : this.activityModel.getActivityType();
    }
    public Integer getItemScopeType() {
        return null == this.activityModel ? null : this.activityModel.getItemScopeType();
    }
    public String getActivityName() {
        return null == this.activityModel ? null : this.activityModel.getActivityName();
    }
    public String getActivityLabel() {
        return null == this.activityModel ? null : this.activityModel.getActivityLabel();
    }
    public String getTemplateCode() {
        return null == this.activityModel ? null : this.activityModel.getTemplateCode();
    }

    // 参与计算的会员编码
    private String memberCode;

    private List<String> couponCodes;

    private String seqNum;

    private String sortRule;

    private String activitySort;

    private transient ActivityCodeComparator comparator;
    public ActivityCodeComparator getComparator() {

        if (null == comparator && null != this.activityModel) {
            return ActivityCodeComparator.builder()
                .activityCode(this.activityModel.getActivityCode())
                .activityType(this.activityModel.getActivityType())
                .templateCode(this.activityModel.getTemplateCode())
                .priority(this.activityModel.getPriority())
                .highPrioriy(false).build();
        }

        return comparator;
    }

    public boolean isBySpu() {

        return ItemScopeTypeEnum.BY_SPU.equalsCode(this.getItemScopeType());
    }

    public ShoppingCartActivity(){
    }

    public ShoppingCartActivity(ActivityModel activityModel, String memberCode, List<String> couponCodes, String seqNum, String sortRule, String activitySort,
                    ActivityCodeComparator comparator){

        this.activityModel = activityModel;
        this.memberCode = memberCode;
        this.couponCodes = couponCodes;
        this.seqNum = seqNum;
        this.sortRule = sortRule;
        this.activitySort = activitySort;
        this.comparator = comparator;
    }

}
