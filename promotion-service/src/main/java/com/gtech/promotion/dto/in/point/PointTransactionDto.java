package com.gtech.promotion.dto.in.point;

import com.gtech.commons.page.PageParam;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2020/12/29 18:36
 */
@Getter
@Setter
@ToString
public class PointTransactionDto extends PageParam implements Serializable {

    private static final long serialVersionUID = -5459310429751680652L;

    private String domainCode;


    private String tenantCode;


    private String transactionSn;


    private String accountCode;


    private Integer accountType;


    private String campaignCode;


    private Integer transactionType;


    private Long transactionDate;


    private String referTransactionSn;


    private String referOrderNumber;


    private String beginDateFrom;


    private String endDateTo;

}
