package com.gtech.promotion.dto.in.activity;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ActivityExtendInDTO implements Serializable {

    private static final long serialVersionUID = -5939234029770380599L;

    @ApiModelProperty(value = "Tenant code",required = true)
    private String tenantCode;

    @ApiModelProperty(value = "Activity code",required = true)
    private String activityCode;

    @ApiModelProperty(value = "Activity end time (format:yyyyMMddHHmmss)",required = true)
    private String endTime;

    @ApiModelProperty(value = "User code of the operator.", example="U00001", required = true)
    private String operateUser;

    private String operateLastName;

    @ApiModelProperty(value = "User first name of the operator.")
    private String operateFirstName;
}
