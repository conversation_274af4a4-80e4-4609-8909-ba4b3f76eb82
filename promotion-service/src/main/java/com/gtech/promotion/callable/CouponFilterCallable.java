/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.callable;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import com.google.common.base.Splitter;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.calc.CalcExecuter;
import com.gtech.promotion.calc.model.CalcShoppingCart;
import com.gtech.promotion.code.activity.ActivityTypeEnum;
import com.gtech.promotion.component.activity.ShoppingCartDomain;
import com.gtech.promotion.dto.cache.ActivityCacheDTO;
import com.gtech.promotion.dto.in.activity.ShoppingCartDTO;
import com.gtech.promotion.dto.out.activity.ShoppingCartOutDTO;
import com.gtech.promotion.exception.ErrorCodes;

import lombok.extern.slf4j.Slf4j;

/**
 * 筛选优惠券的任务
 */
@Slf4j
public class CouponFilterCallable implements PromotionCallable<ShoppingCartDTO> {

    private ShoppingCartDomain shoppingCartDomain;
    private Map<String, ActivityCacheDTO> activityCacheMap;
    private ShoppingCartDTO shoppingCart;
    private CalcExecuter calcExecuter;

    public CouponFilterCallable(ShoppingCartDomain shoppingCartDomain, Map<String, ActivityCacheDTO> activityCacheMap, ShoppingCartDTO shoppingCart, CalcExecuter calcExecuter) {

        this.shoppingCartDomain = shoppingCartDomain;
        this.activityCacheMap = activityCacheMap;
        this.shoppingCart = shoppingCart;
        this.calcExecuter = calcExecuter;
    }

    @Override
    public ShoppingCartDTO call() throws Exception {

        return this.filterCouponsByShoppingCart();
    }

    public ShoppingCartDTO filterCouponsByShoppingCart() {

        // 计算
        try {
            shoppingCart = shoppingCartDomain.queryActivity(shoppingCart, activityCacheMap);
            List<ShoppingCartOutDTO> scOutList = calcExecuter.calc(new CalcShoppingCart(shoppingCart), activityCacheMap);
            log.info("筛选券码，购物车：" + shoppingCart);
            if (CollectionUtils.isEmpty(scOutList)) {
                return null;
            }

            Set<String> inCouponCodeSet = new HashSet<>();
            Set<String> outCouponCodeSet = new HashSet<>();
            String couponCodes = shoppingCart.getCouponCodes();
            inCouponCodeSet.addAll(Arrays.asList(couponCodes.split(",")));
            shoppingCart.setCouponCodes(null);

            //提取方法
            checkShoppingCartCouponCode(scOutList, outCouponCodeSet, inCouponCodeSet);
            if (CollectionUtils.isNotEmpty(outCouponCodeSet)) {
                StringBuilder resultCouponCode = new StringBuilder();
                outCouponCodeSet.forEach(x -> {
                    resultCouponCode.append(x).append(",");
                    inCouponCodeSet.remove(x);
                });
                shoppingCart.setCouponCodes(resultCouponCode.substring(0, resultCouponCode.length() - 1));
            }

            for (String couponCode : inCouponCodeSet) {
                shoppingCart.addErrorCouponAndReasons(new ErrorCouponAndReason(couponCode, ErrorCodes.COUPON_USE_FAILED));
            }

            return shoppingCart;
        } catch (Exception e) {
            log.warn("CouponFilterCallable.filterCouponsByShoppingCart::{}", shoppingCart.getCouponCodes(), e);

            if (StringUtil.isBlank(shoppingCart.getCouponCodes())) {
                return null;
            }

            List<String> list = Splitter.on(",").trimResults().omitEmptyStrings().splitToList(shoppingCart.getCouponCodes());
            if (list.size() > 1) {
                shoppingCart.addErrorCouponAndReasons(new ErrorCouponAndReason(list.get(list.size() - 1), ErrorCodes.COUPON_USE_FAILED));
            } else {
                shoppingCart.addErrorCouponAndReasons(new ErrorCouponAndReason(list.get(0), ErrorCodes.COUPON_USE_FAILED));
            }
            shoppingCart.setCouponCodes(null);
            return shoppingCart;
        }
    }

    private void checkShoppingCartCouponCode(List<ShoppingCartOutDTO> shoppingCartOutDTOs, Set<String> outCouponCodeSet, Set<String> inCouponCodeSet) {

        Set<String> existInputCouponCode = new HashSet<>();
        for (ShoppingCartOutDTO shoppingCartOutDTO : shoppingCartOutDTOs){

            int rewardTimes = shoppingCartOutDTO.getRewardTimes();
            String outCouponCode = shoppingCartOutDTO.getCouponCode();
            if (StringUtils.isBlank(outCouponCode)) {
                continue;
            }
            String couponCodes = shoppingCartOutDTO.getCouponCode();

            for(String couponCode : couponCodes.split(",")) {

                existInputCouponCode.add(couponCode);
                if (!ActivityTypeEnum.COUPON.equalsCode(shoppingCartOutDTO.getActivityType()) || !couponCodes.contains(couponCode)){
                    continue;
                }

                if(shoppingCartOutDTO.isEffectiveFlag() && rewardTimes-- > 0){
                    outCouponCodeSet.add(couponCode);
                } else {
                    shoppingCart.addErrorCouponAndReasons(new ErrorCouponAndReason(couponCode, shoppingCartOutDTO.getFailedReason()));
                }
            }
        }

        Set<String> noExistInputCouponCode = inCouponCodeSet.stream().filter(x->!existInputCouponCode.contains(x)).collect(Collectors.toSet());
        log.info("noExistInputCouponCode:{}",noExistInputCouponCode);
        if (CollectionUtils.isEmpty(noExistInputCouponCode)){
            noExistInputCouponCode.forEach(x -> shoppingCart.addErrorCouponAndReasons(new ErrorCouponAndReason(x, ErrorCodes.COUPON_USE_FAILED)));
        }
    }
}
