package com.gtech.promotion.calc.function;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Deque;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.gtech.commons.utils.ConvertUtils;
import com.gtech.promotion.calc.CalcActivity;
import com.gtech.promotion.calc.CalcConstants;
import com.gtech.promotion.calc.CalcFunction;
import com.gtech.promotion.calc.CalcResult;
import com.gtech.promotion.calc.model.CalcActivityIncentive;
import com.gtech.promotion.calc.model.CalcShoppingCart;
import com.gtech.promotion.calc.model.CalcShoppingCartItem;
import com.gtech.promotion.utils.TemplateCodeSubstringUtil;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * 原子函数：促销奖励(04) - 打折扣(02)
 * 
 */
@Component
@Slf4j
public class CalcFunction0402 extends CalcFunction04{

    @Getter
    private FunctionEnum function = FunctionEnum.F0402;


    static {
        // Registers the current atomic function
        CalcFunctionRegister.register(FunctionEnum.F0402, new CalcFunction0402());
    }

    /**
     * 构造函数私有化，不允许自己生成该对象
     */
    private CalcFunction0402(){
        // No codes
    }


    /*
     * (non-Javadoc)
     * @see com.gtech.promotion.calc.CalcFunction#calc(com.gtech.promotion.calc.model.ICalcPromoObject, com.gtech.promotion.calc.model.CalcActivityIncentive, java.util.Deque, 
     * com.gtech.promotion.calc.function.FunctionParamMap, com.gtech.promotion.calc.CalcFunction)
     */
    @Override
    public CalcResult calc(CalcShoppingCart promoObject, CalcActivityIncentive incentive, Deque<FunctionEnum> funcList, FunctionParamMap paramMap, CalcFunction caller) {
        log.info("promoObject:{},incentive:{},funcList:{},paramMap:{},caller:{}", JSON.toJSONString(promoObject),JSON.toJSONString(incentive),JSON.toJSON(funcList),JSON.toJSONString(paramMap),JSON.toJSONString(caller));
        try {
            this.loadCommonValue(promoObject);

            // 折扣参数
            BigDecimal paramDiscount = ConvertUtils.toBigDecimal(paramMap.getFuncParamValue(this.function.code()));

            switch (promoObject.getPromoScope()) {

                case ITEM:
					return handler0101(promoObject, incentive, paramDiscount);

                case ORDER:
                case PRODUCT:
					return handler0102or0103(promoObject, incentive, paramDiscount, paramMap);

                case SINGLE:
					return handler0104(promoObject, incentive, paramDiscount);

                default:
                    return CalcResult.ERROR_DEFAULT;
            }

        } catch (RuntimeException e) {
            return CalcResult.ERROR_DEFAULT;
        }
    }

	private CalcResult handler0101(CalcShoppingCart promoObject, CalcActivityIncentive incentive, BigDecimal paramDiscount) {
		int precision = promoObject.getCalcActivity().getPrecison();
		BigDecimal powerPrecison = promoObject.getCalcActivity().getPowerPrecison();
		log.info("promoObject:{},incentive:{},incentiveValue:{}", JSON.toJSONString(promoObject), JSON.toJSONString(incentive), paramDiscount);

        // 活动限制最大优惠金额
        BigDecimal maxMoneyLimit = maxAmountLimit.setScale(precision, CalcConstants.ROUND_DOWN);
		if (powerPrecison != null) {
			maxMoneyLimit = maxMoneyLimit.divide(powerPrecison).setScale(0, CalcConstants.ROUND_DOWN).multiply(powerPrecison);
		}

        // 活动奖励金额
        List<CalcShoppingCartItem> scItems = promoObject.getCalcShoppingCartItemList();
        BigDecimal promoAmountOrigin = promoObject.getPromoAmount();

		BigDecimal incentiveAmount = this.sumIncentiveAmount(scItems, paramDiscount, precision, powerPrecison);
        if (!this.checkLimitedAndCombine(incentive, promoObject.getCalcShoppingCartItemList(), 1, incentiveAmount)) {
            return CalcResult.SUCCESS_LIMITED;
        }

        String substring = TemplateCodeSubstringUtil.subStringTemplateCodeBegin4End8(incentive.getTemplateCode());
        for (CalcShoppingCartItem scItem : scItems){
            BigDecimal beforePromoAmount = scItem.getPromoAmount().setScale(precision, CalcConstants.ROUND_DOWN);
			if (powerPrecison != null) {
				beforePromoAmount = beforePromoAmount.divide(powerPrecison).setScale(0, CalcConstants.ROUND_DOWN).multiply(powerPrecison);
			}

            // 单品 0402 （商品原总价*折扣）保留小数 改成 商品总价-（商品原总价*优惠 保留小数）
//            BigDecimal promAmount = get0206PromAmount(beforePromoAmount.divide(new BigDecimal(scItem.getQuantity())).setScale(precision, CalcConstants.ROUND_DOWN),paramDiscount,scItem,precision)
//            BigDecimal promAmount = beforePromoAmount.multiply(paramDiscount).setScale(precision, CalcConstants.ROUND_DOWN)
			BigDecimal reAmount = beforePromoAmount.multiply(new BigDecimal(1).subtract(paramDiscount)).setScale(precision, CalcConstants.ROUND_DOWN);
			if (powerPrecison != null) {
				reAmount = reAmount.divide(powerPrecison).setScale(0, CalcConstants.ROUND_DOWN).multiply(powerPrecison);
			}
			BigDecimal promAmount = beforePromoAmount.subtract(reAmount);
            if (FunctionEnum.F0206.equalsCode(substring)) {//0206第折
                promAmount = get0206PromAmount(beforePromoAmount.divide(new BigDecimal(scItem.getQuantity())).setScale(precision, CalcConstants.ROUND_DOWN),paramDiscount,scItem,precision);
            }else if (FunctionEnum.F0204.equalsCode(substring)) {//0204 每第
                Integer value = getFuncParamValue(incentive,0,2);
                if(null != value) {
                    int reduceCount = scItem.getQuantity() / value;
                    if(paramDiscount.compareTo(BigDecimal.ZERO) == 0){
                        promAmount = new BigDecimal(reduceCount).multiply(scItem.getProductAmount());
                    }else{
                        promAmount = scItem.getProductAmount().multiply(new BigDecimal(reduceCount)).multiply(paramDiscount).setScale(precision, CalcConstants.ROUND_DOWN);
                    }

                }
            }
			if (powerPrecison != null) {
				promAmount = promAmount.divide(powerPrecison).setScale(0, CalcConstants.ROUND_DOWN).multiply(powerPrecison);
			}
            // 奖励金额 = 当前的商品总价 - 折后价
            BigDecimal rewardAmount = beforePromoAmount.subtract(promAmount);
            //该商品折扣 大于限制金额，则取限制金额
            if (rewardAmount.compareTo(maxMoneyLimit) > 0){
                promAmount = scItem.getPromoAmount().subtract(maxMoneyLimit);//折后价 = 当前的商品总价 - 奖励金额
            }

            CalcActivity calcActivity = scItem.getCurrentActivity(incentive.getActivityCode());
            // 保存新的促销金额
            scItem.setPromoAmount(promAmount);
            calcActivity.setBeforeAmount(beforePromoAmount);
            calcActivity.setAfterAmount(promAmount);
            calcActivity.setEffectiveFlag(true);
        }

		dealMaxOrderAmount(incentiveAmount, promoObject, incentive, promoAmountOrigin);

        return CalcResult.SUCCESS_TRUE;
    }


    public BigDecimal get0206PromAmount(BigDecimal productPrice,BigDecimal paramDiscount,CalcShoppingCartItem scItem,int precision){
        BigDecimal promAmount;
        BigDecimal afterAmount;
        if (paramDiscount.compareTo(BigDecimal.ZERO) == 0){
            afterAmount = BigDecimal.ZERO;
        }else {
            BigDecimal incentiveAmount = productPrice.multiply(new BigDecimal(1).subtract(paramDiscount)).setScale(precision, CalcConstants.ROUND_DOWN);
            afterAmount = productPrice.subtract(incentiveAmount).setScale(precision, CalcConstants.ROUND_DOWN);
        }
        if (scItem.getQuantity() - 1 <= 0) {
            promAmount = afterAmount;
        } else {
            promAmount = productPrice.multiply(new BigDecimal(scItem.getQuantity() - 1)).add(afterAmount).setScale(precision, CalcConstants.ROUND_DOWN);
        }
        return promAmount;
    }

	private CalcResult handler0102or0103(CalcShoppingCart promoObject, CalcActivityIncentive incentive, BigDecimal paramDiscount, FunctionParamMap paramMap) {

		int precision = promoObject.getCalcActivity().getPrecison();
		BigDecimal powerPrecison = promoObject.getCalcActivity().getPowerPrecison();
        String contionParamValue = paramMap.getFuncParamValue(FunctionEnum.F0302.code());
        // 所有sku总价
        BigDecimal promoAmount = promoObject.getPromoAmount();
        // 活动限制最大优惠金额
        BigDecimal maxMoneyLimit = maxAmountLimit.setScale(precision, CalcConstants.ROUND_DOWN);
		if (powerPrecison != null) {
			maxMoneyLimit = maxMoneyLimit.divide(powerPrecison).setScale(0, CalcConstants.ROUND_DOWN).multiply(powerPrecison);
		}

        // 促销优惠的金额
        BigDecimal promoRewardAmount = BigDecimal.ZERO;
        String substring = TemplateCodeSubstringUtil.subStringTemplateCodeBegin4End8(incentive.getTemplateCode());
        //0204第折 （按照商品价格排序 统计总奖励金额）保留小数
        if (FunctionEnum.F0204.equalsCode(substring) || FunctionEnum.F0206.equalsCode(substring)){
			promoRewardAmount = sumIncentiveAmount(promoObject, incentive, contionParamValue, paramDiscount, promoRewardAmount, substring);
			if (powerPrecison != null) {
				promoRewardAmount = promoRewardAmount.divide(powerPrecison).setScale(0, CalcConstants.ROUND_DOWN).multiply(powerPrecison);
			}
            //优惠总金额
            BigDecimal reduceAmount = promoRewardAmount.min(maxMoneyLimit);

            if (!this.checkLimitedAndCombine(incentive, promoObject.getCalcShoppingCartItemList(), 1, reduceAmount)) {
                return CalcResult.SUCCESS_LIMITED;
            }

            //比例
            BigDecimal scale = getScale(reduceAmount, promoAmount);
            //对所有的sku，进行分摊
			handlerShoppingCartItemList(promoObject.getCalcShoppingCartItemList(), incentive, scale, precision, reduceAmount, powerPrecison);
            return CalcResult.SUCCESS_TRUE;
        }else{
            //0202满折  （商品总价-商品总价*折）保留小数 = 总奖励金额
            BigDecimal incentiveAmount = promoAmount.subtract(promoAmount.multiply(paramDiscount)).setScale(precision, CalcConstants.ROUND_DOWN);
			if (powerPrecison != null) {
				incentiveAmount = incentiveAmount.divide(powerPrecison).setScale(0, CalcConstants.ROUND_DOWN).multiply(powerPrecison);
			}
            incentiveAmount = incentiveAmount.min(maxMoneyLimit);
            BigDecimal scale = getScale(incentiveAmount, promoAmount);

            if (!this.checkLimitedAndCombine(incentive, promoObject.getCalcShoppingCartItemList(), 1, incentiveAmount)) {
                return CalcResult.SUCCESS_LIMITED;
            }

			handlerShoppingCartItemList(promoObject.getCalcShoppingCartItemList(), incentive, scale, precision, incentiveAmount, powerPrecison);
            return CalcResult.SUCCESS_TRUE;
        }
    }

	private CalcResult handler0104(CalcShoppingCart promoObject, CalcActivityIncentive incentive, BigDecimal paramDiscount) {

		int precision = promoObject.getCalcActivity().getPrecison();
		BigDecimal powerPrecison = promoObject.getCalcActivity().getPowerPrecison();
		// 活动限制最大优惠金额
        BigDecimal maxMoneyLimit = maxAmountLimit.setScale(precision, CalcConstants.ROUND_DOWN);
		if (powerPrecison != null) {
			maxMoneyLimit = maxMoneyLimit.divide(powerPrecison).setScale(0, CalcConstants.ROUND_DOWN).multiply(powerPrecison);
		}
        // Current promotion total incentive amount
        BigDecimal totalIncentiveAmount = BigDecimal.ZERO;
        
        Map<String, CalcShoppingCartItem> usedItemMap = new HashMap<>();

        // 活动奖励金额
        Integer leftTimes = incentive.getIncentiveTimes();
        String groupCode = incentive.getCalcActivity().getActivityModel().getGroupCode();
        while(leftTimes > 0) {
            CalcShoppingCartItem scItem = this.nextItem(promoObject.getCalcShoppingCartItemList(), usedItemMap);
            if (null == scItem) {
                break;
            }
            Integer times = leftTimes;
            if (leftTimes > scItem.getQuantity()) {
                times = scItem.getQuantity();
                leftTimes -= times;
            } else {
                leftTimes = 0;
            }
            for(int i = 0; i < times; i++) {
                if (!promoObject.deductLeftTimes0104(scItem.getSkuCode(),groupCode)) {
                    leftTimes += (times-i);
                    times = i;
                    break;
                }
            }
            BigDecimal beforeAmount = scItem.getPromoAmount().setScale(precision, CalcConstants.ROUND_DOWN);
			if (powerPrecison != null) {
				beforeAmount = beforeAmount.divide(powerPrecison).setScale(0, CalcConstants.ROUND_DOWN).multiply(powerPrecison);
			}
            BigDecimal beforePrice = beforeAmount.divide(BigDecimal.valueOf(scItem.getQuantity()), CalcConstants.ROUND_DOWN).max(BigDecimal.ZERO);
            BigDecimal afterPrice = beforePrice.multiply(paramDiscount).setScale(precision, CalcConstants.ROUND_DOWN).max(BigDecimal.ZERO).min(beforePrice);
			if (powerPrecison != null) {
				afterPrice = afterPrice.divide(powerPrecison).setScale(0, CalcConstants.ROUND_DOWN).multiply(powerPrecison);
			}
            BigDecimal itemIncentiveAmount = ((beforePrice.subtract(afterPrice)).min(maxMoneyLimit)).multiply(new BigDecimal(times));
            totalIncentiveAmount = totalIncentiveAmount.add(itemIncentiveAmount);
            BigDecimal afterAmount = beforeAmount.subtract(itemIncentiveAmount);

            if (!this.checkLimitedAndCombine(incentive, promoObject.getCalcShoppingCartItemList(), 1, totalIncentiveAmount)) {
                return CalcResult.SUCCESS_LIMITED;
            }

            usedItemMap.put(scItem.keyString(), scItem);

            // Save the new calculation
            if (scItem.getPromoQuantity() < times) {
                scItem.setPromoQuantity(times);
            }
            scItem.setPromoAmount(afterAmount);
            scItem.setCalculateResult(incentive.getActivityCode(), beforeAmount, afterAmount);
        }

        this.dealNoPromoItems(promoObject, usedItemMap);
        if (leftTimes > 0) {
            incentive.setIncentiveTimes(incentive.getIncentiveTimes() - leftTimes);
        }

        //统计优惠金额
        return CalcResult.SUCCESS_TRUE;
    }

    @Override
    protected CalcShoppingCartItem nextItem(List<CalcShoppingCartItem> scItemList, Map<String, CalcShoppingCartItem> usedItemMap) {
        
        if (CollectionUtils.isEmpty(scItemList)) {
            return null;
        }

        CalcShoppingCartItem mostExpensiveItem = null;
        for(CalcShoppingCartItem scItem : scItemList) {

            if (usedItemMap.containsKey(scItem.keyString())) {
                // 当前项已经参与过活动
                continue;
            }

            if (null == mostExpensiveItem || mostExpensiveItem.getProductPrice().compareTo(scItem.getProductPrice()) < 0) {
                // 当前项的单价更贵
                mostExpensiveItem = scItem;
            }
        }

        return mostExpensiveItem;
    }

    /**
     * 统计优惠金额
     */
	private BigDecimal sumIncentiveAmount(List<CalcShoppingCartItem> scItems, BigDecimal paramDiscount, int precision, BigDecimal powerPrecison) {

        BigDecimal incentiveAmount = BigDecimal.ZERO;

        for (CalcShoppingCartItem scItem : scItems){//NOSONAR   010x里面已经判断过， 不可能有空指针了
            BigDecimal beforePromoAmount = scItem.getPromoAmount().setScale(precision, CalcConstants.ROUND_DOWN);
			if (powerPrecison != null) {
				beforePromoAmount = beforePromoAmount.divide(powerPrecison).setScale(0, CalcConstants.ROUND_DOWN).multiply(powerPrecison);
			}
            //单品 0402 （商品原总价*折扣）保留小数
            BigDecimal promAmount = beforePromoAmount.multiply(paramDiscount).setScale(precision, CalcConstants.ROUND_DOWN);
			if (powerPrecison != null) {
				promAmount = promAmount.divide(powerPrecison).setScale(0, CalcConstants.ROUND_DOWN).multiply(powerPrecison);
			}
            // 奖励金额 = 当前的商品总价 - 折后价
            incentiveAmount = incentiveAmount.add(beforePromoAmount.subtract(promAmount));
        }

        return incentiveAmount;
    }

    /**
     * 统计优惠金额
     */
	private BigDecimal sumIncentiveAmount(CalcShoppingCart promoObject, CalcActivityIncentive incentive, String contionParamValue, BigDecimal paramDiscount,
			BigDecimal promoRewardAmount, String f02) {

		int precision = promoObject.getCalcActivity().getPrecison();
		BigDecimal powerPrecison = promoObject.getCalcActivity().getPowerPrecison();
        Integer originalQuantity = promoObject.getOriginalQuantity();
        //记录优惠的次数
        int record = 0;
        //可以打折的次数
        long number = originalQuantity / Long.parseLong(contionParamValue);//contionParamValue:第X件打折扣的 x
        if (FunctionEnum.F0206.equalsCode(f02) && number > 1L){
            number = 1L;
        }
        for (CalcShoppingCartItem scItemP : promoObject.getCalcShoppingCartItemList()) {
            CalcActivity calcActivity = scItemP.getCurrentActivity(incentive.getActivityCode());
            //该sku数量
            Integer quantity = scItemP.getQuantity();
            //同一个sku数量满足优惠次数时， 计算出优惠总金额
            if (number > record) {
                for (int n = 0; n < quantity; n++) {
                    //该sku总价，上个活动计算后价格，如果没有上个活动就是原单价*数量
                    BigDecimal itemPromoAmount = scItemP.getPromoAmount().setScale(precision, CalcConstants.ROUND_DOWN);
					if (powerPrecison != null) {
						itemPromoAmount = itemPromoAmount.divide(powerPrecison).setScale(0, CalcConstants.ROUND_DOWN).multiply(powerPrecison);
					}
                    //参加活动计算前的商品项总价
                    calcActivity.setBeforeAmount(itemPromoAmount);
                    //商品优惠后价格（最初是原价）
					BigDecimal afterProductsPriceP = itemPromoAmount.divide(new BigDecimal(scItemP.getQuantity()), precision, RoundingMode.HALF_UP);
					if (powerPrecison != null) {
						afterProductsPriceP = afterProductsPriceP.divide(powerPrecison).setScale(0, RoundingMode.HALF_UP).multiply(powerPrecison);
					}
                    //第几件  优惠金额
                    BigDecimal promAmt = afterProductsPriceP.subtract(afterProductsPriceP.multiply(paramDiscount));
                    //统计优惠金额
                    promoRewardAmount = promoRewardAmount.add(promAmt);
                    record++;
                    if (record == number) {
                        break;
                    }
                }
            }
        }
        return promoRewardAmount.setScale(precision, CalcConstants.ROUND_DOWN);
    }

}
