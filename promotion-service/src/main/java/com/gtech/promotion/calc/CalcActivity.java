/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.calc;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

import org.apache.commons.collections4.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import com.gtech.commons.exception.ErrorCode;
import com.gtech.commons.exception.Exceptions;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.promotion.calc.model.CalcShoppingCart;
import com.gtech.promotion.code.activity.FuncTypeEnum;
import com.gtech.promotion.code.activity.ItemScopeTypeEnum;
import com.gtech.promotion.dao.model.activity.ActivityModel;
import com.gtech.promotion.dao.model.activity.TPromoIncentiveLimitedVO;
import com.gtech.promotion.dto.cache.ActivityCacheDTO;
import com.gtech.promotion.dto.in.activity.ShoppingCartActivity;
import com.gtech.promotion.exception.ErrorCodes;
import com.gtech.promotion.helper.ActivityIncentiveLimited;
import com.gtech.promotion.helper.RedisOpsHelper;
import com.gtech.promotion.vo.bean.Giveaway;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 促销活动维度计算
 * 
 */
@Getter
@Setter
@ToString
public class CalcActivity {

    //购物车带过来的活动信息
    private ShoppingCartActivity cartActivity;
    @JSONField(serialize = false)
    public ActivityModel getActivityModel() {
        return null == cartActivity ? null : this.cartActivity.getActivityModel();
    }
    @JSONField(serialize = false)
    public String getTenantCode() {
        return null == this.getActivityModel() ? null : this.getActivityModel().getTenantCode();
    }
    @JSONField(serialize = false)
    public String getActivityCode() {
        return null == this.getActivityModel() ? null : this.getActivityModel().getActivityCode(); 
    }
    @JSONField(serialize = false)
    public String getActivityType() {
        return null == this.getActivityModel() ? null : this.getActivityModel().getActivityType(); 
    }
    @JSONField(serialize = false)
    public Integer getItemScopeType() {
        return null == this.getActivityModel() ? ItemScopeTypeEnum.ALL_SCOPE.number() : this.getActivityModel().getItemScopeType();
    }
    @JSONField(serialize = false)
    public String getTemplateCode() {
        return null == this.getActivityModel() ? null : this.getActivityModel().getTemplateCode(); 
    }
    @JSONField(serialize = false)
    public String getTagCode() {
        return TemplateEnum.code2TagCode(this.getTemplateCode());
    }
    @JSONField(serialize = false)
    public String getExclusionKey() {
        return null == this.getActivityModel() ? null : this.getActivityModel().getExclusionKey(); 
    }
    @JSONField(serialize = false)
    public String getProductSelectionType() {
        return null == this.getActivityModel() ? null : this.getActivityModel().getProductSelectionType(); 
    }
    @JSONField(serialize = false)
    public String getRibbonImage() {
        return null == this.getActivityModel() ? null : this.getActivityModel().getRibbonImage();
    }
    @JSONField(serialize = false)
    public String getRibbonPosition() {
        return null == this.getActivityModel() ? null : this.getActivityModel().getRibbonPosition();
    }

    @JSONField(serialize = false)
    public String getRibbonText() {
        return null == this.getActivityModel() ? null : this.getActivityModel().getRibbonText();
    }


    @JSONField(serialize = false)
    public String getActivityBegin() {
        return null == this.getActivityModel() ? null : this.getActivityModel().getActivityBegin();
    }
    @JSONField(serialize = false)
    public String getActivityEnd() {
        return null == this.getActivityModel() ? null : this.getActivityModel().getActivityEnd();
    }
    @JSONField(serialize = false)
    public String getActivityDesc() {
        return null == this.getActivityModel() ? null : this.getActivityModel().getActivityDesc();
    }
    @JSONField(serialize = false)
    public String getActivityRemark() {
        return null == this.getActivityModel() ? null : this.getActivityModel().getActivityRemark();
    }


    // 促销模板
    private CalcTemplate calcTemplate;
    @JSONField(serialize = false)
    public FuncTypeEnum.ScopeEnum getPromoScope(){
        return TemplateEnum.code2Scope(this.getTemplateCode());
    }




    private String memberCode;

    // seqNums:商品池序号
    private Set<String> seqNums;

    // 赠品赠送最大限制数量
    private String giveawayLimitMax;

    // 条件值单位 01：金额  02：数量
    private String conditionUnit;

    // 条件值
    private BigDecimal conditionValue;

    // Activity matched success flag
    private boolean effectiveFlag;

    // Activity use failed reason
    private ErrorCode failedReason;

    // 还差多少满足活动
    private String needMoreAmount;

    // 数值单位
    private String needMoreUnit;

    // 精确度：0元，1角，3分
	private Integer precison;
    
    private BigDecimal powerPrecison;

    //活动赠品列表
    private List<Giveaway> giveaways;

    private int precision;

    // 计算前金额
    private BigDecimal beforeAmount;
    public BigDecimal getBeforeAmount() {
        return null == this.beforeAmount ? BigDecimal.ZERO : this.beforeAmount;
    }

    // 计算后金额
    private BigDecimal afterAmount;
    public BigDecimal getAfterAmount() {
        return null == this.afterAmount ? BigDecimal.ZERO : this.afterAmount;
    }

    // 总减免金额
    public BigDecimal getPromoRewardAmount() {
        BigDecimal bigDecimal = this.getBeforeAmount().subtract(this.getAfterAmount()).setScale(Optional.ofNullable(this.getPrecison()).orElse(CalcConstants.DEFAULT_PRECISION), CalcConstants.DEFAULT_ROUND);
        if(powerPrecison != null) {
        	bigDecimal = bigDecimal.divide(powerPrecison).setScale(0, RoundingMode.DOWN).multiply(powerPrecison);
        }
        /*if (bigDecimal.compareTo(BigDecimal.ZERO) < 0){
            return this.getBeforeAmount();
        }
        设置为允许优惠金额为负数
        */

        return bigDecimal;
    }
    // 减免运费总金额
    private BigDecimal promoRewardPostage = BigDecimal.ZERO;
    //奖励次数
    private int rewardTimes;

    //奖励类型
    private String rewardType;

    // Activity sponsors
    private String sponsors;

    private ActivityIncentiveLimited incentiveLimited = new ActivityIncentiveLimited();

    // 促销对象(勾选商品项)
    private CalcShoppingCart calcShoppingCart;

    // 促销对象(未勾选商品项)
    private CalcShoppingCart calcShoppingCartNo;

    private RedisOpsHelper redisOpsHelper;

    public CalcActivity(CalcActivity source) {

        BeanCopyUtils.copyProps(source, this);
    }

    public CalcActivity(RedisOpsHelper redisOpsHelper, String memberCode) {

        this.redisOpsHelper = redisOpsHelper;
        this.memberCode = memberCode;
    }

    /**
     * 根据活动编码获取活动完整信息
     * 
     * @param cartActivity
     * @param ruleCacheMap 活动缓存Map
     * @return 活动完整信息
     */
    public void loadActivityByCode(ShoppingCartActivity cartActivity, Map<String, ActivityCacheDTO> ruleCacheMap){

        ActivityCacheDTO ruleCacheDTO = ruleCacheMap.get(cartActivity.getActivityCode());
        if (null == ruleCacheDTO){
            throw Exceptions.fail(ErrorCodes.CALC_ACTIVITY_NOT_FOUND, JSON.toJSONString(cartActivity));
        }

        if (CollectionUtils.isNotEmpty(ruleCacheDTO.getIncentiveLimiteds())){
            for (TPromoIncentiveLimitedVO vo : ruleCacheDTO.getIncentiveLimiteds()){
                this.incentiveLimited.setLimited(vo.getLimitationCode(), vo.getLimitationValue());
            }
        }

        this.cartActivity = cartActivity;
        this.setPrecison(CalcConstants.DEFAULT_PRECISION);
        this.giveaways = ruleCacheDTO.getGiveaways();
        this.sponsors = ruleCacheDTO.getActivityModel().getSponsors();
        this.calcTemplate = new CalcTemplate();
        this.calcTemplate.loadTemplate(ruleCacheDTO, this);
    }



    public CalcResult calc() {

        CalcResult calcResult = CalcResult.ERROR_DEFAULT;

        if (null == this.calcTemplate || null == this.calcShoppingCart){
            return calcResult;
        }

        this.calcShoppingCart.setCalcActivity(this);
        return this.calcTemplate.calc(this.calcShoppingCart);
    }

    /**
     * 设置seqNum
     */
    public void addSeqNum(String seqNum){

        if (null == this.seqNums){
            this.seqNums = new HashSet<>();
            this.seqNums.add(seqNum);
        }else{
            this.seqNums.add(seqNum);
        }
    }

    public void setCalculateResult(BigDecimal beforeAmount, BigDecimal afterAmount){

        this.beforeAmount = beforeAmount;
        this.afterAmount = afterAmount;
        this.effectiveFlag = true;
    }

    public void addRewards(CalcActivity calcActivity) {

        if (!this.getActivityCode().equals(calcActivity.getActivityCode())) {
            // Only the same activity can combine rewards
            return;
        }

        if (calcActivity.isEffectiveFlag()) {
            this.effectiveFlag = true;

            this.beforeAmount = this.getBeforeAmount().add(calcActivity.getBeforeAmount());
            this.afterAmount = this.getAfterAmount().add(calcActivity.getAfterAmount());
            this.rewardTimes += calcActivity.rewardTimes;

            this.setGiveaways(giveaways);
        } else {
            this.beforeAmount = this.getBeforeAmount().add(calcActivity.getBeforeAmount());
            this.afterAmount = this.getAfterAmount().add(calcActivity.getBeforeAmount()); // Promotion ineffective, use beforeAmount
        }
    }
}
