/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.calc.model;

import java.io.Serializable;

/**
 *
 */
public class CalcTemplateFunc implements Serializable{

    private static final long serialVersionUID = -3471989929720062989L;

    // 函数类型：01-促销范围 02-促销条件 03-条件参数 04-促销奖励
    private String functionType;

    // 函数编码
    private String functionCode;

    /**
     * 获取【函数类型】
     *
     * @return 函数类型
     */
    public String getFunctionType(){

        return functionType;
    }

    /**
     * 设定【函数类型】
     *
     * @param functionType
     *            函数类型
     */

    public void setFunctionType(String functionType){

        this.functionType = functionType;
    }

    /**
     * 获取【函数编码】
     *
     * @return 函数编码
     */
    public String getFunctionCode(){

        return functionCode;
    }

    /**
     * 设定【函数编码】
     *
     * @param functionCode
     *            函数编码
     */

    public void setFunctionCode(String functionCode){

        this.functionCode = functionCode;
    }

}
