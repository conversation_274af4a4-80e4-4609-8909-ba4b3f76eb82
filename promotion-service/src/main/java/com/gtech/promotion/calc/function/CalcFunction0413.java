/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.calc.function;

import java.math.BigDecimal;
import java.util.Deque;
import java.util.List;

import org.springframework.stereotype.Component;

import com.gtech.commons.utils.ConvertUtils;
import com.gtech.promotion.calc.CalcConstants;
import com.gtech.promotion.calc.CalcFunction;
import com.gtech.promotion.calc.CalcResult;
import com.gtech.promotion.calc.model.CalcActivityIncentive;
import com.gtech.promotion.calc.model.CalcShoppingCart;
import com.gtech.promotion.calc.model.CalcShoppingCartItem;
import com.gtech.promotion.code.activity.SeqNumEnum;
import com.gtech.promotion.exception.ErrorCodes;

import lombok.Getter;

/**
 * 买A优惠B
 * 原子函数：促销奖励(04) - 13买A范围商品B范围商品打折扣
 */
@Component
public class CalcFunction0413 extends CalcFunction04{

    @Getter
    private FunctionEnum function = FunctionEnum.F0413;

    static {
        // Registers the current atomic function
        CalcFunctionRegister.register(FunctionEnum.F0413, new CalcFunction0413());
    }

    /**
     * 构造函数私有化，不允许自己生成该对象
     */
    private CalcFunction0413(){
        // No codes
    }

    /*
     * (non-Javadoc)
     * @see com.gtech.promotion.calc.CalcFunction#calc(com.gtech.promotion.calc.model.ICalcPromoObject, com.gtech.promotion.calc.model.CalcActivityIncentive, java.util.Deque, 
     * com.gtech.promotion.calc.function.FunctionParamMap, com.gtech.promotion.calc.CalcFunction)
     */
    @Override
    public CalcResult calc(CalcShoppingCart promoObject, CalcActivityIncentive incentive, Deque<FunctionEnum> funcList, FunctionParamMap paramMap, CalcFunction caller) {

        try {
            if (!promoObject.getCalcActivity().getSeqNums().contains(SeqNumEnum.B.code())) {//没有奖励商品
                incentive.setFailedReason(ErrorCodes.ACTIVITY_PARAM_ERROR);
                return CalcResult.ERROR_DEFAULT;
            }

            loadCommonValue(promoObject);
            int precision = promoObject.getCalcActivity().getPrecison();
			BigDecimal powerPrecison = promoObject.getCalcActivity().getPowerPrecison();
            //折扣
            BigDecimal paramDiscount = ConvertUtils.toBigDecimal(paramMap.getFuncParamValue(this.function.code()));
            //所有参与优惠商品总价
            BigDecimal promoAmount = promoObject.getPromoAmount(SeqNumEnum.B.code()).setScale(precision, CalcConstants.ROUND_DOWN);
			if (powerPrecison != null) {
				promoAmount = promoAmount.divide(powerPrecison).setScale(0, CalcConstants.ROUND_DOWN).multiply(powerPrecison);
			}

            //活动限制最大优惠金额
            BigDecimal maxLimit = maxAmountLimit.setScale(precision, CalcConstants.ROUND_DOWN);
			if (powerPrecison != null) {
				maxLimit = maxLimit.divide(powerPrecison).setScale(0, CalcConstants.ROUND_DOWN).multiply(powerPrecison);
			}
            //促销优惠的金额
            BigDecimal incentiveAmount = promoAmount.subtract(promoAmount.multiply(paramDiscount)).setScale(precision, CalcConstants.ROUND_DOWN);
			if (powerPrecison != null) {
				incentiveAmount = incentiveAmount.divide(powerPrecison).setScale(0, CalcConstants.ROUND_DOWN).multiply(powerPrecison);
			}

            incentiveAmount = incentiveAmount.min(maxLimit);

            if (!this.checkLimitedAndCombine(incentive, promoObject.getCalcShoppingCartItemList(), 1, incentiveAmount)) {
                return CalcResult.SUCCESS_LIMITED;
            }

            List<CalcShoppingCartItem> itemList = promoObject.getCalcShoppingCartItemList();
            int seqB = countSeqBNum(itemList, incentive.getActivityCode());
            BigDecimal scale = getScale(incentiveAmount, promoAmount);
			handlerShoppingCartItemList(itemList, incentive, incentiveAmount, scale, seqB, precision, powerPrecison);
            return CalcResult.SUCCESS_TRUE;
        }catch (Exception e){
            return CalcResult.ERROR_DEFAULT;
        }
    }


}
