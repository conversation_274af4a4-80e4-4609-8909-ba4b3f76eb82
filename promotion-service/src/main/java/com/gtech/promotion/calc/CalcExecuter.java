/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.calc;

import static com.gtech.promotion.code.activity.ActivityExtendParamsEnum.GIVEAWAY_RULES;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Optional;
import java.util.Set;
import java.util.TreeMap;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.collections4.map.HashedMap;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.ConvertUtils;
import com.gtech.commons.utils.StringUtil;
import com.gtech.member.common.utils.RandomUtil;
import com.gtech.promotion.calc.model.CalcShoppingCart;
import com.gtech.promotion.calc.model.CalcShoppingCartItem;
import com.gtech.promotion.code.activity.ActivityTagCodeEnum;
import com.gtech.promotion.code.activity.FlagTypeEnum;
import com.gtech.promotion.code.activity.FuncTypeEnum;
import com.gtech.promotion.code.activity.GiveawaySortRuleEnum;
import com.gtech.promotion.code.activity.LimitationCodeEnum;
import com.gtech.promotion.code.activity.OpsTypeEnum;
import com.gtech.promotion.code.activity.PriceConditionEnum;
import com.gtech.promotion.component.activity.ActivityPriceComponentDomain;
import com.gtech.promotion.dao.entity.activity.ActivityEntity;
import com.gtech.promotion.dao.model.activity.ActivityScriptModel;
import com.gtech.promotion.dto.cache.ActivityCacheDTO;
import com.gtech.promotion.dto.cache.ProductSkuDetailDTO;
import com.gtech.promotion.dto.in.activity.ShoppingCartActivity;
import com.gtech.promotion.dto.out.activity.ShoppingCartItemActivityOutDTO;
import com.gtech.promotion.dto.out.activity.ShoppingCartItemOutDTO;
import com.gtech.promotion.dto.out.activity.ShoppingCartOutDTO;
import com.gtech.promotion.helper.ActivityCodeComparator;
import com.gtech.promotion.helper.RedisOpsHelper;
import com.gtech.promotion.service.activity.ActivityProductDetailService;
import com.gtech.promotion.service.common.GroovyService;
import com.gtech.promotion.utils.ExpressionHelper;
import com.gtech.promotion.utils.TemplateCodeSubstringUtil;
import com.gtech.promotion.vo.bean.ActivityPeriod;
import com.gtech.promotion.vo.bean.Giveaway;
import com.gtech.promotion.vo.bean.ShoppingCartItem;
import com.gtech.promotion.vo.param.activity.CustomCondition;
import com.gtech.promotion.vo.result.activity.ActivityGroupCache;

import lombok.extern.slf4j.Slf4j;

/**
 * 促销计算主入口
 */
@Slf4j
@Component
public class CalcExecuter {

    @Autowired
    private RedisOpsHelper redisOpsHelper;



    @Autowired
    private GroovyService groovyService;

    @Autowired
    private ActivityPriceComponentDomain activityPriceComponentDomain;


    @Autowired
    private ActivityProductDetailService productDetailService;


    /**
     * 购物车计算<BR>
     * ShoppingCart中已经决定好当前计算所使用的全部促销规则
     * 
     * @param shoppingCart 购物车对象
     * @param ruleCacheMap 活动缓存
     * @return 计算处理结果
     */
    public List<ShoppingCartOutDTO> calc(CalcShoppingCart shoppingCart, Map<String, ActivityCacheDTO> ruleCacheMap) {

        log.info("into Calc Executer!");
        if (MapUtils.isEmpty(ruleCacheMap) || StringUtil.isBlank(shoppingCart.getActivityExpr())) {
            if(null == shoppingCart){
                log.info("shoppingCart is null!");
            }
            if(null == ruleCacheMap){
                log.info("ruleCacheMap is null!");
            }
            return new ArrayList<>();
        }

        // 加载当前购物车的所有活动信息：将一个个商品里面的活动列出来，把商品放到活动里面，活动和商品对象都是唯一的，引用相互指向
        // map（k:activityId - v:活动计算对象）CalcActivity继承TPromoActivityVO  活动计算对象
        long l = System.currentTimeMillis();
        TreeMap<ActivityCodeComparator, CalcActivity> calcActivityMap = this.loadPromoActivities(shoppingCart, ruleCacheMap);
        log.info("TreeMap calcActivityMap: {}", calcActivityMap);
        log.info("loadPromoActivities times: {}", System.currentTimeMillis() - l);

        Iterator<Entry<ActivityCodeComparator, CalcActivity>> iterator1= calcActivityMap.entrySet().iterator();
        Set<String> removedActivitySet = new HashSet<>();
        ActivityScriptModel activityScriptModel = groovyService.findByCode(shoppingCart.getTenantCode(), "ANY_SKU_QUANTITY");
        if (null != activityScriptModel) {
            while (iterator1.hasNext()) {
                Entry<ActivityCodeComparator, CalcActivity> entry = iterator1.next();
                CalcActivity calcActivity = entry.getValue();

                List<CalcShoppingCartItem> calcShoppingCartItemList = calcActivity.getCalcShoppingCart().getCalcShoppingCartItemList();
                Map<String, Object> map = new HashedMap<>();
                map.put("products", BeanCopyUtils.jsonCopyList(calcShoppingCartItemList, ShoppingCartItem.class));
                boolean anySkuQuantity = groovyService.run(activityScriptModel, map);
                if (!anySkuQuantity) {
                    log.info("购物车脚本验证失败-activityCode：{}", calcActivity.getActivityCode());
                    removedActivitySet.add(calcActivity.getActivityCode());
                    iterator1.remove();
                }
            }
        }
        Iterator<Entry<ActivityCodeComparator, CalcActivity>> iterator = calcActivityMap.entrySet().iterator();



        Integer precision = activityPriceComponentDomain.getTenantPrecision(shoppingCart.getTenantCode());


        while (iterator.hasNext()) {
            Entry<ActivityCodeComparator, CalcActivity> entry = iterator.next();
            CalcActivity calcActivity = entry.getValue();
            if (CollectionUtils.isNotEmpty(removedActivitySet)) {
                List<CalcShoppingCartItem> calcShoppingCartItemList = calcActivity.getCalcShoppingCart().getCalcShoppingCartItemList();
                if (CollectionUtils.isNotEmpty(calcShoppingCartItemList)) {
                    for (CalcShoppingCartItem calcShoppingCartItem : calcShoppingCartItemList) {
                        List<CalcActivity> calcActivityList = calcShoppingCartItem.getCalcActivityList();
                        if (CollectionUtils.isNotEmpty(calcActivityList)) {
                            Iterator<CalcActivity> iterator2 = calcActivityList.iterator();
                            while (iterator2.hasNext()) {
                                CalcActivity next = iterator2.next();
                                if (removedActivitySet.contains(next.getActivityCode())) {
                                    iterator2.remove();
                                    calcShoppingCartItem.setActivityExpr(calcShoppingCartItem.getActivityExpr().replace(next.getActivityCode(), "0"));
                                }
                            }
                        }
                    }
                }
            }

            if (TemplateEnum.T0702.equalsCode(calcActivity.getTemplateCode())) {
                calcActivity.setRewardType("04");
            } else {
                calcActivity.setRewardType(calcActivity.getTemplateCode().substring(14, 16));
            }

            //买A优惠B判断是否符合条件
            String subString = TemplateCodeSubstringUtil.subStringTemplateCodeBegin12End16(calcActivity.getTemplateCode());
            if (FuncTypeEnum.IncentiveEnum.BUY_A_REDUCE_B.equalsCode(subString) || FuncTypeEnum.IncentiveEnum.BUY_A_DISCOUNT_B.equalsCode(subString)) {
                Set<String> seqNums = calcActivity.getSeqNums();
                if (!seqNums.contains("2") || !seqNums.contains("1")) {
                    iterator.remove();
                    continue;
                }
            }

                log.info(MessageFormat.format("开始购物车计算-activityCode：{0}", calcActivity.getActivityCode()));


            calcActivity.setPrecison(precision);
			calcActivity.setPowerPrecison(activityPriceComponentDomain.getTenantPowerPrecision(shoppingCart.getTenantCode()));
            // Calculate in the order of FunctionCode(01xx -> 02xx -> 03xx -> 04xx)
            log.info("start calcActivity calc!TemplateCode:{}",calcActivity.getTemplateCode());
            CalcResult calcResult = calcActivity.calc();
            log.info("end calcActivity calc!calcResult:{}", JSON.toJSONString(calcResult));
        }
        ExpressionHelper.clearThreadLocalMap();
        return getPromoActivityRecordList(calcActivityMap, ruleCacheMap,shoppingCart);
    }

    /**
     * 加载当前购物车的所有的活动信息
     * 
     * @param calcShoppingCart 购物车对象
     * @return 活动红黑树
     */
    private TreeMap<ActivityCodeComparator, CalcActivity> loadPromoActivities(CalcShoppingCart calcShoppingCart, Map<String, ActivityCacheDTO> ruleCacheMap) {

        TreeMap<ActivityCodeComparator, CalcActivity> treeMap = new TreeMap<>(Comparator.reverseOrder());

        // 逐个遍历 商品项
        if (CollectionUtils.isEmpty(calcShoppingCart.getCalcShoppingCartItemList())) {
            return treeMap;
        }

        for (CalcShoppingCartItem cscItem : calcShoppingCart.getCalcShoppingCartItemList()) {
            if (CollectionUtils.isNotEmpty(cscItem.getUsedActivitys())) {

                // 判断当前sku是否可以参加当前活动时使用
                cscItem.setActivityExpr(calcShoppingCart.getActivityExpr());
                cscItem.setPromoAmount(cscItem.getProductAmount());
				cscItem.setGroupCacheList(calcShoppingCart.getGroupCacheList());
                for (ShoppingCartActivity activity : cscItem.getUsedActivitys()) {
                    activity.getComparator().setActivityExpr(calcShoppingCart.getActivityExpr());
                }

                // 遍历商品项下活动
                itemActivitys(calcShoppingCart, ruleCacheMap, treeMap, cscItem);
            }
        }

        return treeMap;
    }

    private void itemActivitys(CalcShoppingCart calcShoppingCart, Map<String, ActivityCacheDTO> ruleCacheMap, TreeMap<ActivityCodeComparator, CalcActivity> treeMap,
                    CalcShoppingCartItem cscItem) {

        for (ShoppingCartActivity activity : cscItem.getUsedActivitys()) {
            // 根据活动编码获取活动完整信息
            CalcActivity exitActi = treeMap.get(activity.getComparator());

            CalcActivity calcActivity = exitOrLoad(ruleCacheMap, activity, exitActi, calcShoppingCart.getUserCode());
            //如果活动不存在或者活动里面的商品项不存在   则创建一个商品项，如果已存在  追加到原活动的sku列表上

            CalcShoppingCart calcCart = exitOrNew(exitActi);
            CalcShoppingCart calcCartNo = exitOrNewNo(exitActi);

            calcCart.setLeftTimes0104Map(calcShoppingCart.getLeftTimes0104Map());

            //可能是"1,2"这种，一个商品属于2个池（买A优惠B）
            String seqNums = activity.getSeqNum();
            //如果有多个默认取第一个（捆绑和买送）
            String seqNum = seqNums.contains(",") ? seqNums.substring(0, seqNums.indexOf(',')) : seqNums;
            //整合所有商品池序号
            setSeqNum(calcActivity, seqNums, seqNum);
            //复制一个新的活动放到商品项里面，不能指向同一个对象，要new一个
            CalcActivity itemActivity = itemActivityNew(calcActivity, seqNum);
            // 为当前的商品项关联当前活动
            cscItem.addCalcActivityList(itemActivity);
            // 给活动绑定商品
            calcCart.setPostage(calcShoppingCart.getPostage());
            setCalcPromoObject(cscItem, activity, calcActivity, calcCart, calcCartNo, seqNums, itemActivity);

            // 判断活动价格类型
            priceCondition(cscItem, activity);

            treeMap.put(activity.getComparator(), calcActivity);
        }
    }

    public void priceCondition(CalcShoppingCartItem cscItem, ShoppingCartActivity activity) {
        String priceCondition = activity.getActivityModel().getPriceCondition();
        if (StringUtil.isNotEmpty(priceCondition) && (PriceConditionEnum.LIST_PRICE.equalsCode(priceCondition))) {
            cscItem.setProductPrice(cscItem.getProductListPrice());
            cscItem.setPromoAmount(cscItem.getProductListPrice());
        }
    }

    private void setCalcPromoObject(CalcShoppingCartItem cscItem, ShoppingCartActivity activity, CalcActivity calcActivity, CalcShoppingCart calcCart, CalcShoppingCart calcCartNo,
                                    String seqNums, CalcActivity itemActivity) {

        if (FlagTypeEnum.YES.equalsCode(cscItem.getSelectionFlag())) {//勾选
            calcCart.addCalcShoppingCartItem(cscItem, activity.getSortRule(), seqNums, itemActivity);
            calcActivity.setCalcShoppingCart(calcCart);
        } else {//未勾选
            calcCartNo.addCalcShoppingCartItem(new CalcShoppingCartItem(cscItem), activity.getSortRule(), seqNums, itemActivity);
            calcCartNo.setCalcActivity(itemActivity);
            calcActivity.setCalcShoppingCartNo(calcCartNo);
        }
    }

    /**
     * 加载活动信息，或者直接取已经加载过放入map中的活动。
     * 
     * @param ruleCacheMap
     * @param activity
     * @param exitActi
     * @return 活动
     */
    private CalcActivity exitOrLoad(Map<String, ActivityCacheDTO> ruleCacheMap, ShoppingCartActivity activity, CalcActivity exitActi, String memberCode) {

        if (exitActi != null) {
            return exitActi;
        }

        CalcActivity calcActivity = new CalcActivity(this.redisOpsHelper, memberCode);
        calcActivity.loadActivityByCode(activity, ruleCacheMap);
        return calcActivity;
    }

    /**
     * new活动商品对象，或者获取已经存在的
     * 
     * @param exitActi
     * @return 活动商品项（已勾选）
     */
    private CalcShoppingCart exitOrNew(CalcActivity exitActi) {

        return (null == exitActi || null == exitActi.getCalcShoppingCart()) ? new CalcShoppingCart() : exitActi.getCalcShoppingCart();
    }

    /**
     * new活动商品对象，或者获取已经存在的
     * 
     * @param exitActi
     * @return 活动商品项（未勾选）
     */
    private CalcShoppingCart exitOrNewNo(CalcActivity exitActi) {

        return (null == exitActi || null == exitActi.getCalcShoppingCartNo()) ? new CalcShoppingCart() : exitActi.getCalcShoppingCartNo();
    }

    /**
     * 整合所有商品池序号
     * 
     * @param calcActivity
     * @param seqNums
     * @param seqNum
     */
    private void setSeqNum(CalcActivity calcActivity, String seqNums, String seqNum) {

        //买A优惠B的要放多个
        if (ActivityTagCodeEnum.BUY_A_DISCOUNT_B.equalsCode(calcActivity.getTagCode())) {
            if (StringUtil.isNotBlank(seqNums)) {
                String[] split = seqNums.split(",");
                for (int i = 0; i < split.length; i++) {
                    calcActivity.addSeqNum(split[i]);
                }
            }
        } else {
            calcActivity.addSeqNum(seqNum);//-捆绑活动：一个商品只能属于一个池，否则都会取第一个池
        }
    }

    /**
     * 活动下面的商品项里面的本活动创建
     * 
     * @param calcActivity
     * @param seqNum
     */
    private CalcActivity itemActivityNew(CalcActivity calcActivity, String seqNum) {

        CalcActivity calcActivityNew = new CalcActivity(calcActivity);

        calcActivityNew.setGiveaways(BeanCopyUtils.jsonCopyList(calcActivity.getGiveaways(), Giveaway.class));

        Set<String> hashSet = new HashSet<>();
        hashSet.add(seqNum);
        calcActivityNew.setSeqNums(hashSet);

        return calcActivityNew;
    }

    /**
     * 组合返回对象
     * 
     * @param calcActivityMap 活动集合
     * @return 购物车的出参封装实体
     */
    private List<ShoppingCartOutDTO> getPromoActivityRecordList(TreeMap<ActivityCodeComparator, CalcActivity> calcActivityMap, Map<String, ActivityCacheDTO> ruleCacheMap,CalcShoppingCart shoppingCart) {

        if (MapUtils.isEmpty(calcActivityMap)) {
            return Collections.emptyList();
        }

        // exclusionMap： k-活动id：v-skuCode集合 记录要删除的活动和商品对应关系（相互斥的活动）
        Map<String, Set<String>> exclusionMap = new HashMap<>();
        // 购物车适用活动列表
        List<ShoppingCartOutDTO> shoppingCartOutDTOs = new ArrayList<>();

        BigDecimal total = BigDecimal.ZERO;
        String tenantCode = "";
        for (Entry<ActivityCodeComparator, CalcActivity> entry : calcActivityMap.entrySet()) {
            CalcActivity calcActivity = entry.getValue();
            tenantCode = calcActivity.getTenantCode();
            //1-------------------活动信息---------------------------------
            ShoppingCartOutDTO shoppingCartOutDTO = createShoppingCartOutDTO(calcActivity, ruleCacheMap);

            //2.1---活动信息下的商品信息（已勾选）------------------------
            List<ShoppingCartItemOutDTO> shoppingCartItems = handlerShoppingCartItem(exclusionMap, shoppingCartOutDTO, calcActivity);
            //2.2--活动信息下的商品信息（未勾选）---------------------------
            List<ShoppingCartItemOutDTO> shoppingCartItemsNo = handlerShoppingCartItemNo(calcActivity.getCalcShoppingCartNo());

            shoppingCartItems.addAll(shoppingCartItemsNo);
            if (CollectionUtils.isEmpty(shoppingCartItems)) {//活动下2个商品项都为空 该活动则不输出出去
                continue;
            }
            shoppingCartOutDTO.setShoppingCartItems(shoppingCartItems);
            shoppingCartOutDTO.setLastScale();
            if (shoppingCartOutDTO.isEffectiveFlag()) {
                total = total.add(shoppingCartOutDTO.getPromoRewardAmount());
            }
            shoppingCartOutDTOs.add(shoppingCartOutDTO);
        }
        // 重新循环，删除相互斥活动对中的 不生效商品
        deleteOppositeItem(exclusionMap, shoppingCartOutDTOs);
        resetPrice(shoppingCartOutDTOs, total);
        int precision = activityPriceComponentDomain.getTenantPrecision(tenantCode);
        shoppingCartOutDTOs.forEach(p-> {
            if(null != p.getPromoRewardAmount()){
                p.setPromoRewardAmount(p.getPromoRewardAmount().setScale(precision, CalcConstants.ROUND_DOWN));
            }
            if(null != p.getPromoRewardPostage()){
                p.setPromoRewardPostage(p.getPromoRewardPostage().setScale(precision, CalcConstants.ROUND_DOWN));
            }
            // 设计随机赠送
            Optional.ofNullable(p.getExtendParams())
                    .ifPresent(extendParams->{
                        List<ActivityEntity.GiveawayRule> rules = Optional.ofNullable(BeanCopyUtils.jsonCopyList(extendParams.get(GIVEAWAY_RULES.getCode()), ActivityEntity.GiveawayRule.class))
                                .orElse(new ArrayList<>());
                        Map<Integer, ActivityEntity.GiveawayRule> ruleMap = rules.stream().collect(Collectors.toMap(ActivityEntity.GiveawayRule::getRankParam, a -> a, (a, b) -> a));
                        List<Giveaway> giveaways = Optional.ofNullable(p.getGiveaways()).orElse(new ArrayList<>());
                        for (Giveaway giveaway : giveaways) {
                            ActivityEntity.GiveawayRule giveawayRule = Optional.ofNullable(ruleMap.get(giveaway.getRankParam()))
                                    .filter(rule -> GiveawaySortRuleEnum.RANDOM.equalsCode(rule.getGiveawaySortRule()))
                                    .orElse(new ActivityEntity.GiveawayRule());
                            if (GiveawaySortRuleEnum.RANDOM.equalsCode(giveawayRule.getGiveawaySortRule())) {
                                // 设置随机序号
                                giveaway.setGiveawaySort(Integer.parseInt(RandomUtil.getRandomNumberStr(9)));
                            }
                        }

                    });
        });


        //处理单品不同特价
        for (ShoppingCartOutDTO shoppingCartOutDTO : shoppingCartOutDTOs) {
            singleItemDifferentPrice(tenantCode, shoppingCartOutDTO);
            Optional.ofNullable(shoppingCartOutDTO.getCustomCondition())
                    .filter(StringUtils::isNotEmpty)
                    .ifPresent(customConditionJsonStr ->
                            shoppingCartOutDTO.setCustomConditionsList(
                                    JSONArray.parseArray(customConditionJsonStr, CustomCondition.class)
                            )
                    );
        }



        calPostageLimit(shoppingCartOutDTOs,shoppingCart);


        ExpressionHelper.clearThreadLocalMap();

        return shoppingCartOutDTOs;
    }


    /**
     * 计算运费上限，会将运费设置上限
     */
    public void calPostageLimit(List<ShoppingCartOutDTO> shoppingCartOutDTOs,CalcShoppingCart shoppingCart){

        if (null == shoppingCart.getPostage()) return;

        BigDecimal totalShippingDiscount = shoppingCartOutDTOs.stream().map(ShoppingCartOutDTO::getPromoRewardPostage).reduce(BigDecimal.ZERO, BigDecimal::add);

        // 如果总运费折扣为零，则无需调整
        if (totalShippingDiscount.compareTo(BigDecimal.ZERO) == 0) return;

        // 应用运费上限
        BigDecimal adjustedTotalDiscount = totalShippingDiscount.min(shoppingCart.getPostage());

        // 计算每个促销的实际扣减
        BigDecimal accumulatedDiscount = BigDecimal.ZERO;

        for (int i = 0; i < shoppingCartOutDTOs.size() - 1; i++) {
            ShoppingCartOutDTO dto = shoppingCartOutDTOs.get(i);
            BigDecimal promoRewardPostage = dto.getPromoRewardPostage();
            BigDecimal actualPostageDiscount = promoRewardPostage.multiply(adjustedTotalDiscount)
                    .divide(totalShippingDiscount, 2, RoundingMode.DOWN);
            accumulatedDiscount = accumulatedDiscount.add(actualPostageDiscount);

            dto.setPromoRewardPostage(actualPostageDiscount);
            dto.setPromoRewardAmount(dto.getPromoRewardAmount().subtract(promoRewardPostage.subtract(actualPostageDiscount)));
        }

        // 处理最后一个促销活动，承担所有剩余误差
        ShoppingCartOutDTO lastDto = shoppingCartOutDTOs.get(shoppingCartOutDTOs.size() - 1);
        BigDecimal lastDiscount = adjustedTotalDiscount.subtract(accumulatedDiscount);
        BigDecimal lastPostage = lastDto.getPromoRewardPostage();

        lastDto.setPromoRewardPostage(lastDiscount);
        lastDto.setPromoRewardAmount(lastDto.getPromoRewardAmount().subtract(lastPostage.subtract(lastDiscount)));
    }


    public void singleItemDifferentPrice(String tenantCode, ShoppingCartOutDTO shoppingCartOutDTO) {
        List<ShoppingCartItemOutDTO> shoppingCartItems = shoppingCartOutDTO.getShoppingCartItems();

        //商品下活动 sku
        for (ShoppingCartItemOutDTO shoppingCartItem : shoppingCartItems) {

            String skuCode = shoppingCartItem.getSkuCode();
            //sku下对应的活动
            List<ShoppingCartItemActivityOutDTO> shoppingCartItemActivityList = shoppingCartItem.getShoppingCartItemActivitys();
            List<String> activityCodes = shoppingCartItemActivityList.stream().filter(x -> OpsTypeEnum.OPS_108.code().equals(x.getOpsType()))
                    .map(ShoppingCartItemActivityOutDTO::getActivityCode).collect(Collectors.toList());

            List<ProductSkuDetailDTO> productSkuDetailDTOS = productDetailService
                    .queryOneGroupByActivityCodes(tenantCode,activityCodes);

            Map<String, List<ProductSkuDetailDTO>> activitySkuMap = productSkuDetailDTOS.stream().collect(Collectors.groupingBy(ProductSkuDetailDTO::getActivityCode));

            for (ShoppingCartItemActivityOutDTO dto : shoppingCartItemActivityList) {
                List<ProductSkuDetailDTO> productSkuDetailDTOList = activitySkuMap.get(dto.getActivityCode());
                if (CollectionUtils.isNotEmpty(productSkuDetailDTOList)){
                    for (ProductSkuDetailDTO detailDTO : productSkuDetailDTOList) {

                        if (StringUtil.isNotEmpty(detailDTO.getSkuCode())){
                            if (skuCode.equals(detailDTO.getSkuCode())){
                                dto.setPromoPrice(detailDTO.getPromoPrice());
                                break;
                            }
                        }else {
                            dto.setPromoPrice(detailDTO.getPromoPrice());
                        }
                    }
                }
            }
        }
    }

    public void resetPrice(List<ShoppingCartOutDTO> shoppingCartOutDTOs, BigDecimal total) {
        Map<String, BigDecimal> map = new HashedMap<>();
        BigDecimal total2 = BigDecimal.ZERO;
        BigDecimal subtractMax = BigDecimal.ZERO;
        String subtractMaxSkuCode = "";
        BigDecimal priceMax = BigDecimal.ZERO;
        String priceMaxSkuCode = "";
        for (ShoppingCartOutDTO shoppingCartOutDTO : shoppingCartOutDTOs) {
            for (ShoppingCartItemOutDTO shoppingCartItem : shoppingCartOutDTO.getShoppingCartItems()) {
                if (FlagTypeEnum.YES.equalsCode(shoppingCartItem.getSelectionFlag()) && !map.containsKey(shoppingCartItem.getCombineOrSkuCode())) {
                    BigDecimal multiply = shoppingCartItem.getProductPrice().multiply(new BigDecimal(shoppingCartItem.getQuantity()));
                    BigDecimal subtract = multiply.subtract(shoppingCartItem.getPromoAmount());
                    map.put(shoppingCartItem.getCombineOrSkuCode(), subtract);
                    total2 = total2.add(subtract);
                    if (subtractMax.compareTo(subtract) < 0) {
                        subtractMax = subtract;
                        subtractMaxSkuCode = shoppingCartItem.getCombineOrSkuCode();
                    }
                    if (subtract.compareTo(BigDecimal.ZERO) > 0 && priceMax.compareTo(shoppingCartItem.getPromoAmount()) < 0) {
                        priceMax = shoppingCartItem.getPromoAmount();
                        priceMaxSkuCode = shoppingCartItem.getCombineOrSkuCode();
                    }
                }
            }
        }
        if (total.subtract(total2).abs().compareTo(new BigDecimal("0.01")) == 0){
            if (total.compareTo(total2) < 0) {
                // 商品金额多扣除了
                for (ShoppingCartOutDTO shoppingCartOutDTO : shoppingCartOutDTOs) {
                    for (ShoppingCartItemOutDTO shoppingCartItem : shoppingCartOutDTO.getShoppingCartItems()) {
                        if (FlagTypeEnum.YES.equalsCode(shoppingCartItem.getSelectionFlag()) && subtractMaxSkuCode.equals(shoppingCartItem.getCombineOrSkuCode())) {
                            shoppingCartItem.setPromoAmount(shoppingCartItem.getPromoAmount().add(total2.subtract(total)));
                        }
                    }
                }
            } else if (total.compareTo(total2) > 0){
                // 商品金额少扣除了
                for (ShoppingCartOutDTO shoppingCartOutDTO : shoppingCartOutDTOs) {
                    for (ShoppingCartItemOutDTO shoppingCartItem : shoppingCartOutDTO.getShoppingCartItems()) {
                        if (FlagTypeEnum.YES.equalsCode(shoppingCartItem.getSelectionFlag()) && priceMaxSkuCode.equals(shoppingCartItem.getCombineOrSkuCode())) {
                            shoppingCartItem.setPromoAmount(shoppingCartItem.getPromoAmount().subtract(total.subtract(total2)));
                        }
                    }
                }
            }
        }
    }

    /**
     * 组装活动信息下的已勾选的商品信息
     * 
     * @param exclusionMap 互斥活动的map
     * @param shoppingCartOutDTO 购物车最外层活动
     * @param calcActivity 已勾选的商品信息
     * @return 商品信息
     */
    public List<ShoppingCartItemOutDTO> handlerShoppingCartItem(Map<String, Set<String>> exclusionMap, ShoppingCartOutDTO shoppingCartOutDTO, CalcActivity calcActivity) {

        List<ShoppingCartItemOutDTO> shoppingCartItemOutDTOs = new ArrayList<>();
        if (null == calcActivity || null == calcActivity.getCalcShoppingCart()) {
            return shoppingCartItemOutDTOs;
        }

        List<CalcShoppingCartItem> list = calcActivity.getCalcShoppingCart().getCalcShoppingCartItemList();
        if (CollectionUtils.isNotEmpty(list)) {
            for (CalcShoppingCartItem item : list) {//遍历活动下商品
                if (checkExpress(exclusionMap, shoppingCartOutDTO, item)) {
                    continue;
                }
                //3.1---------------------------活动信息下的商品信息下的活动信息（已勾选）----------------------------
                shoppingCartItemOutDTOs.add(createShoppingCartItemOutDTO(item));//将该商品添加到该活动的商品列表中
            }
        }

        return shoppingCartItemOutDTOs;
    }

    /**
     * 组装活动信息下的未勾选的商品信息
     * 
     * @param calcPromoObjectNo 未勾选的商品信息
     * @return 商品信息
     */
    private List<ShoppingCartItemOutDTO> handlerShoppingCartItemNo(CalcShoppingCart calcPromoObjectNo) {

        List<ShoppingCartItemOutDTO> shoppingCartItemOutDTOsNo = new ArrayList<>();//未勾选的商品
        if (null == calcPromoObjectNo) {
            return shoppingCartItemOutDTOsNo;
        }

        List<CalcShoppingCartItem> list = calcPromoObjectNo.getCalcShoppingCartItemList();
        if (CollectionUtils.isEmpty(list)) {
            return shoppingCartItemOutDTOsNo;
        }

        for (CalcShoppingCartItem item : list) {//遍历活动下商品

			boolean expressFlag = false;
			List<ActivityGroupCache> groupCacheList = item.getGroupCacheList();
			if (CollectionUtils.isEmpty(groupCacheList)) {
				expressFlag = ExpressionHelper.checkActivitySku(item.getActivityExpr(), item.getCalcActivity(), item.getCombineOrSkuCode(), false);
			} else {
				// 分组叠加互斥
				String groupCode = item.getCalcActivity().getActivityModel().getGroupCode();
				String activityCode = item.getCalcActivity().getActivityModel().getActivityCode();
				expressFlag = ExpressionHelper.checkActivitySkuWithGroup(item.getSkuCode(), groupCode, activityCode, groupCacheList, false);
			}
            if (!expressFlag) {//默认先添加到优先级最高的活动中，后面互斥的活动就不添加了，叠加的活动可以添加
                continue;
            }
            //3.2------------------------------活动信息下的商品信息下的活动信息（未勾选）-------------------------------------
            shoppingCartItemOutDTOsNo.add(createShoppingCartItemOutDTO(item));//将该商品添加到该活动的商品列表中
        }

        return shoppingCartItemOutDTOsNo;
    }

    /**
     * 记录需要删除的活动和商品对应关系：不满足条件的互斥活动处理
     * 
     * @param exclusionMap
     * @param scOut
     * @param cscItem
     */
    private boolean checkExpress(Map<String, Set<String>> exclusionMap, ShoppingCartOutDTO scOut, CalcShoppingCartItem cscItem) {

		List<ActivityGroupCache> groupCacheList = cscItem.getGroupCacheList();

		// 当该商品不满足活动计算条件时，相同的商品不能同时出现在两个互斥活动中
		boolean expressFlag = false;
		String groupCode = null;
		if (CollectionUtils.isEmpty(groupCacheList)) {
			expressFlag = ExpressionHelper.checkActivitySku(cscItem.getActivityExpr(), scOut.getCalcActivity(), cscItem.getSkuCode(), false);
		} else {
			// 分组叠加互斥
			groupCode = scOut.getGroupCode();
			if (!scOut.isEffectiveFlag()) {
				expressFlag = false;
			} else {
				expressFlag = ExpressionHelper.checkActivitySkuWithGroup(cscItem.getSkuCode(), groupCode, scOut.getActivityCode(), groupCacheList, false);
			}
		}

        //跟前面已绑定的活动互斥（已经存在于高优先级中了，不管满足与否）
        if (!expressFlag) {

            // 不满足当前活动条件，不把该商品添加到该活动下，因为已经有存在于高优先级活动中了
            if (!scOut.isEffectiveFlag()) {

                //（高优先级的不满足，低优先级的也不满足的时候，删除低优先级中的）
				putExclusionMap(exclusionMap, scOut.getActivityCode(), cscItem.getCombineOrSkuCode());
                return true;

            } else {

                // 满足当前活动条件（说明前面高优先级的活动都未能满足计算条件，因为满足的就被计算了，后面低优先级的就拿不到了），要删除互斥的那个活动的当前商品
                final String leftCodes = ExpressionHelper.getThreadLocalMap().get(cscItem.getCombineOrSkuCode());
                if (StringUtil.isBlank(leftCodes)) {
                    return true;
                }

				makeExclusionMap(exclusionMap, scOut, cscItem, leftCodes);
            }
        }

        return false;
    }

	private void makeExclusionMap(Map<String, Set<String>> exclusionMap, ShoppingCartOutDTO scOut, CalcShoppingCartItem cscItem, final String leftCodes) {
		List<ActivityGroupCache> groupCacheList = cscItem.getGroupCacheList();
		for (String leftCode : leftCodes.split("-")) {// 找到与该活动互斥的这个活动
			boolean flag = false;
			if (CollectionUtils.isEmpty(groupCacheList)) {
				flag = ExpressionHelper.isExclusion(leftCode, scOut.getActivityCode(), cscItem.getActivityExpr());
			} else {
				flag = ExpressionHelper.isExclusionWithGroup(leftCode, scOut.getGroupCode(), groupCacheList);
			}

			if (StringUtil.isNotBlank(leftCode) && flag) {
				if (CollectionUtils.isEmpty(groupCacheList)) {
					// （高优先级的不满足，低优先级的满足的时候，删除高优先级中的）
					putExclusionMap(exclusionMap, leftCode, cscItem.getCombineOrSkuCode());
				} else {
					putExclusionMap(exclusionMap, scOut.getActivityCode(), cscItem.getCombineOrSkuCode());
				}
			}
		}
	}

    /**
     * 添加互斥关系
     * 
     * @param exclusionMap -- <templateCode, <skuCode, skuCode, ...>>
     */
    private void putExclusionMap(Map<String, Set<String>> exclusionMap, String templateCode, String skuCode) {

        Set<String> tempSet = exclusionMap.get(templateCode);
        if (null == tempSet) {
            tempSet = new HashSet<>();
        }
        tempSet.add(skuCode);
        exclusionMap.put(templateCode, tempSet); // 记录要删除的活动和商品对应关系
    }

    /**
     * 组装购物车商品项参与的活动集合
     * 
     * @param calcActivityList
     *            原始活动集合
     * @return 组装后的活动集合
     */
    private List<ShoppingCartItemActivityOutDTO> setShoppingCartItemActivityOutDTOs(List<CalcActivity> calcActivityList) {

        List<ShoppingCartItemActivityOutDTO> shoppingCartItemActivityOutDTOs = new ArrayList<>();
        if (!CollectionUtils.isEmpty(calcActivityList)) {
            for (CalcActivity calcActivity : calcActivityList) {
                shoppingCartItemActivityOutDTOs.add(createShoppingCartItemActivityOutDTO(calcActivity));
            }
        }
        return shoppingCartItemActivityOutDTOs;
    }

    /**
     * 构建商品项参与的活动对象
     * 
     * @param calcActivity
     *            原始活动对象
     * @return 构建后的活动对象
     */
    private ShoppingCartItemActivityOutDTO createShoppingCartItemActivityOutDTO(CalcActivity calcActivity) {

        ShoppingCartItemActivityOutDTO shoppingCartItemActivityOutDTO = new ShoppingCartItemActivityOutDTO();
        shoppingCartItemActivityOutDTO.setEffectiveFlag(calcActivity.isEffectiveFlag());
        shoppingCartItemActivityOutDTO.setActivityCode(calcActivity.getActivityCode());
        shoppingCartItemActivityOutDTO.setCouponCode(String.join(",", calcActivity.getCartActivity().getCouponCodes()));
        shoppingCartItemActivityOutDTO.setActivityLabel(calcActivity.getCartActivity().getActivityLabel());
        shoppingCartItemActivityOutDTO.setActivityName(calcActivity.getCartActivity().getActivityName());
        shoppingCartItemActivityOutDTO.setAfterAmount(calcActivity.getAfterAmount());
        shoppingCartItemActivityOutDTO.setPromotionCategory(calcActivity.getActivityModel().getPromotionCategory());
        shoppingCartItemActivityOutDTO.setBeforeAmount(calcActivity.getBeforeAmount());
        shoppingCartItemActivityOutDTO.setOpsType(calcActivity.getActivityModel().getOpsType());
        shoppingCartItemActivityOutDTO.setPriceCondition(calcActivity.getActivityModel().getPriceCondition());


        shoppingCartItemActivityOutDTO.setActivityEnd(calcActivity.getActivityEnd());
        shoppingCartItemActivityOutDTO.setActivityBegin(calcActivity.getActivityBegin());
        shoppingCartItemActivityOutDTO.setActivityRemark(calcActivity.getActivityRemark());
        shoppingCartItemActivityOutDTO.setActivityDesc(calcActivity.getActivityDesc());

        if (FuncTypeEnum.IncentiveEnum.GIVEAWAY.rewardType().equals(calcActivity.getRewardType())) {
            shoppingCartItemActivityOutDTO.setGiveawayLimitMax(calcActivity.getGiveawayLimitMax());
            shoppingCartItemActivityOutDTO.setGiveaways(calcActivity.getGiveaways());
        }
        FuncTypeEnum.ScopeEnum promoScope = calcActivity.getPromoScope();
        shoppingCartItemActivityOutDTO.setPromoScope(promoScope.code());
        shoppingCartItemActivityOutDTO.setActivityType(calcActivity.getActivityType());
        return shoppingCartItemActivityOutDTO;
    }

    /**
     * 根据购物车商品项，创建活动对象下的商品对象
     * 
     * @param csci
     *            购物车商品项
     * @return 商品对象
     */
    private ShoppingCartItemOutDTO createShoppingCartItemOutDTO(CalcShoppingCartItem csci) {

        ShoppingCartItemOutDTO shoppingCartItemOutDTO = new ShoppingCartItemOutDTO();
        shoppingCartItemOutDTO.setProductPrice(csci.getProductPrice());
        shoppingCartItemOutDTO.setSelectionFlag(csci.getSelectionFlag());
        shoppingCartItemOutDTO.setPromoAmount(csci.getPromoAmount());
        shoppingCartItemOutDTO.setPromoQuantity(ConvertUtils.toInteger(csci.getPromoQuantity(), csci.getQuantity()));
        shoppingCartItemOutDTO.setQuantity(csci.getQuantity());
        shoppingCartItemOutDTO.setSkuCode(StringUtil.isBlank(csci.getCombineSkuCode()) ? csci.getSkuCode() : null);
        shoppingCartItemOutDTO.setCombineSkuCode(csci.getCombineSkuCode());
        shoppingCartItemOutDTO.setCombineOrSkuCode(csci.getCombineOrSkuCode());
        shoppingCartItemOutDTO.setShoppingCartItemActivitys(setShoppingCartItemActivityOutDTOs(csci.getCalcActivityList()));//设置商品下活动列表
        return shoppingCartItemOutDTO;
    }

    /**
     * 根据活动信息创建购物车最外层活动对象
     * 
     * @param calcActivity 活动计算类
     * @return 活动对象
     */
    private ShoppingCartOutDTO createShoppingCartOutDTO(CalcActivity calcActivity, Map<String, ActivityCacheDTO> ruleCacheMap) {

        ActivityCacheDTO activityCacheDTO = ruleCacheMap.get(calcActivity.getActivityCode());

        ShoppingCartOutDTO shoppingCartOutDTO = new ShoppingCartOutDTO();

        shoppingCartOutDTO.setActivityCode(calcActivity.getActivityCode());
		shoppingCartOutDTO.setGroupCode(calcActivity.getActivityModel().getGroupCode());
        shoppingCartOutDTO.setCalcActivity(calcActivity);
        shoppingCartOutDTO.setTemplateCode(calcActivity.getTemplateCode());
        shoppingCartOutDTO.setCouponCode(String.join(",", calcActivity.getCartActivity().getCouponCodes()));
        shoppingCartOutDTO.setActivityLabel(calcActivity.getCartActivity().getActivityLabel());
        shoppingCartOutDTO.setActivityName(calcActivity.getCartActivity().getActivityName());
        shoppingCartOutDTO.setPromotionCategory(calcActivity.getActivityModel().getPromotionCategory());
        shoppingCartOutDTO.setPromotionCategoryName(calcActivity.getActivityModel().getPromotionCategoryName());
        shoppingCartOutDTO.setAfterAmount(calcActivity.getAfterAmount());
        shoppingCartOutDTO.setBeforeAmount(calcActivity.getBeforeAmount());
        //优惠金额
        shoppingCartOutDTO.setPromoRewardAmount(calcActivity.getPromoRewardAmount().add(calcActivity.getPromoRewardPostage()));
        shoppingCartOutDTO.addRewardTimes(calcActivity.getRewardTimes());
        shoppingCartOutDTO.setRewardType(calcActivity.getRewardType());
        shoppingCartOutDTO.setPromoScope(calcActivity.getPromoScope().code());
        shoppingCartOutDTO.setSponsors(calcActivity.getSponsors());
        shoppingCartOutDTO.setActivityType(calcActivity.getActivityType());
        shoppingCartOutDTO.setActivityUrl(activityCacheDTO.getActivityModel().getActivityUrl());
        shoppingCartOutDTO.setActivityEnd(calcActivity.getActivityEnd());
        shoppingCartOutDTO.setActivityBegin(calcActivity.getActivityBegin());
        shoppingCartOutDTO.setActivityRemark(calcActivity.getActivityRemark());
        shoppingCartOutDTO.setActivityDesc(calcActivity.getActivityDesc());
        shoppingCartOutDTO.setExtendParams(activityCacheDTO.getActivityModel().getExtendParams());
        if ("0102020503020404,0102020303020407,0102020303020408".indexOf(calcActivity.getTemplateCode()) < 0) {
            shoppingCartOutDTO.setNeedMoreAmount(calcActivity.getNeedMoreAmount());
            shoppingCartOutDTO.setNeedMoreUnit(calcActivity.getNeedMoreUnit());
        }
        shoppingCartOutDTO.setProductSelectionType(calcActivity.getProductSelectionType());
        if (FuncTypeEnum.IncentiveEnum.GIVEAWAY.rewardType().equals(calcActivity.getRewardType())) {
            shoppingCartOutDTO.setConditionValue(calcActivity.getConditionValue());
            shoppingCartOutDTO.setConditionUnit(calcActivity.getConditionUnit());
            shoppingCartOutDTO.setGiveawayLimitMax(calcActivity.getGiveawayLimitMax());
            shoppingCartOutDTO.setGiveaways(BeanCopyUtils.jsonCopyList(calcActivity.getGiveaways(), Giveaway.class));
        }
        shoppingCartOutDTO.setUserLimitation(String.valueOf(calcActivity.getIncentiveLimited().getLimitedValue(LimitationCodeEnum.USER_TOTAL_COUNT)));
        //运费优惠金额
        shoppingCartOutDTO.setPromoRewardPostage(calcActivity.getPromoRewardPostage());
        shoppingCartOutDTO.setEffectiveFlag(calcActivity.isEffectiveFlag());
        shoppingCartOutDTO.setFailedReason(calcActivity.isEffectiveFlag() ? null : calcActivity.getFailedReason());
        shoppingCartOutDTO.setActivityPeriod(BeanCopyUtils.jsonCopyBean(activityCacheDTO.getPeriodModel(), ActivityPeriod.class));
        shoppingCartOutDTO.setRibbonImage(calcActivity.getRibbonImage());
        shoppingCartOutDTO.setRibbonPosition(calcActivity.getRibbonPosition());
        shoppingCartOutDTO.setRibbonText(calcActivity.getRibbonText());
        shoppingCartOutDTO.setOpsType(calcActivity.getActivityModel().getOpsType());
        shoppingCartOutDTO.setCustomCondition(calcActivity.getActivityModel().getCustomCondition());
        return shoppingCartOutDTO;
    }

    /**
     * 删除相互斥活动对中的 不生效商品：具体规则如下
     * 1、高优先级活动不满足，低优先级满足的，要删除高优先级和商品对应关系
     * 2、高优先级活动不满足，低优先级也不满足的，要删除低优先级和商品对应关系
     * 3、活动下没有商品项时该活动也要删除
     * 
     * @param exclusionMap
     * @param shoppingCartOutDTOs
     */
    public void deleteOppositeItem(Map<String, Set<String>> exclusionMap, List<ShoppingCartOutDTO> shoppingCartOutDTOs) {

        if (MapUtils.isEmpty(exclusionMap) || CollectionUtils.isEmpty(shoppingCartOutDTOs)) {
            return;
        }

        log.info("*******************************************************************");
        log.info("************CalcExecuter#deleteOppositeItem************************");
        log.info("*******************************************************************");

        Iterator<ShoppingCartOutDTO> scIterator = shoppingCartOutDTOs.iterator();
        while (scIterator.hasNext()) {

            ShoppingCartOutDTO sc = scIterator.next();

            activityItems(exclusionMap, sc, sc.getShoppingCartItems().iterator());

            if (CollectionUtils.isEmpty(sc.getShoppingCartItems())) {
                scIterator.remove();//活动下没有商品项时该活动也要删除
            }
        }
    }

    public void activityItems(Map<String, Set<String>> exclusionMap, ShoppingCartOutDTO shoppingCartOutDTO, Iterator<ShoppingCartItemOutDTO> iterator2) {

        while (iterator2.hasNext()) {
            ShoppingCartItemOutDTO shoppingCartItemOutDTO = iterator2.next();
            Iterator<ShoppingCartItemActivityOutDTO> iterator3 = shoppingCartItemOutDTO.getShoppingCartItemActivitys().iterator();//商品下的活动列表
            while (iterator3.hasNext()) {
                ShoppingCartItemActivityOutDTO next3 = iterator3.next();
                String activityCode = next3.getActivityCode();
                if (exclusionMap.containsKey(activityCode)) {
                    Set<String> set = exclusionMap.get(activityCode);
                    if (set.contains(shoppingCartItemOutDTO.getCombineOrSkuCode())) {
                        iterator3.remove();//先删所有活动下商品项里面的活动
                    }
                }
            }
            if (exclusionMap.containsKey(shoppingCartOutDTO.getActivityCode())) {
                Set<String> set = exclusionMap.get(shoppingCartOutDTO.getActivityCode());//要删除的skuCode
                if (set.contains(shoppingCartItemOutDTO.getCombineOrSkuCode())) {
                    iterator2.remove();//再删活动下商品项
                }
            }
        }
    }

}
