/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.calc;


/**
 * 函数计算结果
 * 
 */
public class CalcResult{

    // 计算结果编码: 0000 - 正常返回
    private String resultCode;

    // 计算结果信息
    private String resultMessage;

    public static final CalcResult SUCCESS_TRUE = new CalcResult("0000", "正确计算完毕");

    public static final CalcResult SUCCESS_FALSE = new CalcResult("0001", "促销计算失败");

    public static final CalcResult SUCCESS_LIMITED = new CalcResult("0002", "促销计算奖励限额失败");

    public static final CalcResult ERROR_DEFAULT = new CalcResult("FFFF", "计算发生错误，无需继续计算");

    /**
     * 构造函数
     * 
     * @param code
     *            计算结果编码
     * @param message
     *            计算结果信息
     */
    public CalcResult(String code, String message){

        this.resultCode = code;
        this.resultMessage = message;
    }

    /**
     * 获取【计算结果编码】
     *
     * @return 计算结果编码
     */
    public String getResultCode(){

        return resultCode;
    }

    /**
     * 设定【计算结果编码】
     *
     * @param resultCode
     *            计算结果编码
     */

    public void setResultCode(String resultCode){

        this.resultCode = resultCode;
    }

    /**
     * 获取【计算结果信息】
     *
     * @return 计算结果信息
     */
    public String getResultMessage(){

        return resultMessage;
    }

    /**
     * 设定【计算结果信息】
     *
     * @param resultMessage
     *            计算结果信息
     */

    public void setResultMessage(String resultMessage){

        this.resultMessage = resultMessage;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj instanceof CalcResult) {
            return this.resultCode.equals(((CalcResult)obj).getResultCode());
        }
        return false;

    }

    @Override
    public int hashCode() {
        return super.hashCode();
    }
}
