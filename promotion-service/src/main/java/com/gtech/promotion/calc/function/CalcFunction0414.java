/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.calc.function;

import java.math.BigDecimal;
import java.util.Deque;

import org.springframework.stereotype.Component;

import com.gtech.commons.utils.ConvertUtils;
import com.gtech.promotion.calc.CalcConstants;
import com.gtech.promotion.calc.CalcFunction;
import com.gtech.promotion.calc.CalcResult;
import com.gtech.promotion.calc.model.CalcActivityIncentive;
import com.gtech.promotion.calc.model.CalcShoppingCart;
import com.gtech.promotion.calc.model.CalcShoppingCartItem;

import lombok.Getter;

/**
 * 原子函数：促销奖励(04) - 每件减金额(14)
 */
@Component
public class CalcFunction0414 extends CalcFunction04{

    @Getter
    private FunctionEnum function = FunctionEnum.F0414;

    static {
        // Registers the current atomic function
        CalcFunctionRegister.register(FunctionEnum.F0414, new CalcFunction0414());
    }

    /**
     * 构造函数私有化，不允许自己生成该对象
     */
    private CalcFunction0414(){
        // No codes
    }

    /*
     * (non-Javadoc)
     * @see com.gtech.promotion.calc.CalcFunction#calc(com.gtech.promotion.calc.model.ICalcPromoObject, com.gtech.promotion.calc.model.CalcActivityIncentive, java.util.Deque, 
     * com.gtech.promotion.calc.function.FunctionParamMap, com.gtech.promotion.calc.CalcFunction)
     */
    @Override
    public CalcResult calc(CalcShoppingCart promoObject, CalcActivityIncentive incentive, Deque<FunctionEnum> funcList, FunctionParamMap paramMap, CalcFunction caller) {

        try {
            loadCommonValue(promoObject);

            int precision = promoObject.getCalcActivity().getPrecison();
			BigDecimal powerPrecison = promoObject.getCalcActivity().getPowerPrecison();
            //04促销奖励参数的数值  减paramAmount元
            String paramValue = paramMap.getFuncParamValue(this.function.code());
            //奖励金额
            BigDecimal incentiveValue = ConvertUtils.toBigDecimal(paramValue).setScale(precision, CalcConstants.ROUND_DOWN);
			if (powerPrecison != null) {
				incentiveValue = incentiveValue.divide(powerPrecison).setScale(0, CalcConstants.ROUND_DOWN).multiply(powerPrecison);
			}

			CalcResult calcResult = handler0414Product(promoObject, incentive, incentiveValue, true);
            if (!CalcResult.SUCCESS_TRUE.equals(calcResult)) {
                return calcResult;
            }
            if (!this.checkActivitySku(incentive, promoObject.getCalcShoppingCartItemList(), false)) {
                return CalcResult.SUCCESS_LIMITED;
            }

			return handler0414Product(promoObject, incentive, incentiveValue, false);

        } catch (Exception e) {
            return CalcResult.ERROR_DEFAULT;
        }
    }

	private CalcResult handler0414Product(CalcShoppingCart promoObject, CalcActivityIncentive incentive, BigDecimal incentiveValue, boolean checkLimited) {
		int precision = promoObject.getCalcActivity().getPrecison();
		BigDecimal powerPrecison = promoObject.getCalcActivity().getPowerPrecison();
        // 活动奖励金额
        BigDecimal maxAmount = maxAmountLimit.setScale(precision, CalcConstants.ROUND_DOWN);
		if (powerPrecison != null) {
			maxAmount = maxAmount.divide(powerPrecison).setScale(0, CalcConstants.ROUND_DOWN).multiply(powerPrecison);
		}
        BigDecimal reduce = incentiveValue.min(maxAmount);
        //计数：累积优惠金额
        BigDecimal incentiveAmount = BigDecimal.ZERO;
        boolean flag = true;
        for (int i = 0; i < promoObject.getCalcShoppingCartItemList().size(); i++) {
            CalcShoppingCartItem scItem = promoObject.getCalcShoppingCartItemList().get(i);
            //该sku总价，上个活动计算后价格，如果没有上个活动就是原单价*数量
            BigDecimal beforeAmount = scItem.getPromoAmount().setScale(precision, CalcConstants.ROUND_DOWN);
			if (powerPrecison != null) {
				beforeAmount = beforeAmount.divide(powerPrecison).setScale(0, CalcConstants.ROUND_DOWN).multiply(powerPrecison);
			}
            //该sku优惠后金额
            BigDecimal afterAmount = beforeAmount;
            //该sku要优惠的总金额
            BigDecimal reduceItem = (reduce.min(beforeAmount)).multiply(new BigDecimal(scItem.getQuantity()));
            incentiveAmount = incentiveAmount.add(reduceItem);
            //超过最大限制金额 只能减剩下的金额了
            if (maxAmount.compareTo(incentiveAmount) < 0 && flag) {
                afterAmount = beforeAmount.subtract(maxAmount.subtract(incentiveAmount.subtract(reduceItem))).max(BigDecimal.ZERO);
                flag = false;
            } else if (maxAmount.compareTo(incentiveAmount) >= 0) {
                afterAmount = beforeAmount.subtract(reduceItem).max(BigDecimal.ZERO);
            }

            if (checkLimited) {
                if (!super.checkLimit(incentive, 1, incentiveAmount)) {
                    return CalcResult.SUCCESS_LIMITED;
                }
            } else {
                scItem.setPromoAmount(afterAmount);
                scItem.setCalculateResult(incentive.getActivityCode(), beforeAmount, afterAmount);
            }
        }

        return CalcResult.SUCCESS_TRUE;
    }

}
