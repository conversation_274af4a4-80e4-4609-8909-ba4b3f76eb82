/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.calc.model;

import java.io.Serializable;

/**
 *
 */
public class CalcActivityFuncParam implements Serializable{

    private static final long serialVersionUID = 6122097773723352291L;

    // 函数类型
    private String funcType;

    // 函数编码
    private String funcCode;

    // 参数类型: 01-空 02-数值 03-JSON
    private String paramType;

    // 参数值
    private String paramValue;

    // 参数单位: 01-元 02-件 03-折
    private String paramUnit;

    /**
     * 获取【函数类型】
     *
     * @return 函数类型
     */
    public String getFuncType(){

        return funcType;
    }

    /**
     * 设定【函数类型】
     *
     * @param funcType
     *            函数类型
     */

    public void setFuncType(String funcType){

        this.funcType = funcType;
    }

    /**
     * 获取【函数编码】
     *
     * @return 函数编码
     */
    public String getFuncCode(){

        return funcCode;
    }

    /**
     * 设定【函数编码】
     *
     * @param funcCode
     *            函数编码
     */

    public void setFuncCode(String funcCode){

        this.funcCode = funcCode;
    }

    /**
     * 获取【参数类型】
     *
     * @return 参数类型
     */
    public final String getParamType(){

        return paramType;
    }

    /**
     * 设定【参数类型】
     *
     * @param paramType
     *            参数类型
     */
    public final void setParamType(String paramType){

        this.paramType = paramType;
    }

    /**
     * 获取【参数值】
     *
     * @return 参数值
     */
    public String getParamValue(){

        return paramValue;
    }

    /**
     * 设定【参数值】
     *
     * @param paramValue
     *            参数值
     */

    public void setParamValue(String paramValue){

        this.paramValue = paramValue;
    }

    /**
     * 获取【参数单位】
     *
     * @return 参数单位
     */
    public final String getParamUnit(){

        return paramUnit;
    }

    /**
     * 设定【参数单位】
     *
     * @param paramUnit
     *            参数单位
     */
    public final void setParamUnit(String paramUnit){

        this.paramUnit = paramUnit;
    }

}
