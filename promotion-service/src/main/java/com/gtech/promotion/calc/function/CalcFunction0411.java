/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.calc.function;

import com.gtech.promotion.calc.CalcActivity;
import com.gtech.promotion.calc.CalcConstants;
import com.gtech.promotion.calc.CalcFunction;
import com.gtech.promotion.calc.CalcResult;
import com.gtech.promotion.calc.model.CalcActivityIncentive;
import com.gtech.promotion.calc.model.CalcShoppingCart;
import com.gtech.promotion.calc.model.CalcShoppingCartItem;
import lombok.Getter;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Deque;

/**
 * 原子函数：促销奖励(04) - 每件不同特价(11)
 * 
 */
@Component
public class CalcFunction0411 extends CalcFunction04{

    @Getter
    private FunctionEnum function = FunctionEnum.F0411;

    static {
        // Registers the current atomic function
        CalcFunctionRegister.register(FunctionEnum.F0411, new CalcFunction0411());
    }

    /**
     * 构造函数私有化，不允许自己生成该对象
     */
    private CalcFunction0411(){
        // No codes
    }

    /*
     * (non-Javadoc)
     * @see com.gtech.promotion.calc.CalcFunction#calc(com.gtech.promotion.calc.model.ICalcPromoObject, com.gtech.promotion.calc.model.CalcActivityIncentive, java.util.Deque, 
     * com.gtech.promotion.calc.function.FunctionParamMap, com.gtech.promotion.calc.CalcFunction)
     */
    @Override
    public CalcResult calc(CalcShoppingCart promoObject, CalcActivityIncentive incentive, Deque<FunctionEnum> funcList, FunctionParamMap paramMap, CalcFunction caller) {

        try {
            if (!FunctionEnum.F0101.equalsCode(promoObject.getPromoScope().code())) {

                // Only support for FunctionEnum.F0101.
                return CalcResult.ERROR_DEFAULT;
            }

            this.loadCommonValue(promoObject);

            int precision = promoObject.getCalcActivity().getPrecison();
            BigDecimal incentiveAmount = this.sumIncentiveAmount(promoObject, incentive,precision);
            if (!this.checkLimitedAndCombine(incentive, promoObject.getCalcShoppingCartItemList(), 1, incentiveAmount)) {
                return CalcResult.SUCCESS_LIMITED;
            }

            for (CalcShoppingCartItem scItem : promoObject.getCalcShoppingCartItemList()) {
                String activityCode = incentive.getActivityCode();
                CalcActivity calcActivity = scItem.getCurrentActivity(activityCode);
                //计算前此项商品总价
                BigDecimal beforAmount = scItem.getPromoAmount().setScale(precision, CalcConstants.ROUND_DOWN);
                //活动价格
                BigDecimal unitPrice = scItem.getActivity0411Price().get(activityCode).setScale(precision, CalcConstants.ROUND_DOWN);
                //计算后此项商品总价
                BigDecimal afterAmount = unitPrice.multiply(BigDecimal.valueOf(scItem.getQuantity()));
                scItem.setPromoAmount(afterAmount);//该sku最终的促销价
                calcActivity.setBeforeAmount(beforAmount);
                calcActivity.setAfterAmount(afterAmount);
                calcActivity.setEffectiveFlag(true);
            }

            return CalcResult.SUCCESS_TRUE;

        } catch (RuntimeException e) {
            return CalcResult.ERROR_DEFAULT;
        }
    }

    /**
     * 统计优惠金额
     */
    private BigDecimal sumIncentiveAmount(CalcShoppingCart promoObject, CalcActivityIncentive activityIncentive,int precision) {

        BigDecimal incentiveAmount = BigDecimal.ZERO;

        for (CalcShoppingCartItem scItem : promoObject.getCalcShoppingCartItemList()){
            String activityCode = activityIncentive.getActivityCode();
            //计算前此项商品总价
            BigDecimal beforAmount = scItem.getPromoAmount().setScale(precision, CalcConstants.ROUND_DOWN);
            //活动价格
            BigDecimal unitPrice = scItem.getActivity0411Price().get(activityCode).setScale(precision, CalcConstants.ROUND_DOWN);
            //计算后此项商品总价
            BigDecimal afterAmount = unitPrice.multiply(BigDecimal.valueOf(scItem.getQuantity()));
            
            incentiveAmount = incentiveAmount.add(beforAmount.subtract(afterAmount));
        }

        return incentiveAmount;
    }

}
