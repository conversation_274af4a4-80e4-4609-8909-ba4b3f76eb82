/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.calc.function;

import com.gtech.commons.utils.ConvertUtils;
import com.gtech.promotion.calc.CalcFunction;
import com.gtech.promotion.calc.CalcResult;
import com.gtech.promotion.calc.model.CalcActivityIncentive;
import com.gtech.promotion.calc.model.CalcShoppingCart;
import com.gtech.promotion.calc.model.CalcShoppingCartItem;
import com.gtech.promotion.code.activity.ActivityTagCodeEnum;
import com.gtech.promotion.code.activity.FuncTypeEnum;
import com.gtech.promotion.code.activity.NeedMoreUnitEnum;
import com.gtech.promotion.exception.ErrorCodes;
import lombok.Getter;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Deque;
import java.util.Iterator;
import java.util.List;

/**
 * 原子函数：条件参数(03) - 金额(03)
 * 
 */
@Component
public class CalcFunction0303 implements CalcFunction{

    @Getter
    private FunctionEnum function = FunctionEnum.F0303;

    static {
        // Registers the current atomic function
        CalcFunctionRegister.register(FunctionEnum.F0303, new CalcFunction0303());
    }

    /**
     * 构造函数私有化，不允许自己生成该对象
     */
    private CalcFunction0303(){
        // No codes
    }

    /*
     * (non-Javadoc)
     * @see com.gtech.promotion.calc.CalcFunction#calc(com.gtech.promotion.calc.model.ICalcPromoObject, com.gtech.promotion.calc.model.CalcActivityIncentive, java.util.Deque, 
     * com.gtech.promotion.calc.function.FunctionParamMap, com.gtech.promotion.calc.CalcFunction)
     */
    @Override
    public CalcResult calc(CalcShoppingCart promoObject, CalcActivityIncentive incentive, Deque<FunctionEnum> funcList, FunctionParamMap paramMap, CalcFunction caller) {

        try{
            BigDecimal amount = promoObject.getPromoAmount();
            if (ActivityTagCodeEnum.BUY_A_DISCOUNT_B.equalsCode(promoObject.getCalcActivity().getTagCode())){//买A优惠B
                amount = promoObject.getPromoAmount("1");
            }
            String paramValue = paramMap.getFuncParamValue(this.function.code());
            BigDecimal paramAmount = ConvertUtils.toBigDecimal(paramValue);

            if (!FuncTypeEnum.CONDITION.equals(caller.getFunction().type())){
                return CalcResult.ERROR_DEFAULT;
            }

            switch (caller.getFunction()) {

                case F0202:
                    return handler0303(promoObject, incentive, amount, paramAmount, 1);
                case F0207:
                    return handlerF01(incentive, promoObject, paramAmount, 0, true);
                case F0203:
                    return handler0303(promoObject, incentive, amount, paramAmount, amount.divideToIntegralValue(paramAmount).intValue());
                default:
                    return CalcResult.ERROR_DEFAULT;
            }

        }catch (Exception e){
            return CalcResult.ERROR_DEFAULT;
        }
    }

    private CalcResult handler0303(CalcShoppingCart promoObject,CalcActivityIncentive activityIncentive,BigDecimal amount,BigDecimal paramAmount,int incentiveTimes){
        if (amount.compareTo(paramAmount) < 0){
            activityIncentive.setNeedMoreAmount(paramAmount.subtract(amount).toString());
            activityIncentive.setNeedMoreUnit(NeedMoreUnitEnum.YUAN.code());
            activityIncentive.setFailedReason(ErrorCodes.ACTIVITY_LIMIT_CONDITION_AMOUNT);
            return CalcResult.SUCCESS_FALSE;
        }else{
            activityIncentive.setIncentiveTimes(incentiveTimes);
            activityIncentive.setPromoQuantity(promoObject.getOriginalQuantity());
            return CalcResult.SUCCESS_TRUE;
        }
    }

    private CalcResult handlerF01(CalcActivityIncentive incentive, CalcShoppingCart project, BigDecimal paramAmount, int incentiveTimes, boolean sum) {
        boolean flag = false;
        int promoQuantity = 0;
        List<CalcShoppingCartItem> calcShoppingCartItemList = project.getCalcShoppingCartItemList();
        List<CalcShoppingCartItem> temp = new ArrayList<>(calcShoppingCartItemList);
        Iterator<CalcShoppingCartItem> iterator = calcShoppingCartItemList.iterator();
        while (iterator.hasNext()){
            CalcShoppingCartItem next = iterator.next();
            BigDecimal amount = next.getPromoAmount();
            if (amount.compareTo(paramAmount) < 0){
                iterator.remove();
            }else{
                flag = true;
                promoQuantity += 1;
                if (sum){
                    incentiveTimes += 1;
                }
            }
        }
        if (flag){
            incentive.setIncentiveTimes(incentiveTimes);
            incentive.setPromoQuantity(promoQuantity);
            return CalcResult.SUCCESS_TRUE;
        }else{
            project.setCalcShoppingCartItemList(temp);
            incentive.setNeedMoreUnit(NeedMoreUnitEnum.YUAN.code());
            incentive.setFailedReason(ErrorCodes.ACTIVITY_LIMIT_CONDITION_QUANTITY);
            return CalcResult.SUCCESS_FALSE;
        }
    }

}
