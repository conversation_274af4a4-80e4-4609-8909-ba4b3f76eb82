/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.calc.function;

import java.math.BigDecimal;
import java.util.Deque;

import org.springframework.stereotype.Component;

import com.gtech.commons.utils.ConvertUtils;
import com.gtech.promotion.calc.CalcActivity;
import com.gtech.promotion.calc.CalcConstants;
import com.gtech.promotion.calc.CalcFunction;
import com.gtech.promotion.calc.CalcResult;
import com.gtech.promotion.calc.model.CalcActivityIncentive;
import com.gtech.promotion.calc.model.CalcShoppingCart;
import com.gtech.promotion.calc.model.CalcShoppingCartItem;

import lombok.Getter;

/**
 * 原子函数：促销奖励(04) - 单件固定金额(03)
 */
@Component
public class CalcFunction0403 extends CalcFunction04{

    @Getter
    private FunctionEnum function = FunctionEnum.F0403;

    static {
        // Registers the current atomic function
        CalcFunctionRegister.register(FunctionEnum.F0403, new CalcFunction0403());
    }

    /**
     * 构造函数私有化，不允许自己生成该对象
     */
    private CalcFunction0403(){
        // No codes
    }

    /*
     * (non-Javadoc)
     * @see com.gtech.promotion.calc.CalcFunction#calc(com.gtech.promotion.calc.model.ICalcPromoObject, com.gtech.promotion.calc.model.CalcActivityIncentive, java.util.Deque, 
     * com.gtech.promotion.calc.function.FunctionParamMap, com.gtech.promotion.calc.CalcFunction)
     */
    @Override
    public CalcResult calc(CalcShoppingCart promoObject, CalcActivityIncentive incentive, Deque<FunctionEnum> funcList, FunctionParamMap paramMap, CalcFunction caller) {
        try {
            if (!this.checkActivitySku(incentive, promoObject.getCalcShoppingCartItemList(), false)) {
                return CalcResult.SUCCESS_LIMITED;
            }
            int precision = promoObject.getCalcActivity().getPrecison();
			BigDecimal powerPrecison = promoObject.getCalcActivity().getPowerPrecison();
            loadCommonValue(promoObject);
            //奖励参数值，设为固定金额0403-每件特价
            String incentiveParamValue = paramMap.getFuncParamValue(this.function.code());
            BigDecimal fixedValue = new BigDecimal(incentiveParamValue).setScale(precision, CalcConstants.ROUND_DOWN);
			if (powerPrecison != null) {
				fixedValue = fixedValue.divide(powerPrecison).setScale(0, CalcConstants.ROUND_DOWN).multiply(powerPrecison);
			}
            BigDecimal maxMoneyLimit = maxAmountLimit.setScale(precision, CalcConstants.ROUND_DOWN);//活动限制最大优惠金额
			if (powerPrecison != null) {
				maxMoneyLimit = maxMoneyLimit.divide(powerPrecison).setScale(0, CalcConstants.ROUND_DOWN).multiply(powerPrecison);
			}
            // 设置每个商品和商品下活动金额
			handlerItem(promoObject, incentive, fixedValue, maxMoneyLimit);
            return CalcResult.SUCCESS_TRUE;
        } catch (RuntimeException e) {
            return CalcResult.ERROR_DEFAULT;
        }
    }

    /**
     * 设置每个商品和商品下活动金额
     */
	private void handlerItem(CalcShoppingCart promoObject, CalcActivityIncentive incentive, BigDecimal fixedValue, BigDecimal maxMoneyLimit) {

		int precision = promoObject.getCalcActivity().getPrecison();
		BigDecimal powerPrecison = promoObject.getCalcActivity().getPowerPrecison();
        BigDecimal promoAmountOrigin = promoObject.getPromoAmount();

        BigDecimal totalQuantity = ConvertUtils.toBigDecimal(promoObject.getOriginalQuantity());
        BigDecimal totalIncentiveValue = promoObject.getPromoAmount("1").subtract(fixedValue.multiply(totalQuantity));

        for (CalcShoppingCartItem scItem : promoObject.getCalcShoppingCartItemList()) {
            CalcActivity calcActivity = scItem.getCurrentActivity(incentive.getActivityCode());
            //计算前此项商品总价
            BigDecimal befroAmount = scItem.getPromoAmount().setScale(precision, CalcConstants.ROUND_DOWN);
			if (powerPrecison != null) {
				befroAmount = befroAmount.divide(powerPrecison).setScale(0, CalcConstants.ROUND_DOWN).multiply(powerPrecison);
			}
            calcActivity.setBeforeAmount(befroAmount);
            //计算后此项商品总价
            BigDecimal afterAmount = fixedValue.multiply(BigDecimal.valueOf(scItem.getQuantity()));
            afterAmount = afterAmount.max(befroAmount.subtract(maxMoneyLimit));
            scItem.setPromoAmount(afterAmount);
            //统计优惠金额
            calcActivity.setAfterAmount(afterAmount);
            calcActivity.setEffectiveFlag(true);
        }

		dealMaxOrderAmount(totalIncentiveValue, promoObject, incentive, promoAmountOrigin);
    }

}
