/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.calc.function;

import com.gtech.promotion.calc.CalcFunction;
import com.gtech.promotion.calc.CalcResult;
import com.gtech.promotion.calc.model.CalcActivityIncentive;
import com.gtech.promotion.calc.model.CalcShoppingCart;
import lombok.Getter;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Deque;

/**
 * 原子函数：促销奖励(04) - 邮费打折(05)
 * 
 */
@Component
public class CalcFunction0405 extends CalcFunction04 {

    @Getter
    private FunctionEnum function = FunctionEnum.F0405;

    static {
        // Registers the current atomic function
        CalcFunctionRegister.register(FunctionEnum.F0405, new CalcFunction0405());
    }

    /**
     * 构造函数私有化，不允许自己生成该对象
     */
    private CalcFunction0405() {
        // No codes
    }

    /*
     * (non-Javadoc)
     * @see com.gtech.promotion.calc.CalcFunction#calc(com.gtech.promotion.calc.model.ICalcPromoObject, com.gtech.promotion.calc.model.CalcActivityIncentive, java.util.Deque, 
     * com.gtech.promotion.calc.function.FunctionParamMap, com.gtech.promotion.calc.CalcFunction)
     */
    @Override
    public CalcResult calc(CalcShoppingCart promoObject, CalcActivityIncentive incentive, Deque<FunctionEnum> funcList, FunctionParamMap paramMap, CalcFunction caller) {

        try {
            if (!this.checkLimitedAndCombine(incentive, promoObject.getCalcShoppingCartItemList(), 1, BigDecimal.ZERO)) {
                return CalcResult.SUCCESS_LIMITED;
            }

            BigDecimal incentiveValue = postageCommon(promoObject, incentive, paramMap, this.function.code());
            BigDecimal postage = promoObject.getPostage();
            promoObject.setPostage(postage.multiply(incentiveValue));
            setCalcActivityValue(promoObject, incentive, postage);
            return CalcResult.SUCCESS_TRUE;
        } catch (Exception e) {
            return CalcResult.ERROR_DEFAULT;
        }
    }



}
