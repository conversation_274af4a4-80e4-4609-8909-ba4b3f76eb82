
/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.calc.function;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.gtech.promotion.calc.model.CalcActivityFuncParam;

/**
 * 模板函数参数Map
 * 
 */
public class FunctionParamMap {

    private final Map<String, CalcActivityFuncParam> paramMap = new HashMap<>();

    public FunctionParamMap(List<CalcActivityFuncParam> funcParamList) {

        for (CalcActivityFuncParam param : funcParamList) {
            paramMap.put(param.getFuncCode(), param);
        }
    }

    public CalcActivityFuncParam getFuncParam(String funcCode) {

        return this.paramMap.get(funcCode);
    }

    public String getFuncParamValue(String funcCode) {

        CalcActivityFuncParam funcParam = this.paramMap.get(funcCode);

        return (null == funcParam) ? "" : funcParam.getParamValue();
    }

}
