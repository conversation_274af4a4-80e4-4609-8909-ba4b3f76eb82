/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.helper;

import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.checker.SystemChecker;
import com.gtech.promotion.exception.PromotionException;
import com.gtech.promotion.utils.DateUtil;
import com.gtech.promotion.utils.NumberUtil;

import java.util.HashSet;
import java.util.Random;
import java.util.Set;

public class CouponUtils {


	private CouponUtils(){
		throw new PromotionException(SystemChecker.CAN_NOT_INSTANCE_ERROR);
	}

	// 上次生成券码的毫秒数
	private static String lastTimestamp = "-1";
	
	private static int sequence = 0;

	private static final Random random = new Random();


	//生成结束需要清除
	private static final  Set<String> couponSet = new HashSet<>(1024);
	private static final  Set<String> couponDigitSet = new HashSet<>(1024);

	private static final String CHARACTERS = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";

    private static final String LETTERS = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";


	public static void clearCouponSet() {
		couponSet.clear();
	}

	public static void clearCouponDigitSet() {
		couponDigitSet.clear();
	}
    //字母加数字混合
	public static synchronized String generateRandomCouponCode(String couponCodePrefix,int length) {

		StringBuilder sb = new StringBuilder(length);
		//1位 首位字母或者数字
        int i1 = random.nextInt(CHARACTERS.length());
        if (i1 % 2 == 0){
            String randomNumbers = NumberUtil.getRandomNumbers(1);
            sb.append(randomNumbers);
        }else {
            sb.append(CHARACTERS.charAt(i1));
        }
        //2-3位 年的最后两位或随机产生两位（字母或数字）
        String dateStr = DateUtil.getDateStr(DateUtil.DATETIMESTOREFORMAT);
        if (i1 % 3 == 0){
            //年的最后1位
            String year = dateStr.substring(3, 4);
            sb.append(year);
			int i2 = random.nextInt(CHARACTERS.length());
			sb.append(CHARACTERS.charAt(i2));
        }else {
            int i2 = random.nextInt(CHARACTERS.length());
			sb.append(CHARACTERS.charAt(i2));
			int i3 = random.nextInt(CHARACTERS.length());
			sb.append(CHARACTERS.charAt(i3));
        }
        //4位 随机一位字母
        int i3 = random.nextInt(LETTERS.length());
        sb.append(LETTERS.charAt(i3));

        //5 毫秒数 1位
        String currentTimeMillis = tilNextMillis(Long.parseLong(lastTimestamp));
        sb.append(currentTimeMillis.charAt(5));

        //6位 字母或数字随机获取一位
        int i4 = random.nextInt(CHARACTERS.length());
        sb.append(CHARACTERS.charAt(i4));

        //7位随机数字
        String randomNumbers = NumberUtil.getRandomNumbers(1);
        sb.append(randomNumbers);

        //8-9 位日期
        String date = dateStr.substring(6, 8);
        sb.append(date);

        int count = length - 9;
		for (int i = 0; i < count; i++) {
			int randomIndex = random.nextInt(CHARACTERS.length());
			char randomChar = CHARACTERS.charAt(randomIndex);
			sb.append(randomChar);
		}

        String couponCode = getCouponCode(sb,couponCodePrefix,length);
        if (couponSet.contains(couponCode)){
            couponCode = generateRandomCouponCode(couponCodePrefix,length);
        }
        couponSet.add(couponCode);

        return couponCode;
	}

	//获取指定位数的券码
    public static String getCouponCode(StringBuilder sb,String couponCodePrefix,Integer couponCodeLength) {


		if (StringUtil.isNotBlank(couponCodePrefix)) {
			sb.insert(0, couponCodePrefix);
		} else {
			couponCodePrefix = "";
		}
		// 默认随机码18位
		if (couponCodeLength == null) {
			couponCodeLength = 18;
		}

		String coupon = sb.toString();
        return coupon.substring(0, couponCodePrefix.length() + couponCodeLength);
    }

    /**
	 * 固定组织代码（20） + 租户编码%1000 + 投放id%10000 + 当前毫秒数后6位 + 2位序列号 + 1位校验码
	 * 当前毫秒数后6位会打乱顺序，租户编码后面2位 + 投放id后面2位 + 序列号后面2位
	 * <strong>适用于投放时批量生产券码</strong>
	 * @param tenantCode
	 * @param releaseCode
	 * @return
	 */

	public static synchronized String createCoupons(String tenantCode, String releaseCode, String couponCodePrefix, Integer couponCodeLength) {

		StringBuilder sb = new StringBuilder();
		// 用户id：用户id的后三位 + 2位时间（当前毫秒数的后六位的第3、1位）
//		tenantCode = "000"+tenantCode;
//		String userIdStr =  tenantCode.substring(tenantCode.length()-3);

		//租户编码
		StringBuilder tenantCode_ = createStringFixLength(tenantCode, 5);
		Long tenantCodeStr = Long.parseLong(tenantCode_.toString()) % Long.parseLong(NumberUtil.getRandomNumber(4));
		StringBuilder tenantCodeResult = createStringFixLength(String.valueOf(tenantCodeStr), 4);

		StringBuilder userIdBuilder = createStringFixLength(tenantCodeResult.toString(), 3);
		// 投放编码
		String releaseCodeStr = String.valueOf(Long.parseLong(releaseCode) % Long.parseLong(NumberUtil.getRandomNumber(4)));
		StringBuilder releaseIdBuilder = createStringFixLength(releaseCodeStr, 4);

		// 当前毫秒的后6位
		String currentTimeMillis = tilNextMillis(Long.parseLong(lastTimestamp));
		// 每毫秒最多只生成99个序列号
		if (lastTimestamp.equals(currentTimeMillis)) {
			sequence = sequence + 1;
			if (sequence >= 100) {
				sequence = 0;
				currentTimeMillis = tilNextMillis(Long.parseLong(lastTimestamp));
			}
		} else {
			sequence = 0;
			lastTimestamp = currentTimeMillis;
		}

		// 2位序列数
		String seq = String.valueOf(sequence);
		if (sequence < 10) {
			seq = "0" + seq;
		}
		// 第一位 随机
		String randomNumbers = NumberUtil.getRandomNumbers(1);
		sb.append(randomNumbers.equals("0") ? String.valueOf(random.nextInt(9) + 1) : randomNumbers);
		// 第二位 releaseIdBuilder最后一位
		sb.append(releaseIdBuilder.charAt(releaseIdBuilder.length()-1));
		// 第三位 随机数
		sb.append(NumberUtil.getRandomNumbers(1));
		// 第四位 毫秒数第6位
		sb.append(currentTimeMillis.charAt(5));
		// 第5位 随机数
		sb.append(NumberUtil.getRandomNumbers(1));

		//替换16位
		sb.append(NumberUtil.getRandomNumbers(1));

//		// 第6位  毫秒数第1位
		sb.append(currentTimeMillis.charAt(0));
		// 第7位 releaseCode % 后 第3位
		sb.append(releaseIdBuilder.charAt(2));
		// 第8位 tenantCode % 后 第3位
		sb.append(userIdBuilder.charAt(2));
		// 第9位 随机数
		sb.append(NumberUtil.getRandomNumbers(1));
		// 第10位 毫秒数第4位
		sb.append(currentTimeMillis.charAt(3));
		// 第11位 tenantCode % 后 第2位
		sb.append(userIdBuilder.charAt(1));
		// 第12位  毫秒数第3位
		sb.append(currentTimeMillis.charAt(2));
		// 第13位 序列数 第2位
		sb.append(seq.charAt(1));
		// 第14位 releaseCode % 后 第2位
		sb.append(releaseIdBuilder.charAt(1));
		// 第15位  毫秒数第2位
		sb.append(currentTimeMillis.charAt(1));
//		// 第16位  releaseCode % 后 第4位
//		sb.append(releaseIdBuilder.charAt(3));
		// 第17位 序列数 第1位
		sb.append(seq.charAt(0));
		// 第18位 毫秒数第5位
		sb.append(currentTimeMillis.charAt(4));
		// 第19位 随机数
		sb.append(NumberUtil.getRandomNumbers(1));
		// 第20位 毫秒数第6位
		sb.append(currentTimeMillis.charAt(5));

		if (StringUtil.isNotBlank(couponCodePrefix)) {
			sb.insert(0, couponCodePrefix);
		} else {
			couponCodePrefix = "";
		}
		// 默认随机码18位
		if (couponCodeLength == null) {
			couponCodeLength = 18;
		}
		// 截取(前缀+随机码总长度)

		String couponCode = sb.substring(0, couponCodePrefix.length() + couponCodeLength);
		if (couponDigitSet.contains(couponCode)) {
			couponCode = generateRandomCouponCode(couponCodePrefix, couponCodePrefix.length());
		}
		couponDigitSet.add(couponCode);

		return couponCode;
	}

	/**
	 * 阻塞到下一个毫秒，直到获得新的时间戳
	 * @param lastTimestamp 上次生成ID的时间截
     * @return 当前时间戳
	 */
    private static String tilNextMillis(final long lastTimestamp) {
        long timestamp = System.currentTimeMillis();
        while (timestamp <= lastTimestamp) {
            timestamp = System.currentTimeMillis();
        }
        String value = String.valueOf(timestamp % 1000000);
        StringBuilder stringBuilder = createStringFixLength(value, 6);
        return stringBuilder.toString();
    }

	private static StringBuilder createStringFixLength(String userIdStr, int length) {
		StringBuilder userIdBuilder = new StringBuilder(userIdStr);
		while (userIdBuilder.length() < length){
			userIdBuilder.append("0");
		}
		return userIdBuilder;
	}

    /*public static void main(String[] args) {
        HashSet<String> hashSet = new HashSet<>();
        long currentTimeMillis1 = System.currentTimeMillis();
		String couponReleaseCode = "1711525825757";
        for (int i = 0; i < 10000; i++) {
			//couponReleaseCode 加一
			couponReleaseCode = String.valueOf(Long.parseLong(couponReleaseCode) + 1);
            String couponCode = createCoupons("200005", couponReleaseCode,"YC",6);
            System.out.println(couponCode);
            hashSet.add(couponCode);
        }
        long currentTimeMillis2 = System.currentTimeMillis();
        System.out.println("耗时毫秒数" + (currentTimeMillis2 - currentTimeMillis1));
        System.out.println("======="+hashSet.size());
    }
*/
}
