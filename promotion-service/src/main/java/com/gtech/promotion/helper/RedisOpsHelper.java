/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.helper;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.gtech.commons.redis.GTechRedisTemplate;
import com.gtech.commons.utils.ConvertUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.commons.utils.LocalCacheUtil;
import com.gtech.promotion.calc.model.SkuQuantity;
import com.gtech.promotion.code.activity.LimitationCodeEnum;
import com.gtech.promotion.dao.model.activity.TPromoIncentiveLimitedVO;
import com.gtech.promotion.utils.Constants;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

/**
 * 
 *
 * <AUTHOR>
 * @Date 2020-02-13
 */
@Slf4j
@Component
public class RedisOpsHelper {

	@Autowired
	private GTechRedisTemplate redisTemplate;

	@Autowired
	private StringRedisTemplate stringRedisTemplate;

	private static final String TENANT_CODE = ":TCODE=";
	private static final String ACTIVITY_CODE = ":ACODE=";
	private static final String USER_CODE = ":UCODE=";
	private static final String DATE = ":DATE=";
	private static final String SKU = ":SKU=";
	private static final String FORMAT = "%s:%s";

	@Getter
	@Setter
	@Builder
	@NoArgsConstructor
	@AllArgsConstructor
	public static class IncentiveLimitedParam {

		private String limitedCode;
		private String tenantCode;
		private String activityCode;
		private String userCode;
		private String skuCode;
		private BigDecimal totalValue;
		private BigDecimal incentiveValue;

		public boolean isTimesValue() {

			return LimitationCodeEnum.isTimes(limitedCode);
		}
	}

	// 创建规则奖励key
	public String buildIncentiveKey(String limitedCode, String tenantCode, String activityCode, String userCode,
			String skuCode) {

		if (LimitationCodeEnum.USER_TOTAL_COUNT.equalsCode(limitedCode)
				|| LimitationCodeEnum.USER_TOTAL_AMOUNT.equalsCode(limitedCode)
				|| LimitationCodeEnum.USER_TOTAL_COUNT_FLASH_SALE.equalsCode(limitedCode)
				|| LimitationCodeEnum.USER_TOTAL_COUNT_ORDER_GROUP.equalsCode(limitedCode)
				|| LimitationCodeEnum.USER_DAY_ORDER_COUNT.equalsCode(limitedCode)
				|| LimitationCodeEnum.SKU_TOTAL_COUNT_GROUP.equalsCode(limitedCode)) {
			return Constants.INCENTIVE_LIMITED + limitedCode + TENANT_CODE + tenantCode + ACTIVITY_CODE + activityCode
					+ USER_CODE + userCode;
		} else if (LimitationCodeEnum.ACTIVITY_TOTAL_COUNT.equalsCode(limitedCode)
				|| LimitationCodeEnum.ACTIVITY_TOTAL_AMOUNT.equalsCode(limitedCode)) {
			return Constants.INCENTIVE_LIMITED + limitedCode + TENANT_CODE + tenantCode + ACTIVITY_CODE + activityCode;
		} else if (LimitationCodeEnum.ACTIVITY_DAY_ORDER_COUNT.equalsCode(limitedCode)) {
			return Constants.INCENTIVE_LIMITED + limitedCode + TENANT_CODE + tenantCode + ACTIVITY_CODE + activityCode
					+ DATE + DateUtil.getCurrentDateAsString(DateUtil.FORMAT_YYYYMMDD);
		} else if (LimitationCodeEnum.USER_DAY_COUNT.equalsCode(limitedCode)) {
			return Constants.INCENTIVE_LIMITED + limitedCode + TENANT_CODE + tenantCode + ACTIVITY_CODE + activityCode
					+ DATE + DateUtil.getCurrentDateAsString(DateUtil.FORMAT_YYYYMMDD) + userCode;
		} else if (LimitationCodeEnum.USER_MONTH_COUNT.equalsCode(limitedCode)) {
			return Constants.INCENTIVE_LIMITED + limitedCode + TENANT_CODE + tenantCode + ACTIVITY_CODE + activityCode
					+ DATE + DateUtil.getCurrentDateAsString(DateUtil.FORMAT_YYYYMM) + userCode;
		} else if (LimitationCodeEnum.USER_WEEK_COUNT.equalsCode(limitedCode)) {
			return Constants.INCENTIVE_LIMITED + limitedCode + TENANT_CODE + tenantCode + ACTIVITY_CODE + activityCode
					+ DATE + DateUtil.getCurrentDateAsString(DateUtil.FORMAT_YYYY) + week() + userCode;
		} else if (LimitationCodeEnum.USER_SKU_COUNT.equalsCode(limitedCode)) {
			return Constants.INCENTIVE_LIMITED + limitedCode + TENANT_CODE + tenantCode + ACTIVITY_CODE + activityCode
					+ SKU + skuCode + USER_CODE + userCode;
		} else if (LimitationCodeEnum.SKU_COUNT.equalsCode(limitedCode)) {
			return Constants.INCENTIVE_LIMITED + limitedCode + TENANT_CODE + tenantCode + ACTIVITY_CODE + activityCode
					+ SKU + skuCode;
		}

		return "";
	}

	private int week() {
		Date date = DateUtil.parseDate(DateUtil.format(new Date(), DateUtil.FORMAT_DEFAULT), DateUtil.FORMAT_DEFAULT);
		Calendar calendar = Calendar.getInstance();
		calendar.setFirstDayOfWeek(Calendar.MONDAY);
		calendar.setTime(date);
		return calendar.get(Calendar.WEEK_OF_YEAR);
	}

	public String buildIncentiveKey(LimitationCodeEnum limitedCode, String tenantCode, String activityCode,
			String userCode, String skuCode) {

		return this.buildIncentiveKey(limitedCode.code(), tenantCode, activityCode, userCode, skuCode);
	}

	public BigDecimal getLimitedValue(LimitationCodeEnum limitedCode, String tenantCode, String activityCode,
			String userCode, String skuCode) {

		return this.getLimitedValue(limitedCode.code(), tenantCode, activityCode, userCode, skuCode);
	}

	public BigDecimal getLimitedValue(String limitedCode, String tenantCode, String activityCode, String userCode,
			String skuCode) {

		String hKey = this.buildIncentiveKey(limitedCode, tenantCode, activityCode, "", "");
		String userLimitKey = this.buildIncentiveKey(limitedCode, tenantCode, activityCode, userCode, skuCode);
		if (LimitationCodeEnum.USER_TOTAL_COUNT.equalsCode(limitedCode)
				|| LimitationCodeEnum.USER_TOTAL_AMOUNT.equalsCode(limitedCode)
				|| LimitationCodeEnum.USER_TOTAL_COUNT_FLASH_SALE.equalsCode(limitedCode)
				|| LimitationCodeEnum.USER_DAY_ORDER_COUNT.equalsCode(limitedCode)
				|| LimitationCodeEnum.USER_SKU_COUNT.equalsCode(limitedCode)
				|| LimitationCodeEnum.SKU_COUNT.equalsCode(limitedCode)) {
			if (!redisTemplate.opsHashHasKey(Constants.APP_KEY, hKey, userLimitKey)) {
				return null;
			}
			Long value = redisTemplate.opsHashIncrement(Constants.APP_KEY, hKey, userLimitKey, 0);
			if (value == null) {
				return null;
			}
			return this.isTimesValue(limitedCode) ? this.timesValue(value) : this.amountValue(value);
		}
		if (!redisTemplate.hasKey(Constants.APP_KEY, userLimitKey)) {
			return null;
		}

		Long value = redisTemplate.opsValueIncrement(Constants.APP_KEY, userLimitKey, 0L);
		if (value == null) {
			return null;
		}

		return this.isTimesValue(limitedCode) ? this.timesValue(value) : this.amountValue(value);
	}

	public boolean extendKey(TPromoIncentiveLimitedVO tPromoIncentiveLimitedVO, long timeout) {
		String limitationCode = tPromoIncentiveLimitedVO.getLimitationCode();
		if (LimitationCodeEnum.ACTIVITY_DAY_ORDER_COUNT.equalsCode(limitationCode)) {
			return true;
		}
		String userLimitKey = this.buildIncentiveKey(limitationCode, tPromoIncentiveLimitedVO.getTenantCode(),
				tPromoIncentiveLimitedVO.getActivityCode(), "", "");
		return redisTemplate.expire(Constants.APP_KEY, userLimitKey, timeout);
	}

	public boolean lockRedisDataSupportSku(List<IncentiveLimitedParam> paramList, IncentiveLimitedParam param,
			long timeout, List<String> skuCodes, List<Integer> skuQuantities) {
		if (LimitationCodeEnum.USER_SKU_COUNT.equalsCode(param.getLimitedCode())
				|| LimitationCodeEnum.SKU_COUNT.equalsCode(param.getLimitedCode())) {
			for (int i = 0; i < skuCodes.size(); i++) {
				String skuCode = skuCodes.get(i);
				int skuQuantity = skuQuantities.get(i);
				param.setIncentiveValue(ConvertUtils.toBigDecimal(skuQuantity));
				if (!lockRedisData(paramList, param, timeout, skuCode)) {
					return false;
				}
			}
			return true;
		} else {
			return lockRedisData(paramList, param, timeout, "");
		}
	}

	public boolean lockRedisData(List<IncentiveLimitedParam> paramList, IncentiveLimitedParam param, long timeout,
			String skuCode) {

		if (param.getTotalValue().compareTo(BigDecimal.ZERO) <= 0) {
			// 没有限制
			return true;
		}

		String limitedCode = param.getLimitedCode();
		String key = this.buildIncentiveKey(limitedCode, param.getTenantCode(), param.getActivityCode(), "", "");
		String userLimitKey = this.buildIncentiveKey(limitedCode, param.getTenantCode(), param.getActivityCode(),
				param.getUserCode(), skuCode);

		long total = 0;
		long delta = 0;

		if (param.isTimesValue()) {
			total = this.timesValue(param.getTotalValue());
			delta = this.timesValue(param.getIncentiveValue());
		} else {
			total = this.amountValue(param.getTotalValue());
			delta = this.amountValue(param.getIncentiveValue());
		}

		// Redis 初始化值
		if (redisInitData(paramList, timeout, limitedCode, key, userLimitKey, total))
			return false;

		if (LimitationCodeEnum.USER_TOTAL_COUNT.equalsCode(limitedCode)
				|| LimitationCodeEnum.USER_DAY_COUNT.equalsCode(limitedCode)
				|| LimitationCodeEnum.USER_WEEK_COUNT.equalsCode(limitedCode)
				|| LimitationCodeEnum.USER_MONTH_COUNT.equalsCode(limitedCode)
				|| LimitationCodeEnum.USER_TOTAL_AMOUNT.equalsCode(limitedCode)
				|| LimitationCodeEnum.USER_TOTAL_COUNT_FLASH_SALE.equalsCode(limitedCode)//秒杀
				|| LimitationCodeEnum.USER_TOTAL_COUNT_ORDER_GROUP.equalsCode(limitedCode)//拼团
				|| LimitationCodeEnum.USER_DAY_ORDER_COUNT.equalsCode(limitedCode)
				|| LimitationCodeEnum.USER_SKU_COUNT.equalsCode(limitedCode)
				|| LimitationCodeEnum.SKU_COUNT.equalsCode(limitedCode)) {
			log.info("redis limit increment, key:{},userLimitKey:{},delta:{}", key, userLimitKey, -delta);
			if (redisTemplate.opsHashIncrement(Constants.APP_KEY, key, userLimitKey, 0 - delta) < 0) {
				redisTemplate.opsHashIncrement(Constants.APP_KEY, key, userLimitKey, delta);
				log.info("redis limit increment rollback, key:{},userLimitKey:{},delta:{}", key, userLimitKey, delta);
				this.rollBackRedisData(paramList);
				return false;
			}
		} else {
			log.info("redis limit increment,userLimitKey:{},delta:{}", userLimitKey, -delta);
			if (redisTemplate.opsValueIncrement(Constants.APP_KEY, userLimitKey, 0 - delta) < 0) {
				redisTemplate.opsValueIncrement(Constants.APP_KEY, userLimitKey, delta);
				log.info("redis limit increment rollback, key:{},userLimitKey:{},delta:{}", key, userLimitKey, delta);
				this.rollBackRedisData(paramList);
				return false;
			}
		}
		param.setSkuCode(skuCode);
		paramList.add(param);
		return true;
	}

	public boolean redisInitData(List<IncentiveLimitedParam> paramList, long timeout, String limitedCode, String key,
			String userLimitKey, long total) {
		if (LocalCacheUtil.setIfAbsent(userLimitKey) && !redisTemplate.hasKey(Constants.APP_KEY, userLimitKey)
				&& !redisTemplate.opsHashHasKey(Constants.APP_KEY, key, userLimitKey)) {

			if (LimitationCodeEnum.USER_TOTAL_COUNT.equalsCode(limitedCode)
					|| LimitationCodeEnum.USER_DAY_COUNT.equalsCode(limitedCode)
					|| LimitationCodeEnum.USER_WEEK_COUNT.equalsCode(limitedCode)
					|| LimitationCodeEnum.USER_MONTH_COUNT.equalsCode(limitedCode)
					|| LimitationCodeEnum.USER_TOTAL_AMOUNT.equalsCode(limitedCode)
					|| LimitationCodeEnum.USER_TOTAL_COUNT_FLASH_SALE.equalsCode(limitedCode)//秒杀
					|| LimitationCodeEnum.USER_DAY_ORDER_COUNT.equalsCode(limitedCode)
					|| LimitationCodeEnum.USER_TOTAL_COUNT_ORDER_GROUP.equalsCode(limitedCode)//拼团
					|| LimitationCodeEnum.USER_SKU_COUNT.equalsCode(limitedCode)
					|| LimitationCodeEnum.SKU_COUNT.equalsCode(limitedCode)) {
				String temp = (String) stringRedisTemplate.opsForHash()
						.get(String.format(FORMAT, Constants.APP_KEY, key), userLimitKey);
				if ("0".equals(temp)) {
					this.rollBackRedisData(paramList);
					return true;
				}
				getRedisTemplateOpsHash(timeout, key, userLimitKey, total);
			} else {
				String temp = stringRedisTemplate.opsForValue()
						.get(String.format(FORMAT, Constants.APP_KEY, userLimitKey));
				if ("0".equals(temp)) {
					this.rollBackRedisData(paramList);
					return true;
				}
				setRedisOpsValue(timeout, userLimitKey, total);
			}
		}
		return false;
	}

	public void setRedisOpsValue(long timeout, String userLimitKey, long total) {
		if (redisTemplate.opsValueIncrement(Constants.APP_KEY, userLimitKey, total) == total) {
			redisTemplate.expire(Constants.APP_KEY, userLimitKey, timeout);
		} else {
			redisTemplate.opsValueIncrement(Constants.APP_KEY, userLimitKey, -total);
		}
	}

	public void getRedisTemplateOpsHash(long timeout, String key, String userLimitKey, long total) {
		if (redisTemplate.opsHashIncrement(Constants.APP_KEY, key, userLimitKey, total) == total) {
			redisTemplate.expire(Constants.APP_KEY, key, timeout);
		} else {
			redisTemplate.opsHashIncrement(Constants.APP_KEY, key, userLimitKey, -total);
		}
	}

	public long lockRedisDataSpu(List<IncentiveLimitedParam> paramList, IncentiveLimitedParam param, long timeout,
			String skuCode) {
		if (param.getTotalValue().compareTo(BigDecimal.ZERO) <= 0) {
			// 没有限制
			return 0;
		}
		String limitedCode = param.getLimitedCode();
		log.info("redis 操作参数:{{}}", JSONObject.toJSONString(param));
		String key = this.buildIncentiveKey(limitedCode, param.getTenantCode(), param.getActivityCode(), "", "");
		String userLimitKey = this.buildIncentiveKey(limitedCode, param.getTenantCode(), param.getActivityCode(),
				param.getUserCode(), skuCode);
		long delta = 0;
		long total = 0;
		if (param.isTimesValue()) {
			delta = this.timesValue(param.getIncentiveValue());
			total = this.timesValue(param.getTotalValue());
		} else {
			total = this.amountValue(param.getTotalValue());
			delta = this.amountValue(param.getIncentiveValue());
		}
		// Redis 初始化值
		if (LimitationCodeEnum.USER_SKU_COUNT.equalsCode(limitedCode)) {
			// redis为空的情况
			String value = (String) stringRedisTemplate.opsForHash().get(String.format(FORMAT, Constants.APP_KEY, key),
					userLimitKey);
			if (null == value) {
				if (setRedisTemplateInfo(paramList, timeout, key, userLimitKey, delta, total))
					return 0;
			} else {
				// redis不为空的情况
				delta = Long.min(Long.parseLong(value), delta);
				if (redisTemplate.opsHashIncrement(Constants.APP_KEY, key, userLimitKey, 0 - delta) < 0) {
					redisTemplate.opsHashIncrement(Constants.APP_KEY, key, userLimitKey, delta);
					this.rollBackRedisData(paramList);
					log.error("条件不足 回滚");
					return 0;
				}
			}
		}
		param.setSkuCode(skuCode);
		paramList.add(param);
		return delta;
	}

	public boolean setRedisTemplateInfo(List<IncentiveLimitedParam> paramList, long timeout, String key,
			String userLimitKey, long delta, long total) {
		getRedisTemplateOpsHash(timeout, key, userLimitKey, total);
		if (redisTemplate.opsHashIncrement(Constants.APP_KEY, key, userLimitKey, 0 - delta) < 0) {
			redisTemplate.opsHashIncrement(Constants.APP_KEY, key, userLimitKey, delta);
			this.rollBackRedisData(paramList);
			log.error("数据被修改条件不足 回滚");
			return true;
		}
		return false;
	}

	// 手动回滚缓存数据
	public void rollBackRedisData(List<IncentiveLimitedParam> paramList) {

		if (CollectionUtils.isEmpty(paramList)) {
			return;
		}

		for (IncentiveLimitedParam param : paramList) {
			String limitedCode = param.getLimitedCode();
			String hKey = this.buildIncentiveKey(limitedCode, param.getTenantCode(), param.getActivityCode(), "", "");
			String key = this.buildIncentiveKey(limitedCode, param.getTenantCode(), param.getActivityCode(),
					param.getUserCode(), param.getSkuCode());
			if (LimitationCodeEnum.USER_TOTAL_COUNT.equalsCode(limitedCode)
					|| LimitationCodeEnum.USER_TOTAL_AMOUNT.equalsCode(limitedCode)
					|| LimitationCodeEnum.USER_TOTAL_COUNT_FLASH_SALE.equalsCode(limitedCode)
					|| LimitationCodeEnum.USER_DAY_ORDER_COUNT.equalsCode(limitedCode)
					|| LimitationCodeEnum.USER_SKU_COUNT.equalsCode(limitedCode)
					|| LimitationCodeEnum.SKU_COUNT.equalsCode(limitedCode)) {
				if (param.isTimesValue()) {
					redisTemplate.opsHashIncrement(Constants.APP_KEY, hKey, key,
							this.timesValue(param.getIncentiveValue()));
				} else {
					redisTemplate.opsHashIncrement(Constants.APP_KEY, hKey, key,
							this.amountValue(param.getIncentiveValue()));
				}
			} else {
				if (param.isTimesValue()) {
					redisTemplate.opsValueIncrement(Constants.APP_KEY, key, this.timesValue(param.getIncentiveValue()));
				} else {
					redisTemplate.opsValueIncrement(Constants.APP_KEY, key,
							this.amountValue(param.getIncentiveValue()));
				}
			}
		}

	}

	// 手动回滚缓存数据
	public void rollBackRedisData(String limitedCode, String tenantCode, String activityCode, String userCode,
			BigDecimal delta, List<SkuQuantity> skuCodes) {
		if (LimitationCodeEnum.USER_SKU_COUNT.equalsCode(limitedCode)
				|| LimitationCodeEnum.SKU_COUNT.equalsCode(limitedCode)) {
			for (SkuQuantity skuQuantity : skuCodes) {
				rollBackRedisDataOpsHash(limitedCode, tenantCode, activityCode, userCode,
						ConvertUtils.toBigDecimal(skuQuantity.getQuantity()), skuQuantity.getSkuCode());
			}
		} else {
			rollBackRedisDataOpsHash(limitedCode, tenantCode, activityCode, userCode, delta, "");
		}
	}

	public void rollBackRedisDataOpsHash(String limitedCode, String tenantCode, String activityCode, String userCode,
			BigDecimal delta, String skuCode) {

		String hKey = this.buildIncentiveKey(limitedCode, tenantCode, activityCode, "", "");
		String limitedKey = this.buildIncentiveKey(limitedCode, tenantCode, activityCode, userCode, skuCode);
		if (LimitationCodeEnum.USER_TOTAL_COUNT.equalsCode(limitedCode)
				|| LimitationCodeEnum.USER_TOTAL_AMOUNT.equalsCode(limitedCode)
				|| LimitationCodeEnum.USER_TOTAL_COUNT_FLASH_SALE.equalsCode(limitedCode)
				|| LimitationCodeEnum.USER_DAY_ORDER_COUNT.equalsCode(limitedCode)
				|| LimitationCodeEnum.USER_SKU_COUNT.equalsCode(limitedCode)
				|| LimitationCodeEnum.SKU_COUNT.equalsCode(limitedCode)) {
			if (this.isTimesValue(limitedCode)) {
				redisTemplate.opsHashIncrement(Constants.APP_KEY, hKey, limitedKey, this.timesValue(delta));
			} else {
				redisTemplate.opsHashIncrement(Constants.APP_KEY, hKey, limitedKey, this.amountValue(delta));
			}
		} else {
			if (this.isTimesValue(limitedCode)) {
				redisTemplate.opsValueIncrement(Constants.APP_KEY, limitedKey, this.timesValue(delta));
			} else {
				redisTemplate.opsValueIncrement(Constants.APP_KEY, limitedKey, this.amountValue(delta));
			}

		}
		log.info("redis limit increment, hKey:{},limitedKey:{},delta:{}", hKey, limitedKey, delta);
	}

	private long timesValue(BigDecimal value) {

		return value.setScale(0, RoundingMode.HALF_UP).longValue();
	}

	private BigDecimal timesValue(long value) {

		return BigDecimal.valueOf(value);
	}

	private long amountValue(BigDecimal value) {

		return value.multiply(new BigDecimal(1000)).setScale(0, RoundingMode.HALF_UP).longValue();
	}

	private BigDecimal amountValue(long value) {

		return BigDecimal.valueOf(value / 1000.0);
	}

	private boolean isTimesValue(String limitedCode) {

		return LimitationCodeEnum.isTimes(limitedCode);
	}

}
