package com.gtech.promotion;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import java.util.concurrent.*;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2022/10/28 15:46
 */
@Component
public class PromotionThreadPoolConfig {

    @Bean
    public ExecutorService flashActivityReceiveThreadPool() {
        ThreadFactory threadFactory = new ThreadFactoryBuilder().setNameFormat("flash-receive-%d").setDaemon(true).build();
        return new ThreadPoolExecutor(50, 200, 5L, TimeUnit.SECONDS, new LinkedBlockingQueue<>(2000), threadFactory,new ThreadPoolExecutor.CallerRunsPolicy());
    }

    @Bean
    public ExecutorService queryActivityListByProductListThreadPool() {
        ThreadFactory threadFactory = new ThreadFactoryBuilder().setNameFormat("activity-receive-%d").setDaemon(true).build();
        return new ThreadPoolExecutor(50, 200, 5L, TimeUnit.SECONDS, new LinkedBlockingQueue<>(2000), threadFactory,new ThreadPoolExecutor.CallerRunsPolicy());
    }
}
