package com.gtech.promotion.domain.marketing;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class TicketReleaseDomain implements Serializable {

    private static final long serialVersionUID = -1654890617006558820L;
    private String domainCode;

    private String tenantCode;

    private String orgCode;

    private String releaseCode;

    private String activityCode;

    private Long quality;

    private String operateUser;
}
