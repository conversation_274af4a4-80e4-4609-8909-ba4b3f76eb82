package com.gtech.promotion.domain.purchaseconstraint;

import com.gtech.promotion.api.ApiConstants;
import com.gtech.promotion.code.activity.ItemScopeTypeEnum;
import com.gtech.promotion.code.activity.ProductSelectionEnum;
import com.gtech.promotion.dto.out.product.ProductDetailInDTO;
import com.gtech.promotion.vo.bean.*;
import com.gtech.promotion.vo.param.activity.CustomCondition;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;

@Data
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PurchaseConstraintDomain {

    private String tenantCode;

    private String orgCode;

    private String domainCode;

    /**
     * 限购状态
     * @see com.gtech.promotion.code.purchaseconstraint.PurchaseConstraintStatusEnum
     */
    private String purchaseConstraintStatus;

    private String purchaseConstraintName;

    private Date purchaseConstraintStartTime;

    private Date purchaseConstraintEndTime;

    private String skuToken;

    @Builder.Default
    @ApiModelProperty(value = ApiConstants.PRODUCT_SELECTION_TYPE)
    private String productSelectionType = ProductSelectionEnum.SELECT.code();
    public void setProductSelectionType(String productSelectionType) {
        this.productSelectionType = StringUtils.isBlank(productSelectionType) ? ProductSelectionEnum.SELECT.code() : productSelectionType;
    }

    @Builder.Default
    private String itemScopeType = ItemScopeTypeEnum.ALL_SCOPE.code();
    public void setItemScopeType(String itemScopeType) {
        this.itemScopeType = StringUtils.isBlank(itemScopeType) ? ItemScopeTypeEnum.ALL_SCOPE.code() : itemScopeType;
    }

    private String conditionProductType;

    public void setConditionProductType(String conditionProductType) {
        this.conditionProductType = conditionProductType;
    }

    private List<ProductScope> products;

    private List<ProductDetailInDTO> productDetails;

    private List<ProductDetailInDTO> productDetailBlackList;

    private List<Qualification> qualifications;

    private String storeType;

    private List<ActivityStore> channelStores;

    private String periodType;

    private ActivityPeriod activityPeriod;

    private String operateUser;

    private String operateLastName;

    private String operateFirstName;

    private Integer firstRefusal;

    private List<PurchaseConstraintRule> purchaseConstraintRules;

    private String description;

    private Integer priority;

    /**
     * 高级价格限购标识，默认0，非高级价格限购
     */
    private Integer priceSetting;
    private List<CustomCondition> customConditionsList;
    private List<CustomCondition> customRulesList;

}
