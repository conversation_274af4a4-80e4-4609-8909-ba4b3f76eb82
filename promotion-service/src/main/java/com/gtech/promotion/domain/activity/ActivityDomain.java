/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.domain.activity;

import com.alibaba.fastjson.JSONObject;
import com.gtech.promotion.api.ApiConstants;
import com.gtech.promotion.dto.out.product.ProductDetailInDTO;
import com.gtech.promotion.vo.bean.*;
import com.gtech.promotion.vo.param.activity.CustomCondition;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serializable;
import java.util.List;

/**
 * 
 *
 * <AUTHOR>
 * @Date 2020-03-03
 */
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ActivityDomain implements Serializable {

    private static final long serialVersionUID = -4870203185742570450L;

    private String externalActivityId;


    // Domain code
    private String domainCode;

    // Tenant code
    private String tenantCode;

    private String promotionCategory;
    private String promotionCategoryName;


    private String orgCode;

    // Activity Name
    private String activityName;

    private String priceCondition;
    //Custom conditions.
    private List<CustomCondition> customConditions;

    // Activity sponsors
    private String sponsors;

    private String opsType;

    // Activity type：01-Activity 02-Coupon
    private String activityType;

    // 多语言属性
    private List<ActivityLanguage> activityLanguages;
    
    // 是否对外显示：1显示；2不显示
    private Integer showFlag;

    // 活动预热开始时间
    private String warmBegin;

    // 活动预热结束时间
    private String warmEnd;

    // 活动开始时间
    private String activityBegin;

    // 活动结束时间
    private String activityEnd;

    // 活动链接
    private String activityUrl;

    // 活动优先级（1-999的整数）
    private Integer priority;

    // 商品上传唯一辨识（有上传文件时必填）
    private String skuToken;

    // ApiConstants.PRODUCT_SELECTION_TYPE
    private String productSelectionType;

    // Product item scope type: 1-All scope 2-By spu in scope
    private Integer itemScopeType;

    // Template code.
    private String templateCode;

    @ApiModelProperty(value = ApiConstants.INCENTIVE_LIMITED_FLAG,required = true)
    private String incentiveLimitedFlag;

    @ApiModelProperty(value = "促销限制列表",required = false)
    private List<IncentiveLimited> incentiveLimiteds;

    @ApiModelProperty(value = "参与资格列表（为空表示不限）",required = false)
    private List<Qualification> qualifications;

    @ApiModelProperty(value = "店铺类型:00-不限；01-指定店铺",required = true)
    private String storeType;

    @ApiModelProperty(value = "渠道店铺列表",required = false)
    private List<ActivityStore> channelStores;

    @ApiModelProperty(value = "赠品列表",required = false)
    private List<Giveaway> giveaways;

    @ApiModelProperty(value = "函数参数列表",required = true)
    private List<FunctionParam> funcParams;

    @ApiModelProperty(value = "商品列表",required = false)
    private List<ProductScope> products;

    @ApiModelProperty(value = "商品详情列表",required = false)
    private List<ProductDetailInDTO> productDetails;

    @ApiModelProperty(value = "黑名单商品详情列表",required = false)
    private List<ProductDetailInDTO> productDetailBlackList;

    private String createUser;

    private String operateLastName;

    private String operateFirstName;

    private String updateUser;

    private String coolDown;

    private String backgroundImage;

    @ApiModelProperty(value = ApiConstants.PRODUCT_CONDITION)
    private String productCondition;

    private ActivityPeriod activityPeriod;

    @ApiModelProperty(value = "Ribbon image.")
    private String ribbonImage;

    @ApiModelProperty(value = "Position of Ribbon image .")
    private String ribbonPosition;

    @ApiModelProperty(value = "Ribbon text.")
    private String ribbonText;

    private JSONObject extendParams;

    private List<ExtImage> extImages;

    public boolean isAllProducts() {
        
        return CollectionUtils.isEmpty(products) && CollectionUtils.isEmpty(productDetails);
    }

}
