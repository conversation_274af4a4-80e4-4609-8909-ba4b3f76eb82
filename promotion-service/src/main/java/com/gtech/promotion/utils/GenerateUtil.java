package com.gtech.promotion.utils;

import java.text.SimpleDateFormat;
import java.util.Date;

public class GenerateUtil {
	private GenerateUtil() {
		throw new IllegalStateException("Utility class");
	}
    /**
     * 根据传入的时间表示格式，返回当前时间的格式 如果是yyyyMMdd，注意字母y不能大写。
     *
     * @param sformat
     *            yyyyMMddhhmmss
     * @return
     */
    public static String getDate(String sformat) {
        Date currentTime = new Date();
        SimpleDateFormat formatter = new SimpleDateFormat(sformat);
        return formatter.format(currentTime);
    }

    public static String getRandomNum(int num){
        String numStr = "";
        for(int i = 0; i < num; i++){
            numStr += (int)(10*(Math.random()));//NOSONAR
        }
        return numStr;
    }
    /**
     * 生成id
     * @return
     */
    public static Long getGenerateID(int num){
        String sformat = "MMddhhmmssSSS";
        String idStr = getDate(sformat) + getRandomNum(num);
        return Long.valueOf(idStr);
    }

}
