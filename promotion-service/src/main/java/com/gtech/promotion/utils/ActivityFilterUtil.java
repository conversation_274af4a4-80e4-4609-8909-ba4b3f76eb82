/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.utils;

import com.gtech.commons.utils.DateUtil;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.code.activity.ActivityStoreEnum;
import com.gtech.promotion.code.activity.ActivityTypeEnum;
import com.gtech.promotion.dao.model.activity.ActivityModel;
import com.gtech.promotion.dao.model.activity.ActivityPeriodModel;
import com.gtech.promotion.dao.model.activity.QualificationModel;
import com.gtech.promotion.dao.model.activity.TPromoActivityStoreVO;
import com.gtech.promotion.dto.cache.ActivityCacheDTO;
import com.gtech.promotion.helper.QualificationFilter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

/**
 * 活动过滤工具类
 *
 */
public class ActivityFilterUtil{
    private ActivityFilterUtil() {
        throw new IllegalStateException("Utility class");
    }
    /**
     * Filter activity with qualifications.
     */
    public static Map<String, ActivityCacheDTO> filterActivityByQualifications(Map<String, ActivityCacheDTO> activityMap, Map<String, List<String>> qualifications){

        if (MapUtils.isEmpty(activityMap)){
            return activityMap;
        }

        Map<String, ActivityCacheDTO> newCaches = new HashMap<>();
        for(Entry<String, ActivityCacheDTO> e : activityMap.entrySet()) {

            QualificationFilter qualificationFilter = new QualificationFilter(e.getValue().getQualificationModels());
            boolean filter = qualificationFilter.filter(QualificationModel.convertToModel(qualifications));
            if (filter){
                newCaches.put(e.getKey(), e.getValue());
            }
        }

        return newCaches;
    }
    /**
     * Filter activity with qualifications.
     */
    public static boolean filterActivityByQualifications(List<QualificationModel> activityQualifications, Map<String, List<String>> qualifications){
        if (CollectionUtils.isEmpty(activityQualifications)){
            return true;
        }
        QualificationFilter qualificationFilter = new QualificationFilter(activityQualifications);
        return qualificationFilter.filter(QualificationModel.convertToModel(qualifications));
    }

    /**
     * Filter activity with promotion store organization codes.
     */
    public static Map<String, ActivityCacheDTO> filterActivityByOrgCodes(Map<String, ActivityCacheDTO> activityMap, List<String> orgCodes) {

        if (MapUtils.isEmpty(activityMap)) {
            return activityMap;
        }

        boolean emptyCondition = CollectionUtils.isEmpty(orgCodes);
        if (!emptyCondition && orgCodes.contains(ActivityStoreEnum.ALL.code())) {
            return activityMap;
        }

        Map<String, ActivityCacheDTO> newCaches = new HashMap<>();
        for(Entry<String, ActivityCacheDTO> e : activityMap.entrySet()) {

            List<TPromoActivityStoreVO> activityStores = e.getValue().getPromoChannels();
            if (CollectionUtils.isEmpty(activityStores)) {
                // 当前活动不限制
                newCaches.put(e.getKey(), e.getValue());
                continue;
            }

            for(TPromoActivityStoreVO activityStore : activityStores) {
                if (!emptyCondition && orgCodes.contains(activityStore.getOrgCode())) {
                    newCaches.put(e.getKey(), e.getValue());
                    break;
                }
            }
        }

        return newCaches;
    }

    /**
     * Filter activity with promotion time.
     */
    public static Map<String, ActivityCacheDTO> filterActivityByTime(Map<String, ActivityCacheDTO> activityMap, String promotionTime) {
        if (MapUtils.isEmpty(activityMap)) {
            return activityMap;
        }
        if (StringUtils.isBlank(promotionTime)) {
            promotionTime = DateUtil.format(new Date(), DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        }
        long longTime = Long.parseLong(promotionTime);
        Map<String, ActivityCacheDTO> newCaches = new HashMap<>();
        for(Entry<String, ActivityCacheDTO> e : activityMap.entrySet()) {
            ActivityCacheDTO value = e.getValue();
            ActivityModel activity = value.getActivityModel();
            checkPeriodModel(promotionTime, longTime, newCaches, e, value, activity);
        }
        return newCaches;
    }

    /**
     * 根据预热时间过滤活动 兼容filterActivityByTime
     */
    public static Map<String, ActivityCacheDTO> filterActivityByWarmTime(Map<String, ActivityCacheDTO> activityMap, String promotionTime,String  warmBeginFrom) {

        if (StringUtils.isBlank(promotionTime)) {
            promotionTime = DateUtil.format(new Date(), DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        }

        if (MapUtils.isEmpty(activityMap)) {
            return activityMap;
        }

        long longTime = Long.parseLong(promotionTime);

        Map<String, ActivityCacheDTO> newCaches = new HashMap<>();
        for (Entry<String, ActivityCacheDTO> e : activityMap.entrySet()) {
            ActivityCacheDTO value = e.getValue();
            ActivityModel activity = value.getActivityModel();
            if (StringUtil.isNotEmpty(warmBeginFrom)) {
                // 根据时间过滤，warmBegin则不能为空
                String warmBegin = activity.getWarmBegin();
                if (StringUtil.isNotEmpty(warmBegin)) {
                    long warmTime = Long.parseLong(warmBegin);
                    checkActivityWarm(warmBeginFrom, newCaches, e, warmTime);
                }else {
                    checkPeriodModel(promotionTime, longTime, newCaches, e, value, activity);
                }
            } else {
                String warmBegin = activity.getWarmBegin();
                if (StringUtil.isNotEmpty(warmBegin)) {
                    long warmTime = Long.parseLong(warmBegin);
                    String currentTime = DateUtil.format(new Date(), DateUtil.FORMAT_YYYYMMDDHHMISS_14);
                    checkActivityWarm(currentTime, newCaches, e,  warmTime);
                }
                checkPeriodModel(promotionTime, longTime, newCaches, e, value, activity);
            }
        }
        return newCaches;
    }

    //周期活动处理
    public static void checkPeriodModel(String promotionTime, long longTime, Map<String, ActivityCacheDTO> newCaches, Entry<String, ActivityCacheDTO> e, ActivityCacheDTO value, ActivityModel activity) {
        if (Long.parseLong(activity.getActivityBegin()) < longTime && Long.parseLong(activity.getActivityEnd()) > longTime) {
            ActivityPeriodModel periodModel = value.getPeriodModel();
            if (null != periodModel && !CronUtil.checkDate(periodModel.getBeginPeriod(), periodModel.getEndPeriod(), periodModel.getIntervalWeek(), activity.getActivityBegin(), activity.getActivityEnd(), promotionTime)) {
                return;
            }
            newCaches.put(e.getKey(), value);
        }
    }

    //促销活动处理预热时间
    public static void checkActivityWarm(String warmBeginFrom, Map<String, ActivityCacheDTO> newCaches, Entry<String, ActivityCacheDTO> e, long warmTime) {

        if (Long.parseLong(warmBeginFrom) >= warmTime) {
            newCaches.put(e.getKey(), e.getValue());
        }
    }


    public static Map<String, ActivityCacheDTO> filterActivityByActivityTime(Map<String, ActivityCacheDTO> activityMap, String activityStartTime,String activityEndTime) {
        if (MapUtils.isEmpty(activityMap)) {
            return activityMap;
        }

        String promotionTime = DateUtil.format(new Date(), DateUtil.FORMAT_YYYYMMDDHHMISS_14);

        Map<String, ActivityCacheDTO> newCaches = new HashMap<>();
        for(Entry<String, ActivityCacheDTO> e : activityMap.entrySet()) {
            ActivityCacheDTO value = e.getValue();
            ActivityModel activity = value.getActivityModel();
            if (StringUtils.isNotBlank(activityStartTime) && StringUtils.isNotBlank(activityEndTime)){
                long startTime = Long.parseLong(activityStartTime);
                long endTime = Long.parseLong(activityEndTime);
                if (Long.parseLong(activity.getActivityBegin()) <= startTime && endTime < Long.parseLong(activity.getActivityEnd())) {
                    ActivityPeriodModel periodModel = value.getPeriodModel();
                    if (null != periodModel && !CronUtil.checkDate(periodModel.getBeginPeriod(), periodModel.getEndPeriod(),
                            periodModel.getIntervalWeek(),activity.getActivityBegin(), activity.getActivityEnd(), promotionTime)){
                        continue;
                    }
                    newCaches.put(e.getKey(), value);
                }
            }else if (StringUtils.isNotBlank(activityStartTime) && StringUtils.isEmpty(activityEndTime)){
                long startTime = Long.parseLong(activityStartTime);

                if (Long.parseLong(activity.getActivityBegin()) <= startTime) {
                    ActivityPeriodModel periodModel = value.getPeriodModel();
                    if (null != periodModel && !CronUtil.checkDate(periodModel.getBeginPeriod(), periodModel.getEndPeriod(),
                            periodModel.getIntervalWeek(),activity.getActivityBegin(), activity.getActivityEnd(), promotionTime)){
                        continue;
                    }
                    newCaches.put(e.getKey(), value);
                }
            }else if (StringUtils.isEmpty(activityStartTime) && StringUtils.isNotBlank(activityEndTime)){
                long endTime = Long.parseLong(activityEndTime);

                if (Long.parseLong(activity.getActivityEnd()) > endTime) {
                    ActivityPeriodModel periodModel = value.getPeriodModel();
                    if (null != periodModel && !CronUtil.checkDate(periodModel.getBeginPeriod(),
                            periodModel.getEndPeriod(), periodModel.getIntervalWeek(),activity.getActivityBegin(), activity.getActivityEnd(), promotionTime)){
                        continue;
                    }
                    newCaches.put(e.getKey(), value);
                }
            }

        }
        return newCaches;
    }

    /**
     * Filter activity with promotion activity type.
     */
    public static Map<String, ActivityCacheDTO> filterActivityByActivityType(Map<String, ActivityCacheDTO> activityMap, ActivityTypeEnum activityType) {
        Map<String, ActivityCacheDTO> newCaches = new HashMap<>();
        if (null == activityType || MapUtils.isEmpty(activityMap)) {
            return activityMap;
        }
        for(Entry<String, ActivityCacheDTO> e : activityMap.entrySet()) {
            ActivityModel activity = e.getValue().getActivityModel();
            if (activityType.equalsCode(activity.getActivityType())) {
                newCaches.put(e.getKey(), e.getValue());
            }
        }
        return newCaches;
    }



}
