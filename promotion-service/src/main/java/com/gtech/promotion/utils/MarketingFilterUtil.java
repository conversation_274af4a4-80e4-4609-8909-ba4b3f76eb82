/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.utils;

import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.code.activity.ActivityStoreEnum;
import com.gtech.promotion.code.activity.ActivityTypeEnum;
import com.gtech.promotion.dao.model.activity.ActivityModel;
import com.gtech.promotion.dao.model.activity.ActivityPeriodModel;
import com.gtech.promotion.dao.model.activity.QualificationModel;
import com.gtech.promotion.dao.model.marketing.flashsale.CacheFlashSaleModel;
import com.gtech.promotion.dao.model.marketing.flashsale.FlashSaleStoreModel;
import com.gtech.promotion.dto.cache.ActivityCacheDTO;
import com.gtech.promotion.helper.QualificationFilter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

/**
 * 活动过滤工具类
 */
public class MarketingFilterUtil {
	  private MarketingFilterUtil() {
		    throw new IllegalStateException("Utility class");
		  }

    /**
     * Filter activity with qualifications.
     */
    public static Map<String, CacheFlashSaleModel> filterActivityByQualifications(Map<String, CacheFlashSaleModel> flashSaleCacheMap, Map<String, List<String>> qualifications){

        if (MapUtils.isEmpty(flashSaleCacheMap)){
            return flashSaleCacheMap;
        }

        Map<String, CacheFlashSaleModel> newCaches = new HashMap<>();
        for(Entry<String, CacheFlashSaleModel> e : flashSaleCacheMap.entrySet()) {

            QualificationFilter qualificationFilter = new QualificationFilter(BeanCopyUtils.jsonCopyList(e.getValue().getQualifications(), QualificationModel.class));
            boolean filter = qualificationFilter.filter(QualificationModel.convertToModel(qualifications));
            if (filter){
                newCaches.put(e.getKey(), e.getValue());
            }
        }

        return newCaches;
    }

    /**
     * Filter activity with promotion time.
     */
    public static Map<String, CacheFlashSaleModel> filterActivityByTime(Map<String, CacheFlashSaleModel> activityMap, String promotionTime) {

        if (MapUtils.isEmpty(activityMap)) {
            return activityMap;
        }

        if (StringUtils.isBlank(promotionTime)) {
            promotionTime = DateUtil.format(new Date(), DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        }

        long longTime = Long.parseLong(promotionTime);

        Map<String, CacheFlashSaleModel> newCaches = new HashMap<>();
        for(Entry<String, CacheFlashSaleModel> e : activityMap.entrySet()) {
            CacheFlashSaleModel activity = e.getValue();
            if (Long.parseLong(activity.getActivityBegin()) < longTime && Long.parseLong(activity.getActivityEnd()) > longTime) {
                ActivityPeriodModel periodModel = activity.getActivityPeriod();
                if (null != periodModel && !CronUtil.checkDate(periodModel.getBeginPeriod(), periodModel.getEndPeriod(), periodModel.getIntervalWeek(),activity.getActivityBegin(), activity.getActivityEnd(), promotionTime)){
                    continue;
                }
                newCaches.put(e.getKey(), e.getValue());
            }
        }

        return newCaches;
    }

    /**
     * 可根据预热时间过滤活动，（兼容 filterActivityByTime 方法）
     * @param activityMap
     * @param promotionTime
     * @param warmBeginFrom
     * @param
     * @return
     */
    public static Map<String, CacheFlashSaleModel> filterActivityByWarmTime(Map<String, CacheFlashSaleModel> activityMap, String promotionTime,String  warmBeginFrom) {

        if (MapUtils.isEmpty(activityMap)) {
            return activityMap;
        }

        if (StringUtils.isBlank(promotionTime)) {
            promotionTime = DateUtil.format(new Date(), DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        }

        Map<String, CacheFlashSaleModel> newCaches = new HashMap<>();

        //当前时间
        long longTime = Long.parseLong(promotionTime);

        for(Entry<String, CacheFlashSaleModel> e : activityMap.entrySet()) {
            CacheFlashSaleModel activity = e.getValue();

            if (StringUtil.isNotEmpty(warmBeginFrom)) {
                // 根据时间过滤，warmBegin则不能为空
                String warmBegin = activity.getWarmBegin();
                if (StringUtil.isNotEmpty(warmBegin)) {
                    checkWarmTime(promotionTime, warmBeginFrom, newCaches, e, activity, warmBegin);
                    continue;
                } else {
                    if (Long.parseLong(activity.getActivityBegin()) < longTime && Long.parseLong(activity.getActivityEnd()) > longTime) {
                        ActivityPeriodModel periodModel = activity.getActivityPeriod();
                        if (null != periodModel && !CronUtil.checkDate(periodModel.getBeginPeriod(), periodModel.getEndPeriod(), periodModel.getIntervalWeek(), activity.getActivityBegin(), activity.getActivityEnd(), promotionTime)) {
                            continue;
                        }
                        newCaches.put(e.getKey(), e.getValue());
                    }
                }
            } else {

                //预热时间不传,如果当前预热开始，则也需要返回
                String warmBegin = activity.getWarmBegin();
                String currentTime = DateUtil.format(new Date(), DateUtil.FORMAT_YYYYMMDDHHMISS_14);
                if (StringUtil.isNotEmpty(warmBegin)) {
                    checkWarmTime(promotionTime, currentTime, newCaches, e, activity, warmBegin);
                }

                if (Long.parseLong(activity.getActivityBegin()) < longTime && Long.parseLong(activity.getActivityEnd()) > longTime) {
                    ActivityPeriodModel periodModel = activity.getActivityPeriod();
                    if (null != periodModel && !CronUtil.checkDate(periodModel.getBeginPeriod(), periodModel.getEndPeriod(), periodModel.getIntervalWeek(), activity.getActivityBegin(), activity.getActivityEnd(), promotionTime)) {
                        continue;
                    }
                    newCaches.put(e.getKey(), e.getValue());
                }
            }
        }

        return newCaches;
    }

    //校验预热时间时间
    public static void checkWarmTime(String promotionTime, String warmBeginFrom, Map<String, CacheFlashSaleModel> newCaches,
                                     Entry<String, CacheFlashSaleModel> e, CacheFlashSaleModel activity, String warmBegin) {
        //周期性活动验证
        ActivityPeriodModel periodModel = activity.getActivityPeriod();
        if (null != periodModel && !CronUtil.checkDate(periodModel.getBeginPeriod(), periodModel.getEndPeriod(), periodModel.getIntervalWeek(), activity.getActivityBegin(), activity.getActivityEnd(), promotionTime)) {
            return;
        }

        long warmTime = Long.parseLong(warmBegin);

        if (Long.parseLong(warmBeginFrom) >= warmTime) {
            newCaches.put(e.getKey(), e.getValue());
        }

    }


    /**
     * Filter activity with promotion store organization codes.
     */
    public static Map<String, CacheFlashSaleModel> filterActivityByOrgCodes(Map<String, CacheFlashSaleModel> flashSaleCacheMap, List<String> orgCodes) {

        if (MapUtils.isEmpty(flashSaleCacheMap)) {
            return flashSaleCacheMap;
        }

        boolean emptyCondition = CollectionUtils.isEmpty(orgCodes);
        if (!emptyCondition && orgCodes.contains(ActivityStoreEnum.ALL.code())) {
            return flashSaleCacheMap;
        }

        Map<String, CacheFlashSaleModel> newCaches = new HashMap<>();
        for(Entry<String, CacheFlashSaleModel> e : flashSaleCacheMap.entrySet()) {

            List<FlashSaleStoreModel> stores = e.getValue().getStores();
            if (CollectionUtils.isEmpty(stores)) {
                // 当前活动不限制
                newCaches.put(e.getKey(), e.getValue());
                continue;
            }

            for(FlashSaleStoreModel activityStore : stores) {
                if (!emptyCondition && orgCodes.contains(activityStore.getOrgCode())) {
                    newCaches.put(e.getKey(), e.getValue());
                    break;
                }
            }
        }

        return newCaches;
    }

    /**
     * Filter activity with promotion activity type.
     */
    public static Map<String, ActivityCacheDTO> filterActivityByActivityType(Map<String, ActivityCacheDTO> activityMap, ActivityTypeEnum activityType) {

        if (MapUtils.isEmpty(activityMap) || null == activityType) {
            return activityMap;
        }

        Map<String, ActivityCacheDTO> newCaches = new HashMap<>();
        for(Entry<String, ActivityCacheDTO> e : activityMap.entrySet()) {
            ActivityModel activity = e.getValue().getActivityModel();
            if (activityType.equalsCode(activity.getActivityType())) {
                newCaches.put(e.getKey(), e.getValue());
            }
        }
        
        return newCaches;
    }




    /**
     * Filter activity with promotion activity type list.
     */
    public static Map<String, CacheFlashSaleModel> filterActivityByActivityTypeList(Map<String, CacheFlashSaleModel> activityMap, List<String> activityTypeList) {

        if (MapUtils.isEmpty(activityMap) || CollectionUtils.isEmpty(activityTypeList)) {
            return activityMap;
        }
        Map<String, CacheFlashSaleModel> newCaches = new HashMap<>();
        for(Entry<String, CacheFlashSaleModel> e : activityMap.entrySet()) {
            if (activityTypeList.contains(e.getValue().getActivityType())) {
                newCaches.put(e.getKey(), e.getValue());
            }
        }
        return newCaches;
    }





}
