/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.utils;

import com.alibaba.fastjson.JSON;
import com.gtech.commons.utils.CheckUtils;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.calc.CalcActivity;
import com.gtech.promotion.calc.TemplateEnum;
import com.gtech.promotion.exception.ErrorCodes;
import com.gtech.promotion.helper.ActivityCodeComparator;
import com.gtech.promotion.vo.result.activity.ActivityGroupCache;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 表达式工具类
 *
 * 调用步骤：
 * 1：组装 活动id=模板编码的map
 * 2：调用sortActivity，将活动排好序
 * 3：每个活动判断是否满足条件之前，都要调用checkActivitySku（每个sku调用一次），判断sku是否可参加此活动，不能参加活动的sku筛选掉
 * 4：活动如果不满足条件，调用cancelActivitySku（每个sku调用一次）
 * 5：整个购物车计算完成，调用clearThreadLocalMap，清除本地线程变量
 */
@Slf4j
public class ExpressionHelper {
	private ExpressionHelper() {
		throw new IllegalStateException("Utility class");
	}
    public static final String DEF_EXCLUSION_KEY = "byOrder";

    public static final String DEF_EXPROSSION = ""

                    //      单件商品用券减金额           单件商品用券打折扣
                    + "[2_0104020103010401,2_0104020103010402]"

                    //         单品无条件减金额,    单品无条件打折扣,     单品无条件每件特价,   单品无条件每件不同特价
                    + " and [1_0101020103010401,1_0101020103010402,1_0101020103010403,1_0101020103010411] "

                    //        单品第数量减金额, 单品每第数量减金额,单品每第数量打折扣,单品第数量打折扣,  范围满数量减金额,  范围满金额减金额,  范围每满数量减金额,范围每满金额减金额,范围每第数量减金额,范围满数量每件减金额,范围第数量减金额, 范围满数量打折扣, 范围满金额打折扣,  范围每第数量打折扣, 范围第数量打折扣
                    + " and [1_0101020603020401,1_0101020403020401,1_0101020403020402,1_0101020603020402,1_0102020203020401,1_0102020203030401,1_0102020303020401,1_0102020303030401,1_0102020403020401,1_0102020203020414,1_0102020603020401,1_0102020203020402,1_0102020203030402,1_0102020403020402,1_0102020603020402]"

                    //      订单满数量减金额,     订单满金额减金额,    订单每满数量减金额,   订单每满金额减金额    订单满数量打折扣,    订单满金额打折扣
                    + " and [1_0103020203020401,1_0103020203030401,1_0103020303020401,1_0103020303030401,1_0103020203020402,1_0103020203030402]"

                    //      范围A满数量范围B减金额 范围A满金额范围B减金额 范围A满数量范围B打折 范围A满金额范围B打折
                    + " and [1_0102020203020412,1_0102020203030412,1_0102020203020413,1_0102020203030413]"

                    //      N范围选N减金额,       N范围选N一口价,     范围满数量一口价,     范围每满数量一口价
                    + " and [1_0102020503020401,1_0102020503020404,1_0102020203020404,1_0102020303020404]"

                    //      范围每满数量送同类商品,范围每满数量送不同类商品
                    + " and [1_0102020303020407,1_0102020303020408]"

                    //        单品第数量减金额,    单品第数量打折扣,    范围满数量减金额,   范围满金额减金额,    范围每满数量减金额,   范围每满金额减金额, 范围每第数量减金额, 范围满数量每件减金额,  范围第数量减金额,    范围满数量打折扣,    范围满金额打折扣,   范围每第数量打折扣,  范围第数量打折扣
                    + " and [2_0101020603020401,2_0101020603020402,2_0102020203020401,2_0102020203030401,2_0102020303020401,2_0102020303030401,2_0102020403020401,2_0102020203020414,2_0102020603020401,2_0102020203020402,2_0102020203030402,2_0102020403020402,2_0102020603020402]"

                    //      订单满数量减金额,     订单满金额减金额,    订单每满数量减金额,   订单每满金额减金额    订单满数量打折扣,    订单满金额打折扣
                    + " and [2_0103020203020401,2_0103020203030401,2_0103020303020401,2_0103020303030401,2_0103020203020402,2_0103020203030402]"

                    //      范围A满数量范围B减金额 范围A满金额范围B减金额 范围A满数量范围B打折 范围A满金额范围B打折
                    + " and [2_0102020203020412,2_0102020203030412,2_0102020203020413,2_0102020203030413]"

                    //      单品满数量送优惠券     ,  单品满金额送优惠券,   范围满数量送赠品              范围满金额送赠品              范围每满数量送赠品       范围每满金额送赠品        订单满数量送赠品             订单满金额送赠品
                    + " and [1_0101020703020406,1_0101020703030406,1_0102020203020406,1_0102020203030406,1_0102020303020406,1_0102020303030406,1_0103020203020406,1_0103020203030406]"

                    //      范围满数量邮费打折       范围满金额邮费打折   范围满数量邮费减金额   范围满金额邮费减金额   订单满数量邮费打折    订单满金额邮费打折
                    + " and [1_0102020203020405,1_0102020203030405,1_0102020203020415,1_0102020203030415,1_0103020203020405,1_0103020203030405]"

                    //      范围满数量邮费打折      范围满金额邮费打折    范围满数量邮费减金额  范围满金额邮费减金额    订单满数量邮费打折    订单满金额邮费打折
                    + " and [2_0102020203020405,2_0102020203030405,2_0102020203020415,2_0102020203030415,2_0103020203020405,2_0103020203030405]"

                    //券-商品范围满数量送赠品  券-商品范围满金额送赠品
                    + "and [2_0102020203020406,2_0102020203030406]"

                    ;
 
    // 优先级设置
    private static final Map<String, Integer> priorityMap = new HashMap<>();

    // 匹配所有模板编码
    private static Pattern pattern = Pattern.compile("\\[(\\d+_\\d+,*)+\\]");

    static{
        priorityMap.put("or", 1);
        priorityMap.put("and", 2);
    }

    public static final boolean isExpiredVersion(String expression) {

        return expression.indexOf("1_0102020203020415") < 0;
    }

    /**
     * 本地线程私有变量
     * map k:skucode;v:-activityCode-activityCode-....
     */
    private static final ThreadLocal<Map<String, String>> threadLocal = new ThreadLocal<>();

    /**
     * 设置本地线程私有变量
     * 
     * @param map 变量
     */
    private static void setThreadLocalMap(Map<String, String> map){
        threadLocal.set(map);
    }

    /**
     * 获取本地线程私有变量
     * 
     * @return 变量
     */
    public static Map<String, String> getThreadLocalMap(){
        log.info("购物车计算:threadLocal:{}",JSON.toJSONString(threadLocal.get()));
        return null == threadLocal.get() ? new HashMap<>() : threadLocal.get();
    }

    /**
     * 清除本地线程私有变量
     */
    public static void clearThreadLocalMap(){
        threadLocal.remove();
    }

    /**
     * 根据优先级互斥叠加表达式，排序组装成活动id表达式
     * 
     * @param expression 优先级互斥叠加表达式
     * @param activityCodeComparatorMap map参数：活动id=模板编码
     * @return 组装后的活动id表达式
     */
    public static String sortActivity(String expression, Map<String, ActivityCodeComparator> activityCodeComparatorMap){
        CheckUtils.isNotBlank(expression, ErrorCodes.PARAM_EMPTY, "expression");
        CheckUtils.isTrue(MapUtils.isNotEmpty(activityCodeComparatorMap), ErrorCodes.PARAM_EMPTY, "activityCodeComparatorMap");

        StringBuilder expressionBuilder = new StringBuilder(expression);

        //匹配所有模板编码
        Matcher matcher = pattern.matcher(expression); 
        while (matcher.find()){
            String expr = matcher.group();

            // 组装同模板下的所有活动,按照活动优先级和创建顺序倒叙
            TreeSet<ActivityCodeComparator> activityCodeSet = new TreeSet<>(Comparator.reverseOrder());
            for (Map.Entry<String, ActivityCodeComparator> entry : activityCodeComparatorMap.entrySet()){
                if (expr.contains(entry.getValue().expressionTemplateCode())){
                    activityCodeSet.add(entry.getValue());
                }
            }

            // 用活动id替换表达式中的模板编码
            int startIndex = expressionBuilder.indexOf(expr);
            expressionBuilder.replace(startIndex, startIndex + expr.length(), activityCodeSet2String(activityCodeSet));
        }

        return expressionBuilder.toString();
    }
    
    private static String activityCodeSet2String(TreeSet<ActivityCodeComparator> activityCodeSet) {

        if (CollectionUtils.isEmpty(activityCodeSet)) {
            return "0";
        }

        StringBuilder stringBuilder = new StringBuilder();
        String orSeparator = " or ";
        String andSeparator = " and ";
        int size = activityCodeSet.size();

        stringBuilder.append(activityCodeSet.pollFirst().getActivityCode());
        while(!activityCodeSet.isEmpty()) {
            ActivityCodeComparator acComparator = activityCodeSet.pollFirst();

            if (TemplateEnum.code2SelfExclusive(acComparator.getTemplateCode())) {
                stringBuilder.append(orSeparator).append(acComparator.getActivityCode());
            } else {
                stringBuilder.append(andSeparator).append(acComparator.getActivityCode());
            }
        }

        // 活动有多个的时候，需要添加小括号
        if (size > 1){
            stringBuilder.insert(0, "( ").append(" )");
        }

        return stringBuilder.toString();
    }

	public static boolean checkActivitySkuWithGroup(String exclusionKey, String groupCode, String activityCode, List<ActivityGroupCache> groupCacheList,
			boolean tryFlag) {
		log.debug("exclusionKey:{},groupCacheList:{},groupMap:{},tryFlag:{}", exclusionKey, groupCode, JSON.toJSONString(groupCacheList), tryFlag);
		if (CollectionUtils.isEmpty(groupCacheList) || StringUtil.isEmpty(groupCode)) {
			log.info("groupCacheList or groupCode empty!");
			return true;
		}
		Map<String, List<String>> groupMap = new HashMap<>();
		for (ActivityGroupCache activityGroupCache : groupCacheList) {
			groupMap.put(activityGroupCache.getGroupCode(), activityGroupCache.getRelationList());
		}
		Map<String, String> map = getThreadLocalMap();
		String value = map.get(exclusionKey); // 是否参加上一个活动组
		if (StringUtil.isEmpty(value)) {
			if (!tryFlag) {
				map.put(exclusionKey, "-" + groupCode + ":" + activityCode);
				setThreadLocalMap(map);
			}
			return true;
		}
		String[] array = value.split("-");
		// 判断是否互斥
		for (int i = 1; i < array.length; i++) {
			String existGroupCode = array[i].split(":")[0];
			if (groupCode.equals(existGroupCode)) {
				return false;
			} else {
				List<String> gropuaList = groupMap.get(existGroupCode);
				if (!CollectionUtils.isEmpty(gropuaList) && gropuaList.contains(groupCode)) {
					return false;
				}
			}
		}
		if (!tryFlag) {
			map.put(exclusionKey, value + "-" + groupCode + ":" + activityCode);
		}

		return true;
	}

    /**
     * 判断当前sku是否可以参加当前活动, 并且添加sku和其参与的活动threadLocal:map[skuCode-活动，活动，...]
     * 
     * @param exclusionKey -- (Optional) Activity exclusive scope identification key.
     */
    public static boolean checkActivitySku(String activityExpression, CalcActivity calcActivity, String exclusionKey, boolean tryFlag) {

        if (StringUtils.isBlank(exclusionKey)) {
            exclusionKey = DEF_EXCLUSION_KEY;
        }
        String activityCode = calcActivity.getActivityCode();

        // 采用后缀表达式方式解析
        Deque<String> stack = midToAfter(activityExpression);
        // 倒序
        Deque<String> reverseStack = new ArrayDeque<>();
        while (!stack.isEmpty()) {
            reverseStack.push(stack.pop());
        }
        Map<String, String> map = getThreadLocalMap();

        String value = map.get(exclusionKey); // 默认当前sku已参加的活动id为0

        // 当前sku没有上一个活动
        if (StringUtil.isBlank(value)) {
            if (!tryFlag) {
                map.put(exclusionKey, "-" + activityCode);
                setThreadLocalMap(map);
            }
            return true;
        }

        // 判断是否可以叠加
        String[] array = value.split("-");
        for(int i = 1; i < array.length; i++) {
            if (activityCode.equals(array[i])) {
                if (TemplateEnum.code2SelfExclusive(calcActivity.getTemplateCode())) {
                    return false;
                }
            } else if(!isCombine(array[i], activityCode, reverseStack)) {
                return false;
            }
        }

        if (!tryFlag) {
            map.put(exclusionKey, value + "-" + activityCode);
        }
        return true;
    }

    /**
     * 中缀表达式转换成后缀表达式
     * 
     * @param midExpression 中缀表达式
     * @return 后缀表达式
     */
    private static Deque<String> midToAfter(String midExpression){

        // 初始化一个运算符栈
        Deque<String> symbolStack = new ArrayDeque<>();
        // 初始化一个后缀栈
        Deque<String> afterStack = new ArrayDeque<>();

        midExpression = midExpression.replaceAll("\\(", " ( ").replaceAll("\\)", " ) ");
        String[] split = midExpression.split("\\s+");

        for (String temp : split){

            if (afterStackPush(afterStack, temp)) continue;

            // 若当前的字符是左括号，则将其压入运算符栈
            if (temp.equals("(")){
                symbolStack.push(temp);
            }else if (temp.equals(")")){
                //若当前字符是右括号，反复将栈顶符号弹出，并送往后缀表达式中，直到栈顶元素为左括号为止，然后将左括号出栈并丢弃
                while (!symbolStack.peek().equals("(")){
                    afterStack.push(symbolStack.pop());
                }
                symbolStack.pop();
            }else{
                // 当此运算符的优先级高于栈顶的运算符时，则将此运算符压入栈,否则，弹出栈顶运算符送往后缀式，并将当前运算符压栈,遇到括号或栈为空要停止
                while (!symbolStack.isEmpty() && !symbolStack.peek().equals("(") && !symbolStack.peek().equals(")") && compareSymbol(temp, symbolStack.peek()) < 0){
                    afterStack.push(symbolStack.pop());
                }
                symbolStack.push(temp);
            }
        }

        // 读取结束，则将运算符栈中剩余的所有的运算符弹出并送往后缀表达式
        while (!symbolStack.isEmpty()){
            afterStack.push(symbolStack.pop());
        }

        return afterStack;
    }

    private static boolean afterStackPush(Deque<String> afterStack, String temp) {
        if (!isSymbol(temp)){
            // 若当前的 字符是操作数，则直接送往后缀表达式
            if (temp.length() > 0) {
                afterStack.push(temp);
            }
            return true;
        }
        return false;
    }

    /**
     * 是否是操作符
     * 
     * @return 是：true 否：false
     */
    private static boolean isSymbol(String str){
        switch (str) {
            case "or":
            case "and":
            case "(":
            case ")":
                return true;
            default:
                return false;
        }
    }

    /**
     * 是否可叠加
     * 
     * @param stackParam 后缀表达式栈
     * @return true：可叠加
     */
    private static boolean isCombine(String leftCode, String rightCode, Deque<String> stackParam){
        
        Deque<String> stack = new ArrayDeque<>(stackParam);
        Deque<String> temp = new ArrayDeque<>();//存放后缀表达式的操作数的栈
        while (!stack.isEmpty()){
            if (!isSymbol(stack.peek())){
                temp.push(stack.pop());
                continue;
            }

            String o1 = temp.pop();
            String o2 = temp.pop();
            if (!stack.isEmpty()){
                if (isaBoolean(leftCode, rightCode, o1, o2)){
                    return "and".equals(stack.peek());
                }else if (o1.equals(leftCode) || o2.equals(leftCode)){
                    temp.push(leftCode);
                }else if (o1.equals(rightCode) || o2.equals(rightCode)){
                    temp.push(rightCode);
                }else{
                    temp.push("0");
                }
                stack.pop();
            }
        }

        return false;
    }

    /**
     * 判断2个活动是否互斥
     * 
     * @param midExpression 表达式
     * @return true：互斥
     */
    public static boolean isExclusion(String leftCode, String rightCode, String midExpression) {

        Deque<String> midStack = midToAfter(midExpression);
        Deque<String> stack = new ArrayDeque<>();
        while (!midStack.isEmpty()) {
            stack.push(midStack.pop());
        }

        Deque<String> temp = new ArrayDeque<>();//存放后缀表达式的操作数的栈
        while (!stack.isEmpty()) {
            if (!isSymbol(stack.peek())) {
                temp.push(stack.pop());
                // 跳过非运算符部分
                continue;
            }

            String o1 = temp.pop();
            String o2 = temp.pop();
            if (!stack.isEmpty()) {
                if (isaBoolean(leftCode, rightCode, o1, o2)) {
                    return !"and".equals(stack.peek());
                } else if (o1.equals(leftCode) || o2.equals(leftCode)) {
                    temp.push(leftCode);
                } else if (o1.equals(rightCode) || o2.equals(rightCode)) {
                    temp.push(rightCode);
                } else {
                    temp.push("0");
                }
                stack.pop();
            }
        }

        return true;
    }

	/**
	 * 判断2个活动是否互斥
	 * 
	 * @param leftCode
	 * @param rightCode
	 * @param groupCacheList
	 * @return
	 */
	public static boolean isExclusionWithGroup(String leftCode, String rightCode, List<ActivityGroupCache> groupCacheList) {

		if (StringUtil.isEmpty(leftCode)) {
			return false;
		}
		leftCode = leftCode.split(":")[0];
		if (CollectionUtils.isEmpty(groupCacheList) || StringUtil.isEmpty(rightCode)) {
			log.info("groupCacheList or groupCode empty!");
			return false;
		}
		if (leftCode.equals(rightCode)) {
			return true;
		}
		for (ActivityGroupCache activityGroupCache : groupCacheList) {
			List<String> relationList = activityGroupCache.getRelationList();
			if (activityGroupCache.getGroupCode().equals(leftCode) && CollectionUtils.isNotEmpty(relationList) && relationList.contains(rightCode)) {
				return true;
			}
		}
		return false;
    }

    public static boolean isaBoolean(String leftCode, String rightCode, String o1, String o2) {
        return (o1.equals(rightCode) && o2.equals(leftCode)) || (o2.equals(rightCode) && o1.equals(leftCode));
    }

    /**
     * 判断操作符的优先级（不含小括号）
     * 
     * @return >0 操作符1优先 <0 操作符2优先 ==0同种操作符
     */
    private static int compareSymbol(String symbol1,String symbol2){

        return priorityMap.get(symbol2) - priorityMap.get(symbol1);
    }

    /**
     * 当前sku取消参加当前活动的计算（不满足活动条件）
     */
    public static void cancelActivitySku(String skuCode){

        Map<String, String> map = getThreadLocalMap();
        String value = map.get(skuCode);
        if (StringUtil.isNotBlank(value)){
            map.put(skuCode, value.substring(0, value.lastIndexOf('-')));
        }

        setThreadLocalMap(map);
    }

}
