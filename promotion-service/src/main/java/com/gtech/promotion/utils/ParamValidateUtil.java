package com.gtech.promotion.utils;

import com.alibaba.fastjson.JSON;
import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.exception.ErrorCodes;
import com.gtech.promotion.feign.MasterDataFeignClient;
import com.gtech.promotion.vo.param.activity.CustomCondition;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Component
public class ParamValidateUtil {


    private static MasterDataFeignClient masterDataFeignClient;

    @Autowired
    public void setMasterDataFeignClient(MasterDataFeignClient client) {
        ParamValidateUtil.masterDataFeignClient = client;
    }

    public static void checkCustomCondition(List<CustomCondition> customConditions, String tenantCode) {
        if (CollectionUtils.isNotEmpty(customConditions)) {
            Set<String> set = new HashSet<>();
            for (CustomCondition customCondition : customConditions) {
                if (set.contains(customCondition.getCustomKey())) {
                    CheckUtils.isTrue(false, ErrorCodes.PROMO_DUPLICATE, "customKey");
                } else {
                    set.add(customCondition.getCustomKey());
                }
                if (CollectionUtils.isNotEmpty(customCondition.getCustomValueList())) {
                    customCondition.setCustomValue(JSON.toJSONString(customCondition.getCustomValueList()));
                }
            }
        }
    }

}
