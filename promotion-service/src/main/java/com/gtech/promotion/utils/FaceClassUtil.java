/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.utils;

import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.code.activity.FuncTypeEnum;
import com.gtech.promotion.code.coupon.FaceUnitEnum;
import com.gtech.promotion.dao.model.activity.FunctionParamModel;
import com.gtech.promotion.dto.in.coupon.FaceClass;

import java.math.BigDecimal;
import java.util.List;

/**   
 * 券面值单位和值
 */
public class FaceClassUtil{
	private FaceClassUtil() {
		throw new IllegalStateException("Utility class");
	}
    public  static FaceClass faceUnitAndFaceValue(String rewardType, List<FunctionParamModel> paramList){
        FaceClass  faceClass = new FaceClass();
        //填充面值
        if ("01".equals(rewardType) || "02".equals(rewardType)){
            for (FunctionParamModel paramVO : paramList){
                if (FuncTypeEnum.IncentiveEnum.REDUCE_AMOUNT.equalsCode(paramVO.getFunctionCode())){
                    faceClass.setFaceUnit(FaceUnitEnum.MONEY.code());
                    faceClass.setFaceValue(new BigDecimal(paramVO.getParamValue()));
                }
                if (FuncTypeEnum.IncentiveEnum.DISCOUNT.equalsCode(paramVO.getFunctionCode())){
                    BigDecimal bigDecimal = new BigDecimal(paramVO.getParamValue());
                    faceClass.setFaceValue(bigDecimal.multiply(new BigDecimal(10)));
                    faceClass.setFaceUnit(FaceUnitEnum.DISCOUNT.code());
                }
                if (StringUtil.isNotBlank(faceClass.getFaceUnit())) {
                    break;
                }
            }
        }
        return faceClass;
    }
}
  
