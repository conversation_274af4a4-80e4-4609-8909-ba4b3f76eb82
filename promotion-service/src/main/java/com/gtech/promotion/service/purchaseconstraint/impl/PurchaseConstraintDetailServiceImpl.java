package com.gtech.promotion.service.purchaseconstraint.impl;

import com.gtech.promotion.dao.entity.purchaseconstraint.PurchaseConstraintDetail;
import com.gtech.promotion.dao.mapper.purchaseconstraint.PurchaseConstraintDetailMapper;
import com.gtech.promotion.dao.model.purchaseconstraint.PurchaseConstraintDetailListModel;
import com.gtech.promotion.service.purchaseconstraint.PurchaseConstraintDetailService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class PurchaseConstraintDetailServiceImpl implements PurchaseConstraintDetailService {
    @Autowired
    private PurchaseConstraintDetailMapper purchaseConstraintDetailMapper;

    @Override
    public void insert(PurchaseConstraintDetail purchaseConstraintDetail) {
        purchaseConstraintDetailMapper.insertSelective(purchaseConstraintDetail);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insert(List<PurchaseConstraintDetail> purchaseConstraintDetail) {
        Optional.ofNullable(purchaseConstraintDetail)
                .filter(CollectionUtils::isNotEmpty)
                .ifPresent(x -> purchaseConstraintDetailMapper.insertList(x));
    }

    @Override
    public List<PurchaseConstraintDetail> list(PurchaseConstraintDetailListModel model) {
        return purchaseConstraintDetailMapper.list(model);
    }

    @Override
    public int decrement(PurchaseConstraintDetail detail) {
        return purchaseConstraintDetailMapper.decrement(detail);
    }

    @Override
    public int delete(List<PurchaseConstraintDetail> detailInserts) {
        if (CollectionUtils.isEmpty(detailInserts)){
            return 0;
        }
        Example example = new Example(PurchaseConstraintDetail.class);
        example.createCriteria()
                .andIn("customerCode", detailInserts.stream().map(PurchaseConstraintDetail::getCustomerCode).collect(Collectors.toList()))
                .andIn("purchaseConstraintCode", detailInserts.stream().map(PurchaseConstraintDetail::getPurchaseConstraintCode).collect(Collectors.toList()));
        return purchaseConstraintDetailMapper.deleteByCondition(example);

    }
}
