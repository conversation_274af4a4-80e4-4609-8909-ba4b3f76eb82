package com.gtech.promotion.service.marketing.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.gtech.commons.page.PageData;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.code.activity.ActivityStatusEnum;
import com.gtech.promotion.code.activity.OperationTypeEnum;
import com.gtech.promotion.code.marketing.ActivityTypeEnum;
import com.gtech.promotion.component.flashsale.DataSyncComponent;
import com.gtech.promotion.dao.entity.marketing.BaseEntity;
import com.gtech.promotion.dao.entity.marketing.MarketingEntity;
import com.gtech.promotion.dao.mapper.marketing.MarketingMapper;
import com.gtech.promotion.dao.model.activity.OperationLogModel;
import com.gtech.promotion.dao.model.marketing.MarketingModel;
import com.gtech.promotion.dto.in.marketing.MarketingQueryInDto;
import com.gtech.promotion.service.activity.OperationLogService;
import com.gtech.promotion.service.marketing.MarketingService;
import com.gtech.promotion.vo.param.marketing.flashsale.FlashSaleQueryListParam;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.Date;
import java.util.List;

@Service
public class MarketingServiceImpl extends BaseServiceImpl<MarketingEntity, MarketingModel> implements MarketingService {

    private static final String SYSTEM_AUTO_COMPLETION = "SYSTEM AUTO COMPLETION";
    private static final String SYSTEM = "SYSTEM";

    @Autowired
    private MarketingMapper marketingMapper;

    @Autowired
    private OperationLogService operationLogService;
    @Autowired
    private DataSyncComponent dataSyncComponent; // 这里循环依赖

    public MarketingServiceImpl() {
        super(MarketingEntity.class, MarketingModel.class);
    }

    public PageData<MarketingModel> queryMarketingList(MarketingQueryInDto inDto){
        PageHelper.startPage(inDto.getPageNum(), inDto.getPageSize());

        Example example = new Example(MarketingEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(BaseEntity.C_DOMAIN_CODE, inDto.getDomainCode())
                .andEqualTo(BaseEntity.C_TENANT_CODE, inDto.getTenantCode())
                ;

        if (StringUtil.isNotBlank(inDto.getOrgCode()) && inDto.getDefaultFlag()) {
            criteria.andEqualTo(BaseEntity.C_ORG_CODE, inDto.getOrgCode());
        }else if (!inDto.getDefaultFlag()){
            criteria.andNotEqualTo(BaseEntity.C_ORG_CODE, "default");
        }
        if (StringUtil.isNotBlank(inDto.getActivityCode())) {
            criteria.andEqualTo(MarketingEntity.C_ACTIVITY_CODE, inDto.getActivityCode());
        }
        if (StringUtil.isNotBlank(inDto.getActivityType())) {
            criteria.andEqualTo(MarketingEntity.ACTIVITY_TYPE, inDto.getActivityType());
        }
        if (StringUtil.isNotBlank(inDto.getOpsType())) {
            criteria.andEqualTo(MarketingEntity.OPS_TYPE, inDto.getOpsType());
        }
        if (StringUtil.isNotBlank(inDto.getGroupCode())) {
            criteria.andEqualTo(MarketingEntity.GROUP_CODE, inDto.getGroupCode());
        }
        if (StringUtil.isNotBlank(inDto.getActivityStatus())) {
            criteria.andEqualTo(MarketingEntity.ACTIVITY_STATUS, inDto.getActivityStatus());
        }
        if (StringUtil.isNotBlank(inDto.getActivityName())) {
            criteria.andLike(MarketingEntity.ACTIVITY_NAME, "%" + inDto.getActivityName() + "%");
        }
        if (StringUtil.isNotBlank(inDto.getSponsors())) {
            criteria.andEqualTo(MarketingEntity.SPONSOR_S, inDto.getSponsors());
        }
        //活动开始时间段
        if (StringUtil.isNotBlank(inDto.getActivityBeginFrom())) {
            criteria.andGreaterThanOrEqualTo(MarketingEntity.ACTIVITY_BEGIN, inDto.getActivityBeginFrom());
        }
        if (StringUtil.isNotBlank(inDto.getActivityBeginTo())) {
            criteria.andLessThanOrEqualTo(MarketingEntity.ACTIVITY_BEGIN, inDto.getActivityBeginTo());
        }
        //活动结束时间段
        if (StringUtil.isNotBlank(inDto.getActivityEndFrom())) {
            criteria.andGreaterThanOrEqualTo(MarketingEntity.ACTIVITY_END, inDto.getActivityEndFrom());
        }
        if (StringUtil.isNotBlank(inDto.getActivityEndTo())) {
            criteria.andLessThanOrEqualTo(MarketingEntity.ACTIVITY_END, inDto.getActivityEndTo());
        }

        if (!StringUtil.isEmpty(inDto.getWarmBeginFrom())) {
            criteria.andGreaterThanOrEqualTo(MarketingEntity.WARM_BEGIN, inDto.getWarmBeginFrom());
        }
        if (!StringUtil.isEmpty(inDto.getWarmBeginTo())) {
            criteria.andLessThanOrEqualTo(MarketingEntity.WARM_BEGIN, inDto.getWarmBeginTo());
        }
        //活动结束时间段
        if (!StringUtil.isEmpty(inDto.getWarmEndFrom())) {
            criteria.andGreaterThanOrEqualTo(MarketingEntity.WARM_END, inDto.getWarmEndFrom());
        }
        if (!StringUtil.isEmpty(inDto.getWarmEndTo())) {
            criteria.andLessThanOrEqualTo(MarketingEntity.WARM_END, inDto.getWarmEndTo());
        }
        //抽奖准入规则
        if (!StringUtil.isEmpty(inDto.getLuckyDrawRuleFlag())) {
            criteria.andEqualTo(MarketingEntity.LUCKY_DRAW_RULE_FLAG, inDto.getLuckyDrawRuleFlag());
        }

        example.orderBy("id").desc();

        PageInfo<MarketingEntity> select = new PageInfo<>(marketingMapper.selectByCondition(example));
        return new PageData<>(BeanCopyUtils.jsonCopyList(select.getList(), MarketingModel.class), select.getTotal());
    }

    @Override
    @Transactional
    public int updateByActivityCode(MarketingModel marketingModel) {
        MarketingEntity marketingEntity = BeanCopyUtils.jsonCopyBean(marketingModel, MarketingEntity.class);

        Example example = new Example(MarketingEntity.class);
        example.createCriteria().andEqualTo(BaseEntity.C_ACTIVITY_CODE, marketingModel.getActivityCode());
        return marketingMapper.updateByConditionSelective(marketingEntity, example);

    }

    @Override
    public List<MarketingModel> queryMarketingList(String tenantCode, String activityType, String activityStatus) {
        Example example = new Example(MarketingEntity.class);
        Example.Criteria criteria = example.createCriteria();
        if(!StringUtils.isEmpty(tenantCode)){
            criteria.andEqualTo(BaseEntity.C_TENANT_CODE, tenantCode);
        }
        if(!StringUtils.isEmpty(activityType)){
            criteria.andEqualTo(MarketingEntity.ACTIVITY_TYPE, activityType);
        }
        if(StringUtil.isNotBlank(activityStatus)){
            criteria.andEqualTo(MarketingEntity.ACTIVITY_STATUS, activityStatus);
        }
        return BeanCopyUtils.jsonCopyList(marketingMapper.selectByCondition(example), MarketingModel.class);
    }

    @Override
    public List<MarketingModel> queryCurrentEffectiveMarketingList(String tenantCode, String activityType) {
        Example example = new Example(MarketingEntity.class);
        Example.Criteria criteria = example.createCriteria();
        String format = DateUtil.format(new Date(), DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        criteria.andEqualTo(BaseEntity.C_TENANT_CODE, tenantCode)
                .andEqualTo(MarketingEntity.ACTIVITY_TYPE, activityType)
                .andEqualTo(MarketingEntity.ACTIVITY_STATUS, ActivityStatusEnum.EFFECTIVE.code())
                .andLessThanOrEqualTo(MarketingEntity.ACTIVITY_BEGIN, format)
                .andGreaterThan(MarketingEntity.ACTIVITY_END, format);
        return BeanCopyUtils.jsonCopyList(marketingMapper.selectByCondition(example), MarketingModel.class);
    }

    @Override
    public PageData<MarketingModel> queryMarketingFlashSaleList(FlashSaleQueryListParam param) {
        PageHelper.startPage(param.getPageNum(), param.getPageSize());

        Example example = new Example(MarketingEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(BaseEntity.C_DOMAIN_CODE, param.getDomainCode())
                .andEqualTo(BaseEntity.C_TENANT_CODE, param.getTenantCode())
                .andEqualTo(MarketingEntity.ACTIVITY_TYPE, ActivityTypeEnum.FLASH_SALE.code());
        if (StringUtil.isNotBlank(param.getActivityStatus())) {
            criteria.andEqualTo(MarketingEntity.ACTIVITY_STATUS, param.getActivityStatus());
            if (ActivityStatusEnum.EFFECTIVE.equalsCode(param.getActivityStatus())) {
                criteria.andGreaterThan(MarketingEntity.ACTIVITY_END, DateUtil.getCurrentDateAsString(DateUtil.FORMAT_YYYYMMDDHHMISS_14));
            }
        }
        if (StringUtil.isNotBlank(param.getSponsors())) {
            criteria.andEqualTo(MarketingEntity.SPONSOR_S, param.getSponsors());
        }
        //活动开始时间段
        if (StringUtil.isNotBlank(param.getStartTime())) {
            criteria.andGreaterThanOrEqualTo(MarketingEntity.ACTIVITY_BEGIN, param.getStartTime());
        }
        if (StringUtil.isNotBlank(param.getEndTime())) {
            criteria.andLessThanOrEqualTo(MarketingEntity.ACTIVITY_BEGIN, param.getEndTime());
        }

        example.orderBy("id").desc();

        PageInfo<MarketingEntity> select;
        if (StringUtil.isNotBlank(param.getOrgCode())){
            select = new PageInfo<>(marketingMapper.selectFlashSaleByStore(param));
        }else{
            select = new PageInfo<>(marketingMapper.selectByCondition(example));
        }
        return new PageData<>(BeanCopyUtils.jsonCopyList(select.getList(), MarketingModel.class), select.getTotal());
    }

    @Override
    @Transactional
    public int expireMarketing() {
        MarketingEntity entity = new MarketingEntity();
        entity.setActivityStatus(ActivityStatusEnum.END.code());

        Example example = new Example(MarketingEntity.class);
        example.createCriteria().andEqualTo(MarketingEntity.ACTIVITY_STATUS, ActivityStatusEnum.EFFECTIVE.code())
                .andLessThanOrEqualTo(MarketingEntity.ACTIVITY_END, DateUtil.getCurrentDateAsString(DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        return marketingMapper.updateByConditionSelective(entity, example);
    }

    @Override
    public List<MarketingModel> queryShouldExpireMarketingList() {
        Example example = new Example(MarketingEntity.class);
        example.createCriteria().andEqualTo(MarketingEntity.ACTIVITY_STATUS, ActivityStatusEnum.EFFECTIVE.code())
                .andLessThanOrEqualTo(MarketingEntity.ACTIVITY_END, DateUtil.getCurrentDateAsString(DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        return BeanCopyUtils.jsonCopyList(marketingMapper.selectByCondition(example), MarketingModel.class);
    }

    @Override
    public List<MarketingModel> queryAllTenantEffectiveFlashSaleList(String minId, int pageSize) {
        PageHelper.startPage(1, pageSize, false);
        Example example = new Example(MarketingEntity.class);
        String currentDateAsString = DateUtil.getCurrentDateAsString(DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        example.createCriteria().andEqualTo(MarketingEntity.ACTIVITY_STATUS, ActivityStatusEnum.EFFECTIVE.code())
                .andEqualTo(MarketingEntity.ACTIVITY_TYPE, ActivityTypeEnum.FLASH_SALE.code())
                .andLessThanOrEqualTo(MarketingEntity.ACTIVITY_BEGIN, currentDateAsString)
                .andGreaterThan(MarketingEntity.ACTIVITY_END, currentDateAsString)
                .andGreaterThan(BaseEntity.C_ID, minId);
        example.orderBy(BaseEntity.C_ID);
        return BeanCopyUtils.jsonCopyList(marketingMapper.selectByCondition(example), MarketingModel.class);
    }

    @Override
    public List<MarketingModel> getMarketingByActivityCode(String tenantCode, List<String> activityCode, String status) {
        Example example = new Example(MarketingEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(BaseEntity.C_TENANT_CODE, tenantCode)
                .andEqualTo("activityStatus", status)
                .andIn("activityCode",activityCode);

        return BeanCopyUtils.jsonCopyList(marketingMapper.selectByCondition(example), MarketingModel.class);
    }

    @Override
    public List<MarketingModel> getMarketingByActivityCodeList(String tenantCode, List<String> activityCode) {
        Example example = new Example(MarketingEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(BaseEntity.C_TENANT_CODE, tenantCode)
                .andIn("activityCode",activityCode);
        return BeanCopyUtils.jsonCopyList(marketingMapper.selectByCondition(example), MarketingModel.class);
    }

    @Override
    @Transactional
    public MarketingModel findByActivityCode(String activityCode) {
        MarketingModel marketingModel = super.findByActivityCode(activityCode);
        if (null != marketingModel && marketingModel.isNeedToDoExpire()){
            marketingModel.setActivityStatus(ActivityStatusEnum.CLOSURE.code());

            MarketingEntity entity = new MarketingEntity();
            entity.setActivityStatus(ActivityStatusEnum.CLOSURE.code());

            Example example = new Example(MarketingEntity.class);
            example.createCriteria().andEqualTo(BaseEntity.C_DOMAIN_CODE, marketingModel.getDomainCode())
                    .andEqualTo(BaseEntity.C_ORG_CODE, marketingModel.getOrgCode())
                    .andEqualTo(BaseEntity.C_TENANT_CODE, marketingModel.getTenantCode())
                    .andEqualTo(BaseEntity.C_ACTIVITY_CODE, marketingModel.getActivityCode());
            marketingMapper.updateByConditionSelective(entity, example);
            operationLogService.insertLog(OperationLogModel.builder()
                    .tenantCode(marketingModel.getTenantCode()).activityCode(activityCode).createUser(SYSTEM).createLastName(SYSTEM)
                    .operationType(OperationTypeEnum.COMPLETION.code()).build(), SYSTEM_AUTO_COMPLETION);
            dataSyncComponent.sendPriceToPim(marketingModel.getTenantCode(), marketingModel.getActivityCode(), true);
        }
        return marketingModel;
    }
}
