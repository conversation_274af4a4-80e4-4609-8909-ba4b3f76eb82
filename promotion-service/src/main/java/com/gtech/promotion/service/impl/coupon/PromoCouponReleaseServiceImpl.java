/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.service.impl.coupon;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.code.coupon.*;
import com.gtech.promotion.dao.entity.coupon.TPromoCouponReleaseEntity;
import com.gtech.promotion.dao.mapper.coupon.TPromoCouponReleaseMapper;
import com.gtech.promotion.dao.model.coupon.CouponReleaseModel;
import com.gtech.promotion.dao.model.coupon.ReleaseCouponVO;
import com.gtech.promotion.domain.coupon.CouponReleaseDomain;
import com.gtech.promotion.domain.coupon.CouponReleaseInventoryDomain;
import com.gtech.promotion.page.RequestPage;
import com.gtech.promotion.service.coupon.PromoCouponReleaseService;
import org.apache.commons.lang3.StringUtils;
import org.apache.shardingsphere.api.hint.HintManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 投放
 */
@Service
public class PromoCouponReleaseServiceImpl implements PromoCouponReleaseService {

    @Autowired
    private TPromoCouponReleaseMapper promoCouponReleaseDao;

    @Override
    @Transactional
    public String createCouponRelease(CouponReleaseModel promoCouponReleaseVO) {

        TPromoCouponReleaseEntity releaseEntity = BeanCopyUtils.jsonCopyBean(promoCouponReleaseVO, TPromoCouponReleaseEntity.class);

        if (!CouponTypeEnum.PROMOTION_CODE.code().equals(releaseEntity.getCouponType()) && StringUtil.isEmpty(releaseEntity.getCouponRuleType())){
            releaseEntity.setCouponRuleType(CouponRuleTypeEnum.DIGIT.code());
        }
        promoCouponReleaseDao.insertSelective(releaseEntity);

        return releaseEntity.getId().toString();
    }

    @Override
    @Transactional
    public int updateCouponReleaseByReleaseCode(CouponReleaseDomain couponReleaseDomain) {
        
        Example example = new Example(TPromoCouponReleaseEntity.class);
        
        example.createCriteria()
            .andEqualTo(TPromoCouponReleaseEntity.C_TENANT_CODE, couponReleaseDomain.getTenantCode())
            .andEqualTo(TPromoCouponReleaseEntity.C_RELEASE_CODE, couponReleaseDomain.getReleaseCode());
        
        TPromoCouponReleaseEntity entity = BeanCopyUtils.jsonCopyBean(couponReleaseDomain, TPromoCouponReleaseEntity.class);

        entity.setUpdateTime(new Date());

        return promoCouponReleaseDao.updateByConditionSelective(entity, example);
    }

    @Override
    public CouponReleaseDomain findCouponReleaseByReleaseCode(String tenantCode, String releaseCode) {
        
        TPromoCouponReleaseEntity param = TPromoCouponReleaseEntity.builder().tenantCode(tenantCode).releaseCode(releaseCode).build();

        return BeanCopyUtils.jsonCopyBean(this.promoCouponReleaseDao.selectOne(param), CouponReleaseDomain.class);
    }

    @Override
    public PageInfo<CouponReleaseDomain> queryReleases(CouponReleaseDomain releaseDomain, RequestPage page) {

        if (null != page) {
            PageHelper.startPage(page.getPageNo(), page.getPageCount());
        }

        List<TPromoCouponReleaseEntity> select = promoCouponReleaseDao.select(BeanCopyUtils.jsonCopyBean(releaseDomain, TPromoCouponReleaseEntity.class));

        List<CouponReleaseDomain> convert = BeanCopyUtils.jsonCopyList(select, CouponReleaseDomain.class);
        PageInfo<TPromoCouponReleaseEntity> pageInfo2 = new PageInfo<>(select);
        PageInfo<CouponReleaseDomain> pageInfo = new PageInfo<>(convert);
        pageInfo.setList(convert);
        pageInfo.setTotal(pageInfo2.getTotal());
        return pageInfo;
    }

    @Override
    public List<CouponReleaseModel> queryCouponRelease(String activityCode) {

        TPromoCouponReleaseEntity entity = new TPromoCouponReleaseEntity();
        entity.setActivityCode(activityCode);
        return BeanCopyUtils.jsonCopyList(promoCouponReleaseDao.select(entity), CouponReleaseModel.class);
    }

    @Override
    public Long queryReleaseCount111(String activityCode) {

        Example example = new Example(TPromoCouponReleaseEntity.class);
        example.createCriteria()
            .andEqualTo(TPromoCouponReleaseEntity.C_ACTIVITY_CODE, activityCode)
            .andNotEqualTo(TPromoCouponReleaseEntity.C_RELEASE_STATUS, CouponReleaseStatusEnum.STOP_RELEASE.code());
        List<TPromoCouponReleaseEntity> entities = promoCouponReleaseDao.selectByCondition(example);
        long count = 0L;
        if (!CollectionUtils.isEmpty(entities)) {
            for (TPromoCouponReleaseEntity entity : entities) {
                if (ReleaseTypeEnum.APPOINTMENT.code().equals(entity.getReleaseType())
                                && (CouponReleaseEnum.STOP_RELEASE.code().equals(entity.getReleaseStatus()))) {
                    continue;
                }
                count += Long.parseLong(entity.getReleaseQuantity());
            }
        }
        return count;
    }

    @Override
    public List<CouponReleaseModel> queryReleasesNotReleased() {

        String nowTime = DateUtil.format(new Date(), DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        Example example = new Example(TPromoCouponReleaseEntity.class);
        example.createCriteria()
            .andEqualTo(TPromoCouponReleaseEntity.C_RELEASE_TYPE, ReleaseTypeEnum.APPOINTMENT.code())
            .andEqualTo(TPromoCouponReleaseEntity.C_RELEASE_STATUS, CouponReleaseStatusEnum.NOT_CREATE_COUPON.code())
            .andLessThanOrEqualTo(TPromoCouponReleaseEntity.C_RELEASE_TIME, nowTime);
        return BeanCopyUtils.jsonCopyList(promoCouponReleaseDao.selectByCondition(example), CouponReleaseModel.class);
    }

    @Override
    @Transactional
    public int stopCouponRelease(String activityCode) {

        TPromoCouponReleaseEntity entity = new TPromoCouponReleaseEntity();
        entity.setReleaseStatus(CouponReleaseEnum.STOP_RELEASE.code());

        Example example = new Example(TPromoCouponReleaseEntity.class);
        example.createCriteria()
            .andEqualTo(TPromoCouponReleaseEntity.C_ACTIVITY_CODE, activityCode)
            .andEqualTo(TPromoCouponReleaseEntity.C_RELEASE_STATUS, CouponReleaseStatusEnum.NOT_CREATE_COUPON.code());

        return promoCouponReleaseDao.updateByConditionSelective(entity, example);
    }

    @Override
    @Transactional
    public int deleteCouponRelease(String tenantCode, String activityCode) {

        TPromoCouponReleaseEntity entity = new TPromoCouponReleaseEntity();
        entity.setTenantCode(tenantCode);
        entity.setActivityCode(activityCode);

        return promoCouponReleaseDao.delete(entity);
    }

    /**
     * 根据活动id查询可领券的所有投放
     * 
     * @param releaseCode -- Coupon activity release code. (Optional)
     */
    @Override
    public List<CouponReleaseDomain> queryCanReceiveReleases(String tenantCode, String activityCode, String releaseCode) {

        HintManager.getInstance().setMasterRouteOnly();

        String nowTime = DateUtil.format(new Date(), DateUtil.FORMAT_YYYYMMDDHHMISS_14);

        Example example = new Example(TPromoCouponReleaseEntity.class);
        Criteria criteria = example.createCriteria()
            .andEqualTo(TPromoCouponReleaseEntity.C_TENANT_CODE, tenantCode)
            .andEqualTo(TPromoCouponReleaseEntity.C_ACTIVITY_CODE, activityCode)
            .andEqualTo(TPromoCouponReleaseEntity.C_RELEASE_STATUS, CouponReleaseStatusEnum.CREATE_COUPON.code())
            .andGreaterThan(TPromoCouponReleaseEntity.C_RECEIVE_END_TIME, nowTime)
            .andLessThanOrEqualTo(TPromoCouponReleaseEntity.C_RECEIVE_START_TIME, nowTime);

        if (StringUtils.isNotBlank(releaseCode)) {
            criteria.andEqualTo(TPromoCouponReleaseEntity.C_RELEASE_CODE, releaseCode);
        }
        example.orderBy(TPromoCouponReleaseEntity.C_ID);
        List<CouponReleaseDomain> couponReleaseDomains = BeanCopyUtils.jsonCopyList(promoCouponReleaseDao.selectByCondition(example), CouponReleaseDomain.class);
        HintManager.clear();
        return couponReleaseDomains;
    }

    @Override
    public List<CouponReleaseDomain> queryCanReceiveReleasesByTime(String tenantCode, String activityCode,  String receiveStartTime, String receiveEndTime) {
        HintManager.getInstance().setMasterRouteOnly();
        Example example = new Example(TPromoCouponReleaseEntity.class);
        Criteria criteria = example.createCriteria()
                .andEqualTo(TPromoCouponReleaseEntity.C_TENANT_CODE, tenantCode)
                .andEqualTo(TPromoCouponReleaseEntity.C_ACTIVITY_CODE, activityCode)
                .andEqualTo(TPromoCouponReleaseEntity.C_RELEASE_STATUS, CouponReleaseStatusEnum.CREATE_COUPON.code())
                .andEqualTo(TPromoCouponReleaseEntity.C_RECEIVE_END_TIME, receiveEndTime)
                .andEqualTo(TPromoCouponReleaseEntity.C_RECEIVE_START_TIME, receiveStartTime);
        example.orderBy(TPromoCouponReleaseEntity.C_ID);
        List<CouponReleaseDomain> couponReleaseDomains = BeanCopyUtils.jsonCopyList(promoCouponReleaseDao.selectByCondition(example), CouponReleaseDomain.class);
        HintManager.clear();
        return couponReleaseDomains;
    }


    @Override
	public List<CouponReleaseDomain> queryReleasesByActivityCodes(String tenantCode, List<String> activityCodes, Boolean inventoryFlag) {

        Example example = new Example(TPromoCouponReleaseEntity.class);

		Criteria criteria = example.createCriteria();
		criteria.andEqualTo(TPromoCouponReleaseEntity.C_TENANT_CODE, tenantCode)
            .andIn(TPromoCouponReleaseEntity.C_ACTIVITY_CODE, activityCodes);
		if (Boolean.TRUE.equals(inventoryFlag)) {
			criteria.andGreaterThan(TPromoCouponReleaseEntity.C_INVENTORY, 0);
		}

        return BeanCopyUtils.jsonCopyList(promoCouponReleaseDao.selectByCondition(example), CouponReleaseDomain.class);
    }

    @Override
    @Transactional
    public List<CouponReleaseInventoryDomain> deductInventory(String tenantCode, String activityCode, List<CouponReleaseDomain> canReleaseDomains, int inventory) {
        int row = 0;
        List<CouponReleaseInventoryDomain> releaseInventoryDomains = new ArrayList<>();
        List<CouponReleaseInventoryDomain> releaseInventoryDomains1 = BeanCopyUtils.jsonCopyList(canReleaseDomains, CouponReleaseInventoryDomain.class);
        for (CouponReleaseInventoryDomain canReleaseDomain : releaseInventoryDomains1) {
            if (inventory == 0){
                break;
            }
            row = promoCouponReleaseDao.deductInventory(tenantCode, activityCode, canReleaseDomain.getReleaseCode(), inventory);
            if (row == 0){
                CouponReleaseDomain couponReleaseByReleaseCode = findCouponReleaseByReleaseCode(tenantCode, canReleaseDomain.getReleaseCode());
                Integer inventory1 = couponReleaseByReleaseCode.getInventory();
                if (inventory1 > 0){
                    row = promoCouponReleaseDao.deductInventory(tenantCode, activityCode, canReleaseDomain.getReleaseCode(), inventory1);
                    if (row == 1){
                        canReleaseDomain.setSendInventory(inventory1);
                        releaseInventoryDomains.add(canReleaseDomain);
                        inventory -= inventory1;
                    }
                }
            }else {
                canReleaseDomain.setSendInventory(inventory);
                releaseInventoryDomains.add(canReleaseDomain);
                inventory = 0;
            }
        }
        if (inventory == 0){
            return releaseInventoryDomains;
        }
        return new ArrayList<>();
    }

    @Override
    public List<CouponReleaseModel> queryCouponReleaseActivityCode(String tenantCode, String startTime, String endTime, String activityCode) {

        Example example = new Example(TPromoCouponReleaseEntity.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo(TPromoCouponReleaseEntity.C_TENANT_CODE, tenantCode);
        if (StringUtil.isNotBlank(startTime)) {
            criteria.andGreaterThanOrEqualTo(TPromoCouponReleaseEntity.C_RELEASE_TIME, startTime)
                .andLessThanOrEqualTo(TPromoCouponReleaseEntity.C_RELEASE_TIME, endTime);
        }
        if (StringUtil.isNotBlank(activityCode)) {
            criteria.andEqualTo(TPromoCouponReleaseEntity.C_ACTIVITY_CODE, activityCode);
        }
        return BeanCopyUtils.jsonCopyList(promoCouponReleaseDao.selectByCondition(example), CouponReleaseModel.class);
    }

    @Override
    public CouponReleaseModel findCanReceiveRelease(String activityCode, String releaseCode) {

        String nowTime = DateUtil.format(new Date(), DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        Example example = new Example(TPromoCouponReleaseEntity.class);
        example.createCriteria()
            .andEqualTo(TPromoCouponReleaseEntity.C_ACTIVITY_CODE, activityCode)
            .andEqualTo(TPromoCouponReleaseEntity.C_RELEASE_CODE, releaseCode)
            .andEqualTo(TPromoCouponReleaseEntity.C_RELEASE_STATUS, CouponReleaseStatusEnum.CREATE_COUPON.code())
            .andGreaterThan(TPromoCouponReleaseEntity.C_RECEIVE_END_TIME, nowTime)
            .andLessThanOrEqualTo(TPromoCouponReleaseEntity.C_RECEIVE_START_TIME, nowTime);
        return BeanCopyUtils.jsonCopyBean(promoCouponReleaseDao.selectOneByExample(example), CouponReleaseModel.class);
    }

    @Override
    public CouponReleaseModel findCanUseRelease(String activityCode, String releaseCode) {

        Example example = new Example(TPromoCouponReleaseEntity.class);
        example.createCriteria()
            .andEqualTo(TPromoCouponReleaseEntity.C_ACTIVITY_CODE, activityCode)
            .andEqualTo(TPromoCouponReleaseEntity.C_RELEASE_CODE, releaseCode);
        return BeanCopyUtils.jsonCopyBean(promoCouponReleaseDao.selectOneByExample(example), CouponReleaseModel.class);
    }

    @Override
    public List<ReleaseCouponVO> queryReleaseCouponRecord(String tenantCode, String activityCode, String releaseCode,Integer sendTotal,Integer receiveType) {
        String dateTime = DateUtil.format(new Date(), DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        PageHelper.startPage(1,sendTotal);
        return promoCouponReleaseDao.queryReleaseCouponRecord(tenantCode, activityCode, releaseCode, dateTime,receiveType);
    }

    @Override
    public List<CouponReleaseDomain> queryReleaseByCondition(String tenantCode, String activityCode, List<String> releaseList) {

        Example example = new Example(TPromoCouponReleaseEntity.class);
        Criteria criteria = example.createCriteria();

        criteria.andEqualTo(TPromoCouponReleaseEntity.C_TENANT_CODE, tenantCode);

        if (StringUtil.isNotEmpty(activityCode)) {
            criteria .andEqualTo(TPromoCouponReleaseEntity.C_ACTIVITY_CODE, activityCode);
        }

        if (!CollectionUtils.isEmpty(releaseList)){
            criteria .andIn(TPromoCouponReleaseEntity.C_RELEASE_CODE,releaseList);
        }

        List<TPromoCouponReleaseEntity> releaseEntities = promoCouponReleaseDao.selectByCondition(example);

        return BeanCopyUtils.jsonCopyList(releaseEntities,CouponReleaseDomain.class);
    }

    @Override
    public List<String> queryActivityCodeByReceiveTime(String tenantCode, String receiveStartTime, String receiveEndTime) {

        if (StringUtils.isEmpty(receiveEndTime) && StringUtils.isEmpty(receiveStartTime)){
            return Collections.emptyList();
        }

        Example example = new Example(TPromoCouponReleaseEntity.class);
        Criteria criteria = example.createCriteria()
                .andEqualTo(TPromoCouponReleaseEntity.C_TENANT_CODE, tenantCode);

        if (StringUtils.isNotBlank(receiveStartTime)) {
            criteria.andLessThanOrEqualTo(TPromoCouponReleaseEntity.C_RECEIVE_START_TIME, receiveStartTime);
        }

        if (StringUtils.isNotBlank(receiveEndTime)) {
            criteria.andGreaterThanOrEqualTo(TPromoCouponReleaseEntity.C_RECEIVE_END_TIME,receiveEndTime);
        }

        List<TPromoCouponReleaseEntity> releaseEntities = promoCouponReleaseDao.selectByCondition(example);

        return releaseEntities.stream().map(TPromoCouponReleaseEntity::getActivityCode).distinct().collect(Collectors.toList());
    }

    @Override
    public int releaseSpecifiedQuantity(CouponReleaseDomain releaseSpecifiedQuantityDomain) {
        return promoCouponReleaseDao.releaseSpecifiedQuantity(releaseSpecifiedQuantityDomain.getTenantCode(), releaseSpecifiedQuantityDomain.getActivityCode(), releaseSpecifiedQuantityDomain.getReleaseCode(), releaseSpecifiedQuantityDomain.getReleaseQuantity());
    }

    @Override
    public List<CouponReleaseDomain> queryCouponReleaseByReleaseCodes(String tenantCode, List<String> releaseCodes) {
        Example example = new Example(TPromoCouponReleaseEntity.class);
        Criteria criteria = example.createCriteria();

        criteria.andEqualTo(TPromoCouponReleaseEntity.C_TENANT_CODE, tenantCode)
                .andIn(TPromoCouponReleaseEntity.C_RELEASE_CODE,releaseCodes);

        return BeanCopyUtils.jsonCopyList(promoCouponReleaseDao.selectByCondition(example),CouponReleaseDomain.class);
    }

}
