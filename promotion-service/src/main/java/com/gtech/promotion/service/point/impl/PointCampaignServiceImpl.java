package com.gtech.promotion.service.point.impl;

import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageInfo;
import com.gtech.commons.redis.GTechCodeGenerator;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.promotion.checker.SystemChecker;
import com.gtech.promotion.dao.entity.point.PointCampaignEntity;
import com.gtech.promotion.dao.mapper.point.PointCampaignMapper;
import com.gtech.promotion.exception.PromotionException;
import com.gtech.promotion.service.BaseService;
import com.gtech.promotion.service.point.PointCampaignService;
import com.gtech.promotion.vo.param.point.PointCampaignParam;
import com.gtech.promotion.vo.param.point.UpdatePointParam;
import com.gtech.promotion.vo.param.point.query.PointCampaignUniqueParam;
import com.gtech.promotion.vo.param.point.query.PointCampaignUniqueParam.PointCampaignStatusUniqueVo;
import com.gtech.promotion.vo.result.point.PointCampaignResult;

import lombok.extern.slf4j.Slf4j;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.util.StringUtil;
@Slf4j
@Service
public class PointCampaignServiceImpl extends BaseService implements PointCampaignService {

	private static final String SYS_CODE = "PointCampaignServiceImpl";

	private static final String CAMPAIGN_CODE = "campaignCode";


	@Autowired
	PointCampaignMapper pointCampaignMapper;

	@Autowired
	GTechCodeGenerator codeGenerator;

	@Override
    @Transactional
	public String savePointCampaign(PointCampaignParam pointCampaignVo) {



		// 1.检测
		String msg = checkPointCampaign(pointCampaignVo, false);

		if (StringUtils.isNotBlank(msg)) {
			throw new PromotionException(SYS_CODE + ".savePointCampaign.checkPointCampaign", msg);
		}
		PointCampaignEntity pointCampaign = BeanCopyUtils.jsonCopyBean(pointCampaignVo, PointCampaignEntity.class);
		// 2.默认值
		setPointCampaignDefault(pointCampaign);
		// 3.保存
		savePointCampaignModel(pointCampaign);

		return pointCampaign.getCampaignCode();
	}


	@Override
    @Transactional
	public void updatePointCampaignStatus(PointCampaignStatusUniqueVo pointCampaignStatusUniqueVo) {

		updateStatusPointCampaignModel(pointCampaignStatusUniqueVo.getTenantCode(), pointCampaignStatusUniqueVo.getCampaignCode(), pointCampaignStatusUniqueVo.getStatus(),
				pointCampaignStatusUniqueVo.getOldStatus());
	}

	@Override
    @Transactional
	public void updatePointCampaign(PointCampaignParam pointCampaignVo) {

		// 1.检测
		String msg = checkPointCampaign(pointCampaignVo, true);

		if (StringUtils.isNotBlank(msg)) {
			throw new PromotionException(SYS_CODE + ".updatePointCampaign.checkPointCampaign", msg);
		}
		// 2.获取MODEL
		PointCampaignEntity oldPointCampaign = getPointCampaignModelByCode(pointCampaignVo.getTenantCode(), pointCampaignVo.getCampaignCode());// NOSONAR
		if (null == oldPointCampaign) {
			throw new PromotionException(SystemChecker.NULL_VO.getCode(), SystemChecker.NULL_VO.getMessage());
		}
		try {
			BeanUtils.copyProperties(pointCampaignVo, oldPointCampaign);

		} catch (Exception e) {
			throw new PromotionException(SYS_CODE + ".updatePointCampaign.copy", "copy exception");
		}
		// 3.默认值
		setPointCampaignUpdataDefault(oldPointCampaign);
		// 4.保存
		updatePointCampaignModel(oldPointCampaign);
	}

	@Override
	public PointCampaignResult getPointCampaign(PointCampaignUniqueParam pointCampaignUniqueVo) {

		PointCampaignEntity pointCampaign = getPointCampaignModelByCode(pointCampaignUniqueVo.getTenantCode(), pointCampaignUniqueVo.getCampaignCode());
		if (pointCampaign == null) {
			return null;
		}
		return BeanCopyUtils.jsonCopyBean(pointCampaign, PointCampaignResult.class);
	}

	@Override
	public PageResult<PointCampaignResult> queryPointCampaignPage(Map<String, Object> map) {
		// 分页查询
		setPage(map);
		map.put("order", true);
		String nowTime = DateUtil.format(new Date(), DateUtil.FORMAT_YYYYMMDDHHMISS_14);
		map.put("nowTime",nowTime);
		List<PointCampaignEntity> pointCampaignList = queryPointCampaignModelPage(map);
		PageInfo<PointCampaignEntity> pageInfo = new PageInfo<>(pointCampaignList);
		List<PointCampaignResult> pointCampaignVoList = BeanCopyUtils.jsonCopyList(pageInfo.getList(), PointCampaignResult.class);


		return new PageResult<>(pointCampaignVoList, pageInfo.getTotal());
	}

	/**
	 * 获取积分池信息
	 * 
	 * @return PointCampaign
	 */
	private PointCampaignEntity getPointCampaignModelByCode(String tenantCode, String campaignCode) {
		try {
			PointCampaignEntity record = new PointCampaignEntity();
			record.setTenantCode(tenantCode);
			record.setCampaignCode(campaignCode);
			return pointCampaignMapper.selectOne(record);
		} catch (Exception e) {
			log.error(SYS_CODE + ".getPointCampaignModelByCode", e);
		}
		return null;
	}

	/**
	 * 检测积分池参数
	 * 
	 * @param pointCampaignVo
	 * @return
	 */
	private String checkPointCampaign(PointCampaignParam pointCampaignVo, boolean flag) {

		String msg = "";
		if (null == pointCampaignVo) {
			return "parameter is null";
		}
		if (flag && StringUtil.isEmpty(pointCampaignVo.getCampaignCode())) {
			msg += "【campaignCode】";
		}
		if (StringUtil.isNotEmpty(msg)) {
			msg += " can not be empty .";
		}
		return msg;
	}

	/**
	 * 设置积分池新增默认值
	 * 
	 * @param pointCampaign
	 */
	private void setPointCampaignDefault(PointCampaignEntity pointCampaign) {

		if (null == pointCampaign)
			return;
		if (null == pointCampaign.getStatus())
			pointCampaign.setStatus(0);
		if (null == pointCampaign.getRemainingPoints()) {
			pointCampaign.setRemainingPoints(pointCampaign.getTotalPoints());
		}
		if (StringUtils.isBlank(pointCampaign.getCampaignCode())) {
			pointCampaign.setCampaignCode(codeGenerator.generateCode(pointCampaign.getTenantCode(), CAMPAIGN_CODE, "PC[D:yyyyMMddHHmmss][SM:%06d]", 1l));
		}
	}

	/**
	 * 设置积分池修改默认值
	 * 
	 * @param pointCampaign
	 */
	private void setPointCampaignUpdataDefault(PointCampaignEntity pointCampaign) {
		setPointCampaignDefault(pointCampaign);
		if (null == pointCampaign)
			return;
		pointCampaign.setUpdateTime(new Date());
	}

	/**
	 * 保存积分池对象
	 * 
	 * @param pointCampaign
	 */
	private void savePointCampaignModel(PointCampaignEntity pointCampaign) {
		if (null == pointCampaign)
			return;
		try {
			pointCampaignMapper.insert(pointCampaign);
		} catch (DuplicateKeyException e) {
			throw new PromotionException(SystemChecker.DUPLICATE_KEY.getCode(), SystemChecker.DUPLICATE_KEY.getMessage());
		} catch (Exception e) {
			throw new PromotionException(SYS_CODE + ".savePointCampaignModel.ex", e.getMessage());
		}

	}

	/**
	 * 更新积分池对象
	 * 
	 * @param pointCampaign
	 * @throws PimException
	 */
	private void updatePointCampaignModel(PointCampaignEntity pointCampaign) {
		if (null == pointCampaign)
			return;
		try {
			pointCampaignMapper.updateByPrimaryKeySelective(pointCampaign);
		} catch (DuplicateKeyException e) {
			throw new PromotionException(SystemChecker.DUPLICATE_KEY.getCode(), SystemChecker.DUPLICATE_KEY.getMessage());
		} catch (Exception e) {
			throw new PromotionException(SYS_CODE + ".updatePointCampaignModel.ex", e.getMessage());
		}
	}

	/**
	 * 更新积分池状态
	 *
	 * @param status
	 * @param oldStatus
	 */
	private void updateStatusPointCampaignModel(String tenantCode, String campaignCode, Integer status, Integer oldStatus) {

		if (StringUtils.isEmpty(campaignCode) || StringUtils.isEmpty(tenantCode))
			return;
		Map<String, Object> param = new HashMap<>();
		param.put("tenantCode", tenantCode);
		param.put(CAMPAIGN_CODE, campaignCode);
		param.put("status", oldStatus);
		int i = 0;
		try {
			PointCampaignEntity record = new PointCampaignEntity();
			record.setStatus(status);
			Example example = new Example(PointCampaignEntity.class);
			example.createCriteria().andAllEqualTo(param);
			i = pointCampaignMapper.updateByExampleSelective(record, example);
		} catch (Exception e) {
			throw new PromotionException(SYS_CODE + ".updateStatusPointCampaignModel.ex", e.getMessage());
		}
		if (i <= 0) {
			throw new PromotionException(SystemChecker.NULL_UPDATE.getCode(), SystemChecker.NULL_UPDATE.getMessage());
		}
	}

	/**
	 * 分页查询积分池
	 * 
	 * @param parammap
	 * @return PointCampaign
	 */
	private List<PointCampaignEntity> queryPointCampaignModelPage(Map<String, Object> parammap) {
		List<PointCampaignEntity> list = null;
		try {
			list = pointCampaignMapper.query(parammap);
		} catch (Exception e) {
			log.error(SYS_CODE + ".queryPointCampaignModel", e);
		}
		return list;
	}

	@SuppressWarnings("unchecked")
	@Override
	public int updatePoint(UpdatePointParam pointChangeVo) {

		PointCampaignEntity entity = new PointCampaignEntity();
		entity.setTenantCode(pointChangeVo.getTenantCode());
		entity.setCampaignCode(pointChangeVo.getCampaignCode());
		PointCampaignEntity pointCampaignEntity = pointCampaignMapper.selectOne(entity);

		if (pointCampaignEntity.getStatus() == 0){
			return 0;
		}
		//积分计划过期
		String nowTime = DateUtil.format(new Date(), DateUtil.FORMAT_YYYYMMDDHHMISS_14);
		if (pointCampaignEntity.getEndTime().compareTo(nowTime)<0){

			return 0;
		}

		if (pointCampaignEntity.getTotalPoints() < 0){
			return 1;
		}

		//当积分池被禁用，所有对该积分池的操作都不可以进行
		if (pointChangeVo.getTransactionType() != 1 || StringUtil.isEmpty(pointChangeVo.getCampaignCode())) {
			return 1;
		}
		Map<String, Object> param = BeanCopyUtils.jsonCopyBean(pointChangeVo, Map.class);
		try {
			return pointCampaignMapper.updatePoint(param);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		return 0;
	}

	@Override
	public Result<Serializable> queryPointCampaignEndTime() {

		Example example = new Example(PointCampaignEntity.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andLessThan("endTime",DateUtil.format(new Date(),DateUtil.FORMAT_YYYYMMDDHHMISS_14));
		criteria.andEqualTo("status",1);
		List<PointCampaignEntity> pointCampaignEntities = pointCampaignMapper.selectByExample(example);

		//如果过期，修改状态
		for (PointCampaignEntity entity:pointCampaignEntities){
			entity.setStatus(0);
			pointCampaignMapper.updateByPrimaryKeySelective(entity);
		}


		return Result.ok();
	}

}
