/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.service.mongo.activity;

import com.gtech.promotion.dao.model.activity.TPromoActivityProductVO;
import com.gtech.promotion.vo.bean.ProductScope;

import java.util.List;

/**
 * <功能描述>
 * 
 */
public interface TPromoProductService {

    /**
     * 插入商品数据
     */
    Integer insertProducts(List<ProductScope> products, String activityCode, String tenantCode);

    /**
     * Update activityId to activityCode
     */
    void updateActivityId2ActivityCode(String tenantCode, String activityId, String activityCode);

    /**
     * 删除商品数据
     */
    Integer deleteProducts(String activityCode);

    /**
     * 删除商品数据
     */
    Integer deleteProductBySeqNum111(String activityCode, Integer seqNum);

    /**
     * 根据活动id查询活动商品信息
     */
    List<ProductScope> getProducts(String activityCode);

    /**
     * 根据活动id查询活动商品信息
     */
    List<TPromoActivityProductVO> getPromoProduct(String activityCode);

    List<TPromoActivityProductVO> getPromoProductByActivityCodes(String tenantCode, List<String> activityCodes);
}
