package com.gtech.promotion.service.purchaseconstraint.impl;

import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.checker.purchaseconstraint.PurchaseConstraintChecker;
import com.gtech.promotion.dao.entity.purchaseconstraint.PurchaseConstraintRuleEntity;
import com.gtech.promotion.dao.mapper.purchaseconstraint.PurchaseConstraintRuleMapper;
import com.gtech.promotion.dao.model.purchaseconstraint.PurchaseConstraintRuleModel;
import com.gtech.promotion.exception.Check;
import com.gtech.promotion.service.purchaseconstraint.PurchaseConstraintRuleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.weekend.WeekendSqls;

import javax.annotation.Resource;
import java.util.List;

@Service
@Slf4j
public class PurchaseConstraintRuleServiceImpl implements PurchaseConstraintRuleService {
    @Resource
    private PurchaseConstraintRuleMapper purchaseConstraintRuleMapper;

    @Override
    public int insertList(List<PurchaseConstraintRuleModel> purchaseConstraintRuleModelList){
        List<PurchaseConstraintRuleEntity> purchaseConstraintRuleEntityList = BeanCopyUtils
                                    .jsonCopyList(purchaseConstraintRuleModelList, PurchaseConstraintRuleEntity.class);
        return purchaseConstraintRuleMapper.insertList(purchaseConstraintRuleEntityList);
    }

    @Override
    public int deletePurchaseConstraintRule(String tenantCode, String purchaseConstraintCode) {
        Check.check(StringUtil.isBlank(tenantCode), PurchaseConstraintChecker.NOT_NULL_TENANT_CODE);
        Check.check(StringUtil.isBlank(purchaseConstraintCode), PurchaseConstraintChecker.NOT_NULL_PURCHASE_CONSTRAINT_CODE);

        Example example = new Example.Builder(PurchaseConstraintRuleEntity.class)
                .where(WeekendSqls.<PurchaseConstraintRuleEntity>custom()
                        .andEqualTo(PurchaseConstraintRuleEntity::getTenantCode, tenantCode)
                        .andEqualTo(PurchaseConstraintRuleEntity::getPurchaseConstraintCode, purchaseConstraintCode)
                ).build();
        return purchaseConstraintRuleMapper.deleteByCondition(example);
    }

    @Override
    public List<PurchaseConstraintRuleModel> queryPurchaseConstraintRuleByCode(String tenantCode, String purchaseConstraintCode) {
        Check.check(StringUtil.isBlank(tenantCode), PurchaseConstraintChecker.NOT_NULL_TENANT_CODE);
        Check.check(StringUtil.isBlank(purchaseConstraintCode), PurchaseConstraintChecker.NOT_NULL_PURCHASE_CONSTRAINT_CODE);
        PurchaseConstraintRuleEntity purchaseConstraintRuleQuery = new PurchaseConstraintRuleEntity();
        purchaseConstraintRuleQuery.setTenantCode(tenantCode);
        purchaseConstraintRuleQuery.setPurchaseConstraintCode(purchaseConstraintCode);
        List<PurchaseConstraintRuleEntity> purchaseConstraintRuleList = purchaseConstraintRuleMapper.select(purchaseConstraintRuleQuery);
        return BeanCopyUtils
                .jsonCopyList(purchaseConstraintRuleList, PurchaseConstraintRuleModel.class);
    }
}
