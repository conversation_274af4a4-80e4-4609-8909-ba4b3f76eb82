/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.service.impl.activity;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.promotion.dao.entity.activity.TPromoActivityStoreEntity;
import com.gtech.promotion.dao.mapper.activity.TPromoActivityStoreMapper;
import com.gtech.promotion.dao.model.activity.TPromoActivityStoreVO;
import com.gtech.promotion.page.RequestPage;
import com.gtech.promotion.service.activity.ActivityStoreService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

/**
 * 店铺服务类实现
 */
@Service
public class TPromoActivityStoreServiceImpl implements ActivityStoreService {

    @Autowired
    private TPromoActivityStoreMapper tPromoRuleStoreMapper;

    @Override
    @Transactional
    public void createStore(TPromoActivityStoreVO storeVO) {

        tPromoRuleStoreMapper.insertSelective(BeanCopyUtils.jsonCopyBean(storeVO, TPromoActivityStoreEntity.class));
    }

    @Override
    public List<TPromoActivityStoreVO> getStoresByActivityCode(String activityCode) {

        Example example = new Example(TPromoActivityStoreEntity.class);
        example.createCriteria().andEqualTo("activityCode", activityCode);
        example.orderBy("id");
        List<TPromoActivityStoreEntity> select = tPromoRuleStoreMapper.selectByCondition(example);
        return BeanCopyUtils.jsonCopyList(select, TPromoActivityStoreVO.class);
    }

    @Override
    @Transactional
    public void deleteByActivityCode(String activityCode) {

        TPromoActivityStoreEntity entity = new TPromoActivityStoreEntity();
        entity.setActivityCode(activityCode);
        tPromoRuleStoreMapper.delete(entity);
    }

    @Override
    public List<String> selectNotByCondition(String tenantCode, String orgCode) {

        return tPromoRuleStoreMapper.selectNotByCondition(tenantCode, orgCode);
    }

    @Override
    public List<TPromoActivityStoreVO> getStoresByActivityCodes(String tenantCode, List<String> activityCodes) {
        Example example = new Example(TPromoActivityStoreEntity.class);
        example.createCriteria().andEqualTo(TPromoActivityStoreEntity.C_TENANT_CODE, tenantCode)
                .andIn(TPromoActivityStoreEntity.C_ACTIVITY_CODE, activityCodes);
        example.orderBy("id");
        List<TPromoActivityStoreEntity> select = tPromoRuleStoreMapper.selectByCondition(example);
        return BeanCopyUtils.jsonCopyList(select, TPromoActivityStoreVO.class);
    }

    @Override
    public PageInfo<TPromoActivityStoreVO> selectByOrgCode(String tenantCode, String orgCode, RequestPage page) {
        PageHelper.startPage(page.getPageNo(), page.getPageCount());
        Example example = new Example(TPromoActivityStoreEntity.class);
        example.createCriteria().andEqualTo(TPromoActivityStoreEntity.C_TENANT_CODE, tenantCode)
                .andEqualTo(TPromoActivityStoreEntity.C_ORG_CODE, orgCode);
        example.orderBy("id").desc();
        PageInfo<TPromoActivityStoreEntity> pageInfo = new PageInfo<>(tPromoRuleStoreMapper.selectByCondition(example));
        PageInfo<TPromoActivityStoreVO> pageInfo2 = new PageInfo<>();
        pageInfo2.setList(BeanCopyUtils.jsonCopyList(pageInfo.getList(), TPromoActivityStoreVO.class));
        pageInfo2.setTotal(pageInfo.getTotal());
        return pageInfo2;
    }

}
