package com.gtech.promotion.service.flashsale.impl;

import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.promotion.dao.entity.marketing.BaseEntity;
import com.gtech.promotion.dao.entity.marketing.flashsale.FlashSaleStoreEntity;
import com.gtech.promotion.dao.mapper.marketing.flashsale.FlashSaleStoreMapper;
import com.gtech.promotion.dao.model.marketing.flashsale.FlashSaleStoreModel;
import com.gtech.promotion.service.flashsale.FlashSaleStoreService;
import com.gtech.promotion.service.marketing.impl.BaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

@Service
public class FlashSaleStoreServiceImpl extends BaseServiceImpl<FlashSaleStoreEntity, FlashSaleStoreModel> implements FlashSaleStoreService {

    public FlashSaleStoreServiceImpl() {
        super(FlashSaleStoreEntity.class, FlashSaleStoreModel.class);
    }

    @Autowired
    private FlashSaleStoreMapper storeMapper;

    @Override
    public List<FlashSaleStoreModel> getStoresByActivityCodes(String tenantCode, List<String> activityCodes) {
        Example example = new Example(FlashSaleStoreEntity.class);
        example.createCriteria().andEqualTo(BaseEntity.C_TENANT_CODE, tenantCode)
                .andIn(BaseEntity.C_ACTIVITY_CODE, activityCodes);
        return BeanCopyUtils.jsonCopyList(storeMapper.selectByCondition(example), FlashSaleStoreModel.class);
    }
}
