package com.gtech.promotion.service.flashsale.impl;

import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.promotion.dao.entity.marketing.BaseEntity;
import com.gtech.promotion.dao.entity.marketing.flashsale.FlashSaleOrderEntity;
import com.gtech.promotion.dao.mapper.marketing.flashsale.FlashSaleOrderMapper;
import com.gtech.promotion.dao.model.marketing.flashsale.FlashSaleOrderModel;
import com.gtech.promotion.service.flashsale.FlashSaleOrderService;
import com.gtech.promotion.service.marketing.impl.BaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

@Service
public class FlashSaleOrderServiceImpl extends BaseServiceImpl<FlashSaleOrderEntity, FlashSaleOrderModel> implements FlashSaleOrderService {

    public FlashSaleOrderServiceImpl() {
        super(FlashSaleOrderEntity.class, FlashSaleOrderModel.class);
    }

    @Autowired
    private FlashSaleOrderMapper orderMapper;

    @Override
    public FlashSaleOrderModel findByOrderNo(String tenantCode, String orderNo) {
        FlashSaleOrderEntity entity = new FlashSaleOrderEntity();
        entity.setOrderId(orderNo);
        entity.setTenantCode(tenantCode);
        return BeanCopyUtils.jsonCopyBean(orderMapper.selectOne(entity), FlashSaleOrderModel.class);
    }

    @Override
    @Transactional
    public int updateStatus(String tenantCode, String orderNo, String orderStatus) {
        FlashSaleOrderEntity entity = new FlashSaleOrderEntity();
        entity.setOrderStatus(orderStatus);

        Example example = new Example(FlashSaleOrderEntity.class);
        example.createCriteria().andEqualTo(BaseEntity.C_TENANT_CODE, tenantCode)
                    .andEqualTo(FlashSaleOrderEntity.ORDER_ID, orderNo);
        return orderMapper.updateByConditionSelective(entity, example);
    }
}
