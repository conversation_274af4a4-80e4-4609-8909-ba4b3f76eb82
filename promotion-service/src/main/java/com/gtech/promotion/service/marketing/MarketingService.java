package com.gtech.promotion.service.marketing;

import com.gtech.commons.page.PageData;
import com.gtech.promotion.dao.entity.marketing.MarketingEntity;
import com.gtech.promotion.dao.model.marketing.MarketingModel;
import com.gtech.promotion.dto.in.marketing.MarketingQueryInDto;
import com.gtech.promotion.vo.param.marketing.flashsale.FlashSaleQueryListParam;

import java.util.List;

public interface MarketingService extends BaseService<MarketingEntity, MarketingModel> {

    PageData<MarketingModel> queryMarketingList(MarketingQueryInDto inDto);

    int updateByActivityCode(MarketingModel marketingModel);

    List<MarketingModel> queryMarketingList(String tenantCode, String activityType, String activityStatus);

    List<MarketingModel> queryCurrentEffectiveMarketingList(String tenantCode, String activityType);

    PageData<MarketingModel> queryMarketingFlashSaleList(FlashSaleQueryListParam param);

    int expireMarketing();

    List<MarketingModel> queryShouldExpireMarketingList();

    List<MarketingModel> queryAllTenantEffectiveFlashSaleList(String minId, int pageSize);

    List<MarketingModel> getMarketingByActivityCode(String tenantCode,List<String> activityCode,String status);

    List<MarketingModel> getMarketingByActivityCodeList(String tenantCode,List<String> activityCode);
}
