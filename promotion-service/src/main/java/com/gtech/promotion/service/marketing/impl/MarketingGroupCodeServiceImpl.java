package com.gtech.promotion.service.marketing.impl;

import com.github.pagehelper.PageHelper;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.dao.entity.marketing.BaseEntity;
import com.gtech.promotion.dao.entity.marketing.MarketingGroupCodeEntity;
import com.gtech.promotion.dao.mapper.marketing.MarketingGroupCodeMapper;
import com.gtech.promotion.dao.model.marketing.MarketingGroupCodeMode;
import com.gtech.promotion.service.marketing.MarketingGroupCodeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/10/17 17:08
 */
@Service
public class MarketingGroupCodeServiceImpl extends BaseServiceImpl<MarketingGroupCodeEntity, MarketingGroupCodeMode> implements MarketingGroupCodeService {


    @Autowired
    private MarketingGroupCodeMapper marketingGroupCodeMapper;

    public MarketingGroupCodeServiceImpl() {
        super(MarketingGroupCodeEntity.class, MarketingGroupCodeMode.class);
    }

    @Override
    public int deductGroupInventory(String tenantCode, String activityCode, String marketingGroupCode) {

        return marketingGroupCodeMapper.deductGroupInventory(tenantCode,activityCode,marketingGroupCode);
    }

    @Override
    public int addGroupInventory(String tenantCode, String activityCode, String marketingGroupCode) {

        return marketingGroupCodeMapper.addGroupInventory(tenantCode,activityCode,marketingGroupCode);
    }

    @Override
    public MarketingGroupCodeMode queryGroupByMarketingGroupCode(String tenantCode, String activityCode, String marketingGroupCode) {

        MarketingGroupCodeEntity entity = new MarketingGroupCodeEntity();
        entity.setTenantCode(tenantCode);
        entity.setActivityCode(activityCode);
        entity.setMarketingGroupCode(marketingGroupCode);

        return BeanCopyUtils.jsonCopyBean(marketingGroupCodeMapper.selectOne(entity),MarketingGroupCodeMode.class);
    }

    @Override
    @Transactional
    public int updateMarketingCodeGroupStatus(String tenantCode, String activityCode, String marketingGroupCode, String groupStatus) {
        Example example = new Example(MarketingGroupCodeEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(BaseEntity.C_TENANT_CODE, tenantCode)
                .andEqualTo(MarketingGroupCodeEntity.C_ACTIVITY_CODE, activityCode)
                .andEqualTo(MarketingGroupCodeEntity.C_MARKETING_GROUP_CODE, marketingGroupCode);

        MarketingGroupCodeEntity entity = new MarketingGroupCodeEntity();
        entity.setGroupStatus(groupStatus);

        return marketingGroupCodeMapper.updateByConditionSelective(entity,example);
    }

    /**
     * 查询所有未开始以及进行中的拼团
     * @param tenantCode
     * @param activityCode
     * @return
     */
    @Override
    public List<MarketingGroupCodeMode> queryGroupByActivityCode(String tenantCode, String activityCode,List<String> groupStatusList,String maxGroupCode) {

        Example example = new Example(MarketingGroupCodeEntity.class);
        example.orderBy(MarketingGroupCodeEntity.C_MARKETING_GROUP_CODE).desc();
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(BaseEntity.C_TENANT_CODE, tenantCode)
                .andEqualTo(MarketingGroupCodeEntity.C_ACTIVITY_CODE, activityCode)
                .andIn(MarketingGroupCodeEntity.C_MARKETING_GROUP_STATUS, groupStatusList);

        if (StringUtil.isNotEmpty(maxGroupCode)){
            criteria.andGreaterThan(MarketingGroupCodeEntity.C_MARKETING_GROUP_CODE,maxGroupCode);
        }

        PageHelper.startPage(1,1000,false);
        List<MarketingGroupCodeEntity> marketingGroupCodeEntities = marketingGroupCodeMapper.selectByCondition(example);

        List<MarketingGroupCodeMode> list = new ArrayList<>();

        for (MarketingGroupCodeEntity marketingGroupCodeEntity : marketingGroupCodeEntities) {
            MarketingGroupCodeMode marketingGroupCodeMode = BeanCopyUtils.jsonCopyBean(marketingGroupCodeEntity, MarketingGroupCodeMode.class);
            marketingGroupCodeMode.setDate(DateUtil.format(marketingGroupCodeEntity.getCreateTime(),DateUtil.FORMAT_YYYYMMDD));

            marketingGroupCodeMode.setDate(DateUtil.format(marketingGroupCodeEntity.getUpdateTime(),DateUtil.FORMAT_YYYYMMDD));

            list.add(marketingGroupCodeMode);

        }
        return list;
    }


}
