/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.service.activity;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.gtech.promotion.code.activity.ActivityStatusEnum;
import com.gtech.promotion.code.activity.ActivityTypeEnum;
import com.gtech.promotion.dao.entity.activity.ActivityEntity;
import com.gtech.promotion.dao.model.activity.ActivityModel;
import com.gtech.promotion.dto.in.activity.GroupBindingActivityVO;
import com.gtech.promotion.dto.in.activity.GroupQueryListVO;
import com.gtech.promotion.dto.in.activity.TPromoActivityListInDTO;
import com.gtech.promotion.dto.in.activity.UpdateExternalActivityInDTO;
import com.gtech.promotion.dto.out.activity.GroupActivityVO;
import com.gtech.promotion.dto.out.activity.TPromoActivityOutDTO;
import com.gtech.promotion.page.RequestPage;
import com.gtech.promotion.vo.param.activity.QueryListParam;
import com.gtech.promotion.vo.param.activity.QueryPromoListByStoreParam;
import com.gtech.promotion.vo.result.activity.QueryListResult;

/**
 * 创建活动业务代码
 * 
 */
public interface ActivityService{

    /**
     * 创建一个活动
     * 
     * @param tPromoActivity 活动创建实体
     * @return ActivityCode
     */
    String createPromoActivity(ActivityModel tPromoActivity);

    /**
     * 查询促销活动
     * 
     * @param activityStatus -- Activity status. (Optional)
     */
    ActivityModel findActivity(String tenantCode, String activityCode, String activityStatus);

    /**
     * 查询Effective状态的促销活动(Readonly)
     */
    ActivityModel findEffectiveActivity(String tenantCode, String activityCode);

    /**
     * 查询Effective状态的促销活动(Readonly)
     */
    List<ActivityModel> queryEffectiveActivity(String tenantCode, List<String> activityCodes);

    /**
     * 根据活动Code列表 批量查询活动(含已经过期的活动)
     */
    List<ActivityModel> queryActivityByActivityCodes(String tenantCode, List<String> activityCodes);

    /**
     * 根据输入参数查询促销活动列表
     * 
     * @param inDTO 输入参数
     * @param page 分页参数
     * @param page 模板id List
     */
    PageInfo<ActivityModel> queryAllActivity(TPromoActivityListInDTO inDTO,RequestPage page,List<String> templateCodes);


    /***
     * 用于分组活动列表
     * @param vo
     * @param page
     * @return
     */
    PageInfo<GroupActivityVO> queryActivityListUnderGroup(GroupQueryListVO vo, RequestPage page);

    /**
     * 更新一个活动
     * 
     * @param promoActivityVO
     */
    int updatePromoActivity(ActivityModel promoActivityVO);

    /**
     * Update activity status by activityCode.
     */
    int updateActivityStatus(String tenantCode, String activityCode, String activityStatus,String operator);

    /**
     * 根据活动编码查询活动
     */
    ActivityModel findActivityByActivityCode(String tenantCode,String activityCode);

    /**
     * 结束到期的活动
     * 
     * @return 更新的活动数
     */
    int endPromoActivity();

    /**
     * 根据商户编码获得活动信息
     * 
     * @param tenantCode 商户编码
     * @param activityType 活动类型
     * @param status 1：已创建的活动，用户购物车沙盒测试 2：已审核的活动，用于生产环境
     * @param page 分页
     */
    List<ActivityModel> queryActivityByTenantCode(String tenantCode,ActivityTypeEnum activityType,RequestPage page,ActivityStatusEnum...status);

    /**
     * 根据商户编码获取已生效活动信息
     * @param tenantCode 商户编码
     * @return
     */
    List<ActivityModel> queryEffectiveActivityByTenantCode(String tenantCode);
    /**
     * 删除一条活动记录
     */
    int deleteActivity111(String tenantCode, String activityCode);

    /**
     * 根据租铺号和ids查询活动信息
     */
    List<ActivityEntity> queryActivityByCodes(String tenantCode, List<String> codes);

    List<ActivityEntity> queryActivityByTenantCodeAndStatusAndType(String tenantCode,Integer activityStatus,ActivityTypeEnum activityType);

    /**
     * 查询生效的活动且包含券活动
     */
    long effectActivity();

    /**
     * 累计活动创建总数且包含券活动(时间段)
     */
    long accumulativeTotal(String startTime,String endTime,List<String> tenantCodes);

    /**
     * 累计活动创建总数且包含券活动
     */
    long accumulativeTotal(List<String> tenantCodes);

    /**
     * 查询该租户下生效的活动且包含券活动 总数
     */
    long queryEffectActivityByTenantCode(String tenantCode);

    /**
     * 根据租户号和状态查询当前正在进行中的活动
     *
     * @param tenantCode
     * @param activityStatus 活动状态 04-生效中
     * @param activityType 01-活动；02-券
     */
    List<ActivityModel> queryActivityByTenantCodeAndStatusAndTime(String tenantCode,String activityStatus,String activityType,String orgCode);

    PageInfo<TPromoActivityOutDTO> queryPromoListByStore(QueryPromoListByStoreParam param);

    PageInfo<QueryListResult> queryList(QueryListParam param);

    int expireActivity();

    /**
     * 查询分类编码
     * @param tenantCode
     * @param promotionCategory
     * @return
     */
    int queryPromotionCategoryCount(String tenantCode,String promotionCategory);

    /**
     * 根据分类编码
     * @param tenantCode
     * @param promotionCategory
     * @return
     */
    int updatePromotionCategoryNull(String tenantCode,String promotionCategory);

    /**
     * 查询最新优惠指令生效的活动
     * @param tenantCode
     * @param activityCodes
     * @return
     */
    String findActivityNewByActivityCode(String tenantCode,List<String> activityCodes);


    /**
     * 根据分组编码查询活动
     * @param tenantCode
     * @param groupCode
     * @return
     */
    List<ActivityModel> queryActivityByGroupCode(String tenantCode, String groupCode);

    /**
     * 将活动绑定到分组
     * @param vo
     * @return
     */
    int bindingActivityToGroup(GroupBindingActivityVO vo);


    /**
     * 更新一个外部关联的活动id
     *
     * @param dto
     */
    int updateExternalActivityId(UpdateExternalActivityInDTO dto);

	/**
	 * 查询有效券活动且有限制使用(仅补偿查询使用)
	 * 
	 * @return
	 */
	List<ActivityEntity> queryActivityLimitTenant();
}
