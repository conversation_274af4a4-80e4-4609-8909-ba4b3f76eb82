/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.service.impl.activity;

import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.promotion.dao.entity.activity.TemplateEntity;
import com.gtech.promotion.dao.mapper.activity.TemplateMapper;
import com.gtech.promotion.dao.model.activity.TemplateModel;
import com.gtech.promotion.service.activity.TemplateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class TemplateServiceImpl implements TemplateService {

    @Autowired
    private TemplateMapper templateMapper;

    /**
     * Query all template model info.
     */
    @Override
    public List<TemplateModel> queryTemplateAll() {

        Example condition = new Example(TemplateEntity.class, true, true);

        return BeanCopyUtils.jsonCopyList(templateMapper.selectByCondition(condition), TemplateModel.class);
    }

    @Override
    public TemplateModel getTemplateByCode(String templateCode){

        TemplateEntity entity = new TemplateEntity();
        entity.setTemplateCode(templateCode);

        return BeanCopyUtils.jsonCopyBean(templateMapper.selectOne(entity), TemplateModel.class);
    }

    @Override
    public TemplateModel getTemplateById(String templateId){

        return BeanCopyUtils.jsonCopyBean(templateMapper.selectByPrimaryKey(templateId), TemplateModel.class);
    }

    @Override
    public Map<String, String> findTemplateTagAll() {
        Map<String, String> result = new HashMap<>();
        List<TemplateEntity> list = templateMapper.selectAll();
        Map<String, List<TemplateEntity>> collect = list.stream().collect(Collectors.groupingBy(TemplateEntity::getTagCode));
        collect.forEach((x,y)->{
            StringBuilder sb = new StringBuilder();
            y.stream().forEach(z-> sb.append(z.getTemplateCode()).append(","));
            result.put(x,sb.substring(0, sb.length() - 1));
        });
        return result;
    }

}
