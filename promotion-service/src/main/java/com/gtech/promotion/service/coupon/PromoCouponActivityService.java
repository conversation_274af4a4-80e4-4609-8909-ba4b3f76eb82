/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.service.coupon;

import com.github.pagehelper.PageInfo;
import com.gtech.promotion.dao.model.activity.ActivityModel;
import com.gtech.promotion.dto.in.coupon.CouponActivityListInDTO;
import com.gtech.promotion.dto.out.coupon.CouponActivityListOutDTO;

/**
 * 促销券活动Service接口
 */
public interface PromoCouponActivityService {

    /**
     * 创建券活动
     */
    String createCouponActivity(ActivityModel couponActivityVO);

    /**
     * 更新一条促销券活动信息
     */
    int updateCouponActivityByActivityCode(ActivityModel couponActivityVO);

    /**
     * 查询优惠券活动列表（用于优惠券配置列表页查询优惠券活动及规则）
     */
    PageInfo<CouponActivityListOutDTO> queryCouponActivityList(CouponActivityListInDTO couponActivityListInDTO);

    /**
     * 根据促销活动ID查询券码活动
     */
    ActivityModel findCouponActivity(String tenantCode, String activityCode);

    /**
     * 查询Effective状态的促销活动(Readonly)
     */
    ActivityModel findEffectiveActivity(String tenantCode, String activityCode);

    /**
     * 删除券活动
     */
    int deleteCouponActivity(String tenantCode, String activityCode);

    int reserveCouponQuota(String tenantCode, String activityCode, int sum);

    int returnCouponQuota(String tenantCode, String activityCode, int sum);
}
