package com.gtech.promotion.service.point;

import com.gtech.commons.page.PageData;
import com.gtech.promotion.dao.entity.point.PointTransactionEntity;
import com.gtech.promotion.dto.in.point.PointTransactionDto;
import com.gtech.promotion.vo.param.point.PointTransactionParam;
import com.gtech.promotion.vo.param.point.query.GetPointTransactionParam;
import com.gtech.promotion.vo.result.point.PointAccountCampaignResult;
import com.gtech.promotion.vo.result.point.PointTransactionResult;

import java.util.List;

public interface PointTransactionService {

	/**
	 * 新增积分流水
	 * 
	 * @param pointTransactionVo
	 * @return
	 */
	String savePointTransaction(PointTransactionParam pointTransactionVo);

	/**
	 * 修改积分流水
	 * 
	 * @param pointTransactionVo
	 */
	void updatePointTransaction(PointTransactionParam pointTransactionVo);

	/**
	 * 根据Code查询积分流水
	 * 
	 * @param code
	 * @return
	 */
	PointTransactionResult getPointTransaction(GetPointTransactionParam param);

	/**
	 * 分页查询积分流水
	 * 
	 * @param map
	 * @return queryResult<PointTransactionResult>
	 */
	PageData<PointTransactionResult> queryPointTransactionPage(PointTransactionDto pointTransactionDto);

	List<PointAccountCampaignResult.CampaignBalance> getCountPointTransactionCampaign(String tenantCode, String accountCode, List<String> campaignCodes);

	List<PointTransactionEntity> queryPointTransactionEndTime(Long maxId, int limit, String nowTime);
}
