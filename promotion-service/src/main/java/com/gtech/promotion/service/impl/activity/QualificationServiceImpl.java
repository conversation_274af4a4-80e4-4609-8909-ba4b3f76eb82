package com.gtech.promotion.service.impl.activity;

import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.promotion.dao.entity.activity.LuckyDrawQualificationEntity;
import com.gtech.promotion.dao.entity.activity.QualificationEntity;
import com.gtech.promotion.dao.mapper.activity.LuckyDrawQualificationMapper;
import com.gtech.promotion.dao.mapper.activity.QualificationMapper;
import com.gtech.promotion.dao.model.activity.QualificationModel;
import com.gtech.promotion.service.activity.QualificationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.List;

@Service
public class QualificationServiceImpl implements QualificationService {

    @Autowired
    private QualificationMapper qualificationMapper;

    @Autowired
    private LuckyDrawQualificationMapper luckyDrawQualificationMapper;

    @Override
    @Transactional
    public int createQualifications(List<QualificationModel> qualificationModels) {
        List<QualificationEntity> entities = BeanCopyUtils.jsonCopyList(qualificationModels, QualificationEntity.class);
        int row = 0;
        for (QualificationEntity entity : entities) {
            row += qualificationMapper.insertSelective(entity);
        }
        return row;
    }

    @Override
    @Transactional
    public int createLuckyDrawQualifications(List<QualificationModel> qualificationModels) {
        List<LuckyDrawQualificationEntity> entities = BeanCopyUtils.jsonCopyList(qualificationModels, LuckyDrawQualificationEntity.class);
        int row = 0;
        for (LuckyDrawQualificationEntity entity : entities) {
            row += luckyDrawQualificationMapper.insertSelective(entity);
        }
        return row;
    }

    @Override
    @Transactional
    public int deleteQualifications(String tenantCode, String activityCode) {
        QualificationEntity entity = new QualificationEntity();
        entity.setTenantCode(tenantCode);
        entity.setActivityCode(activityCode);
        return qualificationMapper.delete(entity);
    }

    @Override
    @Transactional
    public int deleteLuckyDrawQualifications(String tenantCode, String activityCode) {
        LuckyDrawQualificationEntity entity = new LuckyDrawQualificationEntity();
        entity.setTenantCode(tenantCode);
        entity.setActivityCode(activityCode);
        return luckyDrawQualificationMapper.delete(entity);
    }

    @Override
    public List<QualificationModel> queryQualifications(String tenantCode, String activityCode) {
        QualificationEntity entity = new QualificationEntity();
        entity.setTenantCode(tenantCode);
        entity.setActivityCode(activityCode);
        return BeanCopyUtils.jsonCopyList(qualificationMapper.select(entity), QualificationModel.class);
    }

    @Override
    public List<QualificationModel> queryLuckyDrawQualifications(String tenantCode, String activityCode) {
        LuckyDrawQualificationEntity entity = new LuckyDrawQualificationEntity();
        entity.setTenantCode(tenantCode);
        entity.setActivityCode(activityCode);
        return BeanCopyUtils.jsonCopyList(luckyDrawQualificationMapper.select(entity), QualificationModel.class);
    }

    @Override
    public List<QualificationModel> queryQualificationsByActivityCodes(String tenantCode, List<String> activityCodes) {
        Example example = new Example(QualificationEntity.class);
        example.createCriteria().andEqualTo(QualificationEntity.C_TENANT_CODE, tenantCode)
                .andIn(QualificationEntity.C_ACTIVITY_CODE, activityCodes);
        return BeanCopyUtils.jsonCopyList(qualificationMapper.selectByCondition(example), QualificationModel.class);
    }

    @Override
    public List<QualificationModel> queryLuckyDrawQualificationsByActivityCodes(String tenantCode, String activityCodes) {
        Example example = new Example(QualificationEntity.class);
        example.createCriteria().andEqualTo(QualificationEntity.C_TENANT_CODE, tenantCode)
                .andEqualTo(QualificationEntity.C_ACTIVITY_CODE, activityCodes);
        return BeanCopyUtils.jsonCopyList(luckyDrawQualificationMapper.selectByCondition(example), QualificationModel.class);
    }

    @Override
    public List<QualificationModel> queryQualificationsForSendCoupon(String tenantCode, String activityCode) {
        List<String> list = new ArrayList<>();
        list.add("isMember");
        list.add("memberTagCode");
        Example example = new Example(QualificationEntity.class);
        example.createCriteria().andEqualTo(QualificationEntity.C_TENANT_CODE, tenantCode)
                .andEqualTo(QualificationEntity.C_ACTIVITY_CODE, activityCode)
                .andIn(QualificationEntity.C_QUALIFICATION_CODE, list);
        return BeanCopyUtils.jsonCopyList(qualificationMapper.selectByCondition(example), QualificationModel.class);
    }

    @Override
    public List<QualificationModel> queryQualificationsByMemberTags(String tenantCode, List<String> activityCodeList,List<String> qualificationCodeValueList) {
        Example example = new Example(QualificationEntity.class);
        example.createCriteria().andEqualTo(QualificationEntity.C_TENANT_CODE, tenantCode)
                .andEqualTo(QualificationEntity.C_QUALIFICATION_CODE, "memberTagCode")
                .andIn(QualificationEntity.C_ACTIVITY_CODE, activityCodeList)
                .andIn(QualificationEntity.C_QUALIFICATION_VALUE, qualificationCodeValueList);
        return BeanCopyUtils.jsonCopyList(qualificationMapper.selectByCondition(example), QualificationModel.class);
    }

    @Override
    public List<QualificationModel> queryQualificationsByCode(String tenantCode,String qualificationCode,String qualificationValue) {

        Example example = new Example(QualificationEntity.class);
        example.createCriteria().andEqualTo(QualificationEntity.C_TENANT_CODE, tenantCode)
                .andEqualTo(QualificationEntity.C_QUALIFICATION_CODE, qualificationCode)
                .andEqualTo(QualificationEntity.C_QUALIFICATION_VALUE, qualificationValue);
        return BeanCopyUtils.jsonCopyList(qualificationMapper.selectByCondition(example), QualificationModel.class);
    }
}
