package com.gtech.promotion.service.impl.activity;


import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.code.marketing.DeleteEnum;
import com.gtech.promotion.code.marketing.SwitchEnum;
import com.gtech.promotion.dao.entity.activity.PromoGroupRelationEntity;
import com.gtech.promotion.dao.mapper.activity.PromoGroupRelationMapper;
import com.gtech.promotion.dao.model.activity.ActivityGroupRelationVO;
import com.gtech.promotion.dao.model.activity.GroupRelationVO;
import com.gtech.promotion.dao.model.activity.QueryGroupRelationVO;
import com.gtech.promotion.service.activity.PromotionGroupRelationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import java.util.Collections;
import java.util.List;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/7/12 10:34
 */
@Service
public class PromotionGroupRelationServiceImpl implements PromotionGroupRelationService {

    private static final String TENANT_CODE = "tenantCode";

    private static final String DOMAIN_CODE = "domainCode";


    private static final String GROUP_CODE_A = "groupCodeA";

    private static final String GROUP_CODE_B =  "groupCodeB";

    public static final String LOGIC_DELETE = "logicDelete";

    @Autowired
    private PromoGroupRelationMapper promoGroupRelationMapper;



    @Override
    public int createGroupRelation(String tenantCode, String domainCode,GroupRelationVO relation) {

        //先查询是否已经存在
        int countA = queryGroupRelationByGroupCodeAB(tenantCode, relation.getGroupCodeA(), relation.getGroupCodeB());
        int countB = queryGroupRelationByGroupCodeAB(tenantCode, relation.getGroupCodeB(), relation.getGroupCodeA());

        int result = 0;

        PromoGroupRelationEntity entityA = new PromoGroupRelationEntity();
        if(countA == 0){
            entityA.setGroupCodeA(relation.getGroupCodeA());
            entityA.setGroupCodeB(relation.getGroupCodeB());
            entityA.setTenantCode(tenantCode);
            entityA.setDomainCode(domainCode);
            result = promoGroupRelationMapper.insertSelective(entityA);
        }
        PromoGroupRelationEntity entityB = new PromoGroupRelationEntity();


        if (countB == 0){
            entityB.setTenantCode(entityA.getTenantCode());
            entityB.setDomainCode(domainCode);
            entityB.setGroupCodeA(entityA.getGroupCodeB());
            entityB.setGroupCodeB(entityA.getGroupCodeA());
            result = promoGroupRelationMapper.insertSelective(entityB);
        }
        return result;

    }

    @Override
    @Transactional
    public int deleteGroupRelation(String tenantCode,String domainCode,GroupRelationVO relation){
        PromoGroupRelationEntity entity = new PromoGroupRelationEntity();
        entity.setTenantCode(tenantCode);
        entity.setDomainCode(domainCode);
        entity.setGroupCodeA(relation.getGroupCodeA());
        if (!StringUtil.isEmpty(relation.getGroupCodeB())){
            entity.setGroupCodeB(relation.getGroupCodeB());
        }
        promoGroupRelationMapper.delete(entity);
        PromoGroupRelationEntity entityB = new PromoGroupRelationEntity();
        entityB.setTenantCode(tenantCode);
        entityB.setGroupCodeB(relation.getGroupCodeA());
        if (!StringUtil.isEmpty(relation.getGroupCodeB())){
            entityB.setGroupCodeA(relation.getGroupCodeB());
        }
        return promoGroupRelationMapper.delete(entityB);
    }

    @Override
    public List<ActivityGroupRelationVO> queryListGroupRelationByGroupCodeA(QueryGroupRelationVO relationVO) {

        Example example = new Example(PromoGroupRelationEntity.class);

        example.createCriteria()
                .andEqualTo(TENANT_CODE, relationVO.getTenantCode())
                .andEqualTo(GROUP_CODE_A, relationVO.getGroupCodeA())
                .andEqualTo(LOGIC_DELETE, DeleteEnum.NORMAL.code());

        return BeanCopyUtils.jsonCopyList(promoGroupRelationMapper.selectByCondition(example), ActivityGroupRelationVO.class);


    }

    @Override
    public List<ActivityGroupRelationVO> queryGroupRelationByGroupCodeA(String tenantCode, List<String> groupCodeA) {
       if (CollectionUtils.isEmpty(groupCodeA)){
           return  Collections.emptyList();
       }
        Example example = new Example(PromoGroupRelationEntity.class);

        example.createCriteria()
                .andEqualTo(TENANT_CODE, tenantCode)
                .andIn(GROUP_CODE_A, groupCodeA)
                .andEqualTo(LOGIC_DELETE, SwitchEnum.YES.code());
        return BeanCopyUtils.jsonCopyList(promoGroupRelationMapper.selectByCondition(example), ActivityGroupRelationVO.class);
    }

    @Override
    public int deleteGroupRelationByGroupCode(String tenantCode, String domainCode, String groupCode) {

        PromoGroupRelationEntity entity = new PromoGroupRelationEntity();
        entity.setTenantCode(tenantCode);
        entity.setDomainCode(domainCode);
        entity.setGroupCodeA(groupCode);
        promoGroupRelationMapper.delete(entity);

        PromoGroupRelationEntity entityB = new PromoGroupRelationEntity();
        entityB.setTenantCode(tenantCode);
        entityB.setDomainCode(domainCode);
        entityB.setGroupCodeB(groupCode);

        return promoGroupRelationMapper.delete(entityB);
    }

    @Override
    public int deleteGroupRelationByTenantCode(String tenantCode, String domainCode) {

        Example example = new Example(PromoGroupRelationEntity.class);
        example.createCriteria()
                .andEqualTo(TENANT_CODE, tenantCode)
                .andEqualTo(DOMAIN_CODE, domainCode);

        return promoGroupRelationMapper.deleteByCondition(example);
    }

    @Override
    public int queryGroupRelationByGroupCodeAB(String tenantCode, String groupCodeA, String groupCodeB) {

        Example example = new Example(PromoGroupRelationEntity.class);

        example.createCriteria()
                .andEqualTo(TENANT_CODE, tenantCode)
                .andEqualTo(GROUP_CODE_A, groupCodeA)
                .andEqualTo(GROUP_CODE_B, groupCodeB)
                .andEqualTo(LOGIC_DELETE, SwitchEnum.YES.code());
        return  promoGroupRelationMapper.selectCountByCondition(example);
    }


}
