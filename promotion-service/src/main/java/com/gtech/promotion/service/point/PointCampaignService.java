package com.gtech.promotion.service.point;

import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.promotion.vo.param.point.PointCampaignParam;
import com.gtech.promotion.vo.param.point.UpdatePointParam;
import com.gtech.promotion.vo.param.point.query.PointCampaignUniqueParam;
import com.gtech.promotion.vo.param.point.query.PointCampaignUniqueParam.PointCampaignStatusUniqueVo;
import com.gtech.promotion.vo.result.point.PointCampaignResult;

import java.io.Serializable;
import java.util.Map;

public interface PointCampaignService {

	/**
	 * 新增积分池
	 * 
	 * @param pointCampaignVo
	 * @return
	 */
	String savePointCampaign(PointCampaignParam pointCampaignVo);

	/**
	 * 修改积分池
	 * 
	 * @param pointCampaignVo
	 */
	void updatePointCampaign(PointCampaignParam pointCampaignVo);

	/**
	 * 修改积分池状态
	 * 
	 * @param code
	 * @param status
	 * @param oldStatus
	 */
	void updatePointCampaignStatus(PointCampaignStatusUniqueVo pointCampaignStatusUniqueVo);

	/**
	 * 根据Code查询积分池
	 * 
	 * @param code
	 * @return
	 */
	PointCampaignResult getPointCampaign(PointCampaignUniqueParam pointCampaignUniqueVo);

	/**
	 * 分页查询积分池
	 * 
	 * @param map
	 * @return queryResult<PointCampaignResult>
	 */
	PageResult<PointCampaignResult> queryPointCampaignPage(Map<String, Object> map);

	int updatePoint(UpdatePointParam pointChangeVo);

	Result<Serializable> queryPointCampaignEndTime();



}
