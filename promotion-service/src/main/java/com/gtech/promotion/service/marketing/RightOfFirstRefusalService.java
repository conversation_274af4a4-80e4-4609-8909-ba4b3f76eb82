package com.gtech.promotion.service.marketing;

import com.gtech.promotion.dao.entity.marketing.RightOfFirstRefusalEntity;
import com.gtech.promotion.dao.model.marketing.RightOfFirstRefusalModel;
import com.gtech.promotion.dto.in.flashsale.WriteOffOfPreEmptiveRightsDto;

import java.util.List;

public interface RightOfFirstRefusalService  extends BaseService<RightOfFirstRefusalEntity, RightOfFirstRefusalModel>{
    RightOfFirstRefusalEntity findRightOfFirstRefusalByMember(WriteOffOfPreEmptiveRightsDto dto);
    int updateStatus(WriteOffOfPreEmptiveRightsDto dto);

    List<RightOfFirstRefusalEntity> queryRightOfFirstRefusalByMember(WriteOffOfPreEmptiveRightsDto dto);

}
