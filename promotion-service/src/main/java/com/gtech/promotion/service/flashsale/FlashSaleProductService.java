package com.gtech.promotion.service.flashsale;

import com.gtech.promotion.dao.entity.marketing.flashsale.FlashSaleProductEntity;
import com.gtech.promotion.dao.model.marketing.flashsale.FlashSaleProductModel;
import com.gtech.promotion.service.marketing.BaseService;

import java.util.List;

public interface FlashSaleProductService extends BaseService<FlashSaleProductEntity, FlashSaleProductModel> {
    List<FlashSaleProductModel> getProductsByActivityCodes(String tenantCode, List<String> activityCodes);

    List<FlashSaleProductModel> getProductsByActivityCodesAndProducts(String tenantCode, List<String> activityCodes, List<String> skuCodes);

    List<FlashSaleProductModel> getProductsByActivityCodesAndProductsBySpu(String tenantCode, List<String> activityCodes, List<String> spuCodes);


    FlashSaleProductModel findByActivityAndSku(String activityCode, String skuCode);

    int dealInventory(String activityCode, String skuCode, Integer promoQuantity);

    int dealInventoryBySpu(String activityCode, String productCode, Integer promoQuantity);


    int updateActivityCodeAndOrgCode(String importNo, String orgCode, String activityCode);

    List<FlashSaleProductModel> findByActivityAndSkuList(String activityCode, List<String> skuList);


    List<FlashSaleProductModel> queryProductsByActivityCodesAndSkuCode(List<String> activityCodes,String skuCode);

    FlashSaleProductModel findByActivityAndProduct(String activityCode, String productCode);

}
