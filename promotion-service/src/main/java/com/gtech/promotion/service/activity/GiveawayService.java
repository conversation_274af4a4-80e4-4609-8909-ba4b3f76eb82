/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.service.activity;

import com.gtech.promotion.dao.model.activity.GiveawayVO;

import java.util.List;

/**
 * 促销赠品service
 *
 */
public interface GiveawayService {

    /**
     * 创建促销赠品
     */
    void createGiveaway(GiveawayVO giftVO);

    /**
     * 根据规则id获取赠品列表
     */
    List<GiveawayVO> getGiftListByActivityCode(String tenantCode, String activityCode);

    /**
     * 根据活动编码删除赠品
     */
    void deleteGiveaway(String tenantCode, String activityCode);

    List<GiveawayVO> getGiftListByActivityCodes(String tenantCode, List<String> activityCodes);
}
