package com.gtech.promotion.service.impl.activity;

import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.promotion.dao.entity.activity.LuckyDrawRulesEntity;
import com.gtech.promotion.dao.entity.marketing.BaseEntity;
import com.gtech.promotion.dao.mapper.activity.LuckyDrawRulesMapper;
import com.gtech.promotion.dao.model.activity.LuckyDrawRuleModel;
import com.gtech.promotion.dao.model.marketing.flashsale.FlashSaleProductModel;
import com.gtech.promotion.service.activity.LuckyDrawRuleService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/1/26 10:03
 */
@Service
public class LuckyDrawRuleServiceImpl implements LuckyDrawRuleService {


    @Autowired
    private LuckyDrawRulesMapper luckyDrawRulesMapper;

    @Override
    @Transactional
    public int createLuckyDrawRule(String activityCode, List<LuckyDrawRuleModel> ruleModels, String tenantCode) {
        List<LuckyDrawRulesEntity> entities = BeanCopyUtils.jsonCopyList(ruleModels, LuckyDrawRulesEntity.class);
        int row = 0;
        for (LuckyDrawRulesEntity entity : entities) {
            entity.setActivityCode(activityCode);
            entity.setTenantCode(tenantCode);
            row += luckyDrawRulesMapper.insertSelective(entity);
        }
        return row;
    }

    @Override
    @Transactional
    public int deleteLuckyDrawRuleByActivityCode(String activityCode) {
        Example example = new Example(LuckyDrawRulesEntity.class);
        example.createCriteria().andEqualTo(BaseEntity.C_ACTIVITY_CODE, activityCode);
        return luckyDrawRulesMapper.deleteByCondition(example);
    }

    @Override
    public List<LuckyDrawRuleModel> queryLuckyDrawRulesByActivityCode(String tenantCode, String activityCode) {
        LuckyDrawRulesEntity entity = new LuckyDrawRulesEntity();
        entity.setTenantCode(tenantCode);
        entity.setActivityCode(activityCode);
        return BeanCopyUtils.jsonCopyList(luckyDrawRulesMapper.select(entity), LuckyDrawRuleModel.class);
    }

    @Override
    public List<LuckyDrawRuleModel> queryLuckyDrawRulesByActivityCode(String tenantCode, List<String> activityCodes) {
        Example example = new Example(LuckyDrawRulesEntity.class);
        example.createCriteria()
                .andEqualTo(BaseEntity.C_TENANT_CODE, tenantCode)
                .andIn(BaseEntity.C_ACTIVITY_CODE,activityCodes);
        return BeanCopyUtils.jsonCopyList(luckyDrawRulesMapper.selectByCondition(example), LuckyDrawRuleModel.class);
    }

    /**
     * 跟据spuCode集合查询所有此spu的活动
     * @param tenantCode
     * @param productCodes
     * @return
     */
    @Override
    public List<LuckyDrawRuleModel> queryLuckyDrawRulesByProductCode(String tenantCode, List<String> productCodes) {
        Example example = new Example(LuckyDrawRulesEntity.class);
        example.createCriteria()
                .andEqualTo(BaseEntity.C_TENANT_CODE, tenantCode)
                .andIn("productCode",productCodes);
        return BeanCopyUtils.jsonCopyList(luckyDrawRulesMapper.selectByCondition(example), LuckyDrawRuleModel.class);
    }

    @Override
    public List<FlashSaleProductModel> getLuckDrawProductsByActivityCodesAndProducts(String tenantCode, ArrayList<String> strings, List<String> skuCodeList) {
        if (CollectionUtils.isEmpty(strings) || CollectionUtils.isEmpty(skuCodeList)){
            return Collections.emptyList();
        }

        Example example = new Example(LuckyDrawRulesEntity.class);
        example.createCriteria()
                .andEqualTo("tenantCode",tenantCode)
                .andIn("activityCode",strings)
                .andIn("productCode",skuCodeList);



        List<LuckyDrawRulesEntity> luckyDrawRulesEntities = luckyDrawRulesMapper.selectByCondition(example);

        //获取product信息
        List<FlashSaleProductModel> flashSaleProductModels = new ArrayList<>();
        for (LuckyDrawRulesEntity luckyDrawRulesEntity : luckyDrawRulesEntities) {
            FlashSaleProductModel flashSaleProductModel = new FlashSaleProductModel();
            flashSaleProductModel.setActivityCode(luckyDrawRulesEntity.getActivityCode());
            flashSaleProductModel.setProductCode(luckyDrawRulesEntity.getProductCode());
            flashSaleProductModels.add(flashSaleProductModel);
        }

        return flashSaleProductModels;
    }
}
