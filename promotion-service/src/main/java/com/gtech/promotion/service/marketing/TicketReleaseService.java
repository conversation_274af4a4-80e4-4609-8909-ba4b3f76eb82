package com.gtech.promotion.service.marketing;

import com.gtech.promotion.dao.entity.marketing.TicketReleaseEntity;
import com.gtech.promotion.dao.model.marketing.BaseModel;
import com.gtech.promotion.dao.model.marketing.TicketReleaseModel;
import com.gtech.promotion.dto.in.marketing.TicketSendOutDto;

import java.util.List;

public interface TicketReleaseService extends BaseService<TicketReleaseEntity, TicketReleaseModel> {
    List<TicketSendOutDto> deductInventory(BaseModel baseModel, int inventory);

    int addUsed(BaseModel baseModel, String releaseCode);
}
