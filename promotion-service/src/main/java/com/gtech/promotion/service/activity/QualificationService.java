package com.gtech.promotion.service.activity;

import com.gtech.promotion.dao.model.activity.QualificationModel;

import java.util.List;

public interface QualificationService {

    int createQualifications(List<QualificationModel> qualificationModels);

    int createLuckyDrawQualifications(List<QualificationModel> qualificationModels);

    int deleteQualifications(String tenantCode, String activityCode);

    int deleteLuckyDrawQualifications(String tenantCode, String activityCode);

    List<QualificationModel> queryQualifications(String tenantCode, String activityCode);

    List<QualificationModel> queryLuckyDrawQualifications(String tenantCode, String activityCode);

    List<QualificationModel> queryQualificationsByActivityCodes(String tenantCode, List<String> activityCodes);

    List<QualificationModel> queryLuckyDrawQualificationsByActivityCodes(String tenantCode, String activityCodes);

    List<QualificationModel> queryQualificationsForSendCoupon(String tenantCode, String activityCode);

    List<QualificationModel> queryQualificationsByMemberTags(String tenantCode, List<String> activityCodeList, List<String> qualificationCodeValueList);


    List<QualificationModel> queryQualificationsByCode(String tenantCode,String qualificationCode,String qualificationValue);

}
