package com.gtech.promotion.service.marketing;

import com.github.pagehelper.PageInfo;
import com.gtech.promotion.code.marketing.UserGroupStatusEnum;
import com.gtech.promotion.dao.entity.marketing.MarketingGroupUserEntity;
import com.gtech.promotion.dao.model.marketing.GroupUserCountDto;
import com.gtech.promotion.dao.model.marketing.MarketingGroupUserMode;
import com.gtech.promotion.vo.param.marketing.MarketingGroupUserListParam;
import com.gtech.promotion.vo.param.marketing.MarketingGroupUserParam;
import com.gtech.promotion.vo.param.marketing.flashsale.FindGroupUserParam;

import java.util.List;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/8/7 14:01
 */
public interface MarketingGroupUserService extends BaseService<MarketingGroupUserEntity, MarketingGroupUserMode>{

    /**
     *
     * @param param
     * @return
     */
    MarketingGroupUserMode findGroupUserCode(FindGroupUserParam param);

    /**
     * 团员参团 查询生效中的团长信息
     * @param tenantCode
     * @param marketingGroupCode
     * @return
     */
    MarketingGroupUserMode findGroupLeaderUserByMarketingGroupCode(String tenantCode,String marketingGroupCode);


    /***
     * 用于团长支付时，查询团长信息
     * @param tenantCode
     * @param marketingGroupCode
     * @return
     */
    MarketingGroupUserMode confirmGroupLeaderByMarketingGroupCode(String tenantCode,String marketingGroupCode);


    /**
     * 统计参团人数
     * @param tenantCode
     * @param activityCode
     * @param marketingGroupCode
     * @return
     */
    int queryGroupUserListByMarketingGroupCode(String tenantCode, String activityCode,String marketingGroupCode);


    /**
     * 统计团员未支付情况
     * @param tenantCode
     * @param activityCode
     * @param marketingGroupCode
     * @return
     */
    int queryGroupMemberNoPay(String tenantCode, String activityCode,String marketingGroupCode);


    /**
     * 用户参团情况
     * @param tenantCode
     * @param activityCode
     * @param userCode
     * @return
     */
    List<MarketingGroupUserMode> queryGroupUserListByUserCode(String tenantCode, String activityCode,String userCode);


    /**
     * 查询已支付拼团
     * @param param
     * @return
     */
    List<MarketingGroupUserMode> queryPayGroupUserListByActivityCode(MarketingGroupUserParam param);


    /**
     * 查询进行中的拼团数据
     * @param param
     * @return
     */
    List<MarketingGroupUserMode> queryGroupUserListByActivityCode(MarketingGroupUserParam param);


    /**
     * 根据条件查询拼团
     * @param param
     * @return
     */
    PageInfo<MarketingGroupUserMode> queryAllGroupUserListByCondition(MarketingGroupUserListParam param);


    /**
     * 根据用户编码拼团状态变更
     * @param tenantCode
     * @param marketingGroupCode
     * @param userCode
     */
    void updateGroupSuccessStatusByUserCode(String tenantCode, String marketingGroupCode, String userCode, UserGroupStatusEnum groupStatusEnum);

    /**
     * 拼团状态进行中，参团人员整体状态变更
     * @param tenantCode
     * @param marketingGroupCode
     */
    void updateGroupStatusByMarketingGroupCode(String tenantCode, String marketingGroupCode, UserGroupStatusEnum groupStatusEnum);


    /**
	 * 修改参与人拼团状态
	 * 
	 * @param tenantCode
	 * @param marketingGroupCode
	 * @param groupStatus
	 * @param oldGroupStatus
	 */
	void updateGroupUserStatus(String tenantCode, String marketingGroupCode, String groupStatus, String oldGroupStatus);

    /**
     * 填充该团的有效时间
     * @param tenantCode
     * @param marketingGroupCode
     * @param userCode
     * @param effectiveTime
     */
    void updateGroupEffectiveTimeByMarketingGroupCode(String tenantCode, String marketingGroupCode, String userCode, String effectiveTime);


    /**
     * 取消拼团删除
     * @param tenantCode
     * @param marketingGroupCode
     * @param userCode
     */
    void cancelGroupByMarketingGroupAndUserCode(String tenantCode, String marketingGroupCode, String userCode);


    /**
     * 是否团长 true 是 false 否
     * @param tenantCode
     * @param marketingGroupCode
     * @param activityCode
     * @param userCode
     * @return
     */
    boolean existLeaderByGroupCode(String tenantCode, String marketingGroupCode, String activityCode,String userCode);


    /**
     * 团员参与,校验同一拼团的商品，商品维度相同，才能参加拼团
     * @param tenantCode
     * @param activityCode
     * @param marketingGroupCode
     * @param productCode
     * @return
     */
    int checkGroupProductCode(String tenantCode, String activityCode, String marketingGroupCode,String productCode, String skuCode);




    /**
     * 同一拼团团长多次参与校验,校验同一拼团的商品，商品维度相同，不准多次参团
     * @param tenantCode
     * @param activityCode
     * @param marketingGroupCode
     * @param productCode
     * @return
     */
    int checkGroupLeaderProductCode(String tenantCode, String activityCode, String marketingGroupCode,String productCode, String skuCode);


    /**
     * 参与拼团人数
     * @param dto
     * @return
     */
    int countOfParticipants(GroupUserCountDto dto);
}
