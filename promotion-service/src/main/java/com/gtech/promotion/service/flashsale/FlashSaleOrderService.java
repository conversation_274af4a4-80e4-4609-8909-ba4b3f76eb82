package com.gtech.promotion.service.flashsale;

import com.gtech.promotion.dao.entity.marketing.flashsale.FlashSaleOrderEntity;
import com.gtech.promotion.dao.model.marketing.flashsale.FlashSaleOrderModel;
import com.gtech.promotion.service.marketing.BaseService;

public interface FlashSaleOrderService extends BaseService<FlashSaleOrderEntity, FlashSaleOrderModel> {
    FlashSaleOrderModel findByOrderNo(String tenantCode, String orderNo);

    int updateStatus(String tenantCode, String orderNo, String orderStatus);
}
