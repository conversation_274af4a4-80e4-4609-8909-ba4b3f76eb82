package com.gtech.promotion.service.common.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.gtech.basic.filecloud.api.model.ImportFileMessage;
import com.gtech.basic.filecloud.api.model.ImportFileMessage.Payload;
import com.gtech.basic.filecloud.api.model.ImportFileMessage.Result;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.RedisClient;
import com.gtech.promotion.dao.model.marketing.flashsale.FlashSaleProductModel;
import com.gtech.promotion.feign.PimFeignClient;
import com.gtech.promotion.feign.bean.SkuCodeQueryRequest;
import com.gtech.promotion.service.common.ExcelService;
import com.gtech.promotion.service.flashsale.FlashSaleProductService;
import com.gtech.promotion.utils.FlashSaleConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class ExcelServiceImpl implements ExcelService {

	@Autowired
	private PimFeignClient pimFeignClient;

	@Autowired
	private FlashSaleProductService flashSaleProductService;

	@Autowired
	private RedisClient redisClient;

	private static final String CODE = "marketing.import";
	private static final String REDIS_STR = "1";
	private static final String KEY = "FLASH_SALE:UPLOAD:IMPORTNO=";

	public ImportFileMessage<Result> importSku(ImportFileMessage<String> importFileMessage) {

		ImportFileMessage.Builder<Result> builder = ImportFileMessage.buildResult().category(importFileMessage.getCategory()).subcategory(importFileMessage.getSubcategory());
		if (CollectionUtils.isEmpty(importFileMessage.getPayload())) {
			return builder.build();
		}
		List<Payload<String>> payloadList = importFileMessage.getPayload();
		List<Payload<Result>> resultPayloadList = new CopyOnWriteArrayList<>();
		String importNo = payloadList.get(0).getHead().split("-")[0];
		if (errorMessage(payloadList, resultPayloadList, importNo)) return builder.payload(resultPayloadList).build();
		try {
			List<String> skuCodeList = new ArrayList<>();
			if (isErrorMessageByPayloadList(payloadList, resultPayloadList, importNo, skuCodeList))
				return builder.payload(resultPayloadList).build();

			if (CollectionUtils.isEmpty(skuCodeList)){
				return builder.build();
			}
			SkuCodeQueryRequest request = new SkuCodeQueryRequest();
			request.setTenantCode(importFileMessage.getTenantCode());
			request.setSkuCodeList(skuCodeList);
			request.setPageSize(skuCodeList.size());
			log.info("pimFeignClient querySkuPage param : {{}}", JSONObject.toJSONString(request));
			JSONObject resultJson = pimFeignClient.querySkuPage(request);
			JSONObject data = resultJson.getJSONObject("data");
			JSONArray jsonArray = data.getJSONArray("list");

			List<FlashSaleProductModel> productModels = new ArrayList<>();
			boolean delete = false;
			for (Payload<String> stringPayload : payloadList) {
				boolean flag = false;
				JSONObject jsonObject = JSON.parseObject(stringPayload.getBody());
				String skuCode = jsonObject.getString(FlashSaleConstants.FILE_SKU_CODE);

				String errMsg = "Not exist sku code : ";
				FlashSaleProductModel productModel = null;
				for (int i = 0; i < jsonArray.size(); i ++){
					JSONObject jsonObject1 = jsonArray.getJSONObject(i);
					if (jsonObject1.getString("skuCode").equals(skuCode)){
						flag = true;
						productModel = new FlashSaleProductModel();
						productModel.setActivityCode(importNo);
						productModel.setSkuInventory(jsonObject.getInteger(FlashSaleConstants.FILE_SKU_QUOTA));
						productModel.setFlashPrice(jsonObject.getBigDecimal(FlashSaleConstants.FILE_SKU_FLASH_PRICE));
						productModel.setListPrice(jsonObject1.getBigDecimal("productPrice"));
						productModel.setMaxPerUser(jsonObject.getInteger(FlashSaleConstants.FILE_SKU_MAX_USER));
						productModel.setSalePrice(jsonObject1.getBigDecimal("salePrice"));
						productModel.setSkuCode(jsonObject.getString(FlashSaleConstants.FILE_SKU_CODE));
						productModel.setSkuName(jsonObject1.getString("skuName"));
						productModel.setSkuQuota(jsonObject.getInteger(FlashSaleConstants.FILE_SKU_QUOTA));
						productModel.setTenantCode(importFileMessage.getTenantCode());
						productModel.setOrgCode("default");
						productModel.setDomainCode(importFileMessage.getDomainCode());
						if(productModel.getSalePrice().compareTo(productModel.getFlashPrice()) < 0){
							flag = false;
							errMsg = "Campaign Price must be lower than sale Price : ";
						}
						productModels.add(productModel);
						break;
					}
				}
				delete = isDelete(resultPayloadList, delete, stringPayload, flag, skuCode, errMsg, productModel);
			}

			deleteBySizeOrDelete(payloadList, importNo, productModels, delete);
		} catch (Exception e){
			log.error("import sku 异常", e);
			setRedis(importNo);
			flashSaleProductService.deleteByActivityCode(importNo);
			String message = e.getMessage();
			allFailed(payloadList, resultPayloadList, message.substring(0, Math.min(message.length(), 100)));
		}
		return builder.payload(resultPayloadList).build();
	}

	public void deleteBySizeOrDelete(List<Payload<String>> payloadList, String importNo, List<FlashSaleProductModel> productModels, boolean delete) {
		if (productModels.size() != payloadList.size() || delete){
			setRedis(importNo);
			flashSaleProductService.deleteByActivityCode(importNo);
		}
	}

	public boolean isDelete(List<Payload<Result>> resultPayloadList, boolean delete, Payload<String> stringPayload, boolean flag, String skuCode, String errMsg, FlashSaleProductModel productModel) {
		if (flag){
			delete = insertData(resultPayloadList, delete, stringPayload, productModel);
		}else{
			resultPayloadList.add(Payload.of(stringPayload.getHead(), Result.error(CODE, errMsg + skuCode)));
		}
		return delete;
	}
	public boolean isErrorMessageByPayloadList(List<Payload<String>> payloadList, List<Payload<Result>> resultPayloadList, String importNo, List<String> skuCodeList) {
		for (Payload<String> stringPayload : payloadList) {
			JSONObject jsonObject = JSON.parseObject(stringPayload.getBody());
			String skuCode = jsonObject.getString(FlashSaleConstants.FILE_SKU_CODE);
			String errMsg = "";
			BigDecimal flashPrice = null;
			try {
				flashPrice = jsonObject.getBigDecimal(FlashSaleConstants.FILE_SKU_FLASH_PRICE);
			}catch (NumberFormatException e){
				errMsg = "Incorrect input parameter format";
			}
			Integer skuQuota = jsonObject.getInteger(FlashSaleConstants.FILE_SKU_QUOTA);
			Integer maxPerUser = jsonObject.getInteger(FlashSaleConstants.FILE_SKU_MAX_USER);
			if (StringUtil.isEmpty(errMsg)){
				if (null == flashPrice){
					errMsg = "Campaign Price must not be empty";
				}else if (flashPrice.compareTo(BigDecimal.ZERO) <= 0){
					errMsg = "Campaign Price  must larger than 0.";
				}
			}
			if (StringUtil.isBlank(skuCode)){
				errMsg = "SKU Code must not be empty";
			}else if (null == skuQuota){
				errMsg = "Campaign Stock must not be empty";
			}else if (0 >= skuQuota){
				errMsg = "Campaign Stock must larger than 0.";
			}else if (null == maxPerUser){
				errMsg = "Max. Per User must not be empty";
			}else if (0 >= maxPerUser){
				errMsg = "Max. Per User must larger than 0.";
			}
			if (StringUtil.isNotBlank(errMsg)){
				setRedis(importNo);
				flashSaleProductService.deleteByActivityCode(importNo);
				allFailed(payloadList, resultPayloadList, errMsg);
				return true;
			}
			skuCodeList.add(skuCode);
		}
		return false;
	}

	public boolean errorMessage(List<Payload<String>> payloadList, List<Payload<Result>> resultPayloadList, String importNo) {
		if (StringUtil.isBlank(importNo)){
			allFailed(payloadList, resultPayloadList, "importNo is null");
			return true;
		}

		if (REDIS_STR.equals(getRedis(importNo))){
			allFailed(payloadList, resultPayloadList, "already failed");
			return true;
		}
		return false;
	}

	public boolean insertData(List<Payload<Result>> resultPayloadList, boolean delete, Payload<String> stringPayload, FlashSaleProductModel productModel) {
		try {
			flashSaleProductService.insert(productModel);
			resultPayloadList.add(Payload.of(stringPayload.getHead(), Result.ok()));
		} catch (DuplicateKeyException e) {
			log.error("import sku 重复sku", e);
			delete = true;
			String message = e.getMessage();
			char ch = '\'';
			String substring = message.substring(message.indexOf(ch) + 1);
			message = "Existing sku: " + substring.substring(0, substring.indexOf(ch));
			resultPayloadList.add(Payload.of(stringPayload.getHead(), Result.error(CODE, message)));
		}
		return delete;
	}

	private String getRedis(String importNo) {
		return redisClient.getString(KEY + importNo);
	}

	private void setRedis(String importNo) {
		redisClient.setStringValue(KEY + importNo, REDIS_STR, 24 * 60 * 60L, TimeUnit.SECONDS);
	}

	private void allFailed(List<Payload<String>> payloadList, List<Payload<Result>> resultPayloadList, String errorMsg) {
		resultPayloadList.clear();
		for (Payload<String> stringPayload : payloadList) {
			resultPayloadList.add(Payload.of(stringPayload.getHead(), Result.error(CODE, errorMsg)));
		}
	}

}
