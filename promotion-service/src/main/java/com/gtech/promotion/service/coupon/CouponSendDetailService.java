package com.gtech.promotion.service.coupon;

import com.gtech.promotion.dao.entity.coupon.PromoCouponSendDetailEntity;
import com.gtech.promotion.dao.model.activity.PromoCouponSendDetailModel;
import com.gtech.promotion.vo.param.coupon.QueryCouponSendDetailParam;

import java.util.List;

public interface CouponSendDetailService {
    int createCouponSendDetail(PromoCouponSendDetailModel promoCouponSendDetailModel);

    void createCouponSendDetailList(List<PromoCouponSendDetailModel> couponSendDetailModelList);

    List<PromoCouponSendDetailEntity> queryCouponSendDetailList(QueryCouponSendDetailParam queryCouponSendDetailParam);
}
