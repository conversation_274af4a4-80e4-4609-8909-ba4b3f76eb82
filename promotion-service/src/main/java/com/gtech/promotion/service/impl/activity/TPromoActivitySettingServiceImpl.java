/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.service.impl.activity;

import java.util.List;
import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.promotion.dao.entity.activity.TPromoActivitySettingEntity;
import com.gtech.promotion.dao.mapper.activity.TPromoActivitySettingMapper;
import com.gtech.promotion.dao.model.activity.TPromoActivitySettingVO;
import com.gtech.promotion.helper.ActivityRedisHelpler;
import com.gtech.promotion.service.activity.TPromoActivitySettingService;
import com.gtech.promotion.utils.Constants;

import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

/**
 * 活动设置项服务类实现
 * 
 */
@Service
public class TPromoActivitySettingServiceImpl implements TPromoActivitySettingService{

    @Autowired
    private TPromoActivitySettingMapper settingMapper;

    @Autowired
    private ActivityRedisHelpler redisService;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Override
    @Transactional
    public int deleteSetting(String tenantCode,Integer settingType){
        TPromoActivitySettingEntity entity = new TPromoActivitySettingEntity();
        entity.setTenantCode(tenantCode);
        entity.setSettingType(settingType);
        int delete = settingMapper.delete(entity);
        redisTemplate.delete(Constants.PROMOTION_ACTIVITY_SETTING.concat("：").concat(tenantCode).concat(":").concat(settingType + ""));
        return delete;
    }

    @Override
    public TPromoActivitySettingVO selectSetting(String tenantCode,Integer settingType,String settingCode){

        TPromoActivitySettingEntity entity = new TPromoActivitySettingEntity();
        entity.setTenantCode(tenantCode);
        entity.setSettingType(settingType);
        entity.setSettingCode(settingCode);
        return BeanCopyUtils.jsonCopyBean(settingMapper.selectOne(entity), TPromoActivitySettingVO.class);
    }

    @Override
    @Transactional
    public void addActivitySetting(String tenantCode,Integer settingType,String settingCode,String settingValue){
        TPromoActivitySettingEntity entity = new TPromoActivitySettingEntity();
        entity.setTenantCode(tenantCode);
        entity.setSettingType(settingType);
        entity.setSettingCode(settingCode);
        entity.setSettingValue(settingValue);
        settingMapper.insertSelective(entity);
        redisService.setActivitySetting(tenantCode, settingType, settingCode, settingValue);
    }

    @Override
    @Transactional
    public void updateActivitySetting(String tenantCode,Integer settingType,String settingCode,String settingValue){
        TPromoActivitySettingEntity entity = new TPromoActivitySettingEntity();
        entity.setSettingValue(settingValue);
        Example example = new Example(TPromoActivitySettingEntity.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("tenantCode", tenantCode);
        criteria.andEqualTo("settingType", settingType);
        criteria.andEqualTo("settingCode", settingCode);
        settingMapper.updateByConditionSelective(entity, example);
        redisService.setActivitySetting(tenantCode, settingType, settingCode, settingValue);
    }

    @Override
    @Transactional
    public void addAllActivitySetting(){
        List<TPromoActivitySettingEntity> selectAll = settingMapper.selectAll();
        Set<String> keys = redisService.getAll(Constants.PROMOTION_ACTIVITY_SETTING.concat("*"));
        redisTemplate.delete(keys);
        if (!CollectionUtils.isEmpty(selectAll)){
            for (TPromoActivitySettingEntity entity : selectAll){
                redisService.setActivitySetting(entity.getTenantCode(), entity.getSettingType(), entity.getSettingCode(), entity.getSettingValue());
            }
        }
    }

}
