package com.gtech.promotion.service.flashsale.impl;

import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.promotion.dao.entity.marketing.BaseEntity;
import com.gtech.promotion.dao.entity.marketing.flashsale.FlashSaleProductEntity;
import com.gtech.promotion.dao.mapper.marketing.flashsale.FlashSaleProductMapper;
import com.gtech.promotion.dao.model.marketing.flashsale.FlashSaleProductModel;
import com.gtech.promotion.service.flashsale.FlashSaleProductService;
import com.gtech.promotion.service.marketing.impl.BaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

@Slf4j
@Service
public class FlashSaleProductServiceImpl extends BaseServiceImpl<FlashSaleProductEntity, FlashSaleProductModel> implements FlashSaleProductService {

    public FlashSaleProductServiceImpl() {
        super(FlashSaleProductEntity.class, FlashSaleProductModel.class);
    }

    @Autowired
    private FlashSaleProductMapper productMapper;

    @Override
    public List<FlashSaleProductModel> getProductsByActivityCodes(String tenantCode, List<String> activityCodes) {
        Example example = new Example(FlashSaleProductEntity.class);
        example.createCriteria().andEqualTo(BaseEntity.C_TENANT_CODE, tenantCode)
                .andIn(BaseEntity.C_ACTIVITY_CODE, activityCodes);
        return BeanCopyUtils.jsonCopyList(productMapper.selectByCondition(example), FlashSaleProductModel.class);
    }

    @Override
    public List<FlashSaleProductModel> getProductsByActivityCodesAndProducts(String tenantCode, List<String> activityCodes, List<String> skuCodes) {
        Example example = new Example(FlashSaleProductEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(BaseEntity.C_TENANT_CODE, tenantCode);
        if(!CollectionUtils.isEmpty(activityCodes)){
            criteria.andIn(BaseEntity.C_ACTIVITY_CODE, activityCodes);
        }
        if(!CollectionUtils.isEmpty(skuCodes)){
            criteria.andIn(FlashSaleProductEntity.SKU_CODE, skuCodes);
        }
        List<FlashSaleProductEntity> entityList = productMapper.selectByCondition(example);
        return BeanCopyUtils.jsonCopyList(entityList, FlashSaleProductModel.class);
    }

    @Override
    public List<FlashSaleProductModel> getProductsByActivityCodesAndProductsBySpu(String tenantCode, List<String> activityCodes, List<String> spuCodes) {
        Example example = new Example(FlashSaleProductEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(BaseEntity.C_TENANT_CODE, tenantCode);
        if(!CollectionUtils.isEmpty(activityCodes)){
            criteria.andIn(BaseEntity.C_ACTIVITY_CODE, activityCodes);
        }
        if(!CollectionUtils.isEmpty(spuCodes)){
            criteria.andIn(FlashSaleProductEntity.SPU_CODE, spuCodes);
        }
        List<FlashSaleProductEntity> entityList = productMapper.selectByCondition(example);
        return BeanCopyUtils.jsonCopyList(entityList, FlashSaleProductModel.class);
    }

    @Override
    public FlashSaleProductModel findByActivityAndSku(String activityCode, String skuCode) {
        FlashSaleProductEntity entity = new FlashSaleProductEntity();
        entity.setActivityCode(activityCode);
        entity.setSkuCode(skuCode);
        return BeanCopyUtils.jsonCopyBean(productMapper.selectOne(entity), FlashSaleProductModel.class);
    }


    @Override
    public List<FlashSaleProductModel> findByActivityAndSkuList(String activityCode, List<String> skuList) {
        Example example = new Example(FlashSaleProductEntity.class);
        example.createCriteria().andEqualTo(BaseEntity.C_ACTIVITY_CODE, activityCode).andIn(BaseEntity.C_SKU_CODE,skuList);

        List<FlashSaleProductEntity> flashSaleProductEntityList = productMapper.selectByExample(example);
        return BeanCopyUtils.jsonCopyList(flashSaleProductEntityList, FlashSaleProductModel.class);
    }

    @Override
    public List<FlashSaleProductModel> queryProductsByActivityCodesAndSkuCode(List<String> activityCodes, String skuCode) {
        Example example = new Example(FlashSaleProductEntity.class);
        example.createCriteria().andEqualTo(BaseEntity.C_SKU_CODE,skuCode)
                .andIn(BaseEntity.C_ACTIVITY_CODE,activityCodes);
        List<FlashSaleProductEntity> flashSaleProductEntityList = productMapper.selectByExample(example);
        return BeanCopyUtils.jsonCopyList(flashSaleProductEntityList, FlashSaleProductModel.class);
    }

    @Override
    public FlashSaleProductModel findByActivityAndProduct(String activityCode, String productCode) {
        FlashSaleProductEntity entity = new FlashSaleProductEntity();
        entity.setActivityCode(activityCode);
        entity.setProductCode(productCode);
        return BeanCopyUtils.jsonCopyBean(productMapper.selectOne(entity), FlashSaleProductModel.class);
    }

    @Override
    @Transactional
    public int dealInventory(String activityCode, String skuCode, Integer promoQuantity) {
        return productMapper.dealInventory(activityCode, skuCode, promoQuantity);
    }

    @Override
    @Transactional
    public int dealInventoryBySpu(String activityCode, String productCode, Integer promoQuantity) {
        return productMapper.dealInventoryBySpu(activityCode, productCode, promoQuantity);
    }

    @Override
    @Transactional
    public int updateActivityCodeAndOrgCode(String importNo, String orgCode, String activityCode) {
        FlashSaleProductEntity entity = new FlashSaleProductEntity();
        entity.setActivityCode(activityCode);
        entity.setOrgCode(orgCode);

        Example example = new Example(FlashSaleProductEntity.class);
        example.createCriteria().andEqualTo(BaseEntity.C_ACTIVITY_CODE, importNo);
        return productMapper.updateByConditionSelective(entity, example);
    }
}
