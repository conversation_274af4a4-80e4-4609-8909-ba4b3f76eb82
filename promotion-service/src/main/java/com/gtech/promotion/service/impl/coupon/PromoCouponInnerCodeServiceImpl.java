/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.service.impl.coupon;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.gtech.commons.code.LogicFlagEnum;
import com.gtech.commons.page.PageData;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.code.coupon.CouponFrozenStatusEnum;
import com.gtech.promotion.code.coupon.CouponStatusEnum;
import com.gtech.promotion.code.coupon.CouponTypeEnum;
import com.gtech.promotion.dao.entity.coupon.TPromoCouponCodeUserEntity;
import com.gtech.promotion.dao.entity.coupon.TPromoCouponInnerCodeEntity;
import com.gtech.promotion.dao.mapper.coupon.TPromoCouponInnerCodeMapper;
import com.gtech.promotion.dao.model.coupon.CountCouponCodeModel;
import com.gtech.promotion.dao.model.coupon.CouponInnerRelationVO;
import com.gtech.promotion.dao.model.coupon.PromoPassVO;
import com.gtech.promotion.dao.model.coupon.TPromoCouponInnerCodeVO;
import com.gtech.promotion.dto.in.activity.CouponQuantityDTO;
import com.gtech.promotion.dto.in.activity.ManagementDataInDTO;
import com.gtech.promotion.dto.in.coupon.ExportCouponInDTO;
import com.gtech.promotion.dto.in.coupon.ExportCouponRelationDto;
import com.gtech.promotion.dto.in.coupon.TCouponListQueryDTO;
import com.gtech.promotion.dto.out.coupon.CouponInfoImportOutDTO;
import com.gtech.promotion.dto.out.coupon.ExportCouponOutDTO;
import com.gtech.promotion.dto.out.coupon.ManagementDataOutDTO;
import com.gtech.promotion.page.RequestPage;
import com.gtech.promotion.service.coupon.PromoCouponInnerCodeService;
import com.gtech.promotion.service.coupon.SqlPublicMethodsService;

import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

/**
 */
@Service
public class PromoCouponInnerCodeServiceImpl implements PromoCouponInnerCodeService {

    @Autowired
    private TPromoCouponInnerCodeMapper couponInnerCodeMapper;

    @Autowired
    private SqlPublicMethodsService sql;

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public int insertPromoCouponInnerCode(List<TPromoCouponInnerCodeVO> innerCodeVOs) {

        List<TPromoCouponInnerCodeEntity> innerCodeEntities = new ArrayList<>(100);
        int result = 0;
        if (innerCodeVOs != null) {
            for (TPromoCouponInnerCodeVO innerCodeVO : innerCodeVOs) {
                TPromoCouponInnerCodeEntity entity = BeanCopyUtils.jsonCopyBean(innerCodeVO, TPromoCouponInnerCodeEntity.class);
                entity.setLogicDelete(LogicFlagEnum.FALSE.number());
                Date now = DateUtil.now();
                entity.setCreateTime(now);
                entity.setUpdateTime(now);
                innerCodeEntities.add(entity);
                if (innerCodeEntities.size() == 100) {
                    result += couponInnerCodeMapper.insertList(innerCodeEntities);
                    innerCodeEntities.clear();
                }
            }
            if (!CollectionUtils.isEmpty(innerCodeEntities)) {
                result += couponInnerCodeMapper.insertList(innerCodeEntities);
            }
        }
        return result;
    }

    /**
     * 查询优惠券
     */
    @Override
    public TPromoCouponInnerCodeVO findCouponByCouponCode(String tenantCode, String couponCode) {

        TPromoCouponInnerCodeEntity entity = new TPromoCouponInnerCodeEntity();
        entity.setTenantCode(tenantCode);
        entity.setCouponCode(couponCode);
        return BeanCopyUtils.jsonCopyBean(couponInnerCodeMapper.selectOne(entity), TPromoCouponInnerCodeVO.class);
    }

    @Override
    public TPromoCouponInnerCodeVO findCouponByCouponCodeOrPassword(String tenantCode, String couponCode) {

        Example example = new Example(TPromoCouponInnerCodeEntity.class);
        //查询couponCode或者password
        example.createCriteria()
            .andEqualTo(TPromoCouponInnerCodeEntity.C_TENANT_CODE, tenantCode)
            .andEqualTo(TPromoCouponInnerCodeEntity.C_COUPON_CODE, couponCode);
        example.orderBy(TPromoCouponInnerCodeEntity.C_CREATE_TIME).desc();
        List<TPromoCouponInnerCodeEntity> tPromoCouponInnerCodeEntities = couponInnerCodeMapper.selectByCondition(example);

        if (CollectionUtils.isEmpty(tPromoCouponInnerCodeEntities)) {
            Example couponPwExample = new Example(TPromoCouponInnerCodeEntity.class);
            Criteria criteria = couponPwExample.createCriteria();
            criteria.andEqualTo(TPromoCouponInnerCodeEntity.C_TENANT_CODE, tenantCode)
                    .andEqualTo(TPromoCouponInnerCodeEntity.C_PROMO_PW, couponCode);
            couponPwExample.orderBy(TPromoCouponInnerCodeEntity.C_CREATE_TIME).desc();
            List<TPromoCouponInnerCodeEntity> couponPw = couponInnerCodeMapper.selectByCondition(couponPwExample);
            if (CollectionUtils.isEmpty(couponPw)){
                return null;
            }
            return BeanCopyUtils.jsonCopyBean(couponPw.get(0), TPromoCouponInnerCodeVO.class);
        }

        TPromoCouponInnerCodeEntity tPromoCouponInnerCodeEntity = tPromoCouponInnerCodeEntities.get(0);
        return BeanCopyUtils.jsonCopyBean(tPromoCouponInnerCodeEntity, TPromoCouponInnerCodeVO.class);

    }

    @Override
    @Transactional
    public int updateCouponToGrantedState(String tenantCode, List<String> couponCodes, String takeLabel,Integer frozenStatus) {

        TPromoCouponInnerCodeEntity entity = new TPromoCouponInnerCodeEntity();

        entity.setStatus(CouponStatusEnum.GRANTED.code());
        entity.setTakeLabel(takeLabel);
        if (frozenStatus==2){
            entity.setFrozenStatus(CouponFrozenStatusEnum.FROZENED.code());
        }

        Example example = new Example(TPromoCouponInnerCodeEntity.class);
        Criteria criteria = example.createCriteria()
            .andEqualTo(TPromoCouponInnerCodeEntity.C_TENANT_CODE, tenantCode);
        if (couponCodes.size() == 1) {
            criteria.andEqualTo(TPromoCouponInnerCodeEntity.C_COUPON_CODE, couponCodes.get(0));
        } else {
            criteria.andIn(TPromoCouponInnerCodeEntity.C_COUPON_CODE, couponCodes);
        }

        return couponInnerCodeMapper.updateByConditionSelective(entity, example);
    }

    @Override
    @Transactional
    public int updateCouponInnerFrozenStatus(String tenantCode, String activityCode) {
        TPromoCouponInnerCodeEntity entity = new TPromoCouponInnerCodeEntity();
        entity.setFrozenStatus(CouponFrozenStatusEnum.FROZENED.code());
        Example example = new Example(TPromoCouponInnerCodeEntity.class);
        example.createCriteria()
            .andEqualTo(TPromoCouponInnerCodeEntity.C_TENANT_CODE, tenantCode)
            .andEqualTo(TPromoCouponInnerCodeEntity.C_ACTIVITY_CODE, activityCode);
        return couponInnerCodeMapper.updateByConditionSelective(entity, example);
    }

    @Override
    public int logicDelete(String tenantCode, String activityCode) {
        TPromoCouponInnerCodeEntity entity = new TPromoCouponInnerCodeEntity();
        entity.setLogicDelete(1);
        entity.setFrozenStatus(CouponFrozenStatusEnum.FROZENED.code());
        Example example = new Example(TPromoCouponInnerCodeEntity.class);
        example.createCriteria()
                .andEqualTo(TPromoCouponInnerCodeEntity.C_TENANT_CODE, tenantCode)
                .andEqualTo(TPromoCouponInnerCodeEntity.C_ACTIVITY_CODE, activityCode);
        return couponInnerCodeMapper.updateByConditionSelective(entity, example);
    }

    @Override
    public TPromoCouponInnerCodeVO findActivityCodeByCouponCode(String tenantCode, String couponCode) {
        TPromoCouponInnerCodeEntity entity = new TPromoCouponInnerCodeEntity();
        entity.setCouponCode(couponCode);
        entity.setTenantCode(tenantCode);
        TPromoCouponInnerCodeEntity result = couponInnerCodeMapper.selectOne(entity);
        return BeanCopyUtils.jsonCopyBean(result,TPromoCouponInnerCodeVO.class);
    }

    @Override
    public int updateInnerCoudeById(TPromoCouponInnerCodeVO innerCodeVO) {

        TPromoCouponInnerCodeEntity convert = BeanCopyUtils.jsonCopyBean(innerCodeVO, TPromoCouponInnerCodeEntity.class);
        return couponInnerCodeMapper.updateByPrimaryKey(convert);
    }

    @Override
    @Transactional
    public int updateInnerCodeByCode(String tenantCode, String promotionCodeOld, String promotionCodeNew) {

        TPromoCouponInnerCodeEntity entity = new TPromoCouponInnerCodeEntity();
        entity.setCouponCode(promotionCodeNew);
        Example example = new Example(TPromoCouponInnerCodeEntity.class);
        example.createCriteria()
            .andEqualTo(TPromoCouponInnerCodeEntity.C_TENANT_CODE, tenantCode)
            .andEqualTo(TPromoCouponInnerCodeEntity.C_COUPON_CODE, promotionCodeOld);
        return couponInnerCodeMapper.updateByConditionSelective(entity, example);
    }

    @Override
    public List<TPromoCouponInnerCodeVO> getCouponInnerCodeByCodes(String tenantCode, List<String> couponCodes) {

        Example example = new Example(TPromoCouponInnerCodeEntity.class);
        example.createCriteria()
            .andEqualTo(TPromoCouponInnerCodeEntity.C_TENANT_CODE, tenantCode)
            .andIn(TPromoCouponInnerCodeEntity.C_COUPON_CODE, couponCodes);
        return BeanCopyUtils.jsonCopyList(couponInnerCodeMapper.selectByCondition(example), TPromoCouponInnerCodeVO.class);
    }

    /**
     * 根据activityCode & releaseCode, 更新对应的券的状态为过期状态
     * 
     * @param releaseCode -- Coupon release code. (Optional)
     */
    @Override
    @Transactional
    public int expireByActivityCode(String tenantCode, String activityCode, String releaseCode) {

        if (StringUtils.isBlank(tenantCode) || StringUtils.isBlank(activityCode)) {
            return 0;
        }

        TPromoCouponInnerCodeEntity entity = new TPromoCouponInnerCodeEntity();
        entity.setStatus(CouponStatusEnum.EXPIRE.code());

        Example example = new Example(TPromoCouponInnerCodeEntity.class);
        Criteria criteria = example.createCriteria();

        criteria.andEqualTo(TPromoCouponCodeUserEntity.C_TENANT_CODE, tenantCode);
        criteria.andEqualTo(TPromoCouponCodeUserEntity.C_ACTIVITY_CODE, activityCode);
        criteria.andEqualTo(TPromoCouponInnerCodeEntity.C_STATUS, CouponStatusEnum.UN_GRANT.code());
        
        if (StringUtils.isNotBlank(releaseCode)) {
            criteria.andEqualTo(TPromoCouponCodeUserEntity.C_RELEASE_CODE, releaseCode);
        }

        return couponInnerCodeMapper.updateByConditionSelective(entity, example);
    }

    /**
     * 更新通用优惠码的状态
     * 
     */
    @Override
    @Transactional
    public int updateCouponStatus(String tenantCode, String activityCode, String couponCode, CouponStatusEnum statusEnum) {

        TPromoCouponInnerCodeEntity entity = new TPromoCouponInnerCodeEntity();
        entity.setStatus(statusEnum.code());

        Example example = new Example(TPromoCouponInnerCodeEntity.class);
        Criteria criteria = example.createCriteria();

        criteria.andEqualTo(TPromoCouponCodeUserEntity.C_TENANT_CODE, tenantCode);
        criteria.andEqualTo(TPromoCouponCodeUserEntity.C_ACTIVITY_CODE, activityCode);
        criteria.andEqualTo(TPromoCouponCodeUserEntity.C_COUPON_CODE, couponCode);

        if (CouponStatusEnum.EXPIRE.equals(statusEnum)) {
            criteria.andIn(TPromoCouponCodeUserEntity.C_STATUS, Arrays.asList(CouponStatusEnum.GRANTED.code(), CouponStatusEnum.UN_GRANT.code()));
        }

        return couponInnerCodeMapper.updateByConditionSelective(entity, example);
    }

    @Override
    @Transactional
    public int updateStatusBatch(String tenantCode, List<String> couponCodes, CouponStatusEnum statusEnum) {

        TPromoCouponInnerCodeEntity entity = new TPromoCouponInnerCodeEntity();
        entity.setStatus(statusEnum.code());
        Example example = new Example(TPromoCouponInnerCodeEntity.class);
        example.createCriteria()
            .andEqualTo(TPromoCouponInnerCodeEntity.C_TENANT_CODE, tenantCode)
            .andIn(TPromoCouponInnerCodeEntity.C_COUPON_CODE, couponCodes);
        return couponInnerCodeMapper.updateByConditionSelective(entity, example);
    }

    @Override
    public List<TPromoCouponInnerCodeVO> queryInnerCouponByReleaseCode(String tenantCode, String activityCode, String releaseCode) {

        TPromoCouponInnerCodeEntity entity = new TPromoCouponInnerCodeEntity();

        entity.setTenantCode(tenantCode);
        entity.setActivityCode(activityCode);
        entity.setReleaseCode(releaseCode);

        return BeanCopyUtils.jsonCopyList(couponInnerCodeMapper.select(entity), TPromoCouponInnerCodeVO.class);
    }

    @Override
    public PageInfo<TPromoCouponInnerCodeVO> selectCouponList(String tenantCode, String activityCode, String couponStatus, Date createTimeStart, Date createTimeEnd,
                    RequestPage page, String couponCode) {
        //分页
        if (page != null) {
            PageHelper.startPage(page.getPageNo(), page.getPageCount());
        }
        Example example = new Example(TPromoCouponInnerCodeEntity.class);
        Criteria criteria = example.createCriteria();
        List<TPromoCouponInnerCodeVO> vos = new ArrayList<>();
        if (StringUtils.isBlank(tenantCode) || StringUtils.isBlank(activityCode)) {
            return new PageInfo<>(vos);
        }
        criteria
            .andEqualTo(TPromoCouponInnerCodeEntity.C_TENANT_CODE, tenantCode)
            .andEqualTo(TPromoCouponInnerCodeEntity.C_ACTIVITY_CODE, activityCode);
        if (StringUtil.isNotBlank(couponStatus)) {
            criteria.andEqualTo(TPromoCouponInnerCodeEntity.C_STATUS, couponStatus);
        }
        if (StringUtil.isNotBlank(couponCode)) {
            criteria.andEqualTo(TPromoCouponInnerCodeEntity.C_COUPON_CODE, couponCode);
        }
        if (null != createTimeStart) {
            criteria.andGreaterThanOrEqualTo(TPromoCouponInnerCodeEntity.C_CREATE_TIME, createTimeStart);
        }
        if (null != createTimeEnd) {
            criteria.andLessThanOrEqualTo(TPromoCouponInnerCodeEntity.C_CREATE_TIME, createTimeEnd);
        }

        List<TPromoCouponInnerCodeEntity> list = couponInnerCodeMapper.selectByCondition(example);
        vos = BeanCopyUtils.jsonCopyList(list, TPromoCouponInnerCodeVO.class);
        PageInfo<TPromoCouponInnerCodeEntity> pageInfo = new PageInfo<>(list);
        PageInfo<TPromoCouponInnerCodeVO> pageInfo2 = new PageInfo<>();
        pageInfo2.setList(vos);
        pageInfo2.setTotal(pageInfo.getTotal());

        return pageInfo2;
    }

    @Override
    public PageInfo<TPromoCouponInnerCodeVO> selectCouponCode(TPromoCouponInnerCodeVO innerCodeVO, String couponStatus,
                    Date createTimeStart, Date createTimeEnd, RequestPage page) { 
    	String tenantCode=innerCodeVO.getTenantCode();
    	 String activityCode=innerCodeVO.getActivityCode();
    	 String couponCode=innerCodeVO.getCouponCode();
    	 String releaseCode=innerCodeVO.getReleaseCode();
        Example example = new Example(TPromoCouponInnerCodeEntity.class);
        Criteria criteria = example.createCriteria();

        criteria.andEqualTo(TPromoCouponInnerCodeEntity.C_TENANT_CODE, tenantCode)
            .andEqualTo(TPromoCouponInnerCodeEntity.C_ACTIVITY_CODE, activityCode);

        //couponCode 不为空
        if (StringUtil.isNotBlank(couponCode)) {
            criteria.andEqualTo(TPromoCouponInnerCodeEntity.C_COUPON_CODE, couponCode);
        }
        //投放编码不为空
        if (StringUtil.isNotBlank(releaseCode)) {
            criteria.andEqualTo(TPromoCouponInnerCodeEntity.C_RELEASE_CODE, releaseCode);
        }
        //券状态
        if (StringUtil.isNotBlank(couponStatus)) {
            criteria.andEqualTo(TPromoCouponInnerCodeEntity.C_STATUS, couponStatus);
        }
        if (null != createTimeEnd) {
            criteria.andLessThanOrEqualTo(TPromoCouponInnerCodeEntity.C_CREATE_TIME, createTimeEnd);
        }
        if (null != createTimeStart) {
            criteria.andGreaterThanOrEqualTo(TPromoCouponInnerCodeEntity.C_CREATE_TIME, createTimeStart);
        }
        //分页
        if (page != null) {
            PageHelper.startPage(page.getPageNo(), page.getPageCount());
        }
        List<TPromoCouponInnerCodeEntity> list = couponInnerCodeMapper.selectByCondition(example);
        List<TPromoCouponInnerCodeVO> vos = BeanCopyUtils.jsonCopyList(list, TPromoCouponInnerCodeVO.class);
        PageInfo<TPromoCouponInnerCodeEntity> pageInfo = new PageInfo<>(list);
        PageInfo<TPromoCouponInnerCodeVO> pageInfo2 = new PageInfo<>();
        pageInfo2.setList(vos);
        pageInfo2.setTotal(pageInfo.getTotal());
        return pageInfo2;
    }

    @Override
    public TPromoCouponInnerCodeVO getCouponByActivityCode(String tenantCode, String activityCode) {

        TPromoCouponInnerCodeEntity entity = new TPromoCouponInnerCodeEntity();

        entity.setTenantCode(tenantCode);
        entity.setActivityCode(activityCode);
        entity.setCouponType(CouponTypeEnum.PROMOTION_CODE.code());

        return BeanCopyUtils.jsonCopyBean(couponInnerCodeMapper.selectOne(entity), TPromoCouponInnerCodeVO.class);
    }

    @Override
    public List<CountCouponCodeModel> countCouponCode(String tenantCode, String activityCode, String releaseCode) {

        return couponInnerCodeMapper.countCouponCode(tenantCode, activityCode, releaseCode);
    }

    @Override
    public int countAnonymityCouponCode(String tenantCode, String activityCode, String releaseCode) {
        Example example = new Example(TPromoCouponInnerCodeEntity.class);
        Criteria criteria = example.createCriteria();

        criteria.andEqualTo(TPromoCouponInnerCodeEntity.C_TENANT_CODE, tenantCode)
                .andEqualTo(TPromoCouponInnerCodeEntity.C_ACTIVITY_CODE, activityCode)
                .andEqualTo(TPromoCouponInnerCodeEntity.C_RELEASE_CODE, releaseCode)
                .andEqualTo(TPromoCouponInnerCodeEntity.C_STATUS, CouponStatusEnum.UN_GRANT.code())
                .andIsNotNull(TPromoCouponInnerCodeEntity.C_VALID_END_TIME);
        return couponInnerCodeMapper.selectCountByCondition(example);
    }

    @Override
    public List<CouponInfoImportOutDTO> selectCouponCodeList(TCouponListQueryDTO tCouponListQueryDTO, Date createTimeStart, Date createTimeEnd) {

        return couponInnerCodeMapper.selectCouponCodeList(tCouponListQueryDTO, createTimeStart, createTimeEnd);
    }

    @Override
    public int updateCouponToLockedState(String tenantCode, String couponCode, String takeLabel) {

        TPromoCouponInnerCodeEntity entity = new TPromoCouponInnerCodeEntity();
        entity.setStatus(CouponStatusEnum.LOCKED.code());
        entity.setTakeLabel(takeLabel);
        Example example = new Example(TPromoCouponInnerCodeEntity.class);
        example.createCriteria()
            .andEqualTo(TPromoCouponInnerCodeEntity.C_TENANT_CODE, tenantCode)
            .andEqualTo(TPromoCouponInnerCodeEntity.C_COUPON_CODE, couponCode);
        return couponInnerCodeMapper.updateByConditionSelective(entity, example);
    }

    @Override
    @Transactional
    public int frozenInnerCode(String tenantCode, String couponCode,Integer frozenStatus,Integer logicDelete) {

        TPromoCouponInnerCodeEntity entity = new TPromoCouponInnerCodeEntity();
        if (frozenStatus==2){
            entity.setFrozenStatus(CouponFrozenStatusEnum.FROZENED.code());
        }else {
            entity.setFrozenStatus(CouponFrozenStatusEnum.UN_FROZEN.code());
        }
        //如果logicDelete为1，则为发错券
        if (logicDelete!= null && logicDelete==1){
            entity.setLogicDelete(1);
        }
        Example example = new Example(TPromoCouponInnerCodeEntity.class);
        example.createCriteria()
            .andEqualTo(TPromoCouponInnerCodeEntity.C_TENANT_CODE, tenantCode)
            .andEqualTo(TPromoCouponInnerCodeEntity.C_COUPON_CODE, couponCode);
        return couponInnerCodeMapper.updateByConditionSelective(entity, example);
    }

    @Override
    public int frozenFindInnerCode(String tenantCode, String couponCode) {
        Example example = new Example(TPromoCouponInnerCodeEntity.class);
        example.createCriteria()
                .andEqualTo(TPromoCouponInnerCodeEntity.C_TENANT_CODE, tenantCode)
                .andEqualTo(TPromoCouponInnerCodeEntity.C_COUPON_CODE, couponCode)
                .andEqualTo(TPromoCouponInnerCodeEntity.C_STATUS,CouponStatusEnum.EXPIRE.code());
        return couponInnerCodeMapper.selectCountByCondition(example);
    }

    @Override
    public List<String> queryHeaderCouponCodes(String tenantCode, String activityCode, String releaseCode, int count) {

        String currentDateAsString = DateUtil.getCurrentDateAsString(DateUtil.FORMAT_YYYYMMDDHHMISS_14);

        return couponInnerCodeMapper.queryHeaderCouponCodes(tenantCode, activityCode, releaseCode, currentDateAsString, currentDateAsString, count);
    }

    @Override
    public int getFrozenAndUnGrantCodeCount111(String releaseCode) {

        TPromoCouponInnerCodeEntity entity = new TPromoCouponInnerCodeEntity();
        entity.setStatus(CouponStatusEnum.UN_GRANT.code());
        entity.setFrozenStatus(CouponFrozenStatusEnum.FROZENED.code());
        entity.setReleaseCode(releaseCode);
        return couponInnerCodeMapper.selectCount(entity);
    }

    @Override
    public PageData<TPromoCouponInnerCodeVO> queryManagementData(ManagementDataInDTO inDTO) {

        Example example = new Example(TPromoCouponInnerCodeEntity.class);

        example.orderBy("id").desc();
        Criteria criteria = example.createCriteria();
        sql.sqlSelectCondition(inDTO, criteria);
        PageHelper.startPage(inDTO.getPageNo(), inDTO.getPageCount());
        List<TPromoCouponInnerCodeEntity> list = couponInnerCodeMapper.selectByCondition(example);
        List<TPromoCouponInnerCodeVO> vos = BeanCopyUtils.jsonCopyList(list, TPromoCouponInnerCodeVO.class);
        PageInfo<TPromoCouponInnerCodeEntity> pageInfo = new PageInfo<>(list);

        return new PageData<>(vos, pageInfo.getTotal());
    }

    @Override
    @Transactional
    public int deleteCouponInnerCode111(String tenantCode, String activityCode) {

        TPromoCouponInnerCodeEntity entity = new TPromoCouponInnerCodeEntity();
        entity.setTenantCode(tenantCode);
        entity.setActivityCode(activityCode);
        return couponInnerCodeMapper.delete(entity);
    }

    @Override
    public List<ExportCouponOutDTO> exportCoupon(ExportCouponInDTO exportCouponInDTO) {
        PageHelper.startPage(1, exportCouponInDTO.getSize(), false);
        Example example = new Example(TPromoCouponInnerCodeEntity.class);
        example.createCriteria()
                .andEqualTo(TPromoCouponInnerCodeEntity.C_TENANT_CODE, exportCouponInDTO.getTenantCode())
                .andEqualTo(TPromoCouponInnerCodeEntity.C_ACTIVITY_CODE, exportCouponInDTO.getActivityCode())
                .andGreaterThan(TPromoCouponInnerCodeEntity.C_ID, exportCouponInDTO.getMaxId());
        example.orderBy(TPromoCouponInnerCodeEntity.C_ID);
        return BeanCopyUtils.jsonCopyList(couponInnerCodeMapper.selectByCondition(example), ExportCouponOutDTO.class);
    }

    @Override
    public int updateInnerCouponEndTime(String tenantCode, String activityCode, String releaseCode, String endTime) {
        TPromoCouponInnerCodeEntity entity = new TPromoCouponInnerCodeEntity();
        entity.setReceiveEndTime(endTime);
        Example example = new Example(TPromoCouponInnerCodeEntity.class);
        example.createCriteria()
                .andEqualTo(TPromoCouponInnerCodeEntity.C_TENANT_CODE, tenantCode)
                .andEqualTo(TPromoCouponInnerCodeEntity.C_ACTIVITY_CODE, activityCode)
                .andEqualTo(TPromoCouponInnerCodeEntity.C_RELEASE_CODE, releaseCode);
        return couponInnerCodeMapper.updateByConditionSelective(entity, example);
    }

    @Override
    public int expireCouponCode() {
        TPromoCouponInnerCodeEntity entity = new TPromoCouponInnerCodeEntity();
        entity.setStatus(CouponStatusEnum.EXPIRE.code());
        Example example = new Example(TPromoCouponInnerCodeEntity.class);
        String now = DateUtil.format(new Date(), DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        example.createCriteria()
                .andIn(TPromoCouponInnerCodeEntity.C_STATUS, Arrays.asList(CouponStatusEnum.UN_GRANT.code(), CouponStatusEnum.GRANTED.code()))
                .andLessThanOrEqualTo(TPromoCouponInnerCodeEntity.C_RECEIVE_END_TIME, now);
        return couponInnerCodeMapper.updateByConditionSelective(entity, example);
    }

    /**
     * 优惠券导出
     */
    @Override
    public PageData<ManagementDataOutDTO> queryCouponActivityListService(ManagementDataInDTO inDTO) {

        PageHelper.offsetPage((inDTO.getPageNo() - 1) * inDTO.getPageCount(), inDTO.getPageCount(), false);
        List<ManagementDataOutDTO> list = couponInnerCodeMapper.queryCouponActivityList(inDTO);

        return new PageData<>(list, (long) list.size());
    }

    /**
     * 优惠码inner表
     */
    @Override
    public PageData<TPromoCouponInnerCodeVO> findCouponCodeData(ManagementDataInDTO inDTO) {

        Example example = new Example(TPromoCouponInnerCodeEntity.class);
        Criteria criteria = example.createCriteria();
        example.orderBy("id").desc();
        sql.sqlSelectCouponCodeCondition(inDTO, criteria);
        PageHelper.startPage(inDTO.getPageNo(), inDTO.getPageCount());
        List<TPromoCouponInnerCodeEntity> byExample = couponInnerCodeMapper.selectByCondition(example);
        PageInfo<TPromoCouponInnerCodeEntity> pageInfo = new PageInfo<>(byExample);

        return new PageData<>(BeanCopyUtils.jsonCopyList(byExample, TPromoCouponInnerCodeVO.class), pageInfo.getTotal());
    }

    @Override
    public Integer getCouponQuantity(CouponQuantityDTO couponQuantityDTO) {
        return couponInnerCodeMapper.getCouponQuantity(couponQuantityDTO);
    }

    @Override
    @Transactional
    public void updateBatchInnerCodeValidTime(List<TPromoCouponInnerCodeVO> vos) {
        if (!CollectionUtils.isEmpty(vos)){
            couponInnerCodeMapper.updateBatchInnerCodeValidTime(vos);
        }
    }

    @Override
    public  List<PromoPassVO>  findCouponCodeByPassword(String tenantCode, String promoPassword) {
        Example example = new Example(TPromoCouponInnerCodeEntity.class);
        example.createCriteria()
                .andEqualTo(TPromoCouponInnerCodeEntity.C_TENANT_CODE, tenantCode)
                .andEqualTo(TPromoCouponInnerCodeEntity.C_PROMO_PW, promoPassword)
                .andEqualTo(TPromoCouponInnerCodeEntity.C_STATUS, CouponStatusEnum.UN_GRANT.code());
        List<TPromoCouponInnerCodeEntity> byExample = couponInnerCodeMapper.selectByCondition(example);
        return  BeanCopyUtils.jsonCopyList(byExample,PromoPassVO.class);
    }

    @Override
    @Transactional
	public void updateBatchInnerCodeByCouponCodes(List<TPromoCouponInnerCodeVO> vos) {

        couponInnerCodeMapper.updateBatchInnerCode(vos);

    }

    @Override
    public List<TPromoCouponInnerCodeVO> queryActivityByCouponCodes(String tenantCode, List<String> couponCodes) {

        Example example = new Example(TPromoCouponInnerCodeEntity.class);
        example.createCriteria()
                .andEqualTo(TPromoCouponInnerCodeEntity.C_TENANT_CODE, tenantCode)
                .andIn(TPromoCouponInnerCodeEntity.C_COUPON_CODE, couponCodes);
        List<TPromoCouponInnerCodeEntity> byExample = couponInnerCodeMapper.selectByCondition(example);
        return  BeanCopyUtils.jsonCopyList(byExample,TPromoCouponInnerCodeVO.class);
    }

    @Override
    public List<CouponInnerRelationVO> exportCouponRelationInfo(ExportCouponRelationDto dto) {

        List<CouponInnerRelationVO> couponInnerRelationVOS = couponInnerCodeMapper.exportCouponRelationInfo(dto);

        return  BeanCopyUtils.jsonCopyList(couponInnerRelationVOS,CouponInnerRelationVO.class);

    }
}
