/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.service.coupon;

import java.util.List;
import java.util.Set;

import com.github.pagehelper.PageInfo;
import com.gtech.commons.page.PageData;
import com.gtech.promotion.code.coupon.CouponStatusEnum;
import com.gtech.promotion.dao.model.coupon.CountCouponCodeModel;
import com.gtech.promotion.dao.model.coupon.QueryUserCouponVO;
import com.gtech.promotion.dao.model.coupon.TPromoCouponCodeUserVO;
import com.gtech.promotion.domain.coupon.CouponDomain;
import com.gtech.promotion.dto.in.activity.ActivityTenantInDTO;
import com.gtech.promotion.dto.in.activity.ManagementDataInDTO;
import com.gtech.promotion.dto.in.coupon.ExportCouponUserDto;
import com.gtech.promotion.dto.in.coupon.QueryCouponUsedInDTO;
import com.gtech.promotion.dto.in.coupon.TCouponCheckAndUseInDTO;
import com.gtech.promotion.page.RequestPage;
import com.gtech.promotion.vo.result.coupon.ExportCouponUserDetailResult;
import com.gtech.promotion.vo.result.coupon.ExportCouponUserResult;

/**
 * 用户相关service层
 */
public interface PromoCouponCodeUserService {

    /**
     * 更新用户优惠券表的使用业务编码和优惠券状态为已锁定
     */
    int updateUsedRedIdByCodes(String tenantCode, List<String> couponCodes, String userCode, String usedRefId, CouponStatusEnum statusEnum);

    /**
     * 根据业务编码获取列表
     * 
     * @param usedRefId 业务编号（订单编号）
     */
    List<TPromoCouponCodeUserVO> getCodeUserByUsedRefId(String usedRefId, String tenantCode);

    /**
     * 根据使用的业务编号清空业务编号（订单编号）
     * 
     * @param usedRefId 业务编号（订单编号）
     */
    void clearUsedRefIdByUsedRefId(String usedRefId, String status);

    /***
     * 根据用户ID 商户编码和状态分页获取优惠券列表
     */
    PageData<CouponDomain> getUserCouponByUserId(TPromoCouponCodeUserVO couponCodeUserVO, RequestPage requestPage,List<String> opsList,String beginTime,String endTime,Integer sort);


    PageData<CouponDomain> queryUserCouponByUserId(TPromoCouponCodeUserVO couponCodeUserVO, QueryUserCouponVO queryUserCouponVO);


        /**
         * 根据活动id获取用户拥有的有效券码
         */
    List<TPromoCouponCodeUserVO> getMyCodesByActivityCodes(String userCode, String tenantCode, Set<String> activityCodes,List<String> opsTypeList);

    /**
     * 根据投放id 获得该批次被领取的数量
     */
    int countByReleaseCode(String tenantCode, String activityCode, String releaseCode);

    /**
     * 批量更新优惠券状态
     */
    int updateStatusBatch(String tenantCode, List<String> couponCodes, CouponStatusEnum statusEnum);

    /**
     * 更新通用优惠码的状态
     * 
     * @param usedRefId -- order number(Optional)
     */
    int updateCouponStatus(String tenantCode, String activityCode, String couponCode, CouponStatusEnum statusEnum, String usedRefId);

    /**
     * 根据activityCode & releaseCode, 更新对应的券的状态为过期状态
     * 
     * @param releaseCode -- Coupon release code. (Optional)
     */
    int expireByActivityCode(String tenantCode, String activityCode, String releaseCode);

    /**
     * 将券的状态改为冻结状态
     */
    int updateCouponUserFrozenStatus(String tenantCode, String activityCode);

    /**
     * 获取用户的某一张券码详情
     */
    TPromoCouponCodeUserVO getUserCouponInfo(String tenantCode, String couponCode, String userCode);

    /**
     * 查询 用户 通用优惠码信息 作为导出使用
     */
    List<TPromoCouponCodeUserVO> getUserCouponInfos(String tenantCode, String activityCode, String couponCode, String status);

    /**
     * 获取 通用优惠码 不输入状态查询所有
     */
    PageInfo<TPromoCouponCodeUserVO> getUserCouponCode(String tenantCode, String activityCode, String status, String couponCode, RequestPage page);

    /**
     * 获取用户当前活动领取次数
     */
    int getUserCouponCountByActivityCode(String tenantCode, String activityCode, String userCode);

    /**
     * 获取用户当前活动使用次数
     */
    int getUserUsedCouponCountByActivityCode(String tenantCode, String activityCode, String userCode);

    /**
     * 领取优惠券，插入数据
     */
    int insert(List<TPromoCouponCodeUserVO> couponCodeUserVO);

    /**
     * 订单取消时，删除匿名优惠券绑定的用户
     */
    int deleteCodeUserByUserId(String userId);

    /**
     * 根据 couponStatus 分类统计指定的 activityCode & releaseCode 的券数量, releaseCode为空时统计 activityCode 对应的数据
     */
    List<CountCouponCodeModel> countCouponCode(String tenantCode, String activityCode, String releaseCode);

    /**
     * 查询领取未结束的投放批次
     */
    List<TPromoCouponCodeUserVO> getCouponCodeUserVO(String activityCode, String tenantCode, String releaseCode);

    /**
     * 冻结用户领取券码
     */
    int frozenCouponCodeUser(String tenantCode, String couponCode,Integer frozenStatus,Integer logicDelete);

    /**
     * @Description 获取昨天领取的券数量和使用的券数量
     **/
    Integer getAllocateCouponCountYesterday111(String tenantCode, String activityCode);

    /**
     * @Description 获取昨天使用的券数量和使用的券数量
     * @Param [key, id]
     **/
    Integer getUseCouponCountYesterday111(String tenantCode, String activityCode);

    /**
     * @Description 获取今天领取的券数量和使用的券数量
     * @Param [tenantCode, activityCode]
     **/
    Integer getAllocateCouponCountToday111(String tenantCode, String activityCode);

    /**
     * @Description 获取今天使用的券数量和使用的券数量
     * @Param [key, activityCode]
     **/
    Integer getUseCouponCountToday111(String tenantCode, String activityCode);

    /**
     * 已使用的优惠券总量
     */
    long usedCouponAmount(List<String> tenantCodes);

    /**
     * 已使用的优惠券总量
     */
    long usedCouponAmount(String startTime, String endTime, List<String> tenantCodes);

    /**
     * 领取总数
     */
    int getReceivedCouponAmount(ActivityTenantInDTO tenantInDTO);

    /**
     * 使用总数
     */
    int getUsedCouponAmount(ActivityTenantInDTO tenantInDTO);

    /**
     * 根据券码获取该对象
     */
    TPromoCouponCodeUserVO getUserCouponCode(String tenantCode, String couponCode);

    /**
     * 券码统一管理
     */
    PageData<TPromoCouponCodeUserVO> queryManagementUserData(ManagementDataInDTO inDTO);

    /**
     * 优惠码管理
     */
    PageData<TPromoCouponCodeUserVO> queryManagementCouponCodeData(ManagementDataInDTO inDTO);

    /**
     * 根据查询条件得到对应用户券码
     */
    TPromoCouponCodeUserVO findUserByCouponCode(TCouponCheckAndUseInDTO checkAndUseInDTO);

    /**
     * 核销券码
     */
    int updateUserByCouponCode(TCouponCheckAndUseInDTO checkAndUseInDTO);

    /**
     * 根据优惠码更新优惠码
     */
    int updatePromotionCode(String tenantCode, String promotionCodeOld, String promotionCodeNew);

    int updateCouponEndTime(String tenantCode, String activityCode, String releaseCode, String endTime);

    int expireCouponCode();

    int findCouponUserByActivityCode(String tenantCode,String activityCode);

    List<ExportCouponUserResult> exportCouponUserCode(ExportCouponUserDto dto);

    List<ExportCouponUserDetailResult> exportCouponOrderCode(ExportCouponUserDto dto);

    int logicDelete(String tenantCode, String activityCode);

    /**
     * 查询用户使用券记录
     * @param dto
     * @return
     */
    PageData<TPromoCouponCodeUserVO> queryUsedCouponListService(QueryCouponUsedInDTO dto);


    /**
     * 更新券码
     * @param userVO
     * @return
     */
    void updateCouponUserByCode(List<TPromoCouponCodeUserVO> userVO);


    /**
     * 查询券码
     * @param tenantCode
     * @param couponCode
     * @return
     */
    List<TPromoCouponCodeUserVO> queryUserCouponInfo(String tenantCode, List<String> couponCode);

	/**
	 * 查询指定券状态的数量
	 * 
	 * @param tenantCode
	 * @param activityCode
	 * @param statusList
	 * @return
	 */
	Integer queryCouponCountByCodeAndStataus(String tenantCode, String activityCode, List<String> statusList);
}
