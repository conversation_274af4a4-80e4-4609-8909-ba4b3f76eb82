package com.gtech.promotion.service.marketing.impl;

import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.dao.entity.marketing.HelpRecordEntity;
import com.gtech.promotion.dao.mapper.marketing.HelpRecordMapper;
import com.gtech.promotion.dao.model.marketing.HelpRecordModel;
import com.gtech.promotion.service.marketing.HelpRecordService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

@Service
public class HelpRecordServiceImpl extends BaseServiceImpl<HelpRecordEntity, HelpRecordModel> implements HelpRecordService {
    @Autowired
    private HelpRecordMapper helpRecordMapper;


    public HelpRecordServiceImpl() {
        super(HelpRecordEntity.class, HelpRecordModel.class);
    }

    @Override
    public List<HelpRecordEntity> checkMemberHelpRecord(String sharingRecordCode, String memberCode) {

        Example example = new Example(HelpRecordEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("sharingRecordCode", sharingRecordCode);
        criteria.andEqualTo("helpMemberCode", memberCode);
        return helpRecordMapper.selectByCondition(example);
    }

    @Override
    public List<HelpRecordEntity> queryHelpRecord(HelpRecordModel helpRecordModel, List<String> sharingRecordCodeList) {
        Example example = new Example(HelpRecordEntity.class);
        Example.Criteria criteria = example.createCriteria()
                .andEqualTo("activityCode", helpRecordModel.getActivityCode())
                .andEqualTo("domainCode", helpRecordModel.getDomainCode())
                .andEqualTo("tenantCode", helpRecordModel.getTenantCode())
                .andEqualTo("orgCode", helpRecordModel.getOrgCode());

        if (StringUtil.isNotEmpty(helpRecordModel.getSharingMemberCode())) {
            criteria .andEqualTo("sharingMemberCode", helpRecordModel.getSharingMemberCode());
        }
        if (StringUtil.isNotEmpty(helpRecordModel.getHelpMemberCode())) {
            criteria .andEqualTo("helpMemberCode", helpRecordModel.getHelpMemberCode());
        }
        if (CollectionUtils.isNotEmpty(sharingRecordCodeList)) {
            criteria.andIn("sharingRecordCode", sharingRecordCodeList);
        }

        return helpRecordMapper.selectByCondition(example);
    }
}
