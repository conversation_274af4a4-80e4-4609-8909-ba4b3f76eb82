package com.gtech.promotion.service.purchaseconstraint;

import com.gtech.promotion.dao.entity.purchaseconstraint.PurchaseConstraintCustomer;
import com.gtech.promotion.dao.entity.purchaseconstraint.PurchaseConstraintDetail;
import com.gtech.promotion.dao.model.purchaseconstraint.PcRuleCalculateModel;
import com.gtech.promotion.dao.model.purchaseconstraint.PurchaseConstraintCustomerListMode;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

public interface PurchaseConstraintCustomerService {


    void insert(PurchaseConstraintCustomer constraintCustomer);

    void insert(List<PurchaseConstraintCustomer> constraintCustomer);

    int increment(List<PurchaseConstraintCustomer> customers);

    int decrement(PurchaseConstraintCustomer purchaseConstraintCustomer);

    List<PurchaseConstraintCustomer> list(PurchaseConstraintCustomerListMode mode);

    @Transactional(rollbackFor = Exception.class)
    void decrement(PcRuleCalculateModel model, List<PurchaseConstraintDetail> list, Map<String, PcRuleCalculateModel.IncrementProduct> skuMap);

    int delete(List<PurchaseConstraintCustomer> customers);
}
