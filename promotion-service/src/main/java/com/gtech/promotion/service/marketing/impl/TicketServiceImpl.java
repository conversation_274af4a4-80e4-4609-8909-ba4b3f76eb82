package com.gtech.promotion.service.marketing.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.gtech.commons.code.LogicDeleteEnum;
import com.gtech.commons.page.PageData;
import com.gtech.commons.page.PageParam;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.code.coupon.CouponFrozenStatusEnum;
import com.gtech.promotion.code.marketing.TicketStatusEnum;
import com.gtech.promotion.dao.entity.marketing.BaseEntity;
import com.gtech.promotion.dao.entity.marketing.TicketEntity;
import com.gtech.promotion.dao.mapper.marketing.TicketMapper;
import com.gtech.promotion.dao.model.marketing.BaseModel;
import com.gtech.promotion.dao.model.marketing.TicketModel;
import com.gtech.promotion.domain.marketing.LuckyDrawMemberChanceDomain;
import com.gtech.promotion.service.marketing.TicketService;
import com.gtech.promotion.vo.result.marketing.LuckyDrawMemberChanceResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.List;

@Service
public class TicketServiceImpl extends BaseServiceImpl<TicketEntity, TicketModel> implements TicketService {

    @Autowired
    private TicketMapper ticketMapper;

    public TicketServiceImpl() {
        super(TicketEntity.class, TicketModel.class);
    }

    @Override
    public List<TicketModel> selectListByMemberCode(BaseModel baseModel, String memberCode, String status) {
        Example example = new Example(TicketEntity.class);
        example.createCriteria().andEqualTo(BaseEntity.C_DOMAIN_CODE, baseModel.getDomainCode())
                .andEqualTo(BaseEntity.C_TENANT_CODE, baseModel.getTenantCode())
                .andEqualTo(BaseEntity.C_ACTIVITY_CODE, baseModel.getActivityCode())
                .andEqualTo(TicketEntity.C_MEMBER_CODE, memberCode)
                //未冻结
                .andEqualTo(TicketEntity.C_FROZEN_STATUS, CouponFrozenStatusEnum.UN_FROZEN.code())
                .andEqualTo(TicketEntity.C_STATUS, status)
                .andEqualTo(TicketEntity.C_LOGIC_DELETE, LogicDeleteEnum.NORMAL.number());
        return BeanCopyUtils.jsonCopyList(ticketMapper.selectByCondition(example), TicketModel.class);
    }

    @Override
    public PageData<LuckyDrawMemberChanceResult> queryChanceList(LuckyDrawMemberChanceDomain domain) {
        PageHelper.startPage(domain.getPageNum(), domain.getPageSize());
        PageInfo<LuckyDrawMemberChanceResult> results = PageInfo.of(ticketMapper.queryChanceList(domain));
        return new PageData<>(results.getList(), results.getTotal());
    }

    @Override
    public PageData<TicketModel> queryListByStatus(TicketModel ticketModel, PageParam pageParam) {
        PageHelper.startPage(pageParam.getPageNum(), pageParam.getPageSize());

        Example example = new Example(TicketEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(BaseEntity.C_DOMAIN_CODE, ticketModel.getDomainCode())
                .andEqualTo(BaseEntity.C_TENANT_CODE, ticketModel.getTenantCode())
                .andEqualTo(BaseEntity.C_ORG_CODE, ticketModel.getOrgCode())
                .andEqualTo(BaseEntity.C_ACTIVITY_CODE, ticketModel.getActivityCode())
                .andEqualTo(TicketEntity.C_MEMBER_CODE, ticketModel.getMemberCode())
                .andEqualTo(TicketEntity.C_LOGIC_DELETE, LogicDeleteEnum.NORMAL.number());
        if (StringUtil.isNotBlank(ticketModel.getStatus())){
            criteria.andEqualTo(TicketEntity.C_STATUS, ticketModel.getStatus());
        }else {
            List<String> list = new ArrayList<>();
            list.add(TicketStatusEnum.USED.code());
            list.add(TicketStatusEnum.LUCKY.code());
            criteria.andIn(TicketEntity.C_STATUS, list);
        }

        example.orderBy(TicketEntity.C_USE_TIME).desc();
        PageInfo<TicketEntity> results = PageInfo.of(ticketMapper.selectByCondition(example));
        return new PageData<>(BeanCopyUtils.jsonCopyList(results.getList(), TicketModel.class), results.getTotal());
    }

    @Override
    public int updateByTicketCode(TicketModel ticketModel, String ticketCode) {
        TicketEntity ticketEntity = BeanCopyUtils.jsonCopyBean(ticketModel, TicketEntity.class);
        Example example = new Example(TicketEntity.class);
        example.createCriteria().andEqualTo(TicketEntity.C_TICKET_CODE, ticketCode);
        return ticketMapper.updateByConditionSelective(ticketEntity, example);
    }

    @Override
    public Integer selectCountByMemberCode(BaseModel baseModel, String memberCode, String beginTime, String endTime) {

        return ticketMapper.selectCountByMemberCode(baseModel.getActivityCode(), baseModel.getDomainCode(), baseModel.getTenantCode(), memberCode, beginTime, endTime);
    }

    @Override
    public int selectActivityByStatus(BaseModel ticketModel,String status) {
        TicketEntity entity = new TicketEntity();
        entity.setDomainCode(ticketModel.getDomainCode());
        entity.setTenantCode(ticketModel.getTenantCode());
        entity.setActivityCode(ticketModel.getActivityCode());
        entity.setStatus(status);
        entity.setLogicDelete(LogicDeleteEnum.NORMAL.number());
        return ticketMapper.selectCount(entity);
    }
    @Override
    public void updateFrozenStatus(LuckyDrawMemberChanceDomain domain) {

        Example selectExample = new Example(TicketEntity.class);
        Example.Criteria criteria = selectExample.createCriteria()
                .andEqualTo("domainCode", domain.getDomainCode())
                .andEqualTo("tenantCode", domain.getTenantCode())
                .andEqualTo("activityCode", domain.getActivityCode())
                .andEqualTo("memberCode", domain.getMemberCode())
                .andEqualTo("status", TicketStatusEnum.NO_USE.code());
        if (CouponFrozenStatusEnum.FROZENED.code().equals(domain.getFrozenStatus())){
            criteria.andEqualTo("frozenStatus", CouponFrozenStatusEnum.UN_FROZEN.code());
        } else {
            criteria.andEqualTo("frozenStatus", CouponFrozenStatusEnum.FROZENED.code());
        }

        List<TicketEntity> ticketEntities = ticketMapper.selectByCondition(selectExample);
        if (CollectionUtils.isEmpty(ticketEntities)){
            return;
        }

        ArrayList<String> ticketCodes = new ArrayList<>();
        int i = domain.getQuality();
        for (TicketEntity ticketEntity : ticketEntities) {
            if (i <= 0){
                break;
            }
            ticketCodes.add(ticketEntity.getTicketCode());
            i --;
        }
        if (CollectionUtils.isEmpty(ticketCodes)){
            return;
        }

        TicketEntity ticketEntity = new TicketEntity();
        ticketEntity.setFrozenStatus(domain.getFrozenStatus());
        if (null != domain.getLogicDelete() && LogicDeleteEnum.DELETED.equalsCode(domain.getLogicDelete())){
            ticketEntity.setLogicDelete(LogicDeleteEnum.DELETED.number());
        }
        criteria.andIn("ticketCode",ticketCodes);

        ticketMapper.updateByConditionSelective(ticketEntity,selectExample);

    }
}
