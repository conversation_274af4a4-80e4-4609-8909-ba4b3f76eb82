package com.gtech.promotion.service.marketing;

import com.gtech.promotion.dao.entity.marketing.MarketingLanguageEntity;
import com.gtech.promotion.dao.model.marketing.MarketingLanguageModel;

import java.util.List;

public interface MarketingLanguageService extends BaseService<MarketingLanguageEntity, MarketingLanguageModel> {
    MarketingLanguageModel findByLanguage(String activityCode, String language);

    List<MarketingLanguageModel> getLanguagesByActivityCodes(String tenantCode, List<String> activityCodes);
}
