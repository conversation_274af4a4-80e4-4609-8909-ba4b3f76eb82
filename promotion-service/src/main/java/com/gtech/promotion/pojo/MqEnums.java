package com.gtech.promotion.pojo;

public enum MqEnums {

	EXCEL_REQUEST_MQ("titan_filecloud_imports_flashsale", "flashsale_filecloud_request_cosumer", "flashsale_filecloud_request_producer"),
	EXCEL_RESULT_MQ("titan_filecloud_imports_result", "flashsale_filecloud_response_cosumer", "flashsale_filecloud_response_producer"),
	ORDER_FLINK_MQ("order_flink", "order_flink_promotion_consumer", "order_flink_promotion_producer"),

	MARKETING_GROUP_MQ("titan_marketing_group", "titan_marketing_group_consumer", "titan_marketing_group_producer"),
	MARKETING_SHARING_MQ("titan_marketing_sharing", "titan_marketing_sharing_consumer", "titan_marketing_sharing_producer"),

	;

	private String topicName;
	private String consumerGroupName;
	private String producerGroupName;

	MqEnums(String topicName, String consumerGroupName, String producerGroupName) {
		this.topicName = topicName;
		this.consumerGroupName = consumerGroupName;
		this.producerGroupName = producerGroupName;
    }

	public static MqEnums getByCode(String code) {
		for (MqEnums value : MqEnums.values()) {
			if (value.topicName.equals(code)) {
                return  value;
            }
        }
        return null;
    }

	public String getTopicName() {
		return topicName;
	}

	public String getConsumerGroupName() {
		return consumerGroupName;
	}

	public String getProducerGroupName() {
		return producerGroupName;
	}

}
