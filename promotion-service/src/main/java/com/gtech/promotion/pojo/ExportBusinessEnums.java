package com.gtech.promotion.pojo;

/**
 * 导出业务
 */
public enum ExportBusinessEnums {

    SKU_IMPORT_FLASH_SALE("promotion.flashsale", "限时秒杀上传商品"),
    BUSINESS_PRODUCT("pim.product", "商品导出"),
    BUSINESS_SKU("pim.sku", "商品导出"),

    BUSINESS_IMG("static.pim.img", "商品图片导入");

    private String code;
    private String name;

    ExportBusinessEnums(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static ExportBusinessEnums getByCode(String code) {
        for (ExportBusinessEnums value : ExportBusinessEnums.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static ExportBusinessEnums getByName(String name) {
        for (ExportBusinessEnums value : ExportBusinessEnums.values()) {
            if (value.name().equalsIgnoreCase(name)) {
                return value;
            }
        }
        return null;
    }

    public static boolean contains(String code) {
        return getByCode(code) != null;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
		return name;
	}
}
