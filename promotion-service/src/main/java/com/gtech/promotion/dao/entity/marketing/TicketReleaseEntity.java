package com.gtech.promotion.dao.entity.marketing;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.persistence.Table;

@EqualsAndHashCode(callSuper = true)
@Table(name = "MARKETING_TICKET_RELEASE")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TicketReleaseEntity extends BaseEntity {

    private String activityCode;
    private String releaseCode;
    private Long quality;
    private Long inventory;
    private Long used;

}
