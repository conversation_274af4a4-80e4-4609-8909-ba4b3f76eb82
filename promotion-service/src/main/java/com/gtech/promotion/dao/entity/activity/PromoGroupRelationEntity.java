package com.gtech.promotion.dao.entity.activity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.Date;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/7/12 10:09
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "promo_group_relation")
public class PromoGroupRelationEntity {

    @Id
    @Column(name = "ID")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 租户编码
     */
    @Column(name = "TENANT_CODE")
    private String tenantCode;

    @Column(name = "DOMAIN_CODE")
    private String domainCode;

    /**
     * 分组编码A
     */
    @Column(name = "GROUP_CODE_A")
    private String groupCodeA;

    /**
     * 分组编码A
     */
    @Column(name = "GROUP_CODE_B")
    private String groupCodeB;


    @Column(name="create_user")
    private String createUser;

    // Create time.
    @Column(name="create_time")
    private Date createTime;

    // Lastest update user.
    @Column(name="update_user")
    private String updateUser;

    // Lastest update time.
    @Column(name="update_time")
    private Date updateTime;

    @Column(name="LOGIC_DELETE")
    private Integer logicDelete;

}
