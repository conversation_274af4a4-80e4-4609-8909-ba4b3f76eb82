package com.gtech.promotion.dao.model.coupon;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/12/22 15:00
 */
@Getter
@Setter
@ToString
public class CouponInnerRelationVO implements Serializable {

    private static final long serialVersionUID = -7116220338461581425L;


    private String maxCouponCode;

    // Tenant code
    private String tenantCode;

    // Activity code
    private String activityCode;

    // 券投放ID
    private String releaseCode;

    // 券码
    private String couponCode;

    // 券类型：01-优惠券 02-匿名券 03-优惠码
    private String couponType;

    // 状态：01-未发放 02-已发放 03-已使用 04-已锁定 05-已过期
    private String status;


    // 领取开始时间(yyyyMMddhhmmss)
    private String receiveStartTime;

    // 领取结束时间(yyyyMMddhhmmss)
    private String receiveEndTime;


    // 可用开始时间(yyyyMMddhhmmss)
    private String validStartTime;

    // 可用结束时间(yyyyMMddhhmmss)
    private String validEndTime;
}
