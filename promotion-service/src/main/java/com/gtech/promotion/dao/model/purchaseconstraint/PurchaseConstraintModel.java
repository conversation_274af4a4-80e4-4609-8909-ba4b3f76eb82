package com.gtech.promotion.dao.model.purchaseconstraint;

import com.gtech.promotion.code.PromotionConstants;
import com.gtech.promotion.code.activity.ActivityStatusEnum;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

@Data
@ToString
public class PurchaseConstraintModel implements Serializable {
    /**
     * 主键
     */

    private Long id;

    // DOMAIN code
    private String domainCode;

    /**
     * 租户号
     */
    private String tenantCode;

    /**
     * 组织编号
     */
    private String orgCode;

    /**
     * 限购Code
     */
    private String purchaseConstraintCode;

    /**
     * 限购名称
     */
    private String purchaseConstraintName;

    /**
     * 限购开始时间
     */
    private Date purchaseConstraintStartTime;

    /**
     * 限购结束时间
     */
    private Date purchaseConstraintEndTime;

    /**
     * 状态
     * @see com.gtech.promotion.code.purchaseconstraint.PurchaseConstraintStatusEnum
     */
    private String purchaseConstraintStatus;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 更新人
     */
    private String updateUser;

    /**
     * 周期类型：00-全时段 01-自定义
     */
    private String periodType;

    /**
     * 商品范围正反选：01-正选；02-反选
     */
    private String productSelectionType;

    /**
     * Product item scope type: 1-All scope 2-By spu in scope
     */
    private Integer itemScopeType;

    /**
     * 店铺范围：00-全店铺 01-自定义
     */
    private String storeType;

    /**
     * 审核人
     */
    private String auditUser;

    /**
     * 是否优先购买  0 否 1 是
     */
    private Integer firstRefusal;

    /**
     * 描述
     */
    private String description;

    /**
     * 优先级
     */
    private Integer priority;

    /**
     * 高级价格限购标识，默认0，非高级价格限购
     */
    private Integer priceSetting;

    private String customCondition;
    private String customRule;


    public Integer getPriority() {

        return priority == null ? PromotionConstants.DEF_PRIORITY : priority;
    }

    /**
     * 是否有效限购(验证有效期、活动状态)
     */
    public boolean isValid(Date date) {
        if(null == purchaseConstraintEndTime || null == purchaseConstraintStartTime){
            return ActivityStatusEnum.EFFECTIVE.equalsCode(this.purchaseConstraintStatus);
        }else {
            return ActivityStatusEnum.EFFECTIVE.equalsCode(this.purchaseConstraintStatus)
                    && purchaseConstraintStartTime.getTime() <= date.getTime()
                    && purchaseConstraintEndTime.getTime() > date.getTime();
        }
    }

    /**
     * 判断是否需要做过期处理
     */
    public boolean isNeedToDoExpire() {
        return null != purchaseConstraintEndTime
                && ActivityStatusEnum.EFFECTIVE.equalsCode(this.purchaseConstraintStatus)
                && purchaseConstraintEndTime.getTime() <= System.currentTimeMillis();
    }
}
