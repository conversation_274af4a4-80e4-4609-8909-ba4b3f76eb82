package com.gtech.promotion.dao.entity.marketing.flashsale;

import com.gtech.promotion.dao.entity.marketing.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.persistence.Table;
import java.math.BigDecimal;

@EqualsAndHashCode(callSuper = true)
@Table(name = "flash_sale_order")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FlashSaleOrderEntity extends BaseEntity {

    public static final String ORDER_ID = "orderId";

    public static final String MARKETING_GROUP_CODE =  "marketingGroupCode";

    public static final String MEMBER_CODE =  "memberCode";

    public static final String ORDER_STATUS =  "orderStatus";


    private String activityCode;
    private String orderId;
    private String memberCode;
    private BigDecimal promoAmount;
    private String orderStatus;
    private String marketingGroupCode;

}
