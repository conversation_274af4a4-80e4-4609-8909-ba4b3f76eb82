package com.gtech.promotion.dao.entity.activity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "promo_activity_period")
public class ActivityPeriodEntity {
    public static final String C_ID = "id";
    public static final String C_ACTIVITY_CODE = "activityCode";
    public static final String C_TENANT_CODE = "tenantCode";

    // ID
    @Id
    @Column(name = "ID")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "ACTIVITY_CODE")
    private String activityCode;

    private String domainCode;

    private String tenantCode;

    private String orgCode;

    private String beginPeriod;

    private String endPeriod;

    private Integer intervalWeek;

    // Create user code.
    @Column(name="CREATE_USER")
    private String createUser;

    // Create time.
    @Column(name="CREATE_TIME")
    private Date createTime;

    // Lastest update user.
    @Column(name="UPDATE_USER")
    private String updateUser;

    // Lastest update time.
    @Column(name="UPDATE_TIME")
    private Date updateTime;

    @Column(name="LOGIC_DELETE")
    private Integer logicDelete;
}
