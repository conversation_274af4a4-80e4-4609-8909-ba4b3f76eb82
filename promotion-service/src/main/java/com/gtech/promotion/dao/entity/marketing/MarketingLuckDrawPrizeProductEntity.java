package com.gtech.promotion.dao.entity.marketing;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

@Table(name = "MARKETING_LUCKY_DRAW_PRIZE_PRODUCT")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MarketingLuckDrawPrizeProductEntity  {

    @Id
    @Column(name = "id")
    private Long id;
    @Column(name = "domain_code")
    private String domainCode;
    @Column(name = "tenant_code")
    private String tenantCode;
    @Column(name = "org_code")
    private String orgCode;
    @Column(name = "activity_code")
    private String activityCode;

    @Column(name = "lucky_draw_prize_product_code")
    private String luckyDrawPrizeProductCode;
    @Column(name = "member_code")
    private String memberCode;

    @Column(name = "product_code")
    private String productCode;

    @Column(name = "`status`")
    private String status;

    @Column(name = "order_id")
    private String orderId;

}
