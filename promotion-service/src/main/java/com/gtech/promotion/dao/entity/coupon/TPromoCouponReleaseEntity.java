/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WA<PERSON><PERSON>NTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dao.entity.coupon;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.Date;

/**
 * 促销券码投放表
 * 
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "promo_coupon_release")
public class TPromoCouponReleaseEntity {

    public static final String C_ID = "id";
    public static final String C_TENANT_CODE = "tenantCode";
    public static final String C_RELEASE_CODE = "releaseCode";
    public static final String C_ACTIVITY_CODE = "activityCode";
    public static final String C_RELEASE_STATUS = "releaseStatus";
    public static final String C_RELEASE_TYPE = "releaseType";
    public static final String C_RELEASE_TIME = "releaseTime";
    public static final String C_RECEIVE_END_TIME = "receiveEndTime";
    public static final String C_RECEIVE_START_TIME = "receiveStartTime";

    // ID
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    // Tenant code
    @Column(name = "TENANT_CODE")
    private String tenantCode;

    // Activity code
    @Column(name = "ACTIVITY_CODE")
    private String activityCode;

    // 券类型：01：优惠券 02:匿名券 03：优惠码
    @Column(name = "COUPON_TYPE")
    private String couponType;

    // 投放编码
    @Column(name = "RELEASE_CODE")
    private String releaseCode;
    
    // 投放状态: 01-未生成券码 02-已生成券码 03-外部导入 04-中止投放
    @Column(name = "RELEASE_STATUS")
    private String releaseStatus;

    // 投放数量
    @Column(name = "RELEASE_QUANTITY")
    private String releaseQuantity;

    // 可领取券码数量
    @Column(name = "INVENTORY")
    private String inventory;

    // 投放来源：01-系统生成 02:-外部导入
    @Column(name = "RELEASE_SOURCE")
    private String releaseSource;

    // 投放类型: 01-即时投放 02-预约投放
    @Column(name = "RELEASE_TYPE")
    private String releaseType;

    // 投放时间
    @Column(name = "RELEASE_TIME")
    private String releaseTime;

    // 领取开始时间(yyyyMMddhhmmss)
    @Column(name = "RECEIVE_START_TIME")
    private String receiveStartTime;

    // 领取结束时间(yyyyMMddhhmmss)
    @Column(name = "RECEIVE_END_TIME")
    private String receiveEndTime;

    // 可用开始时间(yyyyMMddhhmmss)
    @Column(name = "VALID_START_TIME")
    private String validStartTime;

    // 可用结束时间(yyyyMMddhhmmss)
    @Column(name = "VALID_END_TIME")
    private String validEndTime;

    // 定长使用时效(天)
    @Column(name = "VALID_DAYS")
    private Integer validDays;

    private String couponCodePrefix;

    // 时间同步活动时间 0-不同步 1-同步
    private String timeSameActivity;

    // Create user code.
    @Column(name="create_user")
    private String createUser;

    // Create time.
    @Column(name="create_time")
    private Date createTime;

    // Lastest update user.
    @Column(name="update_user")
    private String updateUser;

    // Lastest update time.
    @Column(name="update_time")
    private Date updateTime;

    // Logic delete. (0-normal 1-deleted)
    @Column(name="logic_delete")
    private Integer logicDelete;

    @Column(name = "RECEIVE_TIME_SAME_ACTIVITY")
    private String receiveTimeSameActivity;
}