package com.gtech.promotion.dao.model.marketing.flashsale;

import com.gtech.promotion.dao.model.marketing.BaseModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FlashSaleProductModel extends BaseModel {

    private String skuCode;
    private String skuName;
    private Integer skuQuota;
    private Integer skuInventory;
    private Integer maxPerUser;
    private BigDecimal listPrice;
    private BigDecimal salePrice;
    private BigDecimal flashPrice;
    private BigDecimal groupLeaderPrice;
    private Integer limitFlag;
    private String productCode;
    private String spuName;
    private String extendParam;

    //1 有限制 0 无限制
    private Integer maxPerUserFlag;




}
