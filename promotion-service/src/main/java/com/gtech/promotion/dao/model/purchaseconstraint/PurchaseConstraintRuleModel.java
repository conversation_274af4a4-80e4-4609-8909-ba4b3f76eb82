package com.gtech.promotion.dao.model.purchaseconstraint;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

@Data
@ToString
public class PurchaseConstraintRuleModel implements Serializable {
    private Long id;

    /**
     * 租户编码
     */
    private String tenantCode;

    /**
     * 系统域编码，系统拥有者编码
     */
    private String domainCode;

    /**
     * 组织编号
     */
    private String orgCode;

    /**
     * 限购条件编码
     */
    private String purchaseConstraintCode;

    /**
     * 限购条件类型
     * 0:CUSTOMER_MAX_QTY_PER_PRODUCT,1:CUSTOMER_MAX_QTY_ALL_PRODUCTS,2:CUSTOMER_MAX_AMOUNT,3:ORDER_MAX_QTY_PER_SKU,
     * 4:ORDER_MAX_QUANTITY,5:ORDER_MAX_AMOUNT_PER_SKU,6:ORDER_MAX_AMOUNT
     */
    private Integer purchaseConstraintRuleType;

    /**
     * 限购条件值 可能是金额也可能是数量
     */
    private String purchaseConstraintValue;

    /**
     * 限购条件统计时间类型  1: YEARLY, 2:MONTHLY, 3:WEEKLY
     */
    private Integer purchaseConstraintRuleTimeType;

    /**
     * 限购条件统计时间值 YEARLY:MMdd, MONTHLY: dd, WEEKLY:0-6
     */
    private String purchaseConstraintRuleTimeValue;

    private Date createTime;

    private Date updateTime;
}
