package com.gtech.promotion.dao.entity.point;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.sql.Timestamp;
import java.util.Date;

@Table(name = "point_campaign")
@Data
public class PointCampaignEntity {
    /**
     * Primary key.
     */
    @Id
    private Long id;

    /**
     * Domain code.
     */
    @Column(name = "domain_code")
    private String domainCode;

    /**
     * Tenant code.
     */
    @Column(name = "tenant_code")
    private String tenantCode;

    /**
     * campaign code
     */
    @Column(name = "campaign_code")
    private String campaignCode;


    /**
     * program name
     */
    @Column(name = "program_name")
    private String programName;


    /**
     * campaign title
     */
    @Column(name = "campaign_title")
    private String campaignTitle;


    /**
     * campaignTitleLanguage
     */
    @Column(name = "campaign_title_language")
    private String campaignTitleLanguage;


    /**
     * campaign desc
     */
    @Column(name = "campaign_desc")
    private String campaignDesc;

    /**
     * Campaingn sponsor information.
     */
    private String sponsor;

    /**
     * Point campaign begin time.
     */
    @Column(name = "begin_date")
    private Timestamp beginDate;

    /**
     * Point campaign end time.
     */
    @Column(name = "end_date")
    private Timestamp endDate;

    /**
     * Activity total points.
     */
    @Column(name = "total_points")
    private Integer totalPoints;

    /**
     * Activity remaining points.
     */
    @Column(name = "remaining_points")
    private Integer remainingPoints;

    /**
     * campaignstatus.( 0-inactive 1-active)
     */
	private Integer status;

    /**
     * Create user code
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * Create time.
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * Lastest update user.
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * Lastest update time.
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * begin time
     */
    @Column(name = "begin_time")
    private String beginTime;


    /**
     * endTime
     */
    @Column(name = "end_Time")
    private String endTime;


}