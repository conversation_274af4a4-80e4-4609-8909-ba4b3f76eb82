/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dao.mapper.coupon;

import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.promotion.dao.entity.coupon.TPromoCouponReleaseEntity;
import com.gtech.promotion.dao.model.coupon.ReleaseCouponVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import tk.mybatis.mapper.common.example.SelectOneByExampleMapper;

import java.util.List;

/**
 * 投放
 */
//@Mapper
public interface TPromoCouponReleaseMapper extends GTechBaseMapper<TPromoCouponReleaseEntity>, SelectOneByExampleMapper<TPromoCouponReleaseEntity> {

    @Update(" update promo_coupon_release set INVENTORY = INVENTORY - #{inventory}"
            + " where TENANT_CODE = #{tenantCode} and ACTIVITY_CODE = #{activityCode} and RELEASE_CODE = #{releaseCode} and INVENTORY >= #{inventory}")
    int deductInventory(@Param("tenantCode") String tenantCode, @Param("activityCode") String activityCode, @Param("releaseCode") String releaseCode,
                        @Param("inventory") int inventory);

    @Select("<script> select r.TENANT_CODE,r.ACTIVITY_CODE,r.RELEASE_CODE,r.VALID_DAYS,r.VALID_START_TIME,r.VALID_END_TIME, i.COUPON_CODE,r.RECEIVE_END_TIME " +
            " from promo_coupon_release  r " +
            " INNER JOIN promo_coupon_inner_code i  ON " +
            "  r.TENANT_CODE = #{tenantCode} " +
            "  and r.ACTIVITY_CODE = #{activityCode}" +
            "  and r.RELEASE_CODE = i.RELEASE_CODE  " +
            "  and r.COUPON_TYPE = '02' and r.RELEASE_STATUS in ('02','03')  " +

            "  <if test=\"receiveType ==1 \">" +
            "     and r.VALID_DAYS is not null " +
            "  </if>"+

            "  <if test=\"receiveType ==2 \">" +
            "     and r.VALID_DAYS is null " +
            "  </if>"+

            " where " +
            "  i.COUPON_TYPE = '02' and i.`STATUS` ='01' and i.FROZEN_STATUS = '01' " +
            "  and i.TENANT_CODE = #{tenantCode} " +
            "  and i.ACTIVITY_CODE = #{activityCode} " +
            " <if test=\"releaseCode != null and releaseCode != '' \"> " +
            "  and i.RELEASE_CODE = #{releaseCode}  " +
            " </if> " +
            "  and i.VALID_START_TIME is null and r.RECEIVE_START_TIME &lt;= #{dateTime} and r.RECEIVE_END_TIME &gt; #{dateTime}" +
            " </script>")
    List<ReleaseCouponVO> queryReleaseCouponRecord(@Param("tenantCode") String tenantCode, @Param("activityCode") String activityCode,
                                                   @Param("releaseCode") String releaseCode,@Param("dateTime") String dateTime,@Param("receiveType")Integer receiveType);

    @Update(" update promo_coupon_release set RELEASE_QUANTITY = RELEASE_QUANTITY + #{quantity} , INVENTORY = INVENTORY + #{quantity} "
            + " where TENANT_CODE = #{tenantCode} and ACTIVITY_CODE = #{activityCode} and RELEASE_CODE = #{releaseCode}")
    int releaseSpecifiedQuantity(@Param("tenantCode") String tenantCode, @Param("activityCode") String activityCode, @Param("releaseCode") String releaseCode, @Param("quantity") Integer quantity);
}
