/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dao.mongo.activity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import com.gtech.promotion.vo.bean.ProductAttribute;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * <功能描述>
 */
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = "promo_order_detail")
public class OrderDetailEntity implements Serializable {

    private static final long serialVersionUID = -1973633209022652528L;

    @Field("_id")
    private String id;

    // 租户编码
    private String tenantCode;

    // 促销订单ID
    private String promoOrderId;

    // 品类编码：0000-全部品类
    private String categoryCode;

    // 品牌编码：0000-全部品牌
    private String brandCode;

    // 商品编码：0000-不限制
    private String productCode;

    // sku编码：0000-不限制
    private String skuCode;

    private List<ProductAttribute> attributes;

    // 商品原单价
    private BigDecimal productPrice;

    // 商品原总价
    private BigDecimal productAmount;

    // 购买数量
    private Integer quantity;

    private List<OrderDetailActivityEntity> activities;

}
