package com.gtech.promotion.dao.model.marketing.flashsale;

import com.gtech.promotion.dao.model.marketing.BaseModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FlashSaleOrderModel extends BaseModel {

    private String orderId;
    private String memberCode;
    private BigDecimal promoAmount;
    private String orderStatus;

    private String marketingGroupCode;

    private Date createTime;

}
