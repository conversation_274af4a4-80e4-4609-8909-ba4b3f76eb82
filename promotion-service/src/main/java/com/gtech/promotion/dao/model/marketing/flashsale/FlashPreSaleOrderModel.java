package com.gtech.promotion.dao.model.marketing.flashsale;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class FlashPreSaleOrderModel implements Serializable {

    private static final long serialVersionUID = -5014593249825677195L;

    private String orderId;

    private String memberCode;

    private Double promoAmount;

    private String orderStatus;

    private String activityCode;


    private String date;

}
