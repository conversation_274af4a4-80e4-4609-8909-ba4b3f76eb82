package com.gtech.promotion.dao.mapper.point;

import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Repository;

import com.gtech.promotion.dao.entity.point.PointCampaignEntity;

import tk.mybatis.mapper.common.Mapper;

@Repository
public interface PointCampaignMapper extends Mapper<PointCampaignEntity> {
    List<PointCampaignEntity> query(Map<String, Object> parameters);

	int updatePoint(Map<String, Object> param);
}