/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dao.model.activity;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 促销活动奖励表
 */
@Getter
@Setter
@ToString
public class TPromoActivityIncentiveVO {

    // Tenant code
    private String tenantCode;

    // Activity code
    private String activityCode;

    // 促销订单ID
    private String promoOrderId;

    // 奖励类型：01-直降 02-折扣 03-赠品
    private String incentiveType;

    // 奖励金额
    private BigDecimal incentiveAmount;
    // 运费减免金额
    private BigDecimal incentivePostage;

    // 奖励次数
    private Integer incentiveTimes;

    // 会员ID
    private String userCode;

}