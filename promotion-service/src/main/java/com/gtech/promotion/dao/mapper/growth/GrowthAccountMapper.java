package com.gtech.promotion.dao.mapper.growth;

import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Repository;

import com.gtech.promotion.dao.entity.growth.GrowthAccountEntity;

import tk.mybatis.mapper.common.Mapper;

@Repository
public interface GrowthAccountMapper extends Mapper<GrowthAccountEntity> {
    List<GrowthAccountEntity> query(Map<String, Object> parameters);

	int updateGrowth(Map<String, Object> param);
}