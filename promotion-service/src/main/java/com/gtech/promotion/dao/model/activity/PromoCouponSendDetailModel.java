package com.gtech.promotion.dao.model.activity;


import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;


@Getter
@Setter
@ToString
public class PromoCouponSendDetailModel implements Serializable {

    private static final long serialVersionUID = -5353769145991935754L;

    private Long id;

    private String tenantCode;

    private String activityCode;

    private String failReason;

    private String batchNo;

    private String userCode;

    private String createUser;

    private Date createTime;

    private String updateUser;

    private Date updateTime;
}
