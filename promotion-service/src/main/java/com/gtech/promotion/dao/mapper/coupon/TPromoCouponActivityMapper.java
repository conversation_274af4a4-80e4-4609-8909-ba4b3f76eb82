/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dao.mapper.coupon;

import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.promotion.dao.entity.coupon.TPromoCouponActivityEntity;
import com.gtech.promotion.dto.in.coupon.CouponActivityListInDTO;
import com.gtech.promotion.dto.out.coupon.CouponActivityListOutDTO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**   
 * 券活动
 */
//@Mapper
public interface TPromoCouponActivityMapper extends GTechBaseMapper<TPromoCouponActivityEntity> {

	@Select("<script> SELECT "
	        + "  t2.COUPON_TYPE, t2.TOTAL_QUANTITY, t2.USER_LIMIT_MAX, t2.USER_LIMIT_MAX_DAY, t1.ACTIVITY_CODE, t1.ACTIVITY_NAME, t1.ACTIVITY_DESC, t1.ACTIVITY_LABEL, t1.SPONSORS,"
			+ " t1.ORG_CODE ,t1.GROUP_CODE,"
			+ "  t1.ACTIVITY_BEGIN, t1.ACTIVITY_END, t1.ACTIVITY_STATUS, t1.TEMPLATE_CODE, t1.WARM_BEGIN, t1.WARM_END, t1.OPS_TYPE, t1.CREATE_TIME, t1.CREATE_USER,"
			+ "  t1.PRIORITY,t1.AUDIT_USER,t1.EXTERNAL_ACTIVITY_ID"

			+ " <if test=\"couponCode != null and couponCode !=''\"> "
			+ " ,t4.COUPON_CODE,t4.STATUS,t4.FROZEN_STATUS,t4.VALID_START_TIME,VALID_END_TIME "
			+ " </if>"
			+ " FROM promo_activity t1"

			+ " join promo_coupon_activity t2 on t1.ACTIVITY_CODE = t2.ACTIVITY_CODE "

			+ " <if test=\"orgCode != null and orgCode !='' and orgCode !='00'\">"
			+ "   join promo_activity_store t3 on t1.ACTIVITY_CODE = t3.ACTIVITY_CODE"
			+ " </if>"

			+ " <if test=\"couponCode != null and couponCode !=''\">"
			+ "   join promo_coupon_inner_code t4 on t1.ACTIVITY_CODE = t4.ACTIVITY_CODE"
			+ " </if>"

			+ " WHERE t1.TENANT_CODE = #{tenantCode} AND t1.ACTIVITY_TYPE = '02'"

			+ " <if test=\"orgCode != null and orgCode !='' and orgCode !='00' and defaultFlag\"> AND t3.ORG_CODE = #{orgCode}</if>"
			+ " <if test=\"activityOrgCode != null and activityOrgCode !='' and defaultFlag\"> AND t1.ORG_CODE = #{activityOrgCode}</if>"
			+ " <if test=\" defaultFlag != true \"> AND t1.ORG_CODE != 'default'</if>"

			+ " <if test=\"opsType != null and opsType !=''\"> AND t1.OPS_TYPE in (${opsType}) </if>"

            + " <if test=\"activityName != null and activityName!='' \"> AND t1.ACTIVITY_NAME LIKE concat('%',#{activityName},'%')</if>"
			+ " <if test=\"activityCode != null and activityCode!='' \"> AND t1.ACTIVITY_CODE = #{activityCode} </if>"
            + " <if test=\"sponsors != null and sponsors!='' \"> AND t1.SPONSORS LIKE concat(#{sponsors},'%')</if>"

            // activityBeginFrom    activityBeginTo
            + " <if test=\"activityBeginFrom != null and activityBeginFrom !='' \"> AND t1.ACTIVITY_BEGIN &gt;= #{activityBeginFrom}</if>"
            + " <if test=\"activityBeginTo != null and activityBeginTo !='' \"> AND t1.ACTIVITY_BEGIN &lt;= #{activityBeginTo}</if>"
            //createTimeFrom  createTimeTo  
            + " <if test=\"createTimeFrom != null and createTimeFrom !='' \"> AND t1.CREATE_TIME &gt;= #{createTimeFrom}</if>"
            + " <if test=\"createTimeTo != null and createTimeTo !='' \"> AND t1.CREATE_TIME &lt;= #{createTimeTo}</if>"
            // activityEndFrom  activityEndTo
            + " <if test=\"activityEndFrom != null and activityEndFrom !='' \"> AND t1.ACTIVITY_END &gt;= #{activityEndFrom}</if>"
            + " <if test=\"activityEndTo != null and activityEndTo !='' \"> AND t1.ACTIVITY_END &lt;= #{activityEndTo}</if>"

			+" <if test=\"warmBeginFrom != null and warmBeginFrom != ''\"> AND t1.WARM_BEGIN &gt;= #{warmBeginFrom} </if>  "
			+" <if test=\"warmBeginTo != null and warmBeginTo != ''\"> AND t1.WARM_BEGIN &lt;= #{warmBeginTo} </if> "
			+" <if test=\"warmEndFrom != null and warmEndFrom != ''\"> AND t1.WARM_END &gt;= #{warmEndFrom} </if> "
			+" <if test=\"warmEndTo != null and warmEndTo != ''\"> AND t1.WARM_END &lt;= #{warmEndTo} </if> "

			+ " <if test=\"couponType != null and couponType !='' \"> AND t2.COUPON_TYPE in (${couponType})</if>"


	        + " <if test=\"couponCode != null and couponCode !='' \"> AND t4.COUPON_CODE in (${couponCode})</if>"


            + " <if test=\"activityStatus != null and activityStatus !='' \"> AND t1.ACTIVITY_STATUS in (${activityStatus})</if>"
            + " <if test=\"templateCodes != null and templateCodes !='' \"> AND t1.TEMPLATE_CODE in (${templateCodes})</if>"

            + " order by "
            + " <if test=\"orderByType == null or orderByType =='' \"> t1.id desc</if>"
            + " <if test=\"orderByType != null and orderByType !='' and orderByType =='01' \"> t1.ACTIVITY_CODE desc</if>"
            + " <if test=\"orderByType != null and orderByType !='' and orderByType =='02' \"> t1.ACTIVITY_CODE asc</if>"
            + " <if test=\"orderByType != null and orderByType !='' and orderByType =='03' \"> t1.ACTIVITY_BEGIN desc</if>"
            + " <if test=\"orderByType != null and orderByType !='' and orderByType =='04' \"> t1.ACTIVITY_BEGIN asc</if>"
            + " <if test=\"orderByType != null and orderByType !='' and orderByType =='05' \"> t1.ACTIVITY_END desc</if>"
            + " <if test=\"orderByType != null and orderByType !='' and orderByType =='06' \"> t1.ACTIVITY_END asc</if>"

			+ "</script>")
	List<CouponActivityListOutDTO> queryCouponActivityList(CouponActivityListInDTO couponActivityListInDTO);

	@Update(" update promo_coupon_activity set RESERVE_INVENTORY = RESERVE_INVENTORY + #{quota} "
			+ " where TENANT_CODE = #{tenantCode} and ACTIVITY_CODE = #{activityCode}")
	int reserveCouponQuota(@Param("tenantCode") String tenantCode, @Param("activityCode") String activityCode, @Param("quota") int sum);

	@Update(" update promo_coupon_activity set RESERVE_INVENTORY = RESERVE_INVENTORY - #{quota} "
			+ " where TENANT_CODE = #{tenantCode} and ACTIVITY_CODE = #{activityCode} and RESERVE_INVENTORY >= #{quota}")
	int returnCouponQuota(@Param("tenantCode") String tenantCode, @Param("activityCode") String activityCode, @Param("quota") int sum);

	@Select("<script> " +
			" SELECT "
			+ "  t1.id, t2.COUPON_TYPE, t2.TOTAL_QUANTITY, t2.USER_LIMIT_MAX, t2.USER_LIMIT_MAX_DAY, t1.ACTIVITY_CODE, t1.ACTIVITY_NAME, t1.ACTIVITY_DESC, t1.ACTIVITY_LABEL, t1.SPONSORS, "
			+ "  t1.ORG_CODE,t1.GROUP_CODE,"
			+ "  t1.ACTIVITY_BEGIN, t1.ACTIVITY_END, t1.ACTIVITY_STATUS, t1.TEMPLATE_CODE, t1.WARM_BEGIN, t1.WARM_END, t1.OPS_TYPE, t1.CREATE_TIME,t1.CREATE_USER,"
			+ "  t1.PRIORITY,t1.UPDATE_USER,t1.AUDIT_USER,t1.EXTERNAL_ACTIVITY_ID "

			+ " <if test=\"couponCode != null and couponCode !=''\"> " +
			" ,t4.COUPON_CODE,t4.STATUS,t4.FROZEN_STATUS,t4.VALID_START_TIME,VALID_END_TIME "
			+ " </if>"

			+ " FROM promo_activity t1"
			+ "   join promo_coupon_activity t2 on t1.ACTIVITY_CODE = t2.ACTIVITY_CODE "

			+ " <if test=\"orgCode != null and orgCode !='' and orgCode !='00'\">"
			+ "   join promo_activity_store t3 on t1.ACTIVITY_CODE = t3.ACTIVITY_CODE"
			+ " </if>"

			+ " <if test=\"couponCode != null and couponCode !=''\">"
			+ "   join promo_coupon_inner_code t4 on t1.ACTIVITY_CODE = t4.ACTIVITY_CODE"
			+ " </if>"

			+ " WHERE t1.TENANT_CODE = #{tenantCode} AND t1.ACTIVITY_TYPE = '02'"

			+ " <if test=\"orgCode != null and orgCode !='' and orgCode !='00'\"> AND t3.ORG_CODE = #{orgCode}</if>"
			+ " <if test=\"activityOrgCode != null and activityOrgCode !=''\"> AND t1.ORG_CODE = #{activityOrgCode}</if>"

			+ " <if test=\"opsType != null and opsType !=''\"> AND t1.OPS_TYPE in (${opsType}) </if>"

			+ " <if test=\"activityName != null and activityName!='' \"> AND t1.ACTIVITY_NAME LIKE concat('%',#{activityName},'%')</if>"
			+ " <if test=\"activityCode != null and activityCode!='' \"> AND t1.ACTIVITY_CODE = #{activityCode} </if>"
			+ " <if test=\"sponsors != null and sponsors!='' \"> AND t1.SPONSORS LIKE concat(#{sponsors},'%')</if>"

			// activityBeginFrom    activityBeginTo
			+ " <if test=\"activityBeginFrom != null and activityBeginFrom !='' \"> AND t1.ACTIVITY_BEGIN &gt;= #{activityBeginFrom}</if>"
			+ " <if test=\"activityBeginTo != null and activityBeginTo !='' \"> AND t1.ACTIVITY_BEGIN &lt;= #{activityBeginTo}</if>"
			//createTimeFrom  createTimeTo
			+ " <if test=\"createTimeFrom != null and createTimeFrom !='' \"> AND t1.CREATE_TIME &gt;= #{createTimeFrom}</if>"
			+ " <if test=\"createTimeTo != null and createTimeTo !='' \"> AND t1.CREATE_TIME &lt;= #{createTimeTo}</if>"
			// activityEndFrom  activityEndTo
			+ " <if test=\"activityEndFrom != null and activityEndFrom !='' \"> AND t1.ACTIVITY_END &gt;= #{activityEndFrom}</if>"
			+ " <if test=\"activityEndTo != null and activityEndTo !='' \"> AND t1.ACTIVITY_END &lt;= #{activityEndTo}</if>"

			+" <if test=\"warmBeginFrom != null and warmBeginFrom != ''\"> AND t1.WARM_BEGIN &gt;= #{warmBeginFrom} </if>  "
			+" <if test=\"warmBeginTo != null and warmBeginTo != ''\"> AND t1.WARM_BEGIN &lt;= #{warmBeginTo} </if> "
			+" <if test=\"warmEndFrom != null and warmEndFrom != ''\"> AND t1.WARM_END &gt;= #{warmEndFrom} </if> "
			+" <if test=\"warmEndTo != null and warmEndTo != ''\"> AND t1.WARM_END &lt;= #{warmEndTo} </if> "

			+ " <if test=\"couponType != null and couponType !='' \"> AND t2.COUPON_TYPE in (${couponType})</if>"
			+ " <if test=\"couponCode != null and couponCode !='' \"> AND t4.COUPON_CODE = #{couponCode}</if>"
			+ " <if test=\"activityStatus != null and activityStatus !='' \"> AND t1.ACTIVITY_STATUS in (${activityStatus})</if>"
			+ " <if test=\"templateCodes != null and templateCodes !='' \"> AND t1.TEMPLATE_CODE in (${templateCodes})</if>"
			+ " AND EXISTS ( SELECT id FROM promo_qualification t5 WHERE t5.ACTIVITY_CODE = t1.ACTIVITY_CODE "
			+ " <if test=\"channelCode != null and channelCode !='' \"> AND t5.QUALIFICATION_CODE = 'channelCode' AND t5.QUALIFICATION_VALUE = #{channelCode} </if> "
			+ " <if test=\"memberTagList != null\"> AND t5.QUALIFICATION_CODE = 'memberTagCode' AND t5.QUALIFICATION_VALUE in "
			+ "<foreach collection=\"memberTagList\" item=\"memberTag\" separator=\",\" open=\"(\" close=\")\">"
			+ "#{memberTag}"
			+" </foreach> </if> "
			+ ")"
			+ " union SELECT "
			+ "  t1.id, t2.COUPON_TYPE, t2.TOTAL_QUANTITY, t2.USER_LIMIT_MAX, t2.USER_LIMIT_MAX_DAY, t1.ACTIVITY_CODE, t1.ACTIVITY_NAME, t1.ACTIVITY_DESC, t1.ACTIVITY_LABEL, t1.SPONSORS, "
			+ "  t1.ORG_CODE,t1.GROUP_CODE,"
			+ "  t1.ACTIVITY_BEGIN, t1.ACTIVITY_END, t1.ACTIVITY_STATUS, t1.TEMPLATE_CODE, t1.WARM_BEGIN, t1.WARM_END, t1.OPS_TYPE, t1.CREATE_TIME,t1.CREATE_USER,"
			+ "  t1.PRIORITY,t1.UPDATE_USER,t1.AUDIT_USER,t1.EXTERNAL_ACTIVITY_ID "

			+ " <if test=\"couponCode != null and couponCode !=''\"> " +
			" ,t4.COUPON_CODE,t4.STATUS,t4.FROZEN_STATUS,t4.VALID_START_TIME,VALID_END_TIME "
			+ " </if>"


			+ " FROM promo_activity t1"
			+ "   join promo_coupon_activity t2 on t1.ACTIVITY_CODE = t2.ACTIVITY_CODE "

			+ " <if test=\"orgCode != null and orgCode !='' and orgCode !='00'\">"
			+ "   join promo_activity_store t3 on t1.ACTIVITY_CODE = t3.ACTIVITY_CODE"
			+ " </if>"

			+ " <if test=\"couponCode != null and couponCode !=''\">"
			+ "   join promo_coupon_inner_code t4 on t1.ACTIVITY_CODE = t4.ACTIVITY_CODE"
			+ " </if>"

			+ " WHERE t1.TENANT_CODE = #{tenantCode} AND t1.ACTIVITY_TYPE = '02'"

			+ " <if test=\"orgCode != null and orgCode !='' and orgCode !='00'\"> AND t3.ORG_CODE = #{orgCode}</if>"
			+ " <if test=\"activityOrgCode != null and activityOrgCode !=''\"> AND t1.ORG_CODE = #{activityOrgCode}</if>"

			+ " <if test=\"opsType != null and opsType !=''\"> AND t1.OPS_TYPE in (${opsType}) </if>"

			+ " <if test=\"activityName != null and activityName!='' \"> AND t1.ACTIVITY_NAME LIKE concat('%',#{activityName},'%')</if>"
			+ " <if test=\"activityCode != null and activityCode!='' \"> AND t1.ACTIVITY_CODE = #{activityCode} </if>"
			+ " <if test=\"sponsors != null and sponsors!='' \"> AND t1.SPONSORS LIKE concat(#{sponsors},'%')</if>"

			// activityBeginFrom    activityBeginTo
			+ " <if test=\"activityBeginFrom != null and activityBeginFrom !='' \"> AND t1.ACTIVITY_BEGIN &gt;= #{activityBeginFrom}</if>"
			+ " <if test=\"activityBeginTo != null and activityBeginTo !='' \"> AND t1.ACTIVITY_BEGIN &lt;= #{activityBeginTo}</if>"
			//createTimeFrom  createTimeTo
			+ " <if test=\"createTimeFrom != null and createTimeFrom !='' \"> AND t1.CREATE_TIME &gt;= #{createTimeFrom}</if>"
			+ " <if test=\"createTimeTo != null and createTimeTo !='' \"> AND t1.CREATE_TIME &lt;= #{createTimeTo}</if>"
			// activityEndFrom  activityEndTo
			+ " <if test=\"activityEndFrom != null and activityEndFrom !='' \"> AND t1.ACTIVITY_END &gt;= #{activityEndFrom}</if>"
			+ " <if test=\"activityEndTo != null and activityEndTo !='' \"> AND t1.ACTIVITY_END &lt;= #{activityEndTo}</if>"

			+" <if test=\"warmBeginFrom != null and warmBeginFrom != ''\"> AND t1.WARM_BEGIN &gt;= #{warmBeginFrom} </if>  "
			+" <if test=\"warmBeginTo != null and warmBeginTo != ''\"> AND t1.WARM_BEGIN &lt;= #{warmBeginTo} </if> "
			+" <if test=\"warmEndFrom != null and warmEndFrom != ''\"> AND t1.WARM_END &gt;= #{warmEndFrom} </if> "
			+" <if test=\"warmEndTo != null and warmEndTo != ''\"> AND t1.WARM_END &lt;= #{warmEndTo} </if> "

			+ " <if test=\"couponType != null and couponType !='' \"> AND t2.COUPON_TYPE in (${couponType})</if>"

			+ " <if test=\"couponCode != null and couponCode !='' \"> AND t4.COUPON_CODE in (${couponCode})</if>"

			+ " <if test=\"activityStatus != null and activityStatus !='' \"> AND t1.ACTIVITY_STATUS in (${activityStatus})</if>"
			+ " <if test=\"templateCodes != null and templateCodes !='' \"> AND t1.TEMPLATE_CODE in (${templateCodes})</if>"
			+ " <if test=\"channelCode != null and channelCode !='' \"> AND NOT EXISTS ( SELECT id FROM promo_qualification t5 WHERE t5.ACTIVITY_CODE = t1.ACTIVITY_CODE "
			+ "  AND t5.QUALIFICATION_CODE = 'channelCode')</if> "
			+ " <if test=\"memberTagList != null\"> AND NOT EXISTS ( SELECT id FROM promo_qualification t6 WHERE t6.ACTIVITY_CODE = t1.ACTIVITY_CODE "
			+ "  AND t6.QUALIFICATION_CODE = 'memberTagCode')</if> "
			+ " order by "
			+ " <if test=\"orderByType == null or orderByType =='' \"> id desc</if>"
			+ " <if test=\"orderByType != null and orderByType !='' and orderByType =='01' \"> ACTIVITY_CODE desc</if>"
			+ " <if test=\"orderByType != null and orderByType !='' and orderByType =='02' \"> ACTIVITY_CODE asc</if>"
			+ " <if test=\"orderByType != null and orderByType !='' and orderByType =='03' \"> ACTIVITY_BEGIN desc</if>"
			+ " <if test=\"orderByType != null and orderByType !='' and orderByType =='04' \"> ACTIVITY_BEGIN asc</if>"
			+ " <if test=\"orderByType != null and orderByType !='' and orderByType =='05' \"> ACTIVITY_END desc</if>"
			+ " <if test=\"orderByType != null and orderByType !='' and orderByType =='06' \"> ACTIVITY_END asc</if>"
			+ "</script>")
	List<CouponActivityListOutDTO> queryCouponActivityListByQualification(CouponActivityListInDTO couponActivityListInDTO);
}
