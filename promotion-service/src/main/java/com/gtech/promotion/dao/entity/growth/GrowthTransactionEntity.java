package com.gtech.promotion.dao.entity.growth;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Table(name = "growth_transaction")
@Data
public class GrowthTransactionEntity {
	/**
	 * Primary key.
	 */
	@Id
	private Long id;

	/**
	 * Domain code.
	 */
	@Column(name = "domain_code")
	private String domainCode;

	/**
	 * Tenant code.
	 */
	@Column(name = "tenant_code")
	private String tenantCode;

	/**
	 * Transaction serial number.
	 */
	@Column(name = "transaction_sn")
	private String transactionSn;

	/**
	 * Growth account code. (UserCode or OrgCode)
	 */
	@Column(name = "account_code")
	private String accountCode;

	/**
	 * Growth account type. (1-User 2-Organization)
	 */
	@Column(name = "account_type")
	private Integer accountType;

	/**
	 * Growth transaction type. (1-Increase points 2-Deduct points)
	 */
	@Column(name = "transaction_type")
	private Integer transactionType;

	/**
	 * Growth transaction remarks.
	 */
	@Column(name = "transaction_remarks")
	private String transactionRemarks;

	/**
	 * Growth spent/earned in the transaction.
	 */
	@Column(name = "transaction_amount")
	private Integer transactionAmount;

	/**
	 * Transaction date. (yyyyMMddHHmmss)
	 */
	@Column(name = "transaction_date")
	private Long transactionDate;

	/**
	 * Refer transaction serial number.
	 */
	@Column(name = "refer_transaction_sn")
	private String referTransactionSn;

	/**
	 * Refer order number.
	 */
	@Column(name = "refer_order_number")
	private String referOrderNumber;

	/**
	 * Create user code
	 */
	@Column(name = "create_user")
	private String createUser;

	/**
	 * Create time.
	 */
	@Column(name = "create_time")
	private Date createTime;

	/**
	 * Lastest update user.
	 */
	@Column(name = "update_user")
	private String updateUser;

	/**
	 * Lastest update time.
	 */
	@Column(name = "update_time")
	private Date updateTime;

	/**
	 * Before balance.
	 */
	@Column(name = "before_balance")
	private Integer beforeBalance;

	/**
	 * After balance.
	 */
	@Column(name = "after_balance")
	private Integer afterBalance;

    /**
     * origin.
     */
    @Column(name = "origin")
    private Integer origin;
}