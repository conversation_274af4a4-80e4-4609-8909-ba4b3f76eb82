/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dao.model.activity;

import com.alibaba.fastjson.annotation.JSONField;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.code.activity.IsExcludeEnum;
import com.gtech.promotion.vo.bean.Qualification;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 参与资格
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class QualificationModel implements Serializable {

    private static final long serialVersionUID = 8229177424444396969L;
    private Long id;

    private String activityCode;

    private String domainCode;

    private String tenantCode;

    private String orgCode = "default";

    private String qualificationCode;

    private String qualificationValue;

    private String qualificationValueName;

    @ApiModelProperty(value = "Is exclude, 02 按会员标签排除")
    private String isExclude;

    // 资格关系，01-OR 02-AND
    private String relation = "01";

    @JSONField(serialize = false)
    public static List<Qualification> convert(List<QualificationModel> qualificationModels){
        List<Qualification> qualifications = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(qualificationModels)){
            Map<String, List<QualificationModel>> collect = qualificationModels.stream().collect(Collectors.groupingBy(QualificationModel::getQualificationCode));
            for (Map.Entry<String, List<QualificationModel>> stringListEntry : collect.entrySet()) {
                Qualification qualification = new Qualification();
                qualification.setQualificationCode(stringListEntry.getKey());
                List<String> values = new ArrayList<>();
                List<String> valueNames = new ArrayList<>();
                String isExclude = null;
                for (QualificationModel qualificationModel : stringListEntry.getValue()) {
                    values.add(qualificationModel.getQualificationValue());
                    valueNames.add(qualificationModel.getQualificationValueName());
                    if (StringUtil.isNotEmpty(qualificationModel.getIsExclude()) && IsExcludeEnum.EXCLUDE.code().equals(qualificationModel.getIsExclude())){
                        isExclude = qualificationModel.getIsExclude();
                    }
                }
                qualification.setIsExclude(isExclude);
                qualification.setQualificationValue(values);
                qualification.setQualificationValueName(valueNames);
                qualifications.add(qualification);
            }
        }
        return qualifications;
    }

    @JSONField(serialize = false)
    public static List<QualificationModel> convertToModel(List<Qualification> qualifications){
        List<QualificationModel> qualificationModels = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(qualifications)){
            for (Qualification qualification : qualifications) {
                for (int i = 0; i < qualification.getQualificationValue().size(); i++) {
                    String s = qualification.getQualificationValue().get(i);
                    QualificationModel qualificationModel = new QualificationModel();
                    qualificationModel.setQualificationCode(qualification.getQualificationCode());
                    qualificationModel.setQualificationValue(s);
                    if (CollectionUtils.isNotEmpty(qualification.getQualificationValueName()) && qualification.getQualificationValueName().size() > i) {
                        qualificationModel.setQualificationValueName(qualification.getQualificationValueName().get(i));
                    }
                    qualificationModel.setIsExclude(qualification.getIsExclude());
                    qualificationModels.add(qualificationModel);
                }
            }
        }
        return qualificationModels;
    }

    @JSONField(serialize = false)
    public static List<QualificationModel> convertToModel(Map<String, List<String>> qualifications){
        List<QualificationModel> qualificationModels = new ArrayList<>();
        if (null != qualifications && !qualifications.isEmpty()){
            for (Map.Entry<String, List<String>> entry : qualifications.entrySet()) {
                for (String s : entry.getValue()) {
                    QualificationModel qualificationModel = new QualificationModel();
                    qualificationModel.setQualificationCode(entry.getKey());
                    qualificationModel.setQualificationValue(s);
                    qualificationModels.add(qualificationModel);
                }
            }
        }
        return qualificationModels;
    }

}