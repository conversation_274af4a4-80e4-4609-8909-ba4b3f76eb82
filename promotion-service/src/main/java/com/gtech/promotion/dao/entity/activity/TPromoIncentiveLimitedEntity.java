/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dao.entity.activity;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <功能描述>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "promo_incentive_limited")
public class TPromoIncentiveLimitedEntity {

    // ID
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    // Tenant code
    @Column(name = "TENANT_CODE")
    private String tenantCode;

    // Activity code
    @Column(name = "ACTIVITY_CODE")
    private String activityCode;

    // 限制条件编码：参考【促销奖励限制条件表】
    @Column(name = "LIMITATION_CODE")
    private String limitationCode;

    // 限制条件值
    @Column(name = "LIMITATION_VALUE")
    private BigDecimal limitationValue;

    // Create user code.
    @Column(name = "create_user")
    private String createUser;

    // Create time.
    @Column(name = "create_time")
    private Date createTime;

    // Lastest update user.
    @Column(name = "update_user")
    private String updateUser;

    // Lastest update time.
    @Column(name = "update_time")
    private Date updateTime;

    // Logic delete. (0-normal 1-deleted)
    @Column(name = "logic_delete")
    private Integer logicDelete;

}