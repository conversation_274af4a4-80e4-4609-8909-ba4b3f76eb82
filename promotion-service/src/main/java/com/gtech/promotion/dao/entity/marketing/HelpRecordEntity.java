package com.gtech.promotion.dao.entity.marketing;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Table(name = "marketing_help_record")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class HelpRecordEntity {

    @Id
    @Column(name = "id")
    private Long id;
    @Column(name = "domain_code")
    private String domainCode;
    @Column(name = "tenant_code")
    private String tenantCode;
    @Column(name = "org_code")
    private String orgCode;
    @Column(name = "activity_code")
    private String activityCode;
    @Column(name = "sharing_record_code")
    private String sharingRecordCode;
    @Column(name = "help_record_code")
    private String helpRecordCode;
    @Column(name = "sharing_member_code")
    private String sharingMemberCode;
    @Column(name = "help_member_code")
    private String helpMemberCode;
    @Column(name = "help_record_status")
    private String helpRecordStatus;
    @Column(name = "create_time")
    private Date createTime;



}
