package com.gtech.promotion.dao.entity.marketing;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Table;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/8/7 13:38
 */
@EqualsAndHashCode(callSuper = true)
@Table(name = "marketing_group_user")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MarketingGroupUserEntity extends BaseEntity {

    public static final String C_MARKETING_GROUP_CODE = "marketingGroupCode";

    public static final String C_USER_CODE = "userCode";

    public static final String C_GROUP_STATUS = "groupStatus";

    public static final String C_TEAM_LEADER = "teamLeader";

    public static final String C_EFFECTIVE_TIME = "effectiveTime";


    public static final String C_SKU_CODE = "skuCode";

    public static final String C_PRODUCT_CODE = "productCode";
    public static final String C_CANCEL_TIME = "cancelTime";


    @Column(name="ACTIVITY_CODE")
    private String activityCode;
    /**
     * 拼团业务编码
     */
    @Column(name="MARKETING_GROUP_CODE")
    private String marketingGroupCode;

    /**
     * 会员编码
     */
    @Column(name="USER_CODE")
    private String userCode;

    /**
     *有效小时
     */
    @Column(name="EFFECTIVE_HOUR")
    private Integer effectiveHour;

    /***
     * 有效截止时间
     */
    @Column(name="EFFECTIVE_TIME")
    private String effectiveTime;

    /**
     * 用户拼团状态 01进行中，02 拼团成功 03 取消拼团， 04 已支付
     */
    @Column(name="GROUP_STATUS")
    private String groupStatus;

    @Column(name="TEAM_LEADER")
    private String teamLeader;


    /***
     * 取消订单时间
     */
    @Column(name="CANCEL_TIME")
    private String cancelTime;

    @Column(name="PRODUCT_CODE")
    private String productCode;

    @Column(name="SKU_CODE")
    private String skuCode;

}
