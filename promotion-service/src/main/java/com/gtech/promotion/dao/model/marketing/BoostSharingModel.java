package com.gtech.promotion.dao.model.marketing;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class BoostSharingModel extends BaseModel{

    private String boostSharingType;

    private String attractNewCustomers;

    private String numberOfBoostSharing;

    /**-----------------分享人奖励----------------------*/
    private String shareToGetCouponActivityCode;

    private String shareToGetCouponActivityName;

    private String rightOfFirstRefusalProductCode;
    private String rightOfFirstRefusalProductNo;
    private String rightOfFirstRefusalProductName;

    private String rightOfFirstRefusalStartTime;

    private String rightOfFirstRefusalEndTime;

    private String luckyDrawActivityCode;

    private String luckyDrawActivityName;
    /**-----------------分享人奖励----------------------*/


    /**-----------------助力人奖励----------------------*/
    private String helpToGetCouponActivityCode;

    private String helpToGetCouponActivityName;

    /**-----------------助力人奖励----------------------*/
    private String redirectLink;
    private String eventPageImage;
    private String eventBackGroundPageImage;
    private String eventPageLink;
    private String miniProgramImage;
    private String miniProgramShareCopy;
    private String posterImage;
    private String popupImage;
    private String popupShareCopy;
    private String createUser;
    private String updateUser;


}
