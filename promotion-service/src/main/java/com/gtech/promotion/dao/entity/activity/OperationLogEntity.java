/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WA<PERSON><PERSON>NTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dao.entity.activity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.Date;

/**   
 * <功能描述>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "promo_activity_operation_log")
public class OperationLogEntity {
    public static final String C_ID = "id";
    public static final String C_ACTIVITY_CODE = "activityCode";
    public static final String C_TENANT_CODE = "tenantCode";
    // ID
    @Id
    @Column(name = "ID")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "ACTIVITY_CODE")
    private String activityCode;

    @Column(name = "TENANT_CODE")
    private String tenantCode;

    @Column(name = "OPERATION_CODE")
    private String operationCode;

    @Column(name = "OPERATION_TYPE")
    private String operationType;

    // Create user code.
    @Column(name="CREATE_USER")
    private String createUser;

    // Create time.
    @Column(name="CREATE_TIME")
    private Date createTime;

    // Lastest update user.
    @Column(name="UPDATE_USER")
    private String updateUser;

    // Lastest update time.
    @Column(name="UPDATE_TIME")
    private Date updateTime;

    @Column(name="LOGIC_DELETE")
    private Integer logicDelete;

    private String createLastName;

    private String createFirstName;

}