package com.gtech.promotion.dao.model.marketing;

import com.alibaba.fastjson.annotation.JSONField;
import com.gtech.commons.utils.DateUtil;
import com.gtech.promotion.code.activity.ActivityStatusEnum;
import com.gtech.promotion.vo.bean.ActivityStore;
import com.gtech.promotion.vo.bean.ExtImage;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MarketingModel extends BaseModel {

    private String id;
    private String activityType;
    private String opsType;
    private String groupCode;
    private String activityName;
    private String activityBegin;
    private String activityEnd;
    // 活动状态：01-待提交 02-审核中 03-已拒绝 04-已生效 05-已结束 06-暂停中 07-已终止
    private String activityStatus;
    private String activityUrl;
    private String sponsors;
    private String backgroundImage;
    private String ribbonImage;
    private String ribbonPosition;
    private String ribbonText;
    private String coolDown;
    private String warmBegin;
    private String warmEnd;
    private String createUser;
    private String updateUser;
    private String createUserFirstName;
    private String createUserLastName;
    private String syncPriceStatus;
    private String incentiveLimitedFlag;
    private String luckyDrawRuleFlag;
    private String preSalePayType;
    private String shippingTime;
    private String importNo;
    private String needAudit;
    private String needDifferentOperator;
    private String auditUser;
    private String commitUser;

    private String selectProductType;


    private Date createTime;

    private List<ActivityStore> stores;

    private List<ExtImage> extImages;
    /**
     * 判断是否需要做过期处理
     */
    @JSONField(serialize = false)
    public boolean isNeedToDoExpire() {

        return ActivityStatusEnum.EFFECTIVE.equalsCode(this.activityStatus)
                && DateUtil.parseDate(this.activityEnd, DateUtil.FORMAT_YYYYMMDDHHMISS_14).getTime() <= System.currentTimeMillis();
    }

}
