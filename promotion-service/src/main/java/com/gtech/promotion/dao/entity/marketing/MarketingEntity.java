package com.gtech.promotion.dao.entity.marketing;

import com.gtech.promotion.dao.handler.ExtImageTypeHandler;
import com.gtech.promotion.vo.bean.ExtImage;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import tk.mybatis.mapper.annotation.ColumnType;

import javax.persistence.Column;
import javax.persistence.Table;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Table(name = "MARKETING")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MarketingEntity extends BaseEntity {

    public static final String ACTIVITY_TYPE = "activityType";
    public static final String OPS_TYPE = "opsType";
    public static final String GROUP_CODE = "groupCode";
    public static final String ACTIVITY_NAME = "activityName";
    public static final String ACTIVITY_BEGIN = "activityBegin";
    public static final String ACTIVITY_END = "activityEnd";
    public static final String WARM_BEGIN = "warmBegin";
    public static final String WARM_END = "warmEnd";
    public static final String LUCKY_DRAW_RULE_FLAG = "luckyDrawRuleFlag";
    public static final String SPONSOR_S = "sponsors";
    public static final String ACTIVITY_STATUS = "activityStatus";
    private String activityCode;
    private String activityType;
    private String opsType;

    private String groupCode;
    private String activityName;
    private String activityBegin;
    private String activityEnd;
    // 活动状态：01-待提交 02-审核中 03-已拒绝 04-已生效 05-已结束 06-暂停中 07-已终止
    private String activityStatus;
    private String activityUrl;
    private String sponsors;
    private String backgroundImage;
    private String ribbonImage;
    private String ribbonPosition;
    private String ribbonText;
    private String coolDown;
    private String warmBegin;
    private String warmEnd;
    private String syncPriceStatus;
    private String incentiveLimitedFlag;
    private String luckyDrawRuleFlag;
    private String preSalePayType;
    private String shippingTime;
    private String importNo;
    private String auditUser;

    private String selectProductType;
    @Column(name = "EXT_IMAGES")
    @ColumnType(typeHandler = ExtImageTypeHandler.class)
    private List<ExtImage> extImages;
}
