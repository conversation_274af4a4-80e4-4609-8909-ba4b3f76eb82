package com.gtech.promotion.dao.entity.purchaseconstraint;

import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Table(name = "promo_purchase_constraint_customer")
public class PurchaseConstraintCustomer implements Serializable {
    private static final long serialVersionUID = -82753270732772687L;
    @Id
    @Column(name = "ID")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    /**
     * 租户编码
     */
    private String tenantCode;
    /**
     * 条件限购唯一码
     */
    private String customerCode;
    /**
     * 限购条件编码
     */
    private String purchaseConstraintCode;
    /**
     * 限购规则条件类型枚举,0:单商品最大量,1:所有商品最大量,2:最大金额
     */
    private Integer purchaseConstraintRuleType;
    /**
     * 用户编码
     */
    private String userCode;
    /**
     * 商品编码
     */
    private String productCode;
    /**
     * 限购量(数量/金额)
     */
    private BigDecimal purchaseConstraintValue;
    /**
     * 已使用限购量(数量/金额)
     */
    private BigDecimal purchaseConstraintValueUsed;
    /**
     * 限购周期开始时间
     */
    private Date customerStartTime;
    /**
     * 限购周期结束时间
     */
    private Date customerEndTime;

    private Date createTime;

    private Date updateTime;


}

