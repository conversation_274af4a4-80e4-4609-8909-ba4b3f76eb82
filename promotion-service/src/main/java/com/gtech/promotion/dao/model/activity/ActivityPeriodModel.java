package com.gtech.promotion.dao.model.activity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ActivityPeriodModel {
    private Long id;

    private String activityCode;

    private String domainCode;

    private String tenantCode;

    private String orgCode = "default";

    private String beginPeriod;

    private String endPeriod;

    private Integer intervalWeek;

    private String createUser;

    // Create time.
    private Date createTime;

    // Lastest update user.
    private String updateUser;

    // Lastest update time.
    private Date updateTime;

    private Integer logicDelete;
}
