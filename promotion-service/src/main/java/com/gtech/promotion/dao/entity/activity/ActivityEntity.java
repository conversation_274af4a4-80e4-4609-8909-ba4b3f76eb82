/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.dao.entity.activity;

import com.alibaba.fastjson.JSONObject;
import com.gtech.promotion.code.activity.ActivityExtendParamsEnum;
import com.gtech.promotion.dao.handler.ExtImageTypeHandler;
import com.gtech.promotion.dao.handler.JacksonTypeHandler;
import com.gtech.promotion.vo.bean.ExtImage;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.type.JdbcType;
import tk.mybatis.mapper.annotation.ColumnType;

import javax.persistence.*;
import java.util.Date;
import java.util.List;
/**
 * 活动基础数据
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "promo_activity")
public class ActivityEntity {

    public static final String C_TENANT_CODE = "tenantCode";
    public static final String C_ACTIVITY_CODE = "activityCode";
    public static final String C_ACTIVITY_STATUS = "activityStatus";
    public static final String C_ACTIVITY_BEGIN = "activityBegin";
    public static final String C_ACTIVITY_END = "activityEnd";

    public static final String C_DOMAIN_CODE = "domainCode";

    public static final String C_PROMOTION_CATEGORY = "promotionCategory";

    public static final String C_GROUP_CODE = "groupCode";


    // ID
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;


    // DOMAIN code
    @Column(name = "DOMAIN_CODE")
    private String domainCode;

    // Tenant code
    @Column(name = "TENANT_CODE")
    private String tenantCode;

    // Org Code
    @Column(name = "ORG_CODE")
    private String orgCode;

    // 活动编码
    @Column(name = "ACTIVITY_CODE")
    private String activityCode;

    // 活动类型: 01-活动 02-券
    @Column(name = "ACTIVITY_TYPE")
    private String activityType;

    // 活动分类标签
    @Column(name = "ACTIVITY_LABEL")
    private String activityLabel;

    @Column(name = "ACTIVITY_URL")
    private String activityUrl;

    // 活动名称
    @Column(name = "ACTIVITY_NAME")
    private String activityName;

    // 活动描述
    @Column(name = "ACTIVITY_DESC")
    private String activityDesc;

    // 活动备注
    @Column(name = "ACTIVITY_REMARK")
    private String activityRemark;

    // 活动公式
    @Column(name = "ACTIVITY_EXPR")
    private String activityExpr;

    // 是否对外显示：1显示；2不显示（1.21版本新加）
    @Column(name = "SHOW_FLAG")
    private Integer showFlag;

    // Template code
    @Column(name = "TEMPLATE_CODE")
    private String templateCode;

    // 奖励限制标志：0-无限制 1-有限制
    @Column(name = "INCENTIVE_LIMITED_FLAG")
    private String incentiveLimitedFlag;

    // 预热开始时间：yyyyMMddhhmmss
    @Column(name = "WARM_BEGIN")
    private String warmBegin;

    // 预热结束时间：yyyyMMddhhmmss
    @Column(name = "WARM_END")
    private String warmEnd;

    // 开始时间：yyyyMMddhhmmss
    @Column(name = "ACTIVITY_BEGIN")
    private String activityBegin;

    // 结束时间：yyyyMMddhhmmss
    @Column(name = "ACTIVITY_END")
    private String activityEnd;

    // 活动状态：01-待提交 02-审核中 03-已拒绝 04-已生效 05-已结束 06-暂停中 07-已终止
    @Column(name = "ACTIVITY_STATUS")
    private String activityStatus;

    // 商品范围正反选：01-正选；02-反选
    @Column(name = "PRODUCT_SELECTION_TYPE")
    private String productSelectionType;

    // Product item scope type: 1-All scope 2-By spu in scope
    @Column(name = "ITEM_SCOPE_TYPE")
    private Integer itemScopeType;

    // 周期类型：00-全时段 01-自定义
    @Column(name = "PERIOD_TYPE")
    private String periodType;

    // 活动优先级
    @Column(name = "PRIORITY")
    private Integer priority;

    // 店铺范围：00-全店铺 01-自定义
    @Column(name = "STORE_TYPE")
    private String storeType;

    @Column(name = "PRICE_CONDITION")
    private String priceCondition;

    // Activity sponsors
    @Column(name = "SPONSORS")
    private String sponsors;

    private String opsType;

    private String coolDown;

    @Column(name = "BACKGROUND_IMAGE")
    private String backgroundImage;

    //外部系统活动id
    @Column(name = "EXTERNAL_ACTIVITY_ID")
    private String externalActivityId;


    @Column(name = "PROMOTION_CATEGORY")
    private String promotionCategory;


    @Column(name = "PROMOTION_CATEGORY_NAME")
    private String promotionCategoryName;


    @Column(name = "CUSTOM_CONDITION")
    private String customCondition;

    // 商品条件：01-买A优惠B，B单价要低于A
    @Column(name = "PRODUCT_CONDITION")
    private String productCondition;

    private String ribbonImage;
    private String ribbonPosition;
    private String ribbonText;

    // Create user code.
    @Column(name="create_user")
    private String createUser;

    // Create time.
    @Column(name="create_time")
    private Date createTime;

    // Lastest update user.
    @Column(name="update_user")
    private String updateUser;

    // Lastest update time.
    @Column(name="update_time")
    private Date updateTime;

    // Logic delete. (0-normal 1-deleted)
    @Column(name="logic_delete")
    private Integer logicDelete;

    @Column(name="audit_user")
    private String auditUser;

    @Column(name="GROUP_CODE")
	private String groupCode;
    /**
     * 所有扩展字段
     * @see ActivityExtendParamsEnum
     */
    @Column(name="EXTEND_PARAMS")
    @ColumnType(typeHandler = JacksonTypeHandler.class, jdbcType = JdbcType.VARCHAR, column = "EXTEND_PARAMS")
    private JSONObject extendParams;


    @Column(name = "EXT_IMAGES")
    @ColumnType(typeHandler = ExtImageTypeHandler.class)
    private List<ExtImage> extImages;



    @Data
    public static class GiveawayRule {

        /**
         * 赠送方式
         * 1:全送 2:部分送
         */
        private String giveawayMethod;

        /**
         * 赠品可选不同赠品数量
         */
        private Integer giveawayChooseQty;

        /**
         * 赠品排序规则 1:有序 2:随机
         */
        private String giveawaySortRule;

        /**
         * 层级
         */
        private Integer rankParam;

    }

}