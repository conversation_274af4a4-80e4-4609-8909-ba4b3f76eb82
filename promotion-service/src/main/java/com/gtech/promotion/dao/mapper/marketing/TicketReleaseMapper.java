package com.gtech.promotion.dao.mapper.marketing;

import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.promotion.dao.entity.marketing.TicketReleaseEntity;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

//@Mapper
public interface TicketReleaseMapper extends GTechBaseMapper<TicketReleaseEntity> {

    @Update(" update MARKETING_TICKET_RELEASE set INVENTORY = INVENTORY - #{inventory}"
            + " where TENANT_CODE = #{tenantCode} and ACTIVITY_CODE = #{activityCode} and RELEASE_CODE = #{releaseCode} and INVENTORY >= #{inventory}")
    int updateInventory(@Param("tenantCode") String tenantCode, @Param("activityCode") String activityCode,
                        @Param("releaseCode") String releaseCode, @Param("inventory") long inventory);

    @Update(" update MARKETING_TICKET_RELEASE set USED = USED + 1 "
            + " where TENANT_CODE = #{tenantCode} and ACTIVITY_CODE = #{activityCode} and RELEASE_CODE = #{releaseCode}")
    int addUsed(@Param("tenantCode") String tenantCode, @Param("activityCode") String activityCode, @Param("releaseCode") String releaseCode);


    @Update(" update MARKETING_TICKET_RELEASE set QUALITY = QUALITY + #{quantity} , INVENTORY = INVENTORY + #{quantity} "
            + " where TENANT_CODE = #{tenantCode} and ACTIVITY_CODE = #{activityCode} and RELEASE_CODE = #{releaseCode}")
    int releaseSpecifiedQuantity(@Param("tenantCode") String tenantCode, @Param("activityCode") String activityCode, @Param("releaseCode") String releaseCode, @Param("quantity") Long quantity);


}
