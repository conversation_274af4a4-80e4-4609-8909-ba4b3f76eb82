package com.gtech.promotion.dao.mapper.purchaseconstraint;

import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.promotion.dao.entity.purchaseconstraint.PurchaseConstraintDetail;
import com.gtech.promotion.dao.model.purchaseconstraint.PurchaseConstraintDetailListModel;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.example.SelectByExampleMapper;
import tk.mybatis.mapper.common.example.SelectOneByExampleMapper;

import java.util.List;

public interface PurchaseConstraintDetailMapper extends GTechBaseMapper<PurchaseConstraintDetail>, SelectByExampleMapper<PurchaseConstraintDetail> {

    List<PurchaseConstraintDetail> list(@Param("query") PurchaseConstraintDetailListModel model);

    int decrement(@Param("query") PurchaseConstraintDetail detail);
}
