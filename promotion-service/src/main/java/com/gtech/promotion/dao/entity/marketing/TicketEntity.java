package com.gtech.promotion.dao.entity.marketing;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.persistence.Table;

@EqualsAndHashCode(callSuper = true)
@Table(name = "MARKETING_TICKET")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TicketEntity extends BaseEntity {

    public static final String C_TICKET_CODE = "ticketCode";
    public static final String C_MEMBER_CODE = "memberCode";
    public static final String C_STATUS = "status";
    public static final String C_USE_TIME = "useTime";
    public static final String C_FROZEN_STATUS = "frozenStatus";

    private String activityCode;
    private String releaseCode;
    private String ticketCode;
    private String memberCode;
    // 状态：01-已发放 02-已使用（未中奖） 03-已中奖
    private String status;
    private String useTime;
    private String luckyTime;
    private String frozenStatus;
}
