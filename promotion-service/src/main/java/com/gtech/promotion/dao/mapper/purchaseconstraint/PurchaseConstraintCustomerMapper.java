package com.gtech.promotion.dao.mapper.purchaseconstraint;

import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.promotion.dao.entity.purchaseconstraint.PurchaseConstraintCustomer;
import com.gtech.promotion.dao.model.purchaseconstraint.PurchaseConstraintCustomerListMode;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.example.SelectOneByExampleMapper;

import java.util.List;

public interface PurchaseConstraintCustomerMapper extends GTechBaseMapper<PurchaseConstraintCustomer>, SelectOneByExampleMapper<PurchaseConstraintCustomer> {
    int increment(@Param("query") List<PurchaseConstraintCustomer> customers);

    int decrement(@Param("query") PurchaseConstraintCustomer purchaseConstraintCustomer);

    List<PurchaseConstraintCustomer> list(@Param("query") PurchaseConstraintCustomerListMode mode);

}
