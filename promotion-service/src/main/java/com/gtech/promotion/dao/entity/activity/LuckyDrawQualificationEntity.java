package com.gtech.promotion.dao.entity.activity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2021/1/25 13:55
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "lucky_draw_qualification")
public class LuckyDrawQualificationEntity {

    public static final String C_ID = "id";
    public static final String C_ACTIVITY_CODE = "activityCode";
    public static final String C_TENANT_CODE = "tenantCode";
    public static final String C_QUALIFICATION_CODE = "qualificationCode";

    // ID
    @Id
    @Column(name = "ID")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "ACTIVITY_CODE")
    private String activityCode;

    private String domainCode;

    private String tenantCode;

    private String orgCode;

    @Column(name = "QUALIFICATION_CODE")
    private String qualificationCode;

    @Column(name = "QUALIFICATION_VALUE")
    private String qualificationValue;

    private String qualificationValueName;

    // 资格关系，01-OR 02-AND
    private String relation;

    // Create user code.
    @Column(name="CREATE_USER")
    private String createUser;

    // Create time.
    @Column(name="CREATE_TIME")
    private Date createTime;

    // Lastest update user.
    @Column(name="UPDATE_USER")
    private String updateUser;

    // Lastest update time.
    @Column(name="UPDATE_TIME")
    private Date updateTime;

    @Column(name="LOGIC_DELETE")
    private Integer logicDelete;


    //根据标签指定排除会员 02
    @Column(name="IS_EXCLUDE")
    private String isExclude;
}
