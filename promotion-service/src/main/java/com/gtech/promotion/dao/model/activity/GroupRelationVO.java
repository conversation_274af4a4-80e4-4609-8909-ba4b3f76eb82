package com.gtech.promotion.dao.model.activity;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/7/13 15:16
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GroupRelationVO {

    @ApiModelProperty(value = "分组编码A", example = "1231209", required = true)
    private String groupCodeA;

    @ApiModelProperty(value = "分组编码B", example = "1231209", required = true)
    private String groupCodeB;

    @ApiModelProperty(value = "分组关系 1:叠加 2：互斥", example = "1", required = true)
    private Integer relation;

}
