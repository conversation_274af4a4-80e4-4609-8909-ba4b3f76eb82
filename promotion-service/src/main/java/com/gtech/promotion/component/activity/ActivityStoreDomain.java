/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.component.activity;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.code.activity.StoreTypeEnum;
import com.gtech.promotion.dao.model.activity.TPromoActivityStoreVO;
import com.gtech.promotion.service.activity.ActivityStoreService;

/**
 * 多店铺相关
 * 
 */
@Service
public class ActivityStoreDomain {

    @Autowired
    private ActivityStoreService storeService;

    /**
     * 校验店铺是否可参与活动
     */
    public boolean checkStore111(String orgCode,String activityCode){
        if (StringUtil.isNotBlank(orgCode) && !StoreTypeEnum.ALL.code().equals(orgCode)){
            List<TPromoActivityStoreVO> storeVOs = storeService.getStoresByActivityCode(activityCode);
            if (!CollectionUtils.isEmpty(storeVOs)){
                boolean flag = false;
                for (TPromoActivityStoreVO storeVO : storeVOs){
                    if (orgCode.equals(storeVO.getOrgCode())){
                        flag = true;
                        break;
                    }
                }
                return flag;
            }
        }
        return true;

    }

}
