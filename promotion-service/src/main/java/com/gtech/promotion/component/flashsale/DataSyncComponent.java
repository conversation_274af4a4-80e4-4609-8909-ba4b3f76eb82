package com.gtech.promotion.component.flashsale;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.pim.client.PimClient;
import com.gtech.pim.request.ProductVo;
import com.gtech.pim.request.QuerySkuVo;
import com.gtech.pim.response.JsonResult;
import com.gtech.pim.response.ResultSkuCodeVo;
import com.gtech.promotion.code.flashsale.SyncPriceStatusEnum;
import com.gtech.promotion.code.marketing.ActivityTypeEnum;
import com.gtech.promotion.dao.model.activity.ActivityPeriodModel;
import com.gtech.promotion.dao.model.marketing.MarketingModel;
import com.gtech.promotion.dao.model.marketing.flashsale.FlashSaleProductModel;
import com.gtech.promotion.dto.in.flashsale.QueryFlashSalePriceByProductInDto;
import com.gtech.promotion.dto.out.marketing.QueryFlashSalePriceByProductOutDto;
import com.gtech.promotion.service.activity.ActivityPeriodService;
import com.gtech.promotion.service.flashsale.FlashSaleProductService;
import com.gtech.promotion.service.marketing.MarketingService;
import com.gtech.promotion.utils.CronUtil;
import com.gtech.promotion.utils.ListUtil;

@Component
public class DataSyncComponent {

    @Autowired
    private FlashSaleProductService flashSaleProductService;
    @Autowired
    private MarketingService marketingService;
    @Autowired
    private ActivityPeriodService activityPeriodService;
    @Autowired
    private PimClient pimClient;

    /**
     * 根据批量商品查询秒杀价格，同spu多个秒杀价的情况，取最低的价格
     * @param inDto 入参
     * @return 秒杀价格
     */
    public List<QueryFlashSalePriceByProductOutDto> queryFlashSalePriceByProduct(QueryFlashSalePriceByProductInDto inDto){
        List<QueryFlashSalePriceByProductOutDto> resultList = new ArrayList<>();
        List<MarketingModel> marketingModels = marketingService.queryCurrentEffectiveMarketingList(inDto.getTenantCode(), ActivityTypeEnum.FLASH_SALE.code());
        if (CollectionUtils.isEmpty(marketingModels)){
            return resultList;
        }
        List<String> activityCodes = new ArrayList<>(marketingModels.size() * 2);
        List<String> skuCodes = new ArrayList<>(inDto.getProducts().size() * 2);
        marketingModels.forEach(x -> activityCodes.add(x.getActivityCode()));
        inDto.getProducts().forEach(x -> skuCodes.add(x.getSkuCode()));
        List<FlashSaleProductModel> products = flashSaleProductService.getProductsByActivityCodesAndProducts(inDto.getTenantCode(), activityCodes, skuCodes);
        if (CollectionUtils.isEmpty(products)){
            return resultList;
        }
        Map<String, BigDecimal> productPriceMap = new HashMap<>();
        for (QueryFlashSalePriceByProductInDto.Product product : inDto.getProducts()) {
            String productCode = product.getProductCode();
            String skuCode = product.getSkuCode();
            for (FlashSaleProductModel flashSaleProductModel : products) {
                BigDecimal flashPrice = flashSaleProductModel.getFlashPrice();
                if (skuCode.equals(flashSaleProductModel.getSkuCode()) && (productPriceMap.getOrDefault(productCode, flashPrice).compareTo(flashPrice) >= 0)){
                    productPriceMap.put(productCode, flashPrice);
                }
            }
        }
        List<QueryFlashSalePriceByProductOutDto> result = new ArrayList<>();
        for (Map.Entry<String, BigDecimal> stringBigDecimalEntry : productPriceMap.entrySet()) {
            QueryFlashSalePriceByProductOutDto outDto = new QueryFlashSalePriceByProductOutDto();
            outDto.setProductCode(stringBigDecimalEntry.getKey());
            outDto.setPrice(stringBigDecimalEntry.getValue());
            result.add(outDto);
        }
        return result;
    }

    /**
     * 根据批量skuCode查询商品productCode信息
     * @param skuCodes skuCodes
     */
    private List<ResultSkuCodeVo> queryProductBySkuCodes(String tenantCode, List<String> skuCodes){
        QuerySkuVo querySkuVo = new QuerySkuVo();
        querySkuVo.setTenantCode(tenantCode);
        querySkuVo.setSkuCodeList(skuCodes);
        if(skuCodes.size() > 500){
            List<List<String>> codeList = ListUtil.splitList(skuCodes,500);
            List<ResultSkuCodeVo> resultSkuCodeVoList = new ArrayList<>();
            if(!CollectionUtils.isEmpty(codeList)){
                for(List<String> codes : codeList){
                    querySkuVo.setSkuCodeList(codes);
                    JsonResult<List<ResultSkuCodeVo>> listJsonResult = pimClient.queryProductSkuCodeList(querySkuVo);
                    if (Boolean.TRUE.equals(listJsonResult.getSuccess()) && CollectionUtils.isNotEmpty(listJsonResult.getData())){
                        resultSkuCodeVoList.addAll(listJsonResult.getData());
                    }
                }
            }
            return resultSkuCodeVoList;
        }else{
            JsonResult<List<ResultSkuCodeVo>> listJsonResult = pimClient.queryProductSkuCodeList(querySkuVo);
            if (Boolean.TRUE.equals(listJsonResult.getSuccess()) && CollectionUtils.isNotEmpty(listJsonResult.getData())){
                return listJsonResult.getData();
            }
        }

        return Collections.emptyList();
    }

    /**
     * 发送活动价格给商品服务
     */
    @Async
    public void sendPriceToPim(String tenantCode, String activityCode, boolean activityCancel){
        List<FlashSaleProductModel> productModels = flashSaleProductService.findListByActivityCode(activityCode);
        if (CollectionUtils.isEmpty(productModels)){
            return;
        }
        List<String> skuCodes = new ArrayList<>();
        productModels.forEach(x-> skuCodes.add(x.getSkuCode()));
        productModels.clear();
        List<ResultSkuCodeVo> resultSkuCodeVos = queryProductBySkuCodes(tenantCode, skuCodes);
        if (CollectionUtils.isEmpty(resultSkuCodeVos)){
            return;
        }
        QueryFlashSalePriceByProductInDto inDto = new QueryFlashSalePriceByProductInDto();
        inDto.setTenantCode(tenantCode);
        inDto.setProducts(BeanCopyUtils.jsonCopyList(resultSkuCodeVos, QueryFlashSalePriceByProductInDto.Product.class));
        List<QueryFlashSalePriceByProductOutDto> productOutDtoList = queryFlashSalePriceByProduct(inDto);
        List<ProductVo> productVoList = new ArrayList<>();
        setProductRelationInfo(tenantCode, activityCode, activityCancel, resultSkuCodeVos, productOutDtoList, productVoList);
        if (CollectionUtils.isNotEmpty(productOutDtoList)){
            for (QueryFlashSalePriceByProductOutDto queryFlashSalePriceByProductOutDto : productOutDtoList) {
                ProductVo productVo = new ProductVo();
                productVo.setTenantCode(tenantCode);
                productVo.setProductCode(queryFlashSalePriceByProductOutDto.getProductCode());
                productVo.setActivityPrice(queryFlashSalePriceByProductOutDto.getPrice());
                productVo.setActivityCancel(activityCancel);
                productVoList.add(productVo);
                if (productVoList.size() >= 1000) {
                    sendAndSaveStatus(activityCode, activityCancel, productVoList);
                    productVoList.clear();
                }
            }
        }
        if (CollectionUtils.isNotEmpty(productVoList)){
            sendAndSaveStatus(activityCode, activityCancel, productVoList);
        }
    }

    public void setProductRelationInfo(String tenantCode, String activityCode, boolean activityCancel, List<ResultSkuCodeVo> resultSkuCodeVos, List<QueryFlashSalePriceByProductOutDto> productOutDtoList, List<ProductVo> productVoList) {
        if (activityCancel) {
            Map<String, List<QueryFlashSalePriceByProductOutDto>> collect = productOutDtoList.stream().collect(Collectors.groupingBy(QueryFlashSalePriceByProductOutDto::getProductCode));
            for (ResultSkuCodeVo resultSkuCodeVo : resultSkuCodeVos) {
                if (!collect.containsKey(resultSkuCodeVo.getProductCode())){
                    ProductVo productVo = new ProductVo();
                    productVo.setTenantCode(tenantCode);
                    productVo.setProductCode(resultSkuCodeVo.getProductCode());
                    productVo.setActivityCancel(true);
                    productVoList.add(productVo);
                    if (productVoList.size() >= 1000) {
                        sendAndSaveStatus(activityCode, true, productVoList);
                        productVoList.clear();
                    }
                }
            }
        }
    }

    private void sendAndSaveStatus(String activityCode, boolean activityCancel, List<ProductVo> productVoList){
        JsonResult<Void> voidJsonResult = pimClient.batchUpdateProductToEs(productVoList);
        if (voidJsonResult.getSuccess()) {
            MarketingModel model = new MarketingModel();
            model.setActivityCode(activityCode);
            model.setSyncPriceStatus(activityCancel ? SyncPriceStatusEnum.SYNC_CANCEL.getStatus(): SyncPriceStatusEnum.SYNC.getStatus());
            marketingService.updateByActivityCode(model);
        }
    }

    @Async
    public void syncPriceTimer() {
        String minId = "0";
        int pageSize = 500;
        while (true) {
            List<MarketingModel> marketingModels = marketingService.queryAllTenantEffectiveFlashSaleList(minId, pageSize);
            for (MarketingModel marketingModel : marketingModels) {
                minId = getMinId(marketingModel);
            }
            if (marketingModels.size() < pageSize) {
                break;
            }
        }
    }

    public String getMinId(MarketingModel marketingModel) {
        String minId;
        minId = marketingModel.getId();
        String tenantCode = marketingModel.getTenantCode();
        String activityCode = marketingModel.getActivityCode();
        ActivityPeriodModel period = activityPeriodService.findPeriod(tenantCode, activityCode);
        if (null != period) {
            String currentDateAsString = DateUtil.getCurrentDateAsString(DateUtil.FORMAT_YYYYMMDDHHMISS_14);
            boolean checkDate = CronUtil.checkDate(period.getBeginPeriod(), period.getEndPeriod(), period.getIntervalWeek(), marketingModel.getActivityBegin(), marketingModel.getActivityEnd(), currentDateAsString);
            if (checkDate && (null == marketingModel.getSyncPriceStatus() || SyncPriceStatusEnum.SYNC_CANCEL.getStatus().equals(marketingModel.getSyncPriceStatus()))) { // 生效周期内
                sendPriceToPim(tenantCode, activityCode, false);
            }else if (!checkDate && SyncPriceStatusEnum.SYNC.getStatus().equals(marketingModel.getSyncPriceStatus())){
                sendPriceToPim(tenantCode, activityCode, true);
            }
        } else {
            if (null == marketingModel.getSyncPriceStatus() || SyncPriceStatusEnum.SYNC_CANCEL.getStatus().equals(marketingModel.getSyncPriceStatus())){
                sendPriceToPim(tenantCode, activityCode, false);
            }
        }
        return minId;
    }
}
