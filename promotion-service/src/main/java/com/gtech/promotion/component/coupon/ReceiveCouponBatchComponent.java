/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.component.coupon;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Splitter;
import com.gtech.basic.idm.web.vo.result.QueryAccountListResult;
import com.gtech.commons.exception.Exceptions;
import com.gtech.commons.redis.GTechRedisTemplate;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.commons.utils.StringUtil;
import com.gtech.member.client.MemberClient;
import com.gtech.member.request.QueryMemberByCouponSendParam;
import com.gtech.member.response.MemberInfo;
import com.gtech.member.response.QueryMemberByCouponSendResult;
import com.gtech.promotion.checker.activity.TPromoActivityChecker;
import com.gtech.promotion.checker.coupon.CouponAllocateChecker;
import com.gtech.promotion.checker.coupon.CouponExportChecker;
import com.gtech.promotion.code.activity.LimitationCodeEnum;
import com.gtech.promotion.code.coupon.CouponStatusEnum;
import com.gtech.promotion.code.coupon.CouponTypeEnum;
import com.gtech.promotion.component.activity.QualificationDomain;
import com.gtech.promotion.dao.model.activity.ActivityModel;
import com.gtech.promotion.dao.model.activity.PromoCouponSendDetailModel;
import com.gtech.promotion.dao.model.activity.PromoCouponSendLogModel;
import com.gtech.promotion.dao.model.coupon.CouponReleaseModel;
import com.gtech.promotion.dao.model.coupon.ReleaseCouponVO;
import com.gtech.promotion.dao.model.coupon.TPromoCouponInnerCodeVO;
import com.gtech.promotion.domain.coupon.CouponReleaseDomain;
import com.gtech.promotion.domain.coupon.CouponReleaseInventoryDomain;
import com.gtech.promotion.dto.in.coupon.ReceiveCouponBatchDTO;
import com.gtech.promotion.dto.in.coupon.SendAnonymousCouponBatchDTO;
import com.gtech.promotion.dto.out.coupon.ReceiveCouponBatchOutDTO;
import com.gtech.promotion.exception.Check;
import com.gtech.promotion.exception.ErrorCodes;
import com.gtech.promotion.feign.IdmFeignClient;
import com.gtech.promotion.feign.bean.QueryUserByAccountRequest;
import com.gtech.promotion.helper.RedisLock;
import com.gtech.promotion.service.coupon.CouponSendDetailService;
import com.gtech.promotion.service.coupon.CouponSendLogService;
import com.gtech.promotion.service.coupon.PromoCouponInnerCodeService;
import com.gtech.promotion.service.coupon.PromoCouponReleaseService;
import com.gtech.promotion.utils.Constants;
import com.gtech.promotion.vo.result.coupon.SendAnonymousCouponResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ReceiveCouponBatchComponent {

    @Autowired
    private PromoCouponReleaseService promoCouponReleaseService;

    @Autowired
    private CouponActivityComponent couponActivityDomain;

    @Autowired
    private CouponInnerCodeComponent couponInnerCodeDomain;

    @Autowired
    private CouponSendLogService couponSendLogService;

    @Autowired
    private CouponSendDetailService couponSendDetailService;

    @Autowired
    private MemberClient memberClient;

    @Autowired
    private IdmFeignClient idmFeignClient;

    @Autowired
    private GTechRedisTemplate redisTemplate;

    @Autowired
    private QualificationDomain qualificationDomain;

    @Autowired
    private PromoCouponInnerCodeService promoCouponInnerCodeService;

    @Autowired
    private RedisLock redisLock;


    private  static final int PAGE_SIZE = 50;

    private  static final String APP_KEY = "PROMOTION";
    private  static final String ACCOUNT_KEY = "Account(";

    public List<ReceiveCouponBatchOutDTO> receiveCouponBatch(ReceiveCouponBatchDTO paramDto){

        ActivityModel activityModel = couponActivityDomain.findValidActivity(paramDto.getTenantCode(), paramDto.getActivityCode(), null, new Date());
        if (null == activityModel) {
            throw Exceptions.fail(ErrorCodes.VALIDATE_COUPON_ACTIVITY_EXIST, paramDto.getActivityCode());
        }

        Check.check(!CouponTypeEnum.PROMOTION_COUPON.equalsCode(activityModel.getCouponType()), CouponExportChecker.NOT_ANON_COUPON);
        if (null != activityModel.getUserLimitMax() && activityModel.getUserLimitMax() != 0){
            Check.check(activityModel.getUserLimitMax() < paramDto.getReceiveCount(), CouponAllocateChecker.OVER_ALLOCATE_TOPLIMIT);
        }
        // 获取所有可领券的投放
        List<CouponReleaseDomain> couponRelease = promoCouponReleaseService.queryCanReceiveReleases(paramDto.getTenantCode(), paramDto.getActivityCode(),
                paramDto.getReleaseCode());
        if (CollectionUtils.isEmpty(couponRelease)) {
            throw Exceptions.fail(ErrorCodes.COUPON_INVENTORY_NOT_ENOUGH);
        }

        List<ReceiveCouponBatchOutDTO> resultList = new ArrayList<>(paramDto.getUserList().size());

        Set<String> userCodes = new HashSet<>();
        for(ReceiveCouponBatchDTO.UserAndLevel x : paramDto.getUserList()) {
            // 过滤会员等级
            if (!qualificationDomain.checkQualificationForSendCoupon(paramDto.getTenantCode(), paramDto.getActivityCode(), x.getQualifications())) {
                resultList.add(ReceiveCouponBatchOutDTO.builder()
                        .userCode(x.getUserCode())
                        .code(ErrorCodes.COUPON_RECEIVE_MEMBER_QUALIFICATION_FAILED.toExceptionCode())
                        .build());
            }
            userCodes.add(x.getUserCode());
        }

        Map<String, String> userCodesMap = this.couponInnerCodeDomain.allocateCoupon(Arrays.asList(userCodes.toArray(new String[userCodes.size()])), paramDto.getReceiveCount(),
                paramDto.getTakeLabel(), activityModel, couponRelease,1, null);
        for(Map.Entry<String, String> e : userCodesMap.entrySet()) {
            resultList.add(ReceiveCouponBatchOutDTO.builder()
                    .userCode(e.getKey())
                    .code(Result.DEFAULT_SUCCESS_CODE)
                    .couponCode(e.getValue())
                    .build());
        }

        return resultList;
    }

    @Transactional
    public List<SendAnonymousCouponResult> sendAnonymousCoupon(SendAnonymousCouponBatchDTO paramDto) {
        if (StringUtils.isNotBlank(paramDto.getReleaseCode())) {
            CouponReleaseModel release = promoCouponReleaseService.findCanReceiveRelease(paramDto.getActivityCode(), paramDto.getReleaseCode());
            Check.check(null == release, TPromoActivityChecker.NOT_RELEASE_INVENTORY);
        }
        ActivityModel activityModel = couponActivityDomain.findSendCouponValidActivity(paramDto.getTenantCode(), paramDto.getActivityCode(), null, new Date());
        Check.check(null == activityModel, TPromoActivityChecker.NOT_NULL_OR_NO_EFFECTIVE);
        String couponType = activityModel.getCouponType();
        Check.check(!CouponTypeEnum.ANONYMITY_COUPON.equalsCode(couponType), TPromoActivityChecker.SUPPORT_ANONYMOUS);

        //只针对匿名券 查询所有可供发送的券,为空代表没有券可被领取.
        Integer sendTotal = paramDto.getSendTotal();
        List<ReleaseCouponVO> releaseCouponVOS = promoCouponReleaseService.queryReleaseCouponRecord(paramDto.getTenantCode(), paramDto.getActivityCode(),
                paramDto.getReleaseCode(), sendTotal,paramDto.getReceiveType());
        Check.check(CollectionUtils.isEmpty(releaseCouponVOS), TPromoActivityChecker.NO_COUPON);
        //判断券数量是是否够发送
        Check.check(releaseCouponVOS.size() < paramDto.getSendTotal(), TPromoActivityChecker.COUPON_INVENTORY,String.valueOf(releaseCouponVOS.size()));

        //releaseCode 领取添加分布式锁
        List<String> collect = releaseCouponVOS.stream().map(ReleaseCouponVO::getReleaseCode).collect(Collectors.toList());
        List<CouponReleaseDomain> releaseDomains = promoCouponReleaseService.queryReleaseByCondition(activityModel.getTenantCode(), activityModel.getActivityCode(), collect);
        handleAnonymousInventory(releaseDomains, sendTotal, activityModel.getReserveInventory());

        List<TPromoCouponInnerCodeVO> updateInnerList = new ArrayList<>();
        releaseCouponVOS.stream().forEach(x -> {
            TPromoCouponInnerCodeVO vo = new TPromoCouponInnerCodeVO();
            vo.setTenantCode(x.getTenantCode());
            vo.setCouponCode(x.getCouponCode());
            vo.setActivityCode(x.getActivityCode());
            String receiveEndTime = x.getReceiveEndTime();
            //每批投放券记录的领取后几天生效天数
            Integer integer = x.getValidDays();
            if(null != integer && integer.intValue()>0){
                String activityEnd = activityModel.getActivityEnd();
                Calendar instance = Calendar.getInstance();
                Date startDate = instance.getTime();
                String startTime = DateUtil.format(startDate, DateUtil.FORMAT_YYYYMMDDHHMISS_14);
                if (startTime.compareTo(receiveEndTime) <= 0) {
                    vo.setValidStartTime(startTime);
                } else {
                    //判断未被领取的券 领取时间过期 测所有剩下的券都过期
                    vo.setValidStartTime(receiveEndTime);
                    vo.setStatus(CouponStatusEnum.EXPIRE.code());
                }
                instance.add(Calendar.DATE, integer);
                Date endDate = instance.getTime();
                String endTime = DateUtil.format(endDate, DateUtil.FORMAT_YYYYMMDDHHMISS_14);
                if (endTime.compareTo(activityEnd) <= 0) {
                    vo.setValidEndTime(endTime);
                } else {
                    vo.setValidEndTime(activityEnd);
                }
            }else {
                vo.setValidStartTime(x.getValidStartTime());
                vo.setValidEndTime(x.getValidEndTime());
            }
            updateInnerList.add(vo);

        });
        promoCouponInnerCodeService.updateBatchInnerCodeValidTime(updateInnerList);

        return  BeanCopyUtils.jsonCopyList(updateInnerList, SendAnonymousCouponResult.class);
    }
    public List<CouponReleaseInventoryDomain> handleAnonymousInventory(List<CouponReleaseDomain> releaseDomains, int receiveCount, int quota) {

        if (CollectionUtils.isEmpty(releaseDomains)) {
            throw Exceptions.fail(ErrorCodes.COUPON_INVENTORY_NOT_ENOUGH);
        }
        String tenantCode = releaseDomains.get(0).getTenantCode();
        String activityCode = releaseDomains.get(0).getActivityCode();
        List<CouponReleaseInventoryDomain> releaseInventoryDomains;
        checkAnonymousInventory(releaseDomains, receiveCount, quota);
        String lockName = Constants.PROMOTION_COUPON_CODE_LOCK_KEY + ":" + tenantCode + ":" + activityCode;
        String key = "";
        if (releaseDomains.size() > 1){
            // 多个投放批次，添加分布式锁
            key = redisLock.tryLockAndRetry(lockName, 1000L, 3);
            Check.check(StringUtil.isBlank(key), CouponAllocateChecker.LOCK_FAIL);
            try {
                releaseInventoryDomains = promoCouponReleaseService.deductInventory(tenantCode, activityCode, releaseDomains, receiveCount);
            } finally {
                redisLock.unlock(lockName, key);
            }
        }else {
            releaseInventoryDomains = promoCouponReleaseService.deductInventory(tenantCode, activityCode, releaseDomains, receiveCount);
        }
        if (CollectionUtils.isEmpty(releaseInventoryDomains)) {
            throw Exceptions.fail(ErrorCodes.COUPON_INVENTORY_NOT_ENOUGH);
        }
        return releaseInventoryDomains;
    }

    public List<CouponReleaseDomain> checkAnonymousInventory(List<CouponReleaseDomain> releaseDomains, int receiveCount, int quota) {
        List<CouponReleaseDomain> couponReleaseDomains = new ArrayList<>();
        boolean enough = false;
        int inventory = 0;
        for (CouponReleaseDomain releaseDomain : releaseDomains){
            // 寻找还有库存的couponRelease
            if (releaseDomain.getInventory().intValue() >= 0){
                couponReleaseDomains.add(releaseDomain);
                inventory += releaseDomain.getInventory();
                if (inventory >= receiveCount + quota){
                    enough = true;
                    break;
                }
            }
        }
        if (!enough) {
            throw Exceptions.fail(ErrorCodes.COUPON_INVENTORY_NOT_ENOUGH);
        }
        return couponReleaseDomains;
    }

    @Async
    public void receiveCouponBatchAsync(ReceiveCouponBatchDTO paramDto){

        log.info("start receiveCouponBatch!ReceiveCouponBatchDTO:{}", JSON.toJSONString(paramDto));
        String redisKey = paramDto.getTenantCode()+paramDto.getActivityCode()+paramDto.getReleaseCode();
        PromoCouponSendLogModel promoCouponSendLogModel = convertPromoCouponSendLogModel(paramDto);
        try {
            if(!StringUtils.isEmpty(paramDto.getErrorMessage())){
                promoCouponSendLogModel.setFailReason(paramDto.getErrorMessage());
                promoCouponSendLogModel.setStatus(0);
                couponSendLogService.createCouponSendLog(promoCouponSendLogModel);
                return;
            }
            promoCouponSendLogModel.setStatus(3);
            //Redis存在key，则该批次发券正在进行，
            Boolean isExist = checkRedisKeyExist(redisKey);
            if(Boolean.FALSE.equals(isExist)){
                log.info("activityCode:"+paramDto.getActivityCode()+",releaseCode:{}",paramDto.getReleaseCode()+", is issuing vouchers, please operate later. ");
                promoCouponSendLogModel.setFailReason("This batch is issuing vouchers, please operate later.");
                promoCouponSendLogModel.setStatus(0);
                couponSendLogService.createCouponSendLog(promoCouponSendLogModel);
                return;
            }

            //插入日志，状态为正在进行中
            redisTemplate.opsValueSet(APP_KEY, redisKey, redisKey);
            promoCouponSendLogModel.setStatus(3);
            couponSendLogService.createCouponSendLog(promoCouponSendLogModel);

            promoCouponSendLogModel.setStatus(0);
            ActivityModel activityModel = couponActivityDomain.findSendCouponValidActivity(paramDto.getTenantCode(), paramDto.getActivityCode(), null, new Date());
            if (null == activityModel) {
                promoCouponSendLogModel.setFailReason("No effective coupon activity was found");
                couponSendLogService.updateCouponSendLog(promoCouponSendLogModel);
                return;
            }

            if (!CouponTypeEnum.PROMOTION_COUPON.equalsCode(activityModel.getCouponType())) {
                promoCouponSendLogModel.setFailReason("Coupon activity is not a discount activity.");
                couponSendLogService.updateCouponSendLog(promoCouponSendLogModel);
                return;
            }

            if (null != activityModel.getUserLimitMax() && 0 != activityModel.getUserLimitMax() && activityModel.getUserLimitMax() < paramDto.getReceiveCount()) {
                promoCouponSendLogModel.setFailReason("The number of coupons sent exceeds the individual maximum.");
                couponSendLogService.updateCouponSendLog(promoCouponSendLogModel);
                return;
            }

            if (null != activityModel.getUserLimitMaxDay() && 0 != activityModel.getUserLimitMaxDay() && activityModel.getUserLimitMaxDay() < paramDto.getReceiveCount()) {
                promoCouponSendLogModel.setFailReason("The number of coupons sent exceeds the individual maximum.");
                couponSendLogService.updateCouponSendLog(promoCouponSendLogModel);
                return;
            }

            // 获取所有可领券的投放
            List<CouponReleaseDomain> couponRelease = promoCouponReleaseService.queryCanReceiveReleases(paramDto.getTenantCode(), paramDto.getActivityCode(),
                    paramDto.getReleaseCode());
            if (CollectionUtils.isEmpty(couponRelease)) {
                promoCouponSendLogModel.setFailReason("Release coupon is empty.");
                couponSendLogService.updateCouponSendLog(promoCouponSendLogModel);
                return;
            }


            CouponReleaseDomain couponReleaseDomain = couponRelease.get(0);
            promoCouponSendLogModel.setCouponQty(couponReleaseDomain.getInventory());
            log.info("查询券剩余数量为:{}",couponReleaseDomain.getInventory());

            List<String> canSendUserList = new ArrayList<>();
            int  queryMemberCount = queryMemberCount(paramDto, promoCouponSendLogModel, canSendUserList);
            promoCouponSendLogModel.setUserQty(queryMemberCount);
            int canSendUserCount = canSendUserList.size();
            if("1".equals(paramDto.getIsAllUser())){
                canSendUserCount = queryMemberCount;
                log.info("总共需要发送会员数量：{}，实际查询会员数量:{}",queryMemberCount,queryMemberCount);
                if (0 == queryMemberCount) {
                    promoCouponSendLogModel.setFailReason("All users do not exist.");
                    couponSendLogService.updateCouponSendLog(promoCouponSendLogModel);
                    return;
                }
            }else{
                log.info("总共需要发送会员数量：{}，实际查询会员数量:{}",queryMemberCount,canSendUserCount);
                if (0 == canSendUserCount) {
                    promoCouponSendLogModel.setFailReason("All users do not exist.");
                    couponSendLogService.updateCouponSendLog(promoCouponSendLogModel);
                    return;
                }
            }

            //如果发券数量为0，则默认收1张券
            if (paramDto.getReceiveCount() == 0) {
                paramDto.setReceiveCount(1);
            }
            int needSendCount = new BigDecimal(canSendUserCount).multiply(new BigDecimal(paramDto.getReceiveCount())).intValue();
            log.info("实际一共需要发送券数量:{}", needSendCount);
            if(couponReleaseDomain.getInventory() < needSendCount){
                promoCouponSendLogModel.setFailReason("The total quantity of coupons is less than the customers.");
                couponSendLogService.updateCouponSendLog(promoCouponSendLogModel);
                return;
            }

            List<PromoCouponSendDetailModel> errorList = queryMemberAndSendCoupon(paramDto,activityModel,couponRelease,canSendUserList,promoCouponSendLogModel);
            List<PromoCouponSendDetailModel> couponSendDetailModelList = promoCouponSendLogModel.getCouponSendDetailModelList();
            couponSendDetailModelList.addAll(errorList);
            if(!CollectionUtils.isEmpty(couponSendDetailModelList)){
                couponSendDetailService.createCouponSendDetailList(couponSendDetailModelList);
            }

            if(!CollectionUtils.isEmpty(promoCouponSendLogModel.getCouponSendDetailModelList())){
                log.info("send detail mode list is not empty.set status is 2");
                if(promoCouponSendLogModel.getUserQty() == promoCouponSendLogModel.getCouponSendDetailModelList().size()){
                    promoCouponSendLogModel.setStatus(0);
                }else {
                    promoCouponSendLogModel.setStatus(2);
                }
            }else{
                promoCouponSendLogModel.setStatus(1);
            }

            couponSendLogService.updateCouponSendLog(promoCouponSendLogModel);
        }catch (Exception e){
            log.error("发送券批次异常!");
            log.error(e.getMessage(), e);
            promoCouponSendLogModel.setStatus(0);
            promoCouponSendLogModel.setFailReason("system error.");
            couponSendLogService.updateCouponSendLog(promoCouponSendLogModel);
        }finally {
            redisTemplate.delete(APP_KEY,redisKey);
        }
    }

    public Boolean checkRedisKeyExist(String redisKey){
        //Redis存在key，则该批次发券正在进行，
    	return !redisTemplate.hasKey(APP_KEY,redisKey);
    }

    //查询会员并发券
    public List<PromoCouponSendDetailModel> queryMemberAndSendCoupon(ReceiveCouponBatchDTO paramDto,ActivityModel activityModel,List<CouponReleaseDomain> couponRelease,List<String> userCodeList,PromoCouponSendLogModel promoCouponSendLogModel){
        List<PromoCouponSendDetailModel> couponSendDetailModelList = new ArrayList<>();
        String currentDay = DateUtil.format(new Date(),DateUtil.FORMAT_YYYYMMDD);
        
        if(!"1".equals(paramDto.getIsAllUser())){
        	try {
                //过滤不符合用户领取数量的数据
                filterUserMaxLimit(activityModel,userCodeList,couponSendDetailModelList,paramDto.getReceiveCount(),promoCouponSendLogModel,currentDay);
                if(!CollectionUtils.isEmpty(userCodeList)) {
                    allocateCoupon(paramDto, activityModel, couponRelease, userCodeList);
                }
            }catch (Exception e){
                log.error("queryMemberAndSendCoupon error!");
                log.error(e.getMessage(), e);
                String userCode = String.join(",", userCodeList);
                PromoCouponSendDetailModel couponSendDetailModel = buildPromoCouponSendDetailModel(promoCouponSendLogModel);
                couponSendDetailModel.setFailReason(ACCOUNT_KEY+userCode+" ) send coupon error!");
                couponSendDetailModelList.add(couponSendDetailModel);
            }
        	 return couponSendDetailModelList;
        }
        
            Boolean keepQuery = true;
            int initialPageNum = 1;
            String minMemberCode = null;
            while (Boolean.TRUE.equals(keepQuery)) {
                try {
                    com.gtech.member.response.Result<QueryMemberByCouponSendResult> queryMemberByCouponSendResultResult = queryMemberByCouponSend(paramDto.getDomainCode(), paramDto.getTenantCode(), null, null, PAGE_SIZE, initialPageNum, minMemberCode);
                    List<MemberInfo> memberInfoList = queryMemberByCouponSendResultResult.getData().getMemberCodeList();
                    if (CollectionUtils.isEmpty(memberInfoList)) {
//                        keepQuery = false
                        break;
                    }
                    //查询用户
                    userCodeList = memberInfoList.stream().map(MemberInfo::getMemberCode).collect(Collectors.toList());
                    //过滤不符合用户领取数量的数据
                    filterUserMaxLimit(activityModel,userCodeList,couponSendDetailModelList,paramDto.getReceiveCount(),promoCouponSendLogModel,currentDay);
                    if(!CollectionUtils.isEmpty(userCodeList)) {
                        allocateCoupon(paramDto, activityModel, couponRelease, userCodeList);
                    }
                    if(memberInfoList.size() < PAGE_SIZE){
                        keepQuery = false;
                    }
                    minMemberCode = memberInfoList.get(memberInfoList.size() - 1).getMemberCode();
                }catch (Exception e){
                    log.error("queryMemberAndSendCoupon error!");
                    log.error(e.getMessage(), e);
//                    String userCode = Joiner.on(",").join(userCodeList)
                    String userCode = String.join(",", userCodeList);
                    PromoCouponSendDetailModel couponSendDetailModelModel = buildPromoCouponSendDetailModel(promoCouponSendLogModel);
                    couponSendDetailModelModel.setFailReason(ACCOUNT_KEY+userCode+") query member and send coupon error!");
                    couponSendDetailModelList.add(couponSendDetailModelModel);
                }
            }
            
        return couponSendDetailModelList;
    }

    public PromoCouponSendDetailModel buildPromoCouponSendDetailModel(PromoCouponSendLogModel promoCouponSendLogModel){
        PromoCouponSendDetailModel couponSendDetailModel = new PromoCouponSendDetailModel();
        couponSendDetailModel.setActivityCode(promoCouponSendLogModel.getActivityCode());
        couponSendDetailModel.setBatchNo(promoCouponSendLogModel.getSendBatchNo());
        couponSendDetailModel.setTenantCode(promoCouponSendLogModel.getTenantCode());
        couponSendDetailModel.setUpdateUser(promoCouponSendLogModel.getUpdateUser());
        couponSendDetailModel.setUpdateTime(promoCouponSendLogModel.getUpdateTime());
        couponSendDetailModel.setCreateUser(promoCouponSendLogModel.getCreateUser());
        couponSendDetailModel.setCreateTime(promoCouponSendLogModel.getCreateTime());
        return couponSendDetailModel;
    }

    public void filterUserMaxLimit(ActivityModel activityModel,List<String> userCodeList,List<PromoCouponSendDetailModel> couponSendDetailModelList,int receiveCount,PromoCouponSendLogModel promoCouponSendLogModel,String currentDay){
        log.info("start filterUserMaxLimit!");
        Integer userLimitMax = activityModel.getUserLimitMax();
        Integer userLimitMaxDay = activityModel.getUserLimitMaxDay();
        String activityCode = activityModel.getActivityCode();
        if(CollectionUtils.isEmpty(userCodeList)){
            return;
        }
        Iterator<String> userCodeIterator = userCodeList.iterator();
        while (userCodeIterator.hasNext()){
            String userCode = userCodeIterator.next();
            PromoCouponSendDetailModel couponSendDetailModel = buildPromoCouponSendDetailModel(promoCouponSendLogModel);
            String userMaxLimitKey = activityCode + userCode + LimitationCodeEnum.USER_RECEIVE_COUPON_LIMIT.code();
            Integer residue = redisTemplate.opsValueGet(APP_KEY, userMaxLimitKey, Integer.class);
            Boolean needResidue = false;
            if(null != userLimitMax && userLimitMax > 0) {
                log.info("进入用户最大可领券逻辑！");
                needResidue = true;
                Boolean hasUserMax = redisTemplate.hasKey(APP_KEY, userMaxLimitKey);
                if (Boolean.FALSE.equals(hasUserMax)) {
                    redisTemplate.opsValueSet(APP_KEY, userMaxLimitKey, userLimitMax);
                    residue = userLimitMax;
                }
                if (residue < receiveCount) {
                    log.info("发券数量大于用户总共可领取剩余数量!userCode:{},residueDay:{},receiveCount:{}", userCode, residue, receiveCount);
                    couponSendDetailModel.setFailReason(ACCOUNT_KEY+userCode+")The maximum times have been exceeded.");
                    couponSendDetailModel.setUserCode(userCode);
                    couponSendDetailModelList.add(couponSendDetailModel);
                    userCodeIterator.remove();
                    continue;
                }
            }

            if (null != userLimitMaxDay && userLimitMaxDay > 0) {
                log.info("进入用户单日最大可领券逻辑！");
                String userMaxLimitDayKey = activityCode + userCode + LimitationCodeEnum.USER_RECEIVE_COUPON_LIMIT_DAY.code() + currentDay;
                Boolean hasUserMaxDay = redisTemplate.hasKey(APP_KEY, userMaxLimitDayKey);
                if (Boolean.FALSE.equals(hasUserMaxDay)) {
                    redisTemplate.opsValueSet(APP_KEY, userMaxLimitDayKey, userLimitMaxDay);
                }

                Integer residueDay = redisTemplate.opsValueGet(APP_KEY, userMaxLimitDayKey, Integer.class);
                if (residueDay < receiveCount) {
                    log.info("发券数量大于用户每日可领取剩余数量!userCode:{},residueDay:{},receiveCount:{}", userCode, residueDay, receiveCount);
                    couponSendDetailModel.setFailReason(ACCOUNT_KEY+userCode+")The maximum times per day have been exceeded.");
                    couponSendDetailModel.setUserCode(userCode);
                    couponSendDetailModelList.add(couponSendDetailModel);
                    userCodeIterator.remove();
                    continue;
                }
                log.info("开始执行扣减用户单日领取数量！");
                redisTemplate.opsValueSet(APP_KEY, userMaxLimitDayKey, residueDay - receiveCount);

            }

            if(Boolean.TRUE.equals(needResidue)) {
                log.info("开始执行扣减用户领取数量！");
                redisTemplate.opsValueSet(APP_KEY, userMaxLimitKey, residue - receiveCount);
            }
        }
    }

    public void returnUserMaxLimit(ActivityModel activityModel,String userCode,int receiveCount,String currentDay){
        log.info("start returnUserMaxLimit!activityModel:{}，userCode：{}，receiveCount:{},currentDay：{}",JSON.toJSONString(activityModel),userCode,receiveCount,currentDay);
        try {
            String activityCode = activityModel.getActivityCode();
            String userMaxLimitKey = activityCode + userCode + LimitationCodeEnum.USER_RECEIVE_COUPON_LIMIT.code();
            String userMaxLimitDayKey = activityCode + userCode + LimitationCodeEnum.USER_RECEIVE_COUPON_LIMIT_DAY.code() + currentDay;
            Integer residueDay = redisTemplate.opsValueGet(APP_KEY, userMaxLimitDayKey, Integer.class);
            Integer residue = redisTemplate.opsValueGet(APP_KEY, userMaxLimitKey, Integer.class);
            redisTemplate.opsValueSet(APP_KEY, userMaxLimitDayKey, residueDay + receiveCount);
            redisTemplate.opsValueSet(APP_KEY, userMaxLimitKey, residue + receiveCount);
        }catch (Exception e){
            log.error(e.getMessage(), e);
        }
    }

    //执行发券
    public void allocateCoupon(ReceiveCouponBatchDTO paramDto,ActivityModel activityModel,List<CouponReleaseDomain> couponRelease,List<String> userCodeList){
        this.couponInnerCodeDomain.allocateCoupon(userCodeList, paramDto.getReceiveCount(),
                paramDto.getTakeLabel(), activityModel, couponRelease, 1, null);
    }

    //查询会员数量
    public int queryMemberCount(ReceiveCouponBatchDTO paramDto,PromoCouponSendLogModel promoCouponSendLogModel,List<String> canSendUserList){
        List<PromoCouponSendDetailModel> errorDetailList = new ArrayList<>();
        promoCouponSendLogModel.setCouponSendDetailModelList(errorDetailList);
        String domainCode = paramDto.getDomainCode();
        String tenantCode = paramDto.getTenantCode();
        if("1".equals(paramDto.getIsAllUser())){
            com.gtech.member.response.Result<QueryMemberByCouponSendResult> queryMemberByCouponSendResultResult = queryMemberByCouponSend(domainCode,tenantCode,null,null,null,null,null);
            log.info("all user count:{}",queryMemberByCouponSendResultResult.getData().getMemberCount());
            return queryMemberByCouponSendResultResult.getData().getMemberCount();
        }else{
            if(!StringUtils.isEmpty(paramDto.getUserAccounts())){
                try {
                    List<String> accountList = Splitter.on(",").splitToList(paramDto.getUserAccounts());
                    QueryUserByAccountRequest param = new QueryUserByAccountRequest();
                    param.setDomainCode(domainCode);
                    param.setTenantCode(tenantCode);
                    param.setUserAccounts(paramDto.getUserAccounts());
                    Result<List<QueryAccountListResult>> accountResult = idmFeignClient.queryUserCodeByAccountList(param);
                    if(Boolean.FALSE.equals(accountResult.isSuccess()) || CollectionUtils.isEmpty(accountResult.getData())){
                        return accountList.size();
                    }
                    //组装不存在的account
                    for(String account : accountList){
                        Boolean flag = false;
                        String userCode = null;
                        for(QueryAccountListResult queryAccountListResult : accountResult.getData()){
                            if(account.equalsIgnoreCase(queryAccountListResult.getAccount())){
                                flag = true;
                                userCode = queryAccountListResult.getUserCode();
                            }
                        }
                        if(Boolean.FALSE.equals(flag)){
                            PromoCouponSendDetailModel couponSendDetailModel = buildPromoCouponSendDetailModel(promoCouponSendLogModel);
                            couponSendDetailModel.setFailReason(ACCOUNT_KEY+account+") does not exist.");
                            couponSendDetailModel.setUserCode(account);
                            errorDetailList.add(couponSendDetailModel);
                        }else{
                            canSendUserList.add(userCode);
                        }
                    }
                    return accountList.size();
                }catch (Exception e){
                    log.error("call idmFeignClient.queryUserCodeByAccountList error! ");
                    log.error(e.getMessage(), e);
                }
            }else if(!StringUtils.isEmpty(paramDto.getUserMobiles()) || !StringUtils.isEmpty(paramDto.getUserCodes()) || StringUtils.isNotBlank(paramDto.getUserTags())){
                try {
                    List<String> codeList = new ArrayList<>();
                    QueryMemberByCouponSendParam queryMemberByCouponSend = new QueryMemberByCouponSendParam();
                    queryMemberByCouponSend.setTenantCode(paramDto.getTenantCode());
                    queryMemberByCouponSend.setDomainCode(paramDto.getDomainCode());
                    if(!StringUtils.isEmpty(paramDto.getUserMobiles())){
                        codeList = Splitter.on(",").splitToList(paramDto.getUserMobiles());
                        queryMemberByCouponSend.setPhones(codeList);
                    }
                    if(!StringUtils.isEmpty(paramDto.getUserCodes())){
                        codeList = Splitter.on(",").splitToList(paramDto.getUserCodes());
                        queryMemberByCouponSend.setMemberCodes(codeList);
                    }

                    if(!StringUtils.isEmpty(paramDto.getUserTags())){
                        codeList = Splitter.on(",").splitToList(paramDto.getUserTags());
                        queryMemberByCouponSend.setTagCodes(codeList);
                    }

                    queryMemberByCouponSend.setPageNum(1);
                    queryMemberByCouponSend.setPageSize(20);

                    Integer totalMemberCount = queryMemberCount(queryMemberByCouponSend);

                    if (totalMemberCount == 0) {
                        return codeList.size();
                    }
                    List<MemberInfo> memberInfoList = new ArrayList<>();
                    queryMemberByCountOrMobile(queryMemberByCouponSend,memberInfoList);
                    if (CollectionUtils.isEmpty(memberInfoList)) {
                        return codeList.size();
                    }

                    List<String> queryList = new ArrayList<>();
                    Map<String,String> mobileUserMap = new HashMap<>();
                    String note = "User";
                    if(!StringUtils.isEmpty(paramDto.getUserMobiles())){
                        note = "Mobile";
                        for(MemberInfo memberInfo : memberInfoList){
                            queryList.add(memberInfo.getMobile());
                            mobileUserMap.put(memberInfo.getMobile(),memberInfo.getMemberCode());
                        }
                    }else{
                        queryList = memberInfoList.stream().map(MemberInfo::getMemberCode).collect(Collectors.toList());
                        if(!StringUtils.isEmpty(paramDto.getUserTags())){
                            codeList = new ArrayList<>();
                            codeList.addAll(queryList);
                        }
                    }

                    for (String code : codeList) {
                        if (!queryList.contains(code)) {
                            PromoCouponSendDetailModel couponSendDetailModel = buildPromoCouponSendDetailModel(promoCouponSendLogModel);
                            couponSendDetailModel.setFailReason(note+" (" + code + ") does not exist.");
                            errorDetailList.add(couponSendDetailModel);
                        } else {
                            if("Mobile".equals(note)){
                                canSendUserList.add(mobileUserMap.get(code));
                            }else {
                                canSendUserList.add(code);
                            }
                        }
                    }
                    return codeList.size();
                }catch (Exception e){
                    log.error("call memberClient.queryMemberByCouponSend error! ");
                    log.error(e.getMessage(), e);
                }
            }
        }
        return 0;
    }


    public Integer queryMemberCount(QueryMemberByCouponSendParam queryMemberByCouponSend){
        log.info("start queryMemberByCouponSend!request:{}",JSON.toJSONString(queryMemberByCouponSend));
        com.gtech.member.response.Result<QueryMemberByCouponSendResult> memberByCouponSendResultResult = memberClient.queryMemberByCouponSend(queryMemberByCouponSend);
        log.info("end queryMemberByCouponSend!memberByCouponSendResultResult:{}",JSON.toJSONString(memberByCouponSendResultResult));
        if (memberByCouponSendResultResult.getData().getMemberCount() < 1 || CollectionUtils.isEmpty(memberByCouponSendResultResult.getData().getMemberCodeList())) {
            return 0;
        }
        return memberByCouponSendResultResult.getData().getMemberCount();
    }

    public void queryMemberByCountOrMobile(QueryMemberByCouponSendParam queryMemberByCouponSend,List<MemberInfo> memberInfoList){
        log.info("start queryMemberByCouponSend!request:{}",JSON.toJSONString(queryMemberByCouponSend));
        int pageNum = queryMemberByCouponSend.getPageNum();
        com.gtech.member.response.Result<QueryMemberByCouponSendResult> memberByCouponSendResultResult = memberClient.queryMemberByCouponSend(queryMemberByCouponSend);
        log.info("end queryMemberByCouponSend!memberByCouponSendResultResult:{}",JSON.toJSONString(memberByCouponSendResultResult));
        if (memberByCouponSendResultResult.getData().getMemberCount() < 1 || CollectionUtils.isEmpty(memberByCouponSendResultResult.getData().getMemberCodeList())) {
            return;
        }
        memberInfoList.addAll(memberByCouponSendResultResult.getData().getMemberCodeList());
        queryMemberByCouponSend.setPageNum(pageNum+1);
        queryMemberByCountOrMobile(queryMemberByCouponSend,memberInfoList);
    }

    public com.gtech.member.response.Result<QueryMemberByCouponSendResult> queryMemberByCouponSend(String domainCode,String tenantCode,List<String> mobileList,List<String> userCodeList,Integer pageSize,Integer pageNum,String minMemberCode){
        QueryMemberByCouponSendParam queryMemberByCouponSend = new QueryMemberByCouponSendParam();
        queryMemberByCouponSend.setDomainCode(domainCode);
        queryMemberByCouponSend.setTenantCode(tenantCode);
        if(!CollectionUtils.isEmpty(mobileList)){
            queryMemberByCouponSend.setPhones(mobileList);
        }
        if(!CollectionUtils.isEmpty(userCodeList)){
            queryMemberByCouponSend.setMemberCodes(userCodeList);
        }
        if(null != pageSize){
            queryMemberByCouponSend.setPageSize(pageSize);
        }
        if(null != pageNum){
            queryMemberByCouponSend.setPageNum(pageNum);
        }
        if(!StringUtils.isEmpty(minMemberCode)){
            queryMemberByCouponSend.setMinMemberCode(minMemberCode);
        }
        log.info("start queryMemberByCouponSend!queryMemberByCouponSendRequest:{}",JSON.toJSONString(queryMemberByCouponSend));
        com.gtech.member.response.Result<QueryMemberByCouponSendResult> resultResult =  memberClient.queryMemberByCouponSend(queryMemberByCouponSend);
        log.info("start queryMemberByCouponSend!QueryMemberByCouponSendResult:{}",JSON.toJSONString(resultResult));
        return resultResult;
    }

    public PromoCouponSendLogModel convertPromoCouponSendLogModel(ReceiveCouponBatchDTO paramDto){
        Date now = new Date();
        PromoCouponSendLogModel promoCouponSendLogModel = new PromoCouponSendLogModel();
        promoCouponSendLogModel.setTenantCode(paramDto.getTenantCode());
        promoCouponSendLogModel.setActivityCode(paramDto.getActivityCode());
        promoCouponSendLogModel.setCouponBatchNo(paramDto.getReleaseCode());
        promoCouponSendLogModel.setUpdateUser(paramDto.getOperatorCode());
        promoCouponSendLogModel.setCreateUser(paramDto.getOperatorCode());
        promoCouponSendLogModel.setCreateTime(now);
        promoCouponSendLogModel.setUpdateTime(now);
        return promoCouponSendLogModel;
    }

}
