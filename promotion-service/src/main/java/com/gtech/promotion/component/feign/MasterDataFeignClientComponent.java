package com.gtech.promotion.component.feign;

import com.gtech.basic.masterdata.response.JsonResult;
import com.gtech.promotion.feign.MasterDataFeignClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class MasterDataFeignClientComponent {
    @Autowired
    private MasterDataFeignClient masterDataFeignClient;

    public String masterDataValue(String tenantCode, String masterDataCode){
        try {
            JsonResult<String> result = masterDataFeignClient.getValueValue(tenantCode, masterDataCode);
            return result.getData();
        }catch (Exception e){
            log.error("masterDataValue exception", e);
        }
         return null;
    }
}
