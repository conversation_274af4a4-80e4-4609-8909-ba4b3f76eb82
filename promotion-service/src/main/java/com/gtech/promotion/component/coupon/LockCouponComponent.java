/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.component.coupon;

import com.google.common.collect.Lists;
import com.gtech.commons.utils.CheckUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.checker.coupon.CouponCodeUserChecker;
import com.gtech.promotion.code.coupon.*;
import com.gtech.promotion.dao.model.activity.ActivityModel;
import com.gtech.promotion.dao.model.coupon.TPromoCouponCodeUserVO;
import com.gtech.promotion.dao.model.coupon.TPromoCouponInnerCodeVO;
import com.gtech.promotion.dto.in.coupon.TCouponLockDTO;
import com.gtech.promotion.dto.in.coupon.TCouponUnLockDTO;
import com.gtech.promotion.exception.Check;
import com.gtech.promotion.exception.ErrorCodes;
import com.gtech.promotion.helper.ActivityRedisHelpler;
import com.gtech.promotion.service.activity.ActivityService;
import com.gtech.promotion.service.coupon.PromoCouponActivityService;
import com.gtech.promotion.service.coupon.PromoCouponCodeUserService;
import com.gtech.promotion.service.coupon.PromoCouponInnerCodeService;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 *
 */
@Component
public class LockCouponComponent {

    @Autowired
    private PromoCouponCodeUserService couponCodeUserService;

    @Autowired
    private CouponInnerCodeComponent couponInnerCodeDomain;

    @Autowired
    private PromoCouponInnerCodeService couponInnerCodeService;

    @Autowired
    private PromoCouponActivityService promoCouponActivityService;

    @Autowired
    private ActivityRedisHelpler redisService;

    @Autowired
    private ActivityService activityService;

    @Transactional
    public void lockCoupon(TCouponLockDTO couponLockDTO){

        Check.check(StringUtils.isEmpty(couponLockDTO.getCouponCodes()), CouponCodeUserChecker.COUPON_CODE_NOT_NULL);
        List<String> couponCodes = Arrays.asList(couponLockDTO.getCouponCodes().split(","));
        if (CollectionUtils.isEmpty(couponCodes)){
            return;
        }
        //获取券码信息
        List<TPromoCouponInnerCodeVO> innerCodes = couponInnerCodeService.getCouponInnerCodeByCodes(couponLockDTO.getTenantCode(),couponCodes);
        Check.check(CollectionUtils.isEmpty(innerCodes), CouponCodeUserChecker.COUPON_CODE_NOT_EXISTS);
        //数据校验
        for (TPromoCouponInnerCodeVO innerCode : innerCodes){
            // 验证优惠券状态
            // 验证优惠券是否有效
            Check.check(!innerCode.getFrozenStatus().equals(CouponFrozenStatusEnum.UN_FROZEN.code()), CouponCodeUserChecker.COUPON_FROZEN);
            //匿名券
            if (CouponTemplateEnum.ANON_COUPON.code().equals(innerCode.getCouponType())){
                // 先领取
                Check.check(!innerCode.getStatus().equals(CouponStatusEnum.UN_GRANT.code()), CouponCodeUserChecker.COUPON_CANNOT_LOCK);
                //未发放 可以领取
                if (innerCode.getStatus().equals(CouponStatusEnum.UN_GRANT.code())){
                    couponInnerCodeDomain.receiveCouponCodeByOrder(innerCode, couponLockDTO.getOrderNo(), TakeLabelEnum.ANONYMOUS.code(), couponLockDTO.getUserCode());
                }
            }else if (CouponTemplateEnum.COUPON.code().equals(innerCode.getCouponType())){
                //优惠券
                Check.check(!innerCode.getStatus().equals(CouponStatusEnum.GRANTED.code()), CouponCodeUserChecker.COUPON_CANNOT_LOCK);
            }else{
                //通用优惠码
                couponInnerCodeDomain.receiveCouponCodeByOrder(innerCode, couponLockDTO.getOrderNo(), TakeLabelEnum.OTHER.code(), couponLockDTO.getUserCode());
            }

        }
        List<String> couponCodesList = new ArrayList<>();
        for (TPromoCouponInnerCodeVO vo : innerCodes){
            // 优惠券
            if (vo.getCouponType().equals(CouponTypeEnum.PROMOTION_COUPON.code())){
                couponCodesList.add(vo.getCouponCode());
            }
        }
        if (!CollectionUtils.isEmpty(couponCodesList)){
            // 更新优惠券状态为已锁定
            couponInnerCodeService.updateStatusBatch(couponLockDTO.getTenantCode(),couponCodesList, CouponStatusEnum.LOCKED);
            //更新用户优惠券表的使用业务编码和优惠券状态为已锁定
            couponCodeUserService.updateUsedRedIdByCodes(couponLockDTO.getTenantCode(),couponCodesList, couponLockDTO.getUserCode(), couponLockDTO.getOrderNo(), CouponStatusEnum.LOCKED);
        }

    }
    /*
     退单 或取消订单 券逻辑
     */
    @Transactional
    public Integer unLockCoupon(TCouponUnLockDTO couponUnLockDTO) {

        // 根据业务单号 获取用户券码list
        List<TPromoCouponCodeUserVO> list = couponCodeUserService.getCodeUserByUsedRefId(couponUnLockDTO.getOrderNo(), couponUnLockDTO.getTenantCode());
        //已过期
        List<String> expireCodes = Lists.newArrayList();
        //未使用
        List<String> grantedCodes = Lists.newArrayList();
        String tenantCode = "";
        if (CollectionUtils.isEmpty(list)) {
            return 0;
        }
        tenantCode = unLockResourceByUserVO(couponUnLockDTO, list, expireCodes, grantedCodes, tenantCode);
        if (!CollectionUtils.isEmpty(grantedCodes)) {
            List<TPromoCouponInnerCodeVO> list1 = couponInnerCodeService.getCouponInnerCodeByCodes(tenantCode, grantedCodes);
            for (TPromoCouponInnerCodeVO vo : list1) {
                //匿名券
                updateCouponUnGrant(grantedCodes, tenantCode, vo);
            }
        }
        if (!CollectionUtils.isEmpty(expireCodes)) {
            couponInnerCodeService.updateStatusBatch(tenantCode, expireCodes, CouponStatusEnum.EXPIRE);
        }
        return 1;
    }

    public void updateCouponUnGrant(List<String> grantedCodes, String tenantCode, TPromoCouponInnerCodeVO vo) {
        if (CouponTypeEnum.ANONYMITY_COUPON.code().equals(vo.getCouponType())) {
            // 更新优惠券状态为未使用
            if (grantedCodes.contains(vo.getCouponCode())) {
                //未发放
                List<String> unGrantedCodes = Lists.newArrayList();
                unGrantedCodes.add(vo.getCouponCode());
                couponInnerCodeService.updateStatusBatch(tenantCode, unGrantedCodes, CouponStatusEnum.UN_GRANT);
            }
        } else if (CouponTypeEnum.PROMOTION_COUPON.code().equals(vo.getCouponType()) && grantedCodes.contains(vo.getCouponCode())) {
            // 更新优惠券状态为未使用
            //已领取
            List<String> grantedCoupons = Lists.newArrayList();
            grantedCoupons.add(vo.getCouponCode());
            couponInnerCodeService.updateStatusBatch(tenantCode, grantedCoupons, CouponStatusEnum.GRANTED);
        }
    }

    public String unLockResourceByUserVO(TCouponUnLockDTO couponUnLockDTO, List<TPromoCouponCodeUserVO> list, List<String> expireCodes, List<String> grantedCodes, String tenantCode) {
        for (TPromoCouponCodeUserVO vo : list) {
            tenantCode = vo.getTenantCode();

            if (getExpireCodes(couponUnLockDTO, expireCodes, vo)) continue;
            redisUnlockUserAndQuantity(vo);
            int compareTo = DateUtil.getCurrentDateAsString(DateUtil.FORMAT_YYYYMMDDHHMISS_14).compareTo(vo.getValidEndTime());
            if (compareTo >= 0) {
                expireCodes.add(vo.getCouponCode());
            } else {
                grantedCodes.add(vo.getCouponCode());
            }
            if (!CouponTypeEnum.PROMOTION_COUPON.equalsCode(vo.getCouponType())) {
                //匿名优惠券 通用优惠码  删除绑定用户
                couponCodeUserService.deleteCodeUserByUserId(vo.getId());
            } else {
                if (compareTo >= 0) {
                    // 清空使用的业务编号 并更改券状态
                    couponCodeUserService.clearUsedRefIdByUsedRefId(couponUnLockDTO.getOrderNo(), CouponStatusEnum.EXPIRE.code());
                } else {
                    couponCodeUserService.clearUsedRefIdByUsedRefId(couponUnLockDTO.getOrderNo(), CouponStatusEnum.GRANTED.code());
                }

            }
        }
        return tenantCode;
    }

    public void redisUnlockUserAndQuantity(TPromoCouponCodeUserVO vo) {
        if (!CouponTypeEnum.PROMOTION_COUPON.equalsCode(vo.getCouponType())) {
            ActivityModel activityModel = promoCouponActivityService.findCouponActivity(vo.getTenantCode(), vo.getActivityCode());
            CheckUtils.isNotNull(activityModel, ErrorCodes.VALIDATE_COUPON_ACTIVITY_EXIST);
            if (null != activityModel.getUserLimitMax() && 0 != activityModel.getUserLimitMax()) {
                //用户限制资源回滚
                redisService.unlockCouponUser(vo.getTenantCode(), vo.getActivityCode(), vo.getUserCode());
            }
            if (CouponTypeEnum.PROMOTION_CODE.equalsCode(vo.getCouponType())) {
                //优惠码投放数量上限资源回滚
                redisService.unlockCouponReleaseLimit(vo.getTenantCode(), vo.getActivityCode(), vo.getReleaseCode());
            }
        }
    }

    public boolean getExpireCodes(TCouponUnLockDTO couponUnLockDTO, List<String> expireCodes, TPromoCouponCodeUserVO vo) {
        if (StringUtil.isNotBlank(couponUnLockDTO.getOrderStatus())) {
            ActivityModel activity = activityService.findActivityByActivityCode(vo.getTenantCode(), vo.getActivityCode());
            //比较活动时间是否结束 且是退单券活动
            int compareToActivityEnd = DateUtil.getCurrentDateAsString(DateUtil.FORMAT_YYYYMMDDHHMISS_14).compareTo(activity.getActivityEnd());
            if (compareToActivityEnd > 0 && StringUtil.isNotBlank(couponUnLockDTO.getOrderStatus())) {
                expireCodes.add(vo.getCouponCode());
                return true;
            }
        }
        return false;
    }

}
