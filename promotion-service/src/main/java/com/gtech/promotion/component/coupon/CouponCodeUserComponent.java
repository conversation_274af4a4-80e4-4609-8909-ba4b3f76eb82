/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.component.coupon;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.gtech.basic.idm.web.vo.param.QueryUserParam;
import com.gtech.basic.idm.web.vo.result.QueryUserResult;
import com.gtech.commons.exception.Exceptions;
import com.gtech.commons.page.PageData;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.CheckUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.commons.utils.StringUtil;
import com.gtech.member.web.vo.param.GetMemberProfileParam;
import com.gtech.member.web.vo.param.QueryMemberParam;
import com.gtech.member.web.vo.result.GetMemberProfileResult;
import com.gtech.member.web.vo.result.QueryMemberListByConditionResult;
import com.gtech.promotion.checker.coupon.CouponActivityChecker;
import com.gtech.promotion.checker.coupon.CouponCodeUserChecker;
import com.gtech.promotion.checker.coupon.CouponErrorChecker;
import com.gtech.promotion.code.coupon.CouponFrozenStatusEnum;
import com.gtech.promotion.code.coupon.CouponStatusEnum;
import com.gtech.promotion.code.coupon.CouponTemplateEnum;
import com.gtech.promotion.code.coupon.CouponTypeEnum;
import com.gtech.promotion.dao.model.activity.ActivityModel;
import com.gtech.promotion.dao.model.coupon.CouponReleaseModel;
import com.gtech.promotion.dao.model.coupon.TPromoCouponCodeUserVO;
import com.gtech.promotion.dao.model.coupon.TPromoCouponInnerCodeVO;
import com.gtech.promotion.domain.coupon.CouponReleaseDomain;
import com.gtech.promotion.dto.in.coupon.ExportCouponUserDto;
import com.gtech.promotion.dto.in.coupon.QueryCouponUsedInDTO;
import com.gtech.promotion.dto.in.coupon.TCouponCheckAndUseInDTO;
import com.gtech.promotion.exception.Check;
import com.gtech.promotion.exception.ErrorCodes;
import com.gtech.promotion.exception.PromotionException;
import com.gtech.promotion.feign.IdmFeignClient;
import com.gtech.promotion.feign.MemberFeignClient;
import com.gtech.promotion.helper.ActivityRedisHelpler;
import com.gtech.promotion.service.activity.ActivityService;
import com.gtech.promotion.service.coupon.PromoCouponActivityService;
import com.gtech.promotion.service.coupon.PromoCouponCodeUserService;
import com.gtech.promotion.service.coupon.PromoCouponInnerCodeService;
import com.gtech.promotion.service.coupon.PromoCouponReleaseService;
import com.gtech.promotion.utils.Constants;
import com.gtech.promotion.vo.result.coupon.ExportCouponUserDetailResult;
import com.gtech.promotion.vo.result.coupon.ExportCouponUserResult;
import com.gtech.promotion.vo.result.coupon.QueryCouponUsedListResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <功能描述>
 * 
 */
@Service
@Slf4j
public class CouponCodeUserComponent {

    @Autowired
    private PromoCouponCodeUserService couponCodeUserService;

    @Autowired
    private PromoCouponInnerCodeService couponInnerCodeService;

    @Autowired
    private PromoCouponActivityService couponActivityService;

    @Autowired
    private PromoCouponReleaseService promoCouponReleaseService;

    @Autowired
    private ActivityRedisHelpler redisService;

    @Autowired
    private ActivityService activityService;

    @Autowired
    private MemberFeignClient memberFeignClient;

    @Autowired
    private IdmFeignClient idmFeignClient;

    /**
     * @return activityCode
     */
    @Transactional
    public String isCanUse(String tenantCode,String couponCode,String userCode){

        String activityCode = "0";
        long now = Long.parseLong(DateUtil.format(new Date(), DateUtil.FORMAT_YYYYMMDDHHMISS_14));//当前时间
        // 判断券码状态
        TPromoCouponInnerCodeVO couponInnerCode = couponInnerCodeService.findCouponByCouponCode(tenantCode, couponCode);
        returnException(couponInnerCode);

        ActivityModel couponActivity = couponActivityService.findCouponActivity(tenantCode, couponInnerCode.getActivityCode());
        CheckUtils.isNotNull(couponActivity, ErrorCodes.VALIDATE_COUPON_ACTIVITY_EXIST);

        if (CouponTemplateEnum.COUPON.equalsCode(couponInnerCode.getCouponType())) {
            // 优惠券
            Check.check(!CouponStatusEnum.GRANTED.equalsCode(couponInnerCode.getStatus()), CouponErrorChecker.ERROR, CouponStatusEnum.getByCode(couponInnerCode.getStatus()).desc(), couponInnerCode.getCouponCode());
            Check.check(CouponFrozenStatusEnum.FROZENED.equalsCode(couponInnerCode.getFrozenStatus()), CouponErrorChecker.FREEZE, CouponStatusEnum.getByCode(couponInnerCode.getStatus()).desc(), couponInnerCode.getCouponCode());

            if (StringUtil.isNotBlank(userCode)){
                // 如果userid不为空，检查是否已领取
                TPromoCouponCodeUserVO oneEntity = couponCodeUserService.getUserCouponInfo(tenantCode, couponCode, userCode);
                return getActivityCodeByUserCode(activityCode, now, couponInnerCode, oneEntity);
            }
        }else if (CouponTemplateEnum.ANON_COUPON.code().equals(couponInnerCode.getCouponType())){
            // 匿名券
            if (isaBoolean(couponInnerCode)){
                Check.check(!couponInnerCode.getStatus().equals(CouponStatusEnum.UN_GRANT.code()) && !couponInnerCode.getStatus().equals(CouponStatusEnum.GRANTED.code()),
                                CouponErrorChecker.ERROR,
                                CouponStatusEnum.getByCode(couponInnerCode.getStatus()).desc(),
                                couponInnerCode.getCouponCode());
                Check.check(CouponFrozenStatusEnum.FROZENED.code().equals(couponInnerCode.getFrozenStatus()), CouponErrorChecker.FREEZE, CouponStatusEnum.getByCode(couponInnerCode.getStatus()).desc(), couponInnerCode.getCouponCode());
                return activityCode;
            }else if (CouponStatusEnum.GRANTED.code().equals(couponInnerCode.getStatus())) {
                return getActivityCode(tenantCode, couponCode, userCode, activityCode, now);
            }

        }else{
            // 优惠码
            Check.check(CouponFrozenStatusEnum.FROZENED.code().equals(couponInnerCode.getFrozenStatus()), CouponErrorChecker.FREEZE, CouponStatusEnum.getByCode(couponInnerCode.getStatus()).desc(), couponInnerCode.getCouponCode());
            Check.check(CouponStatusEnum.EXPIRE.code().equals(couponInnerCode.getStatus()), CouponErrorChecker.ERROR, CouponStatusEnum.getByCode(couponInnerCode.getStatus()).desc(), couponInnerCode.getCouponCode());
            int count = redisService.getCouponReleaseLimit(tenantCode, couponInnerCode.getActivityCode(), couponInnerCode.getReleaseCode());
            checkTotalQuantity(couponActivity, count);
        }
        CouponReleaseDomain couponReleaseById = promoCouponReleaseService.findCouponReleaseByReleaseCode(couponInnerCode.getTenantCode(), couponInnerCode.getReleaseCode());
        if (isaBoolean(now, couponReleaseById)){
            Check.check(true, CouponErrorChecker.VALID_TIME_NO_REACH);
            return activityCode;
        }
        //该情况只针对 匿名券 领取后生效
        checkAnonCoupon(now, couponInnerCode, couponReleaseById);
        // 还未到领取时间，不能使用，主要针对匿名券
        if (Long.parseLong(couponReleaseById.getReceiveStartTime()) > now){
            Check.check(true, CouponErrorChecker.RECEIVE_NO_REACH);
            return activityCode;
        }

        // 检验单用户领取上限限制
        if (checkUserLimit(userCode, couponInnerCode, couponActivity)){
            return activityCode;
        }

        activityCode = couponActivity.getActivityCode();

        return activityCode;
    }


    public boolean checkUserLimit(String userCode, TPromoCouponInnerCodeVO couponInnerCode, ActivityModel couponActivity) {
        if (null != couponActivity.getUserLimitMax() && couponActivity.getUserLimitMax() != 0){
            // 获取此用户在当前活动的已使用次数
            int count = couponCodeUserService.getUserUsedCouponCountByActivityCode(couponInnerCode.getTenantCode(), couponInnerCode.getActivityCode(), userCode);
            if (count >= couponActivity.getUserLimitMax()){
                Check.check(true, CouponErrorChecker.LIMIT, couponActivity.getUserLimitMax() + "");
                return true;
            }
        }
        return false;
    }

    public void checkAnonCoupon(long now, TPromoCouponInnerCodeVO couponInnerCode, CouponReleaseDomain couponReleaseById) {
        if (null != couponReleaseById.getValidDays() && CouponTemplateEnum.ANON_COUPON.code().equals(couponInnerCode.getCouponType())){
            Check.check(StringUtil.isEmpty(couponInnerCode.getValidStartTime()), CouponErrorChecker.NO_RECEIVE_COUPON);
            Check.check(Long.parseLong(couponInnerCode.getValidStartTime()) > now, CouponErrorChecker.NO_USED_TIME);
            Check.check(Long.parseLong(couponInnerCode.getValidEndTime()) < now, CouponErrorChecker.END_TIME);
        }
    }

    public boolean isaBoolean(long now, CouponReleaseDomain couponReleaseById) {
        return null == couponReleaseById.getValidDays() && (Long.parseLong(couponReleaseById.getValidStartTime()) > now || Long.parseLong(couponReleaseById.getValidEndTime()) < now);
    }

    public boolean isaBoolean(TPromoCouponInnerCodeVO couponInnerCode) {
        return (!CouponStatusEnum.UN_GRANT.code().equals(couponInnerCode.getStatus()) && !CouponStatusEnum.GRANTED.code().equals(couponInnerCode.getStatus()))
                        || CouponFrozenStatusEnum.FROZENED.code().equals(couponInnerCode.getFrozenStatus());
    }

    public void checkTotalQuantity(ActivityModel couponActivity, int count) {
        if (count > 0){
            Check.check(count >= couponActivity.getTotalQuantity(), CouponActivityChecker.USED_LIMIT);
        }
    }

    public void returnException(TPromoCouponInnerCodeVO couponInnerCode) {
        if (null == couponInnerCode){
            throw new PromotionException(CouponErrorChecker.NO_EFFECTIVE);
        }
    }

    public String getActivityCodeByUserCode(String activityCode, long now, TPromoCouponInnerCodeVO couponInnerCode, TPromoCouponCodeUserVO oneEntity) {
        if (null == oneEntity || !oneEntity.getUsedRefId().equals(Constants.DEFAULT_USED_REF_ID) || Long.parseLong(oneEntity.getValidStartTime()) > now || Long.parseLong(oneEntity.getValidEndTime()) < now){
            Check.check(null == oneEntity, CouponErrorChecker.MEMBER_NOT_COUPON);
            Check.check(Long.parseLong(oneEntity.getValidEndTime()) < now, CouponErrorChecker.ERROR, CouponStatusEnum.getByCode(couponInnerCode.getStatus()).desc(), couponInnerCode.getCouponCode());//NOSONAR
            Check.check(Long.parseLong(oneEntity.getValidStartTime()) > now, CouponErrorChecker.VALID_TIME_NO_REACH);//NOSONAR
            return activityCode;
        }else{
            return oneEntity.getActivityCode();
        }
    }

    public String getActivityCode(String tenantCode, String couponCode, String userCode, String activityCode, long now) {
        // 已发放的匿名券
        if (StringUtil.isNotBlank(userCode)){ // 如果userid不为空，检查是否已领取
            TPromoCouponCodeUserVO oneEntity = couponCodeUserService.getUserCouponInfo(tenantCode, couponCode, userCode);
            if (null == oneEntity || !oneEntity.getUsedRefId().equals(Constants.DEFAULT_USED_REF_ID) || Long.parseLong(oneEntity.getValidStartTime()) > now || Long.parseLong(oneEntity.getValidEndTime()) < now){
                Check.check(null == oneEntity, CouponErrorChecker.MEMBER_NOT_COUPON);
                Check.check(Long.parseLong(oneEntity.getValidStartTime()) > now || Long.parseLong(oneEntity.getValidEndTime()) < now, CouponErrorChecker.VALID_TIME_NO_REACH);//NOSONAR
                return activityCode;
            }else{
                return oneEntity.getActivityCode();
            }
        }else{
            return activityCode;
        }
    }

    @Transactional
    public int queryCheckAndUseCouponCode(TCouponCheckAndUseInDTO checkAndUseInDTO){
        String[] coupons = checkAndUseInDTO.getCouponCodes().split(",");
        for (int i = 0; i < coupons.length; i++){
            String coupon = coupons[i];
            checkAndUseInDTO.setCouponCode(coupon);
            TPromoCouponInnerCodeVO innerCodeVO = couponInnerCodeService.findCouponByCouponCode(checkAndUseInDTO.getTenantCode(), checkAndUseInDTO.getCouponCode());
            checkInnerCoponCodeExist(checkAndUseInDTO, coupon, innerCodeVO);
        }
        return coupons.length;

    }

    private void checkInnerCoponCodeExist(TCouponCheckAndUseInDTO checkAndUseInDTO, String couponCode, TPromoCouponInnerCodeVO innerCodeVO) {

        if (null == innerCodeVO) {
            throw Exceptions.fail(ErrorCodes.VALIDATE_COUPON_CODE_EXIST, couponCode);
        }

        CheckUtils.isTrue(!CouponFrozenStatusEnum.FROZENED.equalsCode(innerCodeVO.getFrozenStatus()), ErrorCodes.COUPON_VALID_COUPON_FROZEN, innerCodeVO.getCouponCode());

        //用于可用时间判断
        CouponReleaseModel releaseModel = promoCouponReleaseService.findCanUseRelease(innerCodeVO.getActivityCode(), innerCodeVO.getReleaseCode());
        String string = DateUtil.format(DateUtil.now(), DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        if (CouponTypeEnum.PROMOTION_COUPON.equalsCode(innerCodeVO.getCouponType())) {
            setUserCoupon(checkAndUseInDTO, couponCode, innerCodeVO, releaseModel, string);
        } else {
            //匿名券 优惠码 逻辑
            ActivityModel activityMode = activityService.findActivity(innerCodeVO.getTenantCode(), innerCodeVO.getActivityCode(), null);
            CheckUtils.isNotNull(activityMode, ErrorCodes.VALIDATE_COUPON_ACTIVITY_EXIST);

            releaseModel = promoCouponReleaseService.findCanReceiveRelease(innerCodeVO.getActivityCode(), innerCodeVO.getReleaseCode());
            Check.check(null == releaseModel, CouponCodeUserChecker.NOT_RECEIVE_TIME);
            checkValidTime(releaseModel, string);
            setUserInfo(couponCode, innerCodeVO);
        }
    }

    private void setUserCoupon(TCouponCheckAndUseInDTO checkAndUseInDTO,String coupon,TPromoCouponInnerCodeVO innerCodeVO,CouponReleaseModel releaseVO,String string) {
        if (CouponStatusEnum.GRANTED.code().equals(innerCodeVO.getStatus())) {
            checkValidTime(releaseVO, string);
            TPromoCouponCodeUserVO userVO = couponCodeUserService.findUserByCouponCode(checkAndUseInDTO);
            Check.check(null == userVO, CouponErrorChecker.NO_USER_COUPON_CODE, coupon, checkAndUseInDTO.getUserCode());
        } else {
            Check.check(true, CouponErrorChecker.NO_STATUS, coupon, innerCodeVO.getStatus());
        }
    }

    private void setUserInfo(String coupon,TPromoCouponInnerCodeVO innerCodeVO){

        if (!CouponStatusEnum.UN_GRANT.code().equals(innerCodeVO.getStatus())){
            Check.check(true, CouponErrorChecker.NO_STATUS, coupon, innerCodeVO.getStatus());
        }
    }

    private void checkValidTime(CouponReleaseModel releaseVO,String string){
        //可用时间判断
        if (null == releaseVO.getValidDays()){
            Check.check(!(releaseVO.getValidStartTime().compareTo(string) <= 0 && releaseVO.getValidEndTime().compareTo(string) > 0), CouponCodeUserChecker.NOT_USABLE_TIME);
        }
    }


    public List<ExportCouponUserResult> exportCouponDetail(ExportCouponUserDto dto){
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize(),false);
        Map<String, GetMemberProfileResult> memberMap = new HashMap<>();
        Map<String, QueryUserResult> userMap = new HashMap<>();
        List<ExportCouponUserResult> codeUserEntities = couponCodeUserService.exportCouponUserCode(dto);
        List<String> userCodes = codeUserEntities.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(ExportCouponUserResult::getUserCode))),//o代表object对象，o.list对象的属性值，根据此属性值去重
                ArrayList::new)).stream().map(ExportCouponUserResult::getUserCode).collect(Collectors.toList());
        log.info("userCodes:{}", JSON.toJSONString(userCodes));
        if(!CollectionUtils.isEmpty(userCodes)) {
            //查询用户账号
            Result<List<QueryUserResult>> queryUserResult = queryUserList(userCodes.stream().map(String::valueOf).collect(Collectors.joining(",")), dto.getTenantCode(), dto.getDomainCode());
            log.info("queryUserResult:{}",JSON.toJSONString(queryUserResult));
            if (Boolean.TRUE.equals(queryUserResult.isSuccess()) && !CollectionUtils.isEmpty(queryUserResult.getData())) {
            	queryUserResult.getData().forEach(user->userMap.put(user.getUserCode(), user));
            }
        }
        
        for (ExportCouponUserResult entity : codeUserEntities) {
            QueryUserResult userResult = userMap.get(entity.getUserCode());
            if(null != userResult){
                entity.setAccount(userResult.getAccount());
            }
            if(null == memberMap.get(entity.getUserCode())) {
                //查找会员信息
                GetMemberProfileParam param = new GetMemberProfileParam();
                param.setDomainCode(dto.getDomainCode());
                param.setTenantCode(dto.getTenantCode());
                param.setMemberCode(entity.getUserCode());
                Result<GetMemberProfileResult> memberProfile = memberFeignClient.getMemberProfile(param);
                if (null == memberProfile.getData()) {
                    entity.setFirstName("null");
                    entity.setLastName("null");
                } else {
                    entity.setFirstName(memberProfile.getData().getFirstName());
                    entity.setLastName(memberProfile.getData().getLastName());
                }
            }else {
                GetMemberProfileResult result = memberMap.get(entity.getUserCode());
                entity.setFirstName(result.getFirstName());
                entity.setLastName(result.getLastName());
                memberMap.put(entity.getUserCode(),result);
            }
        }

        return codeUserEntities;
    }

    public Result<List<QueryUserResult>> queryUserList(String userCodes,String tenantCode,String domainCode){
        QueryUserParam queryUserParam = new QueryUserParam();
        queryUserParam.setUserCodes(userCodes);
        queryUserParam.setTenantCode(tenantCode);
        queryUserParam.setDomainCode(domainCode);
        log.info("start queryUserList! queryUserParam:{}",JSON.toJSONString(queryUserParam));
        return idmFeignClient.queryUserList(queryUserParam);
    }


    public List<ExportCouponUserDetailResult> exportCouponOrderDetail(ExportCouponUserDto dto){
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize(),false);

        return couponCodeUserService.exportCouponOrderCode(dto);
    }

    public PageData<QueryCouponUsedListResult> queryUsedCouponList(QueryCouponUsedInDTO dto) {

        //手机号 邮箱查询 转成对应的 用户编码查询 存在会员查询信息
        if (StringUtil.isNotEmpty(dto.getEmail()) || StringUtil.isNotEmpty(dto.getMobile()) || StringUtil.isNotEmpty(dto.getUserCode())) {
            QueryMemberParam param = new QueryMemberParam();
            param.setTenantCode(dto.getTenantCode());
            param.setDomainCode(dto.getDomainCode());
            param.setEmail(dto.getEmail());
            param.setMobile(dto.getMobile());
            if (StringUtil.isNotEmpty(dto.getUserCode())) {
                List<String> memberCodes = new ArrayList<>();
                memberCodes.add(dto.getUserCode());
                param.setMemberCodes(memberCodes);
            }
            Result<List<QueryMemberListByConditionResult>> listResult = memberFeignClient.queryMemberListByMemberCode(param);//会员信息
            if (null == listResult || CollectionUtils.isEmpty(listResult.getData())) {
                //根据会员信息查询不存在
                return new PageData<>(Collections.emptyList(), 0L);
            }
            dto.setUserCode(listResult.getData().get(0).getMemberCode());
            PageData<TPromoCouponCodeUserVO> data = couponCodeUserService.queryUsedCouponListService(dto);//查询所需数据
            List<TPromoCouponCodeUserVO> list = data.getList();
            //遍历数据拿到 活动信息
            List<String> activityCodes = new ArrayList<>();
            getActivityCodesByCouponUser(list, activityCodes);
            List<ActivityModel> activityModels = activityService.queryActivityByActivityCodes(dto.getTenantCode(), activityCodes);
            List<QueryCouponUsedListResult> results = BeanCopyUtils.jsonCopyList(list, QueryCouponUsedListResult.class);
            getResult(listResult, activityModels, results);
            return new PageData(results, data.getTotal());
        } else {

            PageData<TPromoCouponCodeUserVO> data = couponCodeUserService.queryUsedCouponListService(dto);
            if (null != data && !CollectionUtils.isEmpty(data.getList())) {
                List<TPromoCouponCodeUserVO> list = data.getList();
                //获取会员信息
                List<String> memberCodes = new ArrayList<>();
                //遍历数据拿到 活动信息
                List<String> activityCodes = new ArrayList<>();
                setMemberAndActivityCodes(list, memberCodes, activityCodes);
                List<ActivityModel> activityModels = activityService.queryActivityByActivityCodes(dto.getTenantCode(), activityCodes);
                QueryMemberParam param = new QueryMemberParam();
                param.setTenantCode(dto.getTenantCode());
                param.setDomainCode(dto.getDomainCode());
                param.setMemberCodes(memberCodes);//填充memberCodes
                Result<List<QueryMemberListByConditionResult>> listResult = memberFeignClient.queryMemberListByMemberCode(param);//会员信息
                List<QueryMemberListByConditionResult> members = listResult.getData();
                List<QueryCouponUsedListResult> results = BeanCopyUtils.jsonCopyList(list, QueryCouponUsedListResult.class);
                foreachQueryCouponUsedResult(activityModels, listResult, members, results);
                return new PageData<>(results, data.getTotal());
            } else {
                return new PageData<>(Collections.emptyList(), 0L);
            }
        }
    }

    public void getActivityCodesByCouponUser(List<TPromoCouponCodeUserVO> list, List<String> activityCodes) {
        for (TPromoCouponCodeUserVO userVO : list) {
            if (!activityCodes.contains(userVO.getActivityCode())) {
                activityCodes.add(userVO.getActivityCode());
            }
        }
    }

    public void setMemberAndActivityCodes(List<TPromoCouponCodeUserVO> list, List<String> memberCodes, List<String> activityCodes) {
        for (TPromoCouponCodeUserVO userVO : list) {
            if (!memberCodes.contains(userVO.getUserCode())) {
                memberCodes.add(userVO.getUserCode());
            }
            if (!activityCodes.contains(userVO.getActivityCode())) {
                activityCodes.add(userVO.getActivityCode());
            }
        }
    }

    public void foreachQueryCouponUsedResult(List<ActivityModel> activityModels, Result<List<QueryMemberListByConditionResult>> listResult, List<QueryMemberListByConditionResult> members, List<QueryCouponUsedListResult> results) {
        for (QueryCouponUsedListResult result : results) {
            //填充会员信息
            if (null != listResult && !CollectionUtils.isEmpty(members)) {
                for (QueryMemberListByConditionResult member : members) {
                    if (result.getUserCode().equals(member.getMemberCode())) {
                        StringBuilder sb = new StringBuilder();
                        sb.append(member.getLastName()).append(".").append(member.getFirstName());
                        result.setMobile(member.getMobile());
                        result.setUserName(sb.toString());
                        result.setEmail(member.getEmail());
                        result.setOrdCode(member.getOrgCode());
                        break;
                    }
                }
            }
            //活动信息
            getActivityName(activityModels, result);
        }
    }

    public void getResult(Result<List<QueryMemberListByConditionResult>> listResult, List<ActivityModel> activityModels, List<QueryCouponUsedListResult> results) {
        for (QueryCouponUsedListResult result : results) {
            QueryMemberListByConditionResult member = listResult.getData().get(0);
            StringBuilder sb = new StringBuilder();
            sb.append(member.getLastName()).append(".").append(member.getFirstName());
            result.setUserName(sb.toString());
            result.setEmail(member.getEmail());
            result.setMobile(member.getMobile());
            result.setOrdCode(member.getOrgCode());
            if ("DEFAULT".equals(result.getUsedRefId())){
                result.setUsedRefId("");
            }
            getActivityName(activityModels, result);
        }
    }

    public void getActivityName(List<ActivityModel> activityModels, QueryCouponUsedListResult result) {
        for (ActivityModel activityModel : activityModels) {
            if (result.getActivityCode().equals(activityModel.getActivityCode())) {
                result.setActivityName(activityModel.getActivityName());
                break;
            }
        }
    }

}
