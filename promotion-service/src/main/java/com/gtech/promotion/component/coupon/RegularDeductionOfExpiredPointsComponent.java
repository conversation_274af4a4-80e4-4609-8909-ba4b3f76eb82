package com.gtech.promotion.component.coupon;

import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.promotion.dao.entity.point.PointTransactionEntity;
import com.gtech.promotion.dao.mapper.point.PointAccountMapper;
import com.gtech.promotion.dao.mapper.point.PointTransactionMapper;
import com.gtech.promotion.service.point.PointCampaignService;
import com.gtech.promotion.service.point.PointTransactionService;
import com.gtech.promotion.vo.param.point.PointTransactionParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/1/6 14:40
 */
@Component
public class RegularDeductionOfExpiredPointsComponent {


    @Autowired
    private PointCampaignService pointCampaignService;

    @Autowired
    private PointTransactionMapper pointTransactionMapper;

    @Autowired
    private PointAccountMapper pointAccountMapper;

    @Autowired
    private PointTransactionService pointTransactionService;

    @Transactional
    public void regularDeductionOfExpiredPoints(){

        //查询过期了的积分池，状态改为禁用
        pointCampaignService.queryPointCampaignEndTime();

        Long maxId = 0L;
        int limit = 500;
        String nowTime = DateUtil.format(new Date(), DateUtil.FORMAT_YYYYMMDDHHMISS_14);

        //查询所有积分流水   判断是否过期   如果过期并且还有余额，增加积分过期流水   扣除用户积分总额相应的积分
        while (true){

            List<PointTransactionEntity> pointTransactionEntities = pointTransactionService.queryPointTransactionEndTime(maxId,limit,nowTime);

            if (pointTransactionEntities.isEmpty()){
                break;
            }
            maxId = pointTransactionEntities.get(pointTransactionEntities.size()-1).getId();

            for (PointTransactionEntity entity: pointTransactionEntities) {

                PointTransactionParam param = new PointTransactionParam();
                param.setDomainCode(entity.getDomainCode());
                param.setTenantCode(entity.getTenantCode());

                //新增过期积分流水
                param.setTransactionAmount(entity.getBalance());
                param.setOperation(2);
                param.setTransactionRemarks("过期自动扣减");
                param.setAccountCode(entity.getAccountCode());
                param.setCampaignCode(entity.getCampaignCode());
                param.setAccountType(entity.getAccountType());
                param.setCreateUser("System");
                //扣减
                param.setTransactionType(2);
                pointTransactionService.savePointTransaction(param);

                //清空过期积分余额
                entity.setBalance(0);
                pointTransactionMapper.updateByPrimaryKeySelective(entity);

                //扣除账户积分
                Map<String, Object> updatePoint = BeanCopyUtils.jsonCopyBean(param, Map.class);
                pointAccountMapper.updatePoint(updatePoint);

            }
        }


    }
}
