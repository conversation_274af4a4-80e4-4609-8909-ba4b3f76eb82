/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.component.coupon;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.gtech.commons.redis.GTechCodeGenerator;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.CheckUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.RedisClient;
import com.gtech.promotion.checker.coupon.CouponActivityChecker;
import com.gtech.promotion.code.activity.ActivityStatusEnum;
import com.gtech.promotion.code.activity.FuncTypeEnum;
import com.gtech.promotion.code.coupon.*;
import com.gtech.promotion.dao.model.activity.ActivityFunctionParamRankModel;
import com.gtech.promotion.dao.model.activity.ActivityModel;
import com.gtech.promotion.dao.model.activity.FunctionParamModel;
import com.gtech.promotion.dao.model.coupon.CountCouponCodeModel;
import com.gtech.promotion.dao.model.coupon.CouponReleaseModel;
import com.gtech.promotion.dao.model.coupon.TPromoCouponCodeUserVO;
import com.gtech.promotion.dao.model.coupon.TPromoCouponInnerCodeVO;
import com.gtech.promotion.domain.coupon.CouponReleaseDomain;
import com.gtech.promotion.dto.in.coupon.CouponCodeCacheDTO;
import com.gtech.promotion.dto.in.coupon.FaceClass;
import com.gtech.promotion.dto.in.coupon.ReleaseCouponInDTO;
import com.gtech.promotion.exception.Check;
import com.gtech.promotion.exception.ErrorCodes;
import com.gtech.promotion.helper.ActivityRedisHelpler;
import com.gtech.promotion.helper.CouponUtils;
import com.gtech.promotion.page.RequestPage;
import com.gtech.promotion.service.activity.ActivityFuncParamService;
import com.gtech.promotion.service.activity.ActivityFuncRankService;
import com.gtech.promotion.service.coupon.PromoCouponCodeUserService;
import com.gtech.promotion.service.coupon.PromoCouponInnerCodeService;
import com.gtech.promotion.service.coupon.PromoCouponReleaseService;
import com.gtech.promotion.utils.Constants;
import com.gtech.promotion.utils.DateValidUtil;
import com.gtech.promotion.utils.EasyCacheUtil;
import com.gtech.promotion.utils.MarketingConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.data.redis.core.DefaultTypedTuple;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class CouponReleaseComponent {

    @Autowired
    private PromoCouponReleaseService couponReleaseService;

    @Autowired
    private PromoCouponCodeUserService couponCodeUserService;

    @Autowired
    private PromoCouponInnerCodeService couponInnerCodeService;

    @Autowired
    private ActivityFuncParamService activityFuncParamService;

    @Autowired
    private ActivityFuncRankService activityFuncRankService;

    @Autowired
    private CouponActivityComponent couponActivityComponent;

    @Autowired
    private ActivityRedisHelpler redisService;

    @Autowired
    private CouponInnerCodeComponent couponInnerCodeComponent;

    @Autowired
    private RedisClient redisClient;

    @Value("${coupon.auto.release.qty:1000}")
    private Integer releaseQty;

    @Autowired
    private GTechCodeGenerator gTechCodeGenerator;

    public static final String TEMPLATE_EXPR = "[D:yyMMddHHmmss][SM:%03d]";


    @Value("${MAX_RETRY_TIMES:2}")
    private Integer MAX_RETRY_TIMES;

    @Transactional
	public String createUaReleaseAndSendCoupon(ReleaseCouponInDTO coupon, Map<String, String> outCouponMap) {

        CouponReleaseDomain releaseDomain = new CouponReleaseDomain();
        releaseDomain.setTenantCode(coupon.getTenantCode());

		// 根据活动编码查询促销券活动信息
		String key = "release:coupon:activity:" + coupon.getTenantCode() + ":" + coupon.getActivityCode();
		ActivityModel activityModel = (ActivityModel) EasyCacheUtil.get(key);
		if (activityModel == null) {
			activityModel = this.couponActivityComponent.findEffectiveActivity(coupon.getTenantCode(), coupon.getActivityCode(), null);
			EasyCacheUtil.set(key, activityModel, 60);
		}

		checkReleaseCoupon(coupon, activityModel);
		// 查询已经发放券码数量
//		Long releaseTotal = couponReleaseService.queryReleaseCount111(activityModel.getActivityCode());
		// 判断发券数量是否超出，投放总量如果是0，表示不限数量
//		checkQuantity(coupon, activityModel, releaseTotal);



        String releaseCode = StringUtil.EMPTY;
        CouponReleaseModel promoCouponReleaseVO = new CouponReleaseModel();

        List<CouponReleaseDomain> couponReleaseDomains = couponReleaseService.queryCanReceiveReleasesByTime(coupon.getTenantCode(), coupon.getActivityCode(), coupon.getReceiveStart(), coupon.getReceiveEnd());
        //筛选库存数量大于需要投放数量的投放记录
        if (CollectionUtils.isNotEmpty(couponReleaseDomains) && couponReleaseDomains.stream().anyMatch(x -> x.getInventory() >= coupon.getReleaseQuantity())){
            for (CouponReleaseDomain domain : couponReleaseDomains) {
                if (domain.getInventory() >= coupon.getReleaseQuantity()) {
                    releaseDomain = domain;
                    releaseCode = domain.getReleaseCode();
                    promoCouponReleaseVO = makeCouponRelease(coupon, activityModel, releaseCode);

                    //更新库存
                    couponReleaseService.deductInventory(domain.getTenantCode(),domain.getActivityCode(), Lists.newArrayList(domain), coupon.getReleaseQuantity());

                    break;
                }
            }
        }else {
            //releaseCode = String.valueOf(System.currentTimeMillis());
            releaseCode = getCouponReleaseCode();
            promoCouponReleaseVO = createNewRelease(coupon, releaseDomain, activityModel, releaseCode);

        }
        // 生成券码
        generateCouponCode(outCouponMap, activityModel, 0l, promoCouponReleaseVO);

        List<CouponReleaseDomain> couponRelease = new ArrayList<>();
        couponRelease.add(releaseDomain);

//		ActivityModel couponActivity = couponActivityComponent.findSendCouponValidActivity(coupon.getTenantCode(), coupon.getActivityCode(), null, new Date());

        Iterator<String> iterator = outCouponMap.values().iterator();

        // 用户领取券对象
		TPromoCouponCodeUserVO couponCodeUser = couponInnerCodeComponent.builderCouponToUser(activityModel, releaseDomain);
		//不为空
		if (StringUtil.isNotEmpty(coupon.getStatus())){
            couponCodeUser.setStatus(coupon.getStatus());
        }

        // 添加领券记录
        List<TPromoCouponCodeUserVO> userVOS = new ArrayList<>();

        List<TPromoCouponInnerCodeVO> vos = new ArrayList<>();


        while (iterator.hasNext()){
            String couponCode = iterator.next();
			couponCodeUser.setReleaseCode(releaseCode);
			couponCodeUser.setTakeLabel(coupon.getTakeLabel());
            couponCodeUser.setCouponCode(couponCode);
			couponCodeUser.setUserCode(coupon.getUserCode());
			couponCodeUser.setOpsType(activityModel.getOpsType());
			couponCodeUser.setCreateUser(coupon.getUserCode());
            TPromoCouponCodeUserVO userVO = BeanCopyUtils.jsonCopyBean(couponCodeUser, TPromoCouponCodeUserVO.class);
            userVOS.add(userVO);
            TPromoCouponInnerCodeVO vo = new TPromoCouponInnerCodeVO();

            vo.setCouponCode(userVO.getCouponCode());
            vo.setTenantCode(userVO.getTenantCode());
            vo.setTakeLabel(userVO.getTakeLabel());
            vo.setValidEndTime(userVO.getValidEndTime());
            vo.setValidStartTime(userVO.getValidStartTime());
            vo.setStatus(userVO.getStatus());
            vos.add(vo);

        }
        couponCodeUserService.insert(userVOS);

        //更新券码表 填充信息
		couponInnerCodeService.updateBatchInnerCodeByCouponCodes(vos);

        for (TPromoCouponCodeUserVO userVO : userVOS) {
            String couponKey = Constants.PROMOTION_COUPON_USER + ":TENANTCODE=" + activityModel.getTenantCode()
                    + ":ACTIVITYCODE=" + activityModel.getActivityCode() + ":USERCODE=" + userVO.getUserCode() + ":COUPONCODE=" + userVO.getCouponCode();
            String validEndTime = userVO.getValidEndTime();
            Date date = DateUtil.parseDate(validEndTime, DateUtil.FORMAT_YYYYMMDDHHMISS_14);
            long expireTime = date.getTime() / 1000;
            long currentTime = System.currentTimeMillis() / 1000;
            if (CouponStatusEnum.GRANTED.code().equals(userVO.getStatus())){
                redisClient.setStringValue(couponKey, JSON.toJSONString(userVO), expireTime - currentTime, TimeUnit.SECONDS);
            }
        }

		return releaseCode;
    }

    private String getCouponReleaseCode() {
        return gTechCodeGenerator.generateCode(MarketingConstants.APP_KEY, "couponReleaseCode", TEMPLATE_EXPR, 1L);
    }

    public CouponReleaseModel createNewRelease(ReleaseCouponInDTO coupon, CouponReleaseDomain releaseDomain, ActivityModel activityModel, String releaseCode) {
        CouponReleaseModel promoCouponReleaseVO;
        coupon.setInventory(releaseQty - coupon.getReleaseQuantity());
        coupon.setReleaseQuantity(releaseQty);
        promoCouponReleaseVO = makeCouponRelease(coupon, activityModel, releaseCode);

        // 保存促销券码投放信息
        couponReleaseService.createCouponRelease(promoCouponReleaseVO);

        BeanCopyUtils.copyProps(promoCouponReleaseVO, releaseDomain);

        String jsonString = JSONObject.toJSONString(promoCouponReleaseVO);
        if (ReleaseTypeEnum.APPOINTMENT.equalsCode(coupon.getReleaseType())) {
            // 预约缓存的key
            redisService.addCouponDelayRelease(releaseCode, activityModel.getTenantCode(), activityModel.getActivityCode(), coupon.getReleaseTime(),
                    coupon.getReleaseQuantity(), jsonString);
        }
        return promoCouponReleaseVO;
    }


    /**
     * 根据releaseCode查询投放详情
     */
    public CouponReleaseDomain findCouponReleaseByReleaseCode(String tenantCode, String releaseCode) {

        CouponReleaseDomain releaseDomain = couponReleaseService.findCouponReleaseByReleaseCode(tenantCode, releaseCode);

        this.countCouponRelease(releaseDomain);


        return releaseDomain;
    }

    /**
     * 根据条件查询出所有投放记录
     */
    public PageInfo<CouponReleaseDomain> queryCouponRelease(CouponReleaseDomain releaseDomainParam, RequestPage page) {
        PageInfo<CouponReleaseDomain> releaseDomainPage = this.couponReleaseService.queryReleases(releaseDomainParam, page);
        if (CollectionUtils.isNotEmpty(releaseDomainPage.getList())) {
            for (CouponReleaseDomain e : releaseDomainPage.getList()) {
                this.countCouponRelease(e);
            }
        }
        return releaseDomainPage;
    }

    public void countCouponRelease(CouponReleaseDomain releaseDomain) {

        if (StringUtil.isEmpty(releaseDomain.getCouponRuleType()) && !CouponTypeEnum.PROMOTION_CODE.code().equals(releaseDomain.getCouponType())){
            releaseDomain.setCouponRuleType(CouponRuleTypeEnum.DIGIT.code());
        }

        if (CouponTypeEnum.PROMOTION_COUPON.equalsCode(releaseDomain.getCouponType())) {

            List<CountCouponCodeModel> cccModels = this.couponCodeUserService.countCouponCode(releaseDomain.getTenantCode(), releaseDomain.getActivityCode(),
                releaseDomain.getReleaseCode());

            releaseDomain.setReceivedQuantity(CountCouponCodeModel.countByStatus(cccModels, CouponStatusEnum.GRANTED));
            releaseDomain.setUsedTotal(CountCouponCodeModel.countByStatus(cccModels, CouponStatusEnum.USED));
            releaseDomain.setLocked(CountCouponCodeModel.countByStatus(cccModels, CouponStatusEnum.LOCKED));


        } else if (CouponTypeEnum.ANONYMITY_COUPON.equalsCode(releaseDomain.getCouponType())) {

            List<CountCouponCodeModel> cccModels = this.couponInnerCodeService.countCouponCode(releaseDomain.getTenantCode(), releaseDomain.getActivityCode(),
                releaseDomain.getReleaseCode());

            releaseDomain.setUsedTotal(CountCouponCodeModel.countByStatus(cccModels, CouponStatusEnum.USED));
            releaseDomain.setLocked(CountCouponCodeModel.countByStatus(cccModels, CouponStatusEnum.LOCKED));

            //领取数量以前默认0 现在要按最新匿名券领取规则计算领取数量
            int i = couponInnerCodeService.countAnonymityCouponCode(releaseDomain.getTenantCode(), releaseDomain.getActivityCode(), releaseDomain.getReleaseCode());
            releaseDomain.setReceivedQuantity(i);

        } else {
            releaseDomain.setInventory(0);
            releaseDomain.setReceivedQuantity(0);
            releaseDomain.setUsedTotal(couponCodeUserService.findCouponUserByActivityCode(releaseDomain.getTenantCode(), releaseDomain.getActivityCode()));
            releaseDomain.setLocked(0);
        }

    }

    @Transactional
    public String releaseCoupon(ReleaseCouponInDTO dto, Map<String, String> outCouponMap,CouponReleaseDomain releaseDomain) {

        // 根据活动编码查询促销券活动信息
        ActivityModel activityModel = this.couponActivityComponent.findEffectiveActivity(dto.getTenantCode(), dto.getActivityCode(), null);
		checkReleaseCoupon(dto, activityModel);
		// 查询已经发放券码数量
		Long releaseTotal = couponReleaseService.queryReleaseCount111(activityModel.getActivityCode());
		// 判断发券数量是否超出，投放总量如果是0，表示不限数量
		checkQuantity(dto, activityModel, releaseTotal);

		String releaseCode = getCouponReleaseCode();
		CouponReleaseModel promoCouponReleaseVO = makeCouponRelease(dto, activityModel, releaseCode);
		// 保存促销券码投放信息
		couponReleaseService.createCouponRelease(promoCouponReleaseVO);

		if (null != releaseDomain) {
			BeanCopyUtils.copyProps(promoCouponReleaseVO, releaseDomain);
		}

		String jsonString = JSONObject.toJSONString(promoCouponReleaseVO);
		if (ReleaseTypeEnum.APPOINTMENT.equalsCode(dto.getReleaseType())) {
			// 预约缓存的key
			redisService.addCouponDelayRelease(releaseCode.toString(), activityModel.getTenantCode(), activityModel.getActivityCode(), dto.getReleaseTime(),
					dto.getReleaseQuantity(), jsonString);
		}
		// 生成券码
		generateCouponCode(outCouponMap, activityModel, releaseTotal, promoCouponReleaseVO);

		return String.valueOf(releaseCode);
	}

	private CouponReleaseModel makeCouponRelease(ReleaseCouponInDTO dto, ActivityModel activityModel, String releaseCode) {
        dto.setCouponType(activityModel.getCouponType());
        // 填充券类型
        CouponReleaseModel promoCouponReleaseVO = BeanCopyUtils.jsonCopyBean(dto, CouponReleaseModel.class);
        promoCouponReleaseVO.setReceiveStartTime(dto.getReceiveStart());
        promoCouponReleaseVO.setReceiveEndTime(dto.getReceiveEnd());
        promoCouponReleaseVO.setReleaseCode(releaseCode);
        promoCouponReleaseVO.setActivityCode(activityModel.getActivityCode());
        promoCouponReleaseVO.setValidStartTime(dto.getValidStart());
        promoCouponReleaseVO.setValidEndTime(dto.getValidEnd());
        promoCouponReleaseVO.setTimeSameActivity(dto.getTimeSameActivity());
        promoCouponReleaseVO.setReceiveTimeSameActivity(dto.getReceiveTimeSameActivity());
        promoCouponReleaseVO.setReleaseQuantity(dto.getReleaseQuantity());
        Integer inventory = dto.getInventory() == null ? dto.getReleaseQuantity() : dto.getInventory();
        promoCouponReleaseVO.setInventory(inventory);
        promoCouponReleaseVO.setReleaseSource(dto.getReleaseSource());
        promoCouponReleaseVO.setPromotionType(dto.getPromotionType());
        promoCouponReleaseVO.setRemark(dto.getRemark());
        promoCouponReleaseVO.setCreateUser(dto.getCreateUser());
        if (!CouponTypeEnum.PROMOTION_CODE.code().equals(dto.getCouponType())) {
            promoCouponReleaseVO.setCouponRuleType(StringUtil.isEmpty(dto.getCouponRuleType()) ? CouponRuleTypeEnum.DIGIT.code() : dto.getCouponRuleType());
        }
        getReleaseTimeAndStatus(dto, promoCouponReleaseVO);
        return promoCouponReleaseVO;
    }

	private void checkReleaseCoupon(ReleaseCouponInDTO dto, ActivityModel activityModel) {
		CheckUtils.isNotNull(activityModel, ErrorCodes.VALIDATE_COUPON_ACTIVITY_EXIST);

        // 即时投放取当前时间为投放时间
        checkReleaseTime(dto, activityModel);
        //投放时间小于活动结束时间
        if (StringUtil.isNotBlank(dto.getReleaseTime())) {
            Check.check(Long.parseLong(activityModel.getActivityEnd()) <= Long.parseLong(dto.getReleaseTime()), CouponActivityChecker.RELEASE_TIME_END);
        }
        if (ReleaseTimeSameActivityEnum.SAME.getCode().equals(dto.getTimeSameActivity())){
            dto.setValidEnd(activityModel.getActivityEnd());
            dto.setValidStart(activityModel.getActivityBegin());
            dto.setValidDays(null);
        }
        if(ReleaseTimeSameActivityEnum.SAME.getCode().equals(dto.getReceiveTimeSameActivity())){
            dto.setReceiveStart(activityModel.getActivityBegin());
            dto.setReceiveEnd(activityModel.getActivityEnd());
            //如果券有效时间和活动不同期，在领券之后N天生效，券的领券结束时间往前推N天
            if(ReleaseTimeSameActivityEnum.SAME_NOT.getCode().equals(dto.getTimeSameActivity()) && null != dto.getValidDays() ) {
                dto.setReceiveEnd(buildReceiveEndTimeByDays(activityModel.getActivityEnd(),dto.getValidDays()));
            }
        }
        Check.check(Long.parseLong(activityModel.getActivityEnd()) < Long.parseLong(dto.getReceiveEnd()), CouponActivityChecker.TIME_END2);
        String dateAsString = DateUtil.getCurrentDateAsString(DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        if (null == dto.getValidDays()) {
            //没有指定可用天数，可用时间段必须得有
            Check.check(StringUtil.isBlank(dto.getValidStart()) || StringUtil.isBlank(dto.getValidEnd()), CouponActivityChecker.ERROR_VALID_02);
            long validEndLong = Long.parseLong(dto.getValidEnd());
            long validStartLong = Long.parseLong(dto.getValidStart());

            if (Boolean.TRUE.equals(dto.isCheckFlagTime())){
                Check.check(validEndLong < Long.parseLong(dateAsString), CouponActivityChecker.NOM_TIME2);
            }

            //可用时间比较
            Check.check(validStartLong >= validEndLong, CouponActivityChecker.VALID_TIME);
            //可用开始时间大于等于活动开始时间
            Check.check(validStartLong < Long.parseLong(activityModel.getActivityBegin()), CouponActivityChecker.VALID_TIME1);
            //可用开始时间得大于等于领取开始时间
//            Check.check(validStartLong < Long.parseLong(dto.getReceiveStart()), CouponActivityChecker.VALID_RELEASE_TIME)
            //领取结束时间小于等于可用结束时间
//            Check.check(Long.parseLong(dto.getReceiveEnd()) > validEndLong, CouponActivityChecker.TIME_END3)
            //可用结束时间  <= 活动结束时间

            if (Boolean.TRUE.equals(dto.isCheckFlagTime())){
                Check.check(Long.parseLong(activityModel.getActivityEnd()) < validEndLong, CouponActivityChecker.TIME_END1);
            }

        } else {
            Check.check(StringUtil.isNotBlank(dto.getValidStart()) || StringUtil.isNotBlank(dto.getValidEnd()), CouponActivityChecker.ERROR_VALID_01);
        }
        //活动开始时间小于等于领取开始时间
//        Check.check(Long.parseLong(activityModel.getActivityBegin()) > Long.parseLong(dto.getReceiveStart()), CouponActivityChecker.TIME_START1)
        //领取结束时间小于活动结束时间
//        Check.check(Long.parseLong(dto.getReceiveEnd()) > Long.parseLong(activityModel.getActivityEnd()), CouponActivityChecker.TIME_END2)
        //领取结束时间小于当前时间不能投放
        String receiveEnd = dto.getReceiveEnd();
        if (Boolean.TRUE.equals(dto.isCheckFlagTime())){
            Check.check(Long.parseLong(receiveEnd) < Long.parseLong(dateAsString), CouponActivityChecker.NOM_TIME1);
        }
        // 检查活动有效状态
        Check.check(!ActivityStatusEnum.EFFECTIVE.equalsCode(activityModel.getActivityStatus()), CouponActivityChecker.ERROR_ACTIVITY_RELEASE_STATUS);
        long currentDate = Long.parseLong(DateUtil.getCurrentDateAsString(DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        Check.check(Long.parseLong(activityModel.getActivityEnd()) <= currentDate, CouponActivityChecker.COUPON_ACTIVITY_INVALID);

        //优惠码不能进行投放
        Check.check(CouponTypeEnum.PROMOTION_CODE.equalsCode(activityModel.getCouponType()), CouponActivityChecker.PROMOTION_CODE_ACTICVITY);
        if (CouponTypeEnum.ANONYMITY_COUPON.equalsCode(activityModel.getCouponType())) {
            Check.check(Long.valueOf(dto.getReleaseQuantity()) > 10000, CouponActivityChecker.RELEASE_QUANTITY_LIMIT);
        }
	}

    public String buildReceiveEndTimeByDays(String activityEndTime,Integer days){
        Date date = DateUtil.parseDate(activityEndTime,DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DAY_OF_MONTH,-days);
        date = calendar.getTime();
        return DateUtil.format(date,DateUtil.FORMAT_YYYYMMDDHHMISS_14);
    }


    public void checkQuantity(ReleaseCouponInDTO dto, ActivityModel activityModel, Long releaseTotal) {
        if (Long.valueOf(activityModel.getTotalQuantity()) > 0) {
            if (releaseTotal != null) {
                Check.check(releaseTotal + Long.valueOf(dto.getReleaseQuantity()) > Long.valueOf(activityModel.getTotalQuantity()),
                    CouponActivityChecker.RELEASE_QUQNTITY_BEYOND);
            } else {
                Check.check(Long.valueOf(dto.getReleaseQuantity()) > Long.valueOf(activityModel.getTotalQuantity()), CouponActivityChecker.RELEASE_QUQNTITY_BEYOND);
            }
        }
    }

    public void getReleaseTimeAndStatus(ReleaseCouponInDTO dto, CouponReleaseModel promoCouponReleaseVO) {
        if (ReleaseTypeEnum.IMMEDIATELY.equalsCode(promoCouponReleaseVO.getReleaseType())) {
            promoCouponReleaseVO.setReleaseTime(DateUtil.getCurrentDateAsString(DateUtil.FORMAT_YYYYMMDDHHMISS_14));
            promoCouponReleaseVO.setReleaseStatus(CouponReleaseStatusEnum.CREATE_COUPON.code());
        } else {
            promoCouponReleaseVO.setReleaseTime(dto.getReleaseTime());
            promoCouponReleaseVO.setReleaseStatus(CouponReleaseStatusEnum.NOT_CREATE_COUPON.code());
        }
    }

    public void generateCouponCode(Map<String, String> outCouponMap, ActivityModel activityModel, Long releaseTotal, CouponReleaseModel promoCouponReleaseVO) {
        if (outCouponMap != null && outCouponMap.size() > 0) {
            //导入券码
            // 判断发券数量是否超出
            if (Long.valueOf(activityModel.getTotalQuantity()) > 0) {
                if (releaseTotal != null) {
                    Check.check(releaseTotal + outCouponMap.size() > Long.valueOf(activityModel.getTotalQuantity()), CouponActivityChecker.RELEASE_QUQNTITY_BEYOND);
                } else {
                    Check.check(outCouponMap.size() > Long.valueOf(activityModel.getTotalQuantity()), CouponActivityChecker.RELEASE_QUQNTITY_BEYOND);
                }
            }
            importOutCoupons(promoCouponReleaseVO, outCouponMap, activityModel);
        } else {
            // 如果投放类型为即时投放则需要生成券码,非导入的优惠券投放不再生成券码，而是改由领取券码的时候生成
            if (ReleaseTypeEnum.IMMEDIATELY.equalsCode(promoCouponReleaseVO.getReleaseType())) {

                createCouponsWithRetry(promoCouponReleaseVO,activityModel);
            }
        }
    }


    public void createCouponsWithRetry(CouponReleaseModel promoCouponReleaseVO, ActivityModel activityModel) {
        int retry = 0;

        while (retry <= MAX_RETRY_TIMES) {
            try {
                createInnerCoupons(promoCouponReleaseVO, activityModel);
                break;
            } catch (DuplicateKeyException e) {
                log.error("Duplicate key exception occurred during coupon creation. Retry count: " + (retry + 1), e);
                if (retry== MAX_RETRY_TIMES){
                    throw e;
                }
                retry++;
            } catch (Exception e) {
                // 捕获其他异常，并记录
                log.error("An unexpected exception occurred during coupon creation.", e);
                throw e;
            }
        }
    }



    public void checkReleaseTime(ReleaseCouponInDTO dto, ActivityModel activityModel) {
        if (ReleaseTypeEnum.IMMEDIATELY.equalsCode(dto.getReleaseType())) {
            dto.setReleaseTime(DateValidUtil.getNowFormatDate(DateValidUtil.YYYYMMDDHHMMSS));
        } else {
            // 预约投放必须有投放时间
            Check.check(StringUtil.isBlank(dto.getReleaseTime()), CouponActivityChecker.NOT_NULL_RELEASE_TIME);
            Check.check(Long.parseLong(dto.getReleaseTime()) < Long.parseLong(activityModel.getActivityBegin()), CouponActivityChecker.NOT_NULL_RELEASE_END_TIME3);
            if (CouponTypeEnum.PROMOTION_COUPON.code().equals(activityModel.getCouponType())) {
                Check.check(Long.parseLong(dto.getReleaseTime()) >= Long.parseLong(dto.getReceiveEnd()), CouponActivityChecker.NOT_NULL_RELEASE_END_TIME);
            } else {
                //匿名券
                Check.check(Long.parseLong(dto.getReleaseTime()) >= Long.parseLong(dto.getValidEnd()), CouponActivityChecker.NOT_NULL_RELEASE_END_TIME1);
            }
        }
    }

    @Transactional
    public int cancelReleaseCoupon111(String tenantCode, String releaseCode) {

        CouponReleaseDomain releaseDomain = couponReleaseService.findCouponReleaseByReleaseCode(tenantCode, releaseCode);
        CheckUtils.isNotNull(releaseDomain, ErrorCodes.VALIDATE_COUPON_ACTIVITY_EXIST);

        // 只能取消预约投放但还未投放的活动
        Check.check(!ReleaseTypeEnum.APPOINTMENT.code().equals(releaseDomain.getReleaseType()), CouponActivityChecker.NOT_APPOINT_TYPE);
        // 只能取消未投放状态的投放活动
        Check.check(!CouponReleaseStatusEnum.NOT_CREATE_COUPON.code().equals(releaseDomain.getReleaseStatus()), CouponActivityChecker.RELEASE_HAVE_COMPLETE);
        int result = 0;
        if (ReleaseTypeEnum.IMMEDIATELY.code().equals(releaseDomain.getReleaseType())) {
            releaseDomain.setReleaseStatus(CouponReleaseStatusEnum.STOP_RELEASE.code());
            result = couponReleaseService.updateCouponReleaseByReleaseCode(releaseDomain);
            //预约缓存的key
            redisService.deleteCouponDelayRelease(releaseDomain.getReleaseCode(), releaseDomain.getTenantCode(), releaseDomain.getActivityCode(),
                releaseDomain.getReleaseTime(), releaseDomain.getReleaseQuantity());
        }
        return result;
    }

    /**
     * 获取券面值
     * 
     * @return 满减活动的面值，非满减则返回null
     */
    private FaceClass getFaceValue111(String activityCode) {

    	String key = "FaceClass:" + activityCode;
        //获取活动层级列表
		FaceClass face = (FaceClass) EasyCacheUtil.get(key);
		if (face != null) {
			return face;
		}
        List<ActivityFunctionParamRankModel> rankListByRuleId = activityFuncRankService.getRankListByActivityCode(activityCode);
        for (ActivityFunctionParamRankModel vo : rankListByRuleId) {
            List<FunctionParamModel> ruleFuncParamListByRankId = activityFuncParamService.getRuleFuncParamListByRankId(vo.getId());
            for (FunctionParamModel paramVO : ruleFuncParamListByRankId) {

                if (FuncTypeEnum.IncentiveEnum.REDUCE_AMOUNT.equalsCode(paramVO.getFunctionCode())) {
					face = new FaceClass();
                    face.setFaceValue(new BigDecimal(paramVO.getParamValue()));
                    face.setFaceUnit(FaceUnitEnum.MONEY.code());
					EasyCacheUtil.set(key, face, 300);
                    return face;
                }
                if (FuncTypeEnum.IncentiveEnum.DISCOUNT.equalsCode(paramVO.getFunctionCode())) {
					face = new FaceClass();
                    face.setFaceValue(new BigDecimal(paramVO.getParamValue()));
                    face.setFaceUnit(FaceUnitEnum.DISCOUNT.code());
					EasyCacheUtil.set(key, face, 300);
                    return face;
                }
            }
        }
        return null;
    }

    /**
     * 根据投放信息生成券码插入券码表中
     */
    public void createInnerCoupons(CouponReleaseModel releaseVO, ActivityModel activityModel) {

        Integer releaseQuantity = releaseVO.getReleaseQuantity();
        FaceClass face = getFaceValue111(activityModel.getActivityCode());
        Check.check(releaseQuantity > Integer.MAX_VALUE / 2, CouponActivityChecker.TOTAL_QUANTITY_OUT_LIMIT);
        Map<String, String> codeMap = new HashMap<>(releaseQuantity * 2);
        TPromoCouponInnerCodeVO promoCouponInnerCodeVO = null;
        List<TPromoCouponInnerCodeVO> innerCodeVOs;
        if (CouponTypeEnum.PROMOTION_COUPON.equalsCode(releaseVO.getCouponType())) {
            innerCodeVOs = new ArrayList<>(1);
        } else {
            innerCodeVOs = new ArrayList<>(releaseQuantity);
        }
        // 生成券码,优惠券不用生成，只需要模拟一个券码为空的对象放入缓存
        boolean isNeedCreateCouponCode = true;
        while (codeMap.size() < releaseQuantity && isNeedCreateCouponCode) {
			String couponCode;

            if (null == releaseVO.getCouponRuleType() || CouponRuleTypeEnum.DIGIT.code().equals(releaseVO.getCouponRuleType())){
                //纯数字生成方式
                couponCode = CouponUtils.createCoupons(activityModel.getTenantCode(), releaseVO.getReleaseCode(),
                        releaseVO.getCouponCodePrefix(), releaseVO.getCouponCodeLength());
            }else {
                //字母和数字混合方式
                couponCode = CouponUtils.generateRandomCouponCode(releaseVO.getCouponCodePrefix(), releaseVO.getCouponCodeLength());
            }

            if (codeMap.containsKey(couponCode)) {
                continue;
            }
            codeMap.put(couponCode, couponCode);
            promoCouponInnerCodeVO = new TPromoCouponInnerCodeVO();
            promoCouponInnerCodeVO.setCouponCode(couponCode);
            if (CouponTypeEnum.PROMOTION_COUPON.equalsCode(releaseVO.getCouponType())) {
                isNeedCreateCouponCode = false;
                promoCouponInnerCodeVO.setCouponCode(null);
            }
            promoCouponInnerCodeVO.setTenantCode(activityModel.getTenantCode());
            promoCouponInnerCodeVO.setReleaseCode(releaseVO.getReleaseCode());
            promoCouponInnerCodeVO.setActivityCode(releaseVO.getActivityCode());
            promoCouponInnerCodeVO.setCouponType(releaseVO.getCouponType());
            promoCouponInnerCodeVO.setStatus("01");
            promoCouponInnerCodeVO.setFrozenStatus("01");
            if (Objects.isNull(face)) {
                promoCouponInnerCodeVO.setFaceValue(null);
                promoCouponInnerCodeVO.setFaceUnit(null);
            } else {
                promoCouponInnerCodeVO.setFaceValue(face.getFaceValue());
                promoCouponInnerCodeVO.setFaceUnit(face.getFaceUnit());

            }
            promoCouponInnerCodeVO.setReceiveStartTime(releaseVO.getReceiveStartTime());
            promoCouponInnerCodeVO.setReceiveEndTime(releaseVO.getReceiveEndTime());
            promoCouponInnerCodeVO.setLogicDelete("0");
            innerCodeVOs.add(promoCouponInnerCodeVO);
        }
        if (isNeedCreateCouponCode) {
            couponInnerCodeService.insertPromoCouponInnerCode(innerCodeVOs);
        }

        // 券码存缓存，方便领券
        addCouponCache(innerCodeVOs, releaseVO, activityModel);

        CouponUtils.clearCouponSet();
        CouponUtils.clearCouponDigitSet();
    }

    /**
     * 添加券码到缓存
     */
    private void addCouponCache(List<TPromoCouponInnerCodeVO> innerCodeVOs, CouponReleaseModel releaseVO, ActivityModel activityModel) {

        // 券码存缓存，方便领券
        Set<ZSetOperations.TypedTuple<String>> cacheCet = new HashSet<>(innerCodeVOs.size());
        String couponType = innerCodeVOs.get(0).getCouponType();
        // 只有优惠券才添加投放缓存
        if (!couponType.equals(CouponTypeEnum.PROMOTION_CODE.code())) {
            String releaseCode = innerCodeVOs.get(0).getReleaseCode();
            String receiveEndTime = innerCodeVOs.get(0).getReceiveEndTime();
            if (CouponTypeEnum.PROMOTION_COUPON.equalsCode(couponType)) {
                for (int j = 0; j < innerCodeVOs.size(); j++) {
                    TPromoCouponInnerCodeVO innerCodeVO = innerCodeVOs.get(j);
                    ZSetOperations.TypedTuple<String> temp = getStringTypedTuple(releaseVO, activityModel, couponType, releaseCode, receiveEndTime, innerCodeVO);
                    cacheCet.add(temp);
                }
            } else {
                TPromoCouponInnerCodeVO innerCodeVO = innerCodeVOs.get(0);
                ZSetOperations.TypedTuple<String> temp = getStringTypedTuple(releaseVO, activityModel, couponType, releaseCode, receiveEndTime, innerCodeVO);
                cacheCet.add(temp);
            }
            redisService.addCoupon(cacheCet, releaseCode, activityModel.getTenantCode(), activityModel.getActivityCode(), releaseVO.getReceiveStartTime(),
                receiveEndTime, releaseVO.getReleaseQuantity());

        }
    }

    private ZSetOperations.TypedTuple<String> getStringTypedTuple(CouponReleaseModel releaseVO, ActivityModel activityModel, String couponType, String releaseCode, String receiveEndTime, TPromoCouponInnerCodeVO innerCodeVO) {
        CouponCodeCacheDTO cacheDTO = new CouponCodeCacheDTO();
        cacheDTO.setCouponType(couponType);
        cacheDTO.setActivityCode(activityModel.getActivityCode());
        cacheDTO.setCouponCode(innerCodeVO.getCouponCode());
        cacheDTO.setFaceValue(innerCodeVO.getFaceValue());
        cacheDTO.setFaceUnit(innerCodeVO.getFaceUnit());
        cacheDTO.setReleaseCode(releaseCode);
        cacheDTO.setTenantCode(activityModel.getTenantCode());
        cacheDTO.setUserLimitMax(activityModel.getUserLimitMax());
        cacheDTO.setReceiveStartTime(innerCodeVO.getReceiveStartTime());
        cacheDTO.setReceiveEndTime(receiveEndTime);
        cacheDTO.setActivityEndTime(activityModel.getActivityEnd());
        cacheDTO.setValidDays(releaseVO.getValidDays());
        cacheDTO.setValidStartTime(releaseVO.getValidStartTime());
        cacheDTO.setValidEndTime(releaseVO.getValidEndTime());
        String jsonString = JSONObject.toJSONString(cacheDTO);
        return new DefaultTypedTuple<>(jsonString, Double.parseDouble(releaseCode));
    }

    /**
     * 根据投放信息生成券码插入券码表中
     * 
     * @param promoCouponReleaseVO 投放信息
     * @param outCouponMap 券信息
     * @param activityModel 活动信息
     */
    private void importOutCoupons(CouponReleaseModel promoCouponReleaseVO, Map<String, String> outCouponMap, ActivityModel activityModel) {

        FaceClass face = getFaceValue111(activityModel.getActivityCode());
        // 如果投放类型为即时投放则需要生成券码
        if (ReleaseTypeEnum.IMMEDIATELY.code().equals(promoCouponReleaseVO.getReleaseType())) {
            TPromoCouponInnerCodeVO promoCouponInnerCodeVO = null;
            List<TPromoCouponInnerCodeVO> innerCodeVOs = new ArrayList<>(outCouponMap.size());
            // 生成券码
            for (String couponCode : outCouponMap.values()) {
                promoCouponInnerCodeVO = new TPromoCouponInnerCodeVO();
                promoCouponInnerCodeVO.setReleaseCode(promoCouponReleaseVO.getReleaseCode());
                promoCouponInnerCodeVO.setTenantCode(activityModel.getTenantCode());
                promoCouponInnerCodeVO.setCouponCode(couponCode);
                promoCouponInnerCodeVO.setActivityCode(activityModel.getActivityCode());
                promoCouponInnerCodeVO.setCouponType(promoCouponReleaseVO.getCouponType());
                promoCouponInnerCodeVO.setStatus("01");
                promoCouponInnerCodeVO.setFrozenStatus("01");
                promoCouponInnerCodeVO.setFaceValue(face != null ? face.getFaceValue() : null);
                promoCouponInnerCodeVO.setFaceUnit(face != null ? face.getFaceUnit() : null);
                promoCouponInnerCodeVO.setReceiveStartTime(promoCouponReleaseVO.getReceiveStartTime());
                promoCouponInnerCodeVO.setReceiveEndTime(promoCouponReleaseVO.getReceiveEndTime());
                innerCodeVOs.add(promoCouponInnerCodeVO);
            }
            try {
                couponInnerCodeService.insertPromoCouponInnerCode(innerCodeVOs);
            } catch (Exception e) {
                String substring = e.getCause().getMessage().substring(17);
                String[] split = substring.split("'");
                String string3 = split[0];
                String[] split2 = string3.split("-");
                Check.check(true, CouponActivityChecker.DUPLICATE_COUPON_CODE, split2[0]);
            }
            // 券码存缓存，方便领券
            addCouponCache(innerCodeVOs, promoCouponReleaseVO, activityModel);
        }

    }

    @Transactional
    public void couponReleaseTimer() {

        // 查询预约未投放的券活动记录
        List<CouponReleaseModel> releaseVOs = couponReleaseService.queryReleasesNotReleased();
        if (!CollectionUtils.isEmpty(releaseVOs)) {
            for (CouponReleaseModel vo : releaseVOs) {

                ActivityModel activityModel = this.couponActivityComponent.findValidActivity(vo.getTenantCode(), vo.getActivityCode(), null, new Date());
                if (activityModel == null) {
                    continue;
                }

                //生成券码
                releaseCouponCode(vo, activityModel);
            }
        }
    }

    private void releaseCouponCode(CouponReleaseModel vo, ActivityModel activityModel) {

        // 生成券码
        createInnerCoupons(vo, activityModel);
        // 修改投放状态为已投放
        CouponReleaseDomain releaseDomain = new CouponReleaseDomain();
        releaseDomain.setReleaseStatus(CouponReleaseStatusEnum.CREATE_COUPON.code());
        releaseDomain.setReleaseCode(vo.getReleaseCode());
        couponReleaseService.updateCouponReleaseByReleaseCode(releaseDomain);
    }

    public int releaseSpecifiedQuantity(CouponReleaseDomain releaseSpecifiedQuantityDomain) {
        return couponReleaseService.releaseSpecifiedQuantity(releaseSpecifiedQuantityDomain);
    }

}
