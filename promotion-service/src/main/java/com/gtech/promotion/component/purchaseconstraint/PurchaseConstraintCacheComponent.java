package com.gtech.promotion.component.purchaseconstraint;

import com.alibaba.fastjson.JSON;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.RedisClient;
import com.gtech.promotion.code.activity.ActivityStatusEnum;
import com.gtech.promotion.dao.model.purchaseconstraint.PurchaseConstraintModel;
import com.gtech.promotion.dto.cache.PurchaseConstraintCacheDTO;
import com.gtech.promotion.dto.in.purchaseconstraint.FindPurchaseConstraintInDto;
import com.gtech.promotion.dto.out.purchaseconstraint.FindPurchaseConstraintOutDto;
import com.gtech.promotion.service.purchaseconstraint.PurchaseConstraintService;
import com.gtech.promotion.utils.Constants;
import com.gtech.promotion.vo.param.purchaseconstraint.PcRuleCatchRefreshRequest;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Component
public class PurchaseConstraintCacheComponent {
    @Autowired
    private RedisClient redisClient;

    @Autowired
    private PurchaseConstraintService pcService;

    @Autowired
    private PurchaseConstraintComponent pcComponent;

    @Autowired
    private PcRuleComponent pcRuleComponent;

    private static final ThreadPoolExecutor executor = new ThreadPoolExecutor(10, 50, 60, TimeUnit.SECONDS, new LinkedBlockingDeque<>(1000),
            new ThreadPoolExecutor.CallerRunsPolicy());

    /**
     * 将限购缓存对象放入缓存
     * @param pcCacheDTO
     */
    public PurchaseConstraintCacheDTO putPurchaseConstraintCache(PurchaseConstraintCacheDTO pcCacheDTO){
        if(null != pcCacheDTO) {
            String status = pcCacheDTO.getPurchaseConstraintStatus();
            //不符合的状态不存缓存
            if (!ActivityStatusEnum.EFFECTIVE.code().equalsIgnoreCase(status)) {
                return null;
            }

            String key = getCacheKey(pcCacheDTO.getTenantCode(), pcCacheDTO.getPurchaseConstraintCode());

            if(null != pcCacheDTO.getPurchaseConstraintEndTime()) {
                // 当前时间戳
                long currentTime = System.currentTimeMillis() / 1000;
                //活动结束时间
                long endTime = pcCacheDTO.getPurchaseConstraintEndTime().getTime() / 1000;
                long time = endTime - currentTime;
                if(time > 0) {
                    redisClient.setStringValue(key, JSON.toJSONString(pcCacheDTO), time, TimeUnit.SECONDS);
                    return pcCacheDTO;
                }else {
                    delPurchaseConstraintCache(pcCacheDTO.getTenantCode(), pcCacheDTO.getPurchaseConstraintCode());
                }
            }else { //没有结束日期表示是长期的限购
                redisClient.setStringValue(key, JSON.toJSONString(pcCacheDTO));
                return pcCacheDTO;
            }
        }
        return null;
    }

    /**
     * 根据限购code将限购放入缓存
     * @param tenantCode
     * @param purchaseConstraintCode
     */
    public void putPurchaseConstraintCache(String tenantCode, String purchaseConstraintCode) {
        PurchaseConstraintModel pcModel = pcService.getPurchaseConstraint(tenantCode, purchaseConstraintCode);
        if (null != pcModel && pcComponent.isEffectivePurchase(pcModel, new Date())) {
            FindPurchaseConstraintInDto findPurchaseConstraintInDto = FindPurchaseConstraintInDto.builder()
                                                                    .purchaseConstraintCode(purchaseConstraintCode)
                                                                    .domainCode(pcModel.getDomainCode())
                                                                    .tenantCode(pcModel.getTenantCode())
                                                                    .build();
            PurchaseConstraintCacheDTO pcCacheDTO = BeanCopyUtils.jsonCopyBean(pcComponent.findPurchaseConstraint(findPurchaseConstraintInDto), PurchaseConstraintCacheDTO.class);
            this.putPurchaseConstraintCache(pcCacheDTO);
            PcRuleCatchRefreshRequest request = new PcRuleCatchRefreshRequest();
            request.setTenantCode(tenantCode);
            request.setPcCodeList(Collections.singletonList(pcModel.getPurchaseConstraintCode()));
        }
    }

    /**
     * 根据限购Code获取单个限购信息
     * 若缓存中有就直接取缓存，若缓存中没有，则将有效的限购信息存入缓存后，再获取。
     * 若存入缓存失败，则返回null
     * @param tenantCode
     * @param pcCode
     * @return
     */
    public PurchaseConstraintCacheDTO getPcCacheDTOFromCache(String tenantCode, String pcCode) {
        String cacheKey = getCacheKey(tenantCode, pcCode);
        String cacheVal = redisClient.getString(cacheKey);
        if (StringUtil.isNotBlank(cacheVal)) {
            return JSON.parseObject(cacheVal, PurchaseConstraintCacheDTO.class);
        } else { // 若缓存中有就直接取缓存，若缓存中没有，则将有效的限购信息存入缓存后，再获取。
            putPurchaseConstraintCache(tenantCode, pcCode);
            cacheVal = redisClient.getString(cacheKey);
            if (StringUtil.isNotBlank(cacheVal)) {
                return JSON.parseObject(cacheVal, PurchaseConstraintCacheDTO.class);
            } else {
                // 若存入缓存失败，则返回null
                return null;
            }
        }

    }


    /**
     * 获取限购缓存的key
     * @param tenantCode
     * @param purchaseConstraintCode
     * @return
     */
    private String getCacheKey(String tenantCode, String purchaseConstraintCode) {
        return Constants.PURCHASE_CONSTRAINT_CACHE_KEY_PREFIX
                                + Constants.REDIS_KEY_SEPARATOR
                            + tenantCode
                            + Constants.REDIS_KEY_SEPARATOR
                            + purchaseConstraintCode;
    }

    /**
     * 根据限购code删除缓存
     * @param tenantCode
     * @param purchaseConstraintCode
     */
    public void delPurchaseConstraintCache(String tenantCode, String purchaseConstraintCode){
        String key = getCacheKey(tenantCode, purchaseConstraintCode);
        redisClient.delete(key);
        PcRuleCatchRefreshRequest request = new PcRuleCatchRefreshRequest();
        request.setTenantCode(tenantCode);
        request.setPcCodeList(Collections.singletonList(purchaseConstraintCode));
    }

    /**
     * 从缓存中获取所有有效的限购缓存
     * 1. 从数据库中获取所有有效的限购
     * 2. 根据数据库中code获取缓存中的限购详情
     * 3. 若缓存中没有，则调用放入缓存的方法返回缓存
     * 4. 返回所有的集合
     *
     * @param pcModelList
     * @return
     */
    @SneakyThrows
    public List<PurchaseConstraintCacheDTO> queryValidPcFromCache( List<PurchaseConstraintModel> pcModelList) {
        pcModelList.removeIf(x -> {
            Date now = new Date();
            Date pcStartTime = x.getPurchaseConstraintStartTime();
            Date pcEndTime = x.getPurchaseConstraintEndTime();
            return (pcStartTime != null && pcStartTime.compareTo(now) > 0)
                    || (pcEndTime != null && pcEndTime.compareTo(now) < 0);
        });
        if (CollectionUtils.isEmpty(pcModelList)) {
            return new ArrayList<>();
        }

        return pcModelList.stream()
                .map(pcModel -> CompletableFuture.supplyAsync(() -> {
                    PurchaseConstraintCacheDTO pcCacheDTOFromCache = this.getPcCacheDTOFromCache(pcModel.getTenantCode(), pcModel.getPurchaseConstraintCode());

                    return Optional.ofNullable(pcCacheDTOFromCache)
                            // 缓存中若没有，则将查出的有效限购信息放入缓存，一般不会执行else
                            .orElseGet(() -> {
                                FindPurchaseConstraintInDto findPurchaseConstraintInDto = FindPurchaseConstraintInDto.builder()
                                        .purchaseConstraintCode(pcModel.getPurchaseConstraintCode())
                                        .domainCode(pcModel.getDomainCode())
                                        .tenantCode(pcModel.getTenantCode())
                                        .build();
                                FindPurchaseConstraintOutDto pcOutDto = pcComponent.findPurchaseConstraint(findPurchaseConstraintInDto);
                                return putPurchaseConstraintCache(BeanCopyUtils.jsonCopyBean(pcOutDto, PurchaseConstraintCacheDTO.class));
                            });
                }, executor))
                .map(m-> {
                    try {
                        return m.get();
                    } catch (Exception e) {
                        Thread.currentThread().interrupt();
                        log.error("queryValidPcFromCache error", e);
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }



}
