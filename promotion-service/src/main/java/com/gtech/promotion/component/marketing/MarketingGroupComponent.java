package com.gtech.promotion.component.marketing;

import com.alibaba.fastjson.JSON;
import com.alibaba.rocketmq.common.message.Message;
import com.github.pagehelper.PageInfo;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.StringUtil;
import com.gtech.member.web.vo.param.QueryMemberParam;
import com.gtech.member.web.vo.result.QueryMemberListByConditionResult;
import com.gtech.promotion.checker.activity.TPromoActivityChecker;
import com.gtech.promotion.checker.marketing.MarketingChecker;
import com.gtech.promotion.code.activity.OrderStatusEnum;
import com.gtech.promotion.code.activity.SelectorProductTypeEnum;
import com.gtech.promotion.code.marketing.AutoGroupFlagEnum;
import com.gtech.promotion.code.marketing.MarketingGroupStatusEnum;
import com.gtech.promotion.code.marketing.UserGroupStatusEnum;
import com.gtech.promotion.component.flashsale.FlashSaleComponent;
import com.gtech.promotion.dao.model.marketing.MarketingGroupCodeMode;
import com.gtech.promotion.dao.model.marketing.MarketingGroupMode;
import com.gtech.promotion.dao.model.marketing.MarketingGroupUserMode;
import com.gtech.promotion.dao.model.marketing.MarketingModel;
import com.gtech.promotion.dao.model.marketing.flashsale.FlashSaleOrderDetailModel;
import com.gtech.promotion.dao.model.marketing.flashsale.FlashSaleOrderModel;
import com.gtech.promotion.exception.Check;
import com.gtech.promotion.feign.MemberFeignClient;
import com.gtech.promotion.mq.MQProducerFactory;
import com.gtech.promotion.pojo.GroupUserContent;
import com.gtech.promotion.pojo.MqEnums;
import com.gtech.promotion.service.flashsale.FlashSaleOrderDetailService;
import com.gtech.promotion.service.flashsale.FlashSaleOrderService;
import com.gtech.promotion.service.marketing.MarketingGroupCodeService;
import com.gtech.promotion.service.marketing.MarketingGroupService;
import com.gtech.promotion.service.marketing.MarketingGroupUserService;
import com.gtech.promotion.service.marketing.MarketingService;
import com.gtech.promotion.utils.Constants;
import com.gtech.promotion.vo.param.marketing.MarketingGroupUserListParam;
import com.gtech.promotion.vo.param.marketing.MarketingGroupUserParam;
import com.gtech.promotion.vo.param.marketing.flashsale.FindGroupUserParam;
import com.gtech.promotion.vo.result.flashsale.MarketingGroupUserListResult;
import com.gtech.promotion.vo.result.flashsale.MemberWeChatInfoResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/9/6 17:31
 */
@Component
@Slf4j
public class MarketingGroupComponent {

    @Autowired
    private MarketingGroupUserService marketingGroupUserService;

    @Autowired
    private MarketingGroupService marketingGroupService;


    @Autowired
    private MarketingGroupCodeService marketingGroupcodeService;

    @Autowired
    private FlashSaleOrderService flashSaleOrderService;

    @Autowired
    private FlashSaleOrderDetailService flashSaleOrderDetailService;

    @Autowired
    private MarketingService marketingService;

    @Autowired
    private FlashSaleComponent flashSaleComponent;


    @Autowired
    private MemberFeignClient memberFeignClient;

    public List<MarketingGroupUserListResult> queryGroupUserList(MarketingGroupUserParam param){

        MarketingGroupMode byActivityCode = marketingGroupService.findGroupByActivityCode(param.getTenantCode(),param.getActivityCode(),param.getCloseFlag());

        Check.check(null == byActivityCode, TPromoActivityChecker.NOT_NULL);

        Integer groupSize = byActivityCode.getGroupSize();

        //已支付的开团
        List<MarketingGroupUserMode> userModePay = marketingGroupUserService.queryPayGroupUserListByActivityCode(param);

        List<MarketingGroupUserMode> userModes = marketingGroupUserService.queryGroupUserListByActivityCode(param);

        List<MarketingGroupUserListResult> resultList = new ArrayList<>();
        Map<String, Long> collect = userModes.stream().collect(Collectors.groupingBy(MarketingGroupUserMode::getMarketingGroupCode, Collectors.counting()));

        for (MarketingGroupUserMode userMode : userModePay) {
            MarketingGroupUserListResult userResult = new MarketingGroupUserListResult();
            Long aLong = collect.get(userMode.getMarketingGroupCode());//成团人数
            userResult.setUserCode(userMode.getUserCode());
            userResult.setEffectiveTime(userMode.getEffectiveTime());
            userResult.setMarketingGroupCode(userMode.getMarketingGroupCode());
            userResult.setActivityCode(userMode.getActivityCode());
            userResult.setGroupStatus(userMode.getGroupStatus());
            userResult.setProductCode(userMode.getProductCode());
            userResult.setSkuCode(userMode.getSkuCode());
            userResult.setTeamLeader(userMode.getTeamLeader());
            int quantity = groupSize.intValue() - aLong.intValue();
            userResult.setRemainingQuantity(quantity);
            resultList.add(userResult);
        }

        return resultList;
    }


    /**
     * 根据条件查询活动下所有拼团
     * @param param
     * @return
     */
    public PageInfo<MarketingGroupUserListResult> queryAllGroupUserList(MarketingGroupUserListParam param) {

        PageInfo<MarketingGroupUserMode> pageInfo1 = marketingGroupUserService.queryAllGroupUserListByCondition(param);

        PageInfo<MarketingGroupUserListResult> pageInfo = new PageInfo<>();

        List<MarketingGroupUserMode> list = pageInfo1.getList();

        List<MarketingGroupUserListResult>  resultList = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(list)){

            //userCodes
            List<String> userCodes = list.stream().map(MarketingGroupUserMode::getUserCode).distinct().collect(Collectors.toList());
            Map<String, String> userNickMap = getUserWeChatInfoMap(param.getTenantCode(),param.getDomainCode(), userCodes);

            for (MarketingGroupUserMode userMode : list) {
                MarketingGroupCodeMode marketingGroupCodeMode = marketingGroupcodeService.queryGroupByMarketingGroupCode(userMode.getTenantCode(),
                        userMode.getActivityCode(), userMode.getMarketingGroupCode());
                MarketingGroupUserListResult result = BeanCopyUtils.jsonCopyBean(userMode, MarketingGroupUserListResult.class);

                String s = userNickMap.get(result.getUserCode());

                if (StringUtil.isNotEmpty(s)){
                    MemberWeChatInfoResult weChatInfoResult = JSON.parseObject(s, MemberWeChatInfoResult.class);
                    result.setWechatImg(weChatInfoResult.getWechatImg());
                    result.setNickName(weChatInfoResult.getNickName());
                }

                if (null != marketingGroupCodeMode){
                    result.setRemainingQuantity(marketingGroupCodeMode.getInventory());
                }
                resultList.add(result);
            }
        }
        pageInfo.setList(resultList);
        pageInfo.setTotal(pageInfo1.getTotal());

        return pageInfo;
    }

    public Map<String, String> getUserWeChatInfoMap(String tenantCode,String domainCode, List<String> userCodes) {
        QueryMemberParam memberParam = new QueryMemberParam();
        memberParam.setTenantCode(tenantCode);
        memberParam.setDomainCode(domainCode);
        memberParam.setMemberCodes(userCodes);
        Result<List<QueryMemberListByConditionResult>> listResult = memberFeignClient.queryMemberListByMemberCode(memberParam);

        Map<String, String> userNickMap = new HashMap<>();
        if (listResult.isSuccess()){
            List<QueryMemberListByConditionResult> data = listResult.getData();
            Map<String, String> collect = data.stream().filter(x->StringUtil.isNotEmpty(x.getExtParams())).collect(Collectors.toMap(QueryMemberListByConditionResult::getMemberCode, QueryMemberListByConditionResult::getExtParams));
            userNickMap.putAll(collect);
        }
        return userNickMap;
    }

    /**
     * 查询用户参与该拼团情况
     * @param param
     * @return
     */
    public MarketingGroupUserListResult findUserGroupByMarketingGroupAndUserCode(FindGroupUserParam param){

        MarketingGroupUserMode groupUserCode = marketingGroupUserService.findGroupUserCode(param);
        Check.check(null == groupUserCode, MarketingChecker.NO_MARKETING_GROUP_USER);

        MarketingGroupMode byActivityCode = marketingGroupService.findByActivityCode(groupUserCode.getActivityCode());
        Check.check(null == byActivityCode, MarketingChecker.NO_MARKETING_GROUP);

        MarketingGroupUserListResult result = BeanCopyUtils.jsonCopyBean(groupUserCode, MarketingGroupUserListResult.class);

        List<String> userCodes = new ArrayList<>();
        userCodes.add(groupUserCode.getUserCode());
        Map<String, String> userNickMap = getUserWeChatInfoMap(param.getTenantCode(),param.getDomainCode(), userCodes);
        String s = userNickMap.get(result.getUserCode());
        if (StringUtil.isNotEmpty(s)){
            MemberWeChatInfoResult weChatInfoResult = JSON.parseObject(s, MemberWeChatInfoResult.class);
            result.setWechatImg(weChatInfoResult.getWechatImg());
            result.setNickName(weChatInfoResult.getNickName());
        }

        if (UserGroupStatusEnum.FINISH.code().equals(groupUserCode.getGroupStatus())){
            result.setRemainingQuantity(0);
        }else {
            Integer groupSize = byActivityCode.getGroupSize();
            AtomicInteger atomicInteger = new AtomicInteger(groupSize);
            int count = marketingGroupUserService.queryGroupUserListByMarketingGroupCode(param.getTenantCode(), groupUserCode.getActivityCode(), groupUserCode.getMarketingGroupCode());
            int i1 = atomicInteger.addAndGet(-count);
            result.setRemainingQuantity(i1);
        }

        return result;
    }



    /***
     *  拼团 处理redis 过期消息方法
     *  值 ： MARKETING_GROUP_USER_CACHE:TENANTCODE=100016:ACTIVITYCODE=0623090600037630:MARKETINGGROUPCODE=PG20230906162132000001:USERCODE=111-2
     * @param itemValue
     */
    public void marketingGroupUserExpire(String itemValue) {

        String[] items = itemValue.split(Constants.REDIS_KEY_SEPARATOR);
        String tenantCode = items[1].substring(items[1].indexOf(Constants.REGEX) + 1);
        String activityCode = items[2].substring(items[2].indexOf(Constants.REGEX) + 1);
        String marketingGroupCode = items[3].substring(items[3].indexOf(Constants.REGEX) + 1);

        //查询拼团信息 通知是否自动成团
        MarketingGroupMode groupMode = marketingGroupService.findMarketingGroupByActivityCode(tenantCode, activityCode);

        if (null == groupMode) {
            log.info("无对应的拼团活动：{}，拼团编码：{}", activityCode, marketingGroupCode);
            return;
        }

        List<FlashSaleOrderModel> flashSaleOrderModels = flashSaleOrderService.queryOrderByMarketingGroupCode(tenantCode, activityCode, marketingGroupCode);

        //获取未支付的订单，用于资源释放

        List<FlashSaleOrderModel> orderList = flashSaleOrderModels.stream().filter(x ->
                x.getOrderStatus().equals(OrderStatusEnum.UNPAID.code()) || x.getOrderStatus().equals(OrderStatusEnum.PAID.code())).collect(Collectors.toList());
				MarketingGroupCodeMode marketingGroupCodeMode = marketingGroupcodeService.queryGroupByMarketingGroupCode(tenantCode, activityCode,
						marketingGroupCode);

        if (groupMode.getAutoGroupFlag().intValue() == Integer.parseInt(AutoGroupFlagEnum.YES.code())) {

            //状态变更支付变成成功
			marketingGroupUserService.updateGroupUserStatus(tenantCode, marketingGroupCode, UserGroupStatusEnum.FINISH.code(), UserGroupStatusEnum.PAID.code());
            //拼团状态进行中的改为已成功，未开始的状态保持不变
            String groupStatus = marketingGroupCodeMode.getGroupStatus();
			// 消息通知oms拼团成功
			sendMessageToOms(marketingGroupCodeMode, "1");
            if (groupStatus.equals(MarketingGroupStatusEnum.GROUP_PROCESSING.code())){
                marketingGroupcodeService.updateMarketingCodeGroupStatus(tenantCode,activityCode,marketingGroupCode, MarketingGroupStatusEnum.GROUP_SUCCESS.code());
            }

        } else {

            int inventory = marketingGroupCodeMode.getInventory();
            String groupStatus = marketingGroupCodeMode.getGroupStatus();

            if (inventory > 0 && groupStatus.equals(MarketingGroupStatusEnum.GROUP_PROCESSING.code()) ||
                    groupStatus.equals(MarketingGroupStatusEnum.GROUP_NO_START.code())){
                //消息通知oms拼团失败
				sendMessageToOms(marketingGroupCodeMode, "0");

                marketingGroupcodeService.updateMarketingCodeGroupStatus(tenantCode,activityCode,marketingGroupCode, MarketingGroupStatusEnum.GROUP_FAIL.code());

                //进行中以及支付的订单 资源释放
                for (FlashSaleOrderModel byOrderNo : orderList) {
                    cancelMarketingGroupOrder(byOrderNo);
                }
            }

        }
    }

	public void sendMessageToOms(MarketingGroupCodeMode marketingGroupCodeMode, String marketingGroupResult) {
		GroupUserContent groupUserContent = new GroupUserContent();
		groupUserContent.setActivityCode(marketingGroupCodeMode.getActivityCode());
		groupUserContent.setTenantCode(marketingGroupCodeMode.getTenantCode());
		groupUserContent.setDomainCode(marketingGroupCodeMode.getDomainCode());
		groupUserContent.setMarketingGroupCode(marketingGroupCodeMode.getMarketingGroupCode());
		groupUserContent.setMarketingGroupResult(marketingGroupResult);
		sendGroupMessage(groupUserContent);
	}

    /**
     * mq消息通知，用于通知oms
     * @param groupUserContent
     */
    public void sendGroupMessage(GroupUserContent groupUserContent){
        String content = JSON.toJSONString(groupUserContent);
        //消息通知失败
        log.info(" PT start send message:{}",content);
        Message message = new Message(MqEnums.MARKETING_GROUP_MQ.getTopicName(),"pushOms",groupUserContent.getMarketingGroupCode(), content.getBytes());

        try {
            MQProducerFactory.getInstance().getProducer(MqEnums.MARKETING_GROUP_MQ.getTopicName()).send(message);
        } catch (Exception e) {
            log.error("PT send message error");
            log.error(e.getMessage(), e);
        }
        log.info("PT end send message.");
    }


    public void cancelMarketingGroupOrder(FlashSaleOrderModel byOrderNo){

        List<FlashSaleOrderDetailModel> detailsByOrderNo = flashSaleOrderDetailService.findByOrderNo(byOrderNo.getTenantCode(),byOrderNo.getOrderId());

        MarketingModel byActivityCode = marketingService.findByActivityCode(byOrderNo.getActivityCode());

        for (FlashSaleOrderDetailModel detailModel : detailsByOrderNo) {
            String code = SelectorProductTypeEnum.SELECT_SPU.code();
            String selectProductType = byActivityCode.getSelectProductType();
            //spu或者sku资源回滚
            flashSaleComponent.rollBackDealInventory(byOrderNo, detailModel, code, selectProductType);

            //拼团取消业务逻辑，以及资源释放
            flashSaleComponent.rollBackMarketingGroupResource(byOrderNo.getTenantCode(), byOrderNo, detailModel,byActivityCode);

        }
        flashSaleOrderService.updateStatus(byOrderNo.getTenantCode(),byOrderNo.getOrderId(), OrderStatusEnum.CANCELED.code());
    }

}
