<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtech.promotion.dao.mapper.purchaseconstraint.PurchaseConstraintCustomerMapper">

    <resultMap type="PurchaseConstraintCustomer" id="PurchaseConstraintCustomerMap">
        <result property="id" column="ID" jdbcType="INTEGER"/>
        <result property="tenantCode" column="TENANT_CODE" jdbcType="VARCHAR"/>
        <result property="customerCode" column="CUSTOMER_CODE" jdbcType="VARCHAR"/>
        <result property="purchaseConstraintCode" column="PURCHASE_CONSTRAINT_CODE" jdbcType="VARCHAR"/>
        <result property="purchaseConstraintRuleType" column="PURCHASE_CONSTRAINT_RULE_TYPE" jdbcType="INTEGER"/>
        <result property="userCode" column="USER_CODE" jdbcType="VARCHAR"/>
        <result property="productCode" column="PRODUCT_CODE" jdbcType="VARCHAR"/>
        <result property="purchaseConstraintValue" column="PURCHASE_CONSTRAINT_VALUE" jdbcType="NUMERIC"/>
        <result property="purchaseConstraintValueUsed" column="PURCHASE_CONSTRAINT_VALUE_USED" jdbcType="NUMERIC"/>
        <result property="customerStartTime" column="CUSTOMER_START_TIME" jdbcType="TIMESTAMP"/>
        <result property="customerEndTime" column="CUSTOMER_END_TIME" jdbcType="TIMESTAMP"/>
        <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
    </resultMap>
    <update id="increment">
        update promo_purchase_constraint_customer as t
        set PURCHASE_CONSTRAINT_VALUE_USED =
            case
                <foreach collection="query" item="item" >
                    when  t.CUSTOMER_CODE = #{item.customerCode} then PURCHASE_CONSTRAINT_VALUE_USED + #{item.purchaseConstraintValueUsed}
                </foreach>
            end
        where
            <foreach collection="query" item="item" open="(" separator="or" close=")">
                t.tenant_code = #{item.tenantCode}
                and t.CUSTOMER_CODE = #{item.customerCode}
            </foreach>

    </update>
    <update id="decrement">
        update promo_purchase_constraint_customer as t
        set PURCHASE_CONSTRAINT_VALUE_USED = PURCHASE_CONSTRAINT_VALUE_USED - #{query.purchaseConstraintValueUsed}
        where t.tenant_code = #{query.tenantCode}
          and PURCHASE_CONSTRAINT_VALUE_USED - #{query.purchaseConstraintValueUsed} >= 0
          and t.CUSTOMER_CODE = #{query.customerCode}
    </update>
    <select id="list"
            resultType="com.gtech.promotion.dao.entity.purchaseconstraint.PurchaseConstraintCustomer">
        select t.ID,
               t.TENANT_CODE,
               t.CUSTOMER_CODE,
               t.PURCHASE_CONSTRAINT_CODE,
               t.PURCHASE_CONSTRAINT_RULE_TYPE,
               t.USER_CODE,
               t.PRODUCT_CODE,
               t.PURCHASE_CONSTRAINT_VALUE,
               t.PURCHASE_CONSTRAINT_VALUE_USED,
               t.CUSTOMER_START_TIME,
               t.CUSTOMER_END_TIME,
               t.CREATE_TIME,
               t.UPDATE_TIME
        from promo_purchase_constraint_customer as t
        <where>
            t.tenant_code = #{query.tenantCode}
            <if test="query.customerCodes !=null and query.customerCodes.size() >0 ">
                and t.CUSTOMER_CODE in
                <foreach collection="query.customerCodes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        <if test="query.pcCodes != null and query.pcCodes.size()>0">
            and t.PURCHASE_CONSTRAINT_CODE in 
            <foreach collection="query.pcCodes" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        </where>

    </select>


</mapper>

