<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtech.promotion.dao.mapper.purchaseconstraint.PurchaseConstraintDetailMapper">

    <resultMap type="PurchaseConstraintDetail" id="PromoPurchaseConstraintDetailMap">
        <result property="id" column="ID" jdbcType="INTEGER"/>
        <result property="tenantCode" column="TENANT_CODE" jdbcType="VARCHAR"/>
        <result property="customerCode" column="CUSTOMER_CODE" jdbcType="VARCHAR"/>
        <result property="purchaseConstraintCode" column="PURCHASE_CONSTRAINT_CODE" jdbcType="VARCHAR"/>
        <result property="orderId" column="ORDER_ID" jdbcType="VARCHAR"/>
        <result property="productCode" column="PRODUCT_CODE" jdbcType="VARCHAR"/>
        <result property="skuCode" column="SKU_CODE" jdbcType="VARCHAR"/>
        <result property="detailAmount" column="DETAIL_AMOUNT" jdbcType="NUMERIC"/>
        <result property="detailQty" column="DETAIL_QTY" jdbcType="INTEGER"/>
        <result property="detailAmountBack" column="DETAIL_AMOUNT_BACK" jdbcType="NUMERIC"/>
        <result property="detailQtyBack" column="DETAIL_QTY_BACK" jdbcType="INTEGER"/>
        <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
    </resultMap>
    <update id="decrement">
        update promo_purchase_constraint_detail as t
        set DETAIL_AMOUNT_BACK = DETAIL_AMOUNT_BACK + #{query.detailAmountBack},
            t.DETAIL_QTY_BACK       = t.DETAIL_QTY_BACK + #{query.detailQtyBack}
        where t.tenant_code = #{query.tenantCode}
          and t.DETAIL_AMOUNT >= DETAIL_AMOUNT_BACK + #{query.detailAmountBack}
          and t.DETAIL_QTY >= DETAIL_QTY_BACK + #{query.detailQtyBack}
          and t.CUSTOMER_CODE like concat('%', #{query.customerCode}, '%')
          and t.PRODUCT_CODE =#{query.productCode}
          and t.SKU_CODE = #{query.skuCode}
          and t.ORDER_ID = #{query.orderId}
    </update>
    <select id="list" resultType="com.gtech.promotion.dao.entity.purchaseconstraint.PurchaseConstraintDetail">
        select t.ID,
               t.TENANT_CODE,
               t.CUSTOMER_CODE,
               t.PURCHASE_CONSTRAINT_CODE,
               t.ORDER_ID,
               t.PRODUCT_CODE,
               t.SKU_CODE,
               t.DETAIL_AMOUNT,
               t.DETAIL_QTY,
               t.DETAIL_AMOUNT_BACK,
               t.DETAIL_QTY_BACK,
               t.CREATE_TIME,
               t.UPDATE_TIME
        from promo_purchase_constraint_detail as t
        <where>
            t.TENANT_CODE = #{query.tenantCode}
            <if test="query.orderCodes != null and query.orderCodes.size() > 0 ">
                and t.order_id in
                <foreach collection="query.orderCodes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.skuCodes != null and query.skuCodes.size() > 0">
                and t.sku_code in
                <foreach collection="query.skuCodes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>


</mapper>

