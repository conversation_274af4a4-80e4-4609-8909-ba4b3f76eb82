<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.gtech.promotion.dao.mapper.purchaseconstraint.PurchaseConstraintMapper" >
  <resultMap id="BaseResultMap" type="com.gtech.promotion.dao.entity.purchaseconstraint.PurchaseConstraintEntity" >
    <!--
      WARNING - @mbggenerated
    -->
    <id column="ID" property="id" jdbcType="BIGINT" />
    <result column="TENANT_CODE" property="tenantCode" jdbcType="VARCHAR" />
    <result column="ORG_CODE" property="orgCode" jdbcType="VARCHAR" />
    <result column="PURCHASE_CONSTRAINT_CODE" property="purchaseConstraintCode" jdbcType="VARCHAR" />
    <result column="PURCHASE_CONSTRAINT_NAME" property="purchaseConstraintName" jdbcType="VARCHAR" />
    <result column="PURCHASE_CONSTRAINT_START_TIME" property="purchaseConstraintStartTime" jdbcType="TIMESTAMP" />
    <result column="PURCHASE_CONSTRAINT_END_TIME" property="purchaseConstraintEndTime" jdbcType="TIMESTAMP" />
    <result column="PURCHASE_CONSTRAINT_STATUS" property="purchaseConstraintStatus" jdbcType="VARCHAR" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="CREATE_USER" property="createUser" jdbcType="VARCHAR" />
    <result column="UPDATE_USER" property="updateUser" jdbcType="VARCHAR" />
    <result column="AUDIT_USER" property="auditUser" jdbcType="VARCHAR" />
    <result column="DOMAIN_CODE" property="domainCode" jdbcType="VARCHAR" />
    <result column="PERIOD_TYPE" property="periodType" jdbcType="CHAR" />
    <result column="PRODUCT_SELECTION_TYPE" property="productSelectionType" jdbcType="CHAR" />
    <result column="ITEM_SCOPE_TYPE" property="itemScopeType" jdbcType="BIT" />
    <result column="FIRST_REFUSAL" property="firstRefusal" jdbcType="INTEGER" />
    <result column="STORE_TYPE" property="storeType" jdbcType="CHAR" />
    <result column="DESCRIPTION" property="description" jdbcType="VARCHAR" />
    <result column="PRIORITY" property="priority" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    <!--
      WARNING - @mbggenerated
    -->
    ID, TENANT_CODE, ORG_CODE, PURCHASE_CONSTRAINT_CODE, PURCHASE_CONSTRAINT_NAME, PURCHASE_CONSTRAINT_START_TIME,
    PURCHASE_CONSTRAINT_END_TIME, PURCHASE_CONSTRAINT_STATUS, CREATE_TIME, UPDATE_TIME,
    CREATE_USER, UPDATE_USER, PERIOD_TYPE, PRODUCT_SELECTION_TYPE, ITEM_SCOPE_TYPE, STORE_TYPE,AUDIT_USER,DOMAIN_CODE
    ,FIRST_REFUSAL,DESCRIPTION, PRIORITY
  </sql>


    <update id="updatePurchaseConstraintPriority">
        update PROMO_PURCHASE_CONSTRAINT
        set PRIORITY = #{purchaseConstraintPriority}
        <where>
          TENANT_CODE = #{tenantCode}
          AND PURCHASE_CONSTRAINT_CODE = #{purchaseConstraintCode}
        </where>
    </update>


    <select id="queryList" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List"/>
        from PROMO_PURCHASE_CONSTRAINT
        <where>
          TENANT_CODE = #{tenantCode}
          <if test="orgCode != null and defaultFlag ">
            AND ORG_CODE = #{orgCode}
          </if>
          <if test="defaultFlag != true ">
            AND ORG_CODE != 'default'
          </if>
          <if test="purchaseConstraintStatus != null and purchaseConstraintStatus.trim().length() != 0">
            AND PURCHASE_CONSTRAINT_STATUS = #{purchaseConstraintStatus}
          </if>
          <if test="purchaseConstraintName != null and purchaseConstraintName.trim().length() != 0">
            AND PURCHASE_CONSTRAINT_NAME like concat('%', #{purchaseConstraintName}, '%')
          </if>
          <if test="purchaseConstraintBeginFrom != null and purchaseConstraintBeginFrom.trim().length() != 0">
            AND (PURCHASE_CONSTRAINT_START_TIME is null OR  PURCHASE_CONSTRAINT_START_TIME &gt;= #{purchaseConstraintBeginFrom})
          </if>
          <if test="purchaseConstraintBeginTo != null and purchaseConstraintBeginTo.trim().length() != 0">
            AND (PURCHASE_CONSTRAINT_START_TIME is null OR  PURCHASE_CONSTRAINT_START_TIME &lt; #{purchaseConstraintBeginTo})
          </if>

          <if test="purchaseConstraintEndFrom != null and purchaseConstraintEndFrom.trim().length() != 0">
            AND (PURCHASE_CONSTRAINT_END_TIME is null OR  PURCHASE_CONSTRAINT_END_TIME &gt;= #{purchaseConstraintEndFrom})
          </if>
          <if test="purchaseConstraintEndTo != null and purchaseConstraintEndTo.trim().length() != 0">
            AND (PURCHASE_CONSTRAINT_END_TIME is null OR  PURCHASE_CONSTRAINT_END_TIME &lt;= #{purchaseConstraintEndTo})
          </if>
          <if test="purchaseConstraintCode != null and purchaseConstraintCode.trim().length() != 0">
            AND PURCHASE_CONSTRAINT_CODE = #{purchaseConstraintCode}
          </if>
          order by
          <if test="null != orderItems and orderItems.size > 0">
            <foreach collection="orderItems" item="orderItem" separator=",">
              ${orderItem.item} ${orderItem.type}
            </foreach>
          </if>
          <if test="null == orderItems or orderItems.size == 0">
          id desc
          </if>
        </where>
    </select>

    <select id="queryEffectivePc" resultMap="BaseResultMap">
      select
        <include refid="Base_Column_List"/>
      from PROMO_PURCHASE_CONSTRAINT
      <where>
        TENANT_CODE = #{tenantCode}
        and PURCHASE_CONSTRAINT_STATUS = '04'
        and (PURCHASE_CONSTRAINT_START_TIME is null or PURCHASE_CONSTRAINT_START_TIME &lt; NOW())
        and (PURCHASE_CONSTRAINT_END_TIME is null or PURCHASE_CONSTRAINT_END_TIME &gt; NOW())
      </where>
    </select>
</mapper>