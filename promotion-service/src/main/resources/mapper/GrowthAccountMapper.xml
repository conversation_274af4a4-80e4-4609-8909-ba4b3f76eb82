<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtech.promotion.dao.mapper.growth.GrowthAccountMapper">
  <resultMap id="BaseResultMap" type="com.gtech.promotion.dao.entity.growth.GrowthAccountEntity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="domain_code" jdbcType="VARCHAR" property="domainCode" />
    <result column="tenant_code" jdbcType="VARCHAR" property="tenantCode" />
    <result column="growth_account_code" jdbcType="VARCHAR" property="growthAccountCode" />
    <result column="account_code" jdbcType="VARCHAR" property="accountCode" />
    <result column="account_type" jdbcType="INTEGER" property="accountType" />
    <result column="account_desc" jdbcType="VARCHAR" property="accountDesc" />
    <result column="account_grade" jdbcType="VARCHAR" property="accountGrade" />
    <result column="account_balance" jdbcType="INTEGER" property="accountBalance" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="ext_params" jdbcType="CHAR" property="extParams" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="valid_begin_time" jdbcType="TIMESTAMP" property="validBeginTime" />
    <result column="valid_end_time" jdbcType="TIMESTAMP" property="validEndTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, domain_code, tenant_code, account_code, growth_account_code, account_type, account_desc, account_balance, account_grade, 
    status, ext_params, create_user, create_time, update_user, update_time, valid_begin_time ,valid_end_time
  </sql>

  <sql id="growth_account_query_condition">
    <trim prefix="WHERE" prefixOverrides="AND |OR ">
      <if test="domainCode != null and domainCode.trim().length() != 0">
        AND (domain_code = #{domainCode})
      </if>
      <if test="tenantCode != null and tenantCode.trim().length() != 0">
        AND (tenant_code = #{tenantCode})
      </if>
      <if test="accountCode != null and accountCode.trim().length() != 0">
        AND (account_code = #{accountCode})
      </if>
       <if test="growthAccountCode != null and growthAccountCode.trim().length() != 0">
        AND (growth_account_code = #{growthAccountCode})
      </if>
      <if test="accountType != null">
        AND (account_type = #{accountType})
      </if>
      <if test="accountDesc != null and accountDesc.trim().length() != 0">
        AND (account_desc = #{accountDesc})
      </if>
      <if test="accountBalance != null">
        AND (account_balance = #{accountBalance})
      </if>
      <if test="status != null">
        AND (status = #{status})
      </if>
      <if test="extParams != null">
        AND (ext_params = #{extParams})
      </if>
      <if test="createUser != null and createUser.trim().length() != 0">
        AND (create_user = #{createUser})
      </if>
      <if test="createBeginTime != null">
        AND (create_time &gt;= #{createBeginTime})
      </if>
      <if test="createEndTime != null">
        AND (create_time &lt;= #{createEndTime})
      </if>
      <if test="updateUser != null and updateUser.trim().length() != 0">
        AND (update_user = #{updateUser})
      </if>
      <if test="updateBeginTime != null">
        AND (update_time &gt;= #{updateBeginTime})
      </if>
      <if test="updateEndTime != null">
        AND (update_time &lt;= #{updateEndTime})
      </if>
    </trim>
  </sql>
  <select id="query" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
     from growth_account 
    <include refid="growth_account_query_condition" />
    order by CREATE_TIME desc
  </select>
  
    <update id="updateGrowth" parameterType="Map">
  	update growth_account set 
  	<if test="transactionType == 1">
  		account_balance = account_balance + #{transactionAmount}
  		where tenant_code = #{tenantCode} 
  		and account_code = #{accountCode} 
  		and account_type = #{accountType}
  	</if>
  	<if test="transactionType == 2">
  		account_balance = account_balance - #{transactionAmount}
		where tenant_code = #{tenantCode} 
		and account_code = #{accountCode} 
		and account_type = #{accountType} 
		and account_balance &gt;= #{transactionAmount}
  	</if>
  	</update>
</mapper>