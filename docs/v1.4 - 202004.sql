CREATE TABLE `growth_account` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'Primary key.',
  `domain_code` varchar(32) NOT NULL COMMENT 'Domain code.',
  `tenant_code` varchar(32) NOT NULL COMMENT 'Tenant code.',
  `growth_account_code` varchar(32) NOT NULL COMMENT 'Grwoth account code',
  `account_code` varchar(32) NOT NULL COMMENT 'account code. (UserCode or OrgCode)',
  `account_type` tinyint(1) NOT NULL COMMENT 'Growth account type. (1-User 2-Organization)',
  `account_desc` varchar(256) DEFAULT NULL COMMENT 'Growth account description.',
  `account_balance` int(11) NOT NULL DEFAULT '0' COMMENT 'Latest account points.',
  `account_grade` tinyint(1) DEFAULT NULL COMMENT 'Account grade.',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT 'Growth account status.(0-Inactive 1-Active)',
  `ext_params` json DEFAULT NULL COMMENT 'Extends parameters. (JSON String)',
  `create_user` varchar(32) DEFAULT NULL COMMENT 'Create user code',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Create time.',
  `update_user` varchar(32) DEFAULT NULL COMMENT 'Lastest update user.',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Lastest update time.',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uidx_growthaccount_tenant_account` (`tenant_code`,`account_type`,`account_code`),
  UNIQUE KEY `uidx_growth_account_code` (`tenant_code`,`growth_account_code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='Growth account information table.'

CREATE TABLE `growth_transaction` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'Primary key.',
  `domain_code` varchar(32) NOT NULL COMMENT 'Domain code.',
  `tenant_code` varchar(32) NOT NULL COMMENT 'Tenant code.',
  `transaction_sn` varchar(32) NOT NULL COMMENT 'Transaction serial number.',
  `account_code` varchar(32) NOT NULL COMMENT 'Growth account code. (UserCode or OrgCode)',
  `account_type` tinyint(1) NOT NULL COMMENT 'Growth account type. (1-User 2-Organization)',
  `transaction_type` tinyint(1) NOT NULL COMMENT 'Growth transaction type. (1-Increase points 2-Deduct points)',
  `transaction_remarks` varchar(256) DEFAULT NULL COMMENT 'Growth transaction remarks.',
  `transaction_amount` int(11) NOT NULL DEFAULT '0' COMMENT 'Growth spent/earned in the transaction.',
  `transaction_date` bigint(20) DEFAULT NULL COMMENT 'Transaction date. (yyyyMMddHHmmss)',
  `refer_transaction_sn` varchar(32) DEFAULT NULL COMMENT 'Refer transaction serial number.',
  `refer_order_number` varchar(32) DEFAULT NULL COMMENT 'Refer order number.',
  `create_user` varchar(32) DEFAULT NULL COMMENT 'Create user code',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Create time.',
  `update_user` varchar(32) DEFAULT NULL COMMENT 'Lastest update user.',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Lastest update time.',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uidx_growthtransaction_tenant_transactionsn` (`tenant_code`,`transaction_sn`),
  KEY `idx_growthtransaction_tenant_account` (`tenant_code`,`account_type`,`account_code`,`transaction_type`),
  KEY `idx_growthtransaction_tenant_transactiontype` (`tenant_code`,`transaction_type`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='Growth transaction detail information table.'


CREATE TABLE `point_account` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'Primary key.',
  `domain_code` varchar(32) NOT NULL COMMENT 'Domain code.',
  `tenant_code` varchar(32) NOT NULL COMMENT 'Tenant code.',
  `point_account_code` varchar(32) NOT NULL COMMENT 'Point account code.',
  `account_code` varchar(32) NOT NULL COMMENT 'account code. (UserCode or OrgCode)',
  `account_type` tinyint(1) NOT NULL COMMENT 'Point account type. (1-User 2-Organization)',
  `account_desc` varchar(256) DEFAULT NULL COMMENT 'Point account description.',
  `account_balance` int(11) NOT NULL DEFAULT '0' COMMENT 'Account balance',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT 'Point account status.(0-Inactive 1-Active)',
  `ext_params` json DEFAULT NULL COMMENT 'Extends parameters. (JSON String)',
  `create_user` varchar(32) DEFAULT NULL COMMENT 'Create user code',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Create time.',
  `update_user` varchar(32) DEFAULT NULL COMMENT 'Lastest update user.',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Lastest update time.',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uidx_pointaccount_tenant_account` (`tenant_code`,`account_type`,`account_code`),
  UNIQUE KEY `uidx_point_account_code` (`tenant_code`,`point_account_code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='Point account information table.'


CREATE TABLE `point_campaign` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT 'Primary key.',
  `domain_code` VARCHAR(32) NOT NULL COMMENT 'Domain code.',
  `tenant_code` VARCHAR(32) NOT NULL COMMENT 'Tenant code.',
  `campaign_code` VARCHAR(32) NOT NULL COMMENT 'campaign code',
  `campaign_title` VARCHAR(100) DEFAULT NULL COMMENT 'campaign title',
  `campaign_desc` VARCHAR(256) DEFAULT NULL COMMENT 'campaign desc',
  `sponsor` VARCHAR(100) DEFAULT NULL COMMENT 'Campaingn sponsor information.',
  `begin_date` DATE DEFAULT NULL COMMENT 'Point campaign begin time.',
  `end_date` DATE DEFAULT NULL COMMENT 'Point campaign end time.',
  `total_points` INT(11) NOT NULL DEFAULT '0' COMMENT 'Activity total points.',
  `remaining_points` INT(11) NOT NULL DEFAULT '0' COMMENT 'Activity remaining points.',
  `status` TINYINT(1) NOT NULL DEFAULT '0' COMMENT 'campaignstatus.( 0-inactive 1-active)',
  `create_user` VARCHAR(32) DEFAULT NULL COMMENT 'Create user code',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Create time.',
  `update_user` VARCHAR(32) DEFAULT NULL COMMENT 'Lastest update user.',
  `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Lastest update time.',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uidx_pointcampaign_tenant_campaign` (`tenant_code`,`campaign_code`)
) ENGINE=INNODB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='Point activity information table.'


CREATE TABLE `point_transaction` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'Primary key.',
  `domain_code` varchar(32) NOT NULL COMMENT 'Domain code.',
  `tenant_code` varchar(32) NOT NULL COMMENT 'Tenant code.',
  `transaction_sn` varchar(32) NOT NULL COMMENT 'Transaction serial number.',
  `account_code` varchar(32) NOT NULL COMMENT 'Point account code. (UserCode or OrgCode)',
  `account_type` tinyint(1) NOT NULL COMMENT 'Point account type. (1-User 2-Organization)',
  `campaign_code` varchar(32) NOT NULL COMMENT 'Point campaign code.',
  `transaction_type` tinyint(1) NOT NULL COMMENT 'Point transaction type. (1-Increase points 2-Deduct points)',
  `transaction_remarks` varchar(256) DEFAULT NULL COMMENT 'Point transaction remarks.',
  `transaction_amount` int(11) NOT NULL DEFAULT '0' COMMENT 'Point spent/earned in the transaction.',
  `transaction_date` bigint(20) DEFAULT NULL COMMENT 'Transaction date. (yyyyMMddHHmmss)',
  `refer_transaction_sn` varchar(32) DEFAULT NULL COMMENT 'Refer transaction serial number.',
  `refer_order_number` varchar(32) DEFAULT NULL COMMENT 'Refer order number.',
  `create_user` varchar(32) DEFAULT NULL COMMENT 'Create user code',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Create time.',
  `update_user` varchar(32) DEFAULT NULL COMMENT 'Lastest update user.',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Lastest update time.',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uidx_pointtrans_tenant_transactionsn` (`tenant_code`,`transaction_sn`),
  KEY `idx_pointtrans_tenant_account` (`tenant_code`,`account_type`,`account_code`,`transaction_type`),
  KEY `idx_pointtrans_tenant_campaign` (`tenant_code`,`campaign_code`,`transaction_type`),
  KEY `idx_pointtrans_tenant_transactiontype` (`tenant_code`,`transaction_type`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='Point transaction detail information table.'

