UPDATE `promo_activity` SET priority = 999 WHERE priority = 50;

ALTER TABLE `promo_activity` ADD COLUMN `BACKGROUND_IMAGE` VARCHAR(255) NULL COMMENT '活动图片';
ALTER TABLE `promo_activity_incentive` ADD COLUMN `INCENTIVE_POSTAGE` DECIMAL(19,3) DEFAULT 0.000  NOT NULL COMMENT '运费减免金额' AFTER `INCENTIVE_AMOUNT`;

INSERT  INTO `promo_template`(`ID`,`TEMPLATE_CODE`,`TEMPLATE_NAME`,`TEMPLATE_DESC`,`TAG_CODE`,`LOCALE`,`UPDATE_USER`,`UPDATE_TIME`,`CREATE_USER`,`CREATE_TIME`,`LOGIC_DELETE`) VALUES
(505,'0102020203020415','商品范围满数量邮费减金额','多个商品满数量，邮费减金额','05','zh-CN',NULL,CURRENT_TIMESTAMP(),NULL,CURRENT_TIMESTAMP(),0);

INSERT  INTO `promo_template_function`(`TEMPLATE_ID`,TEMPLATE_CODE,`FUNCTION_TYPE`,`FUNCTION_CODE`,`FUNCTION_NAME`,`UPDATE_USER`,`UPDATE_TIME`,`CREATE_USER`,`CREATE_TIME`,`LOGIC_DELETE`) VALUES
(505,'0102020203020415','01','0102','商品范围',NULL,CURRENT_TIMESTAMP(),NULL,CURRENT_TIMESTAMP(),0),
(505,'0102020203020415','02','0202','满',NULL,CURRENT_TIMESTAMP(),NULL,CURRENT_TIMESTAMP(),0),
(505,'0102020203020415','03','0302','数量',NULL,CURRENT_TIMESTAMP(),NULL,CURRENT_TIMESTAMP(),0),
(505,'0102020203020415','04','0415','邮费减金额',NULL,CURRENT_TIMESTAMP(),NULL,CURRENT_TIMESTAMP(),0);

INSERT  INTO `promo_template`(`ID`,`TEMPLATE_CODE`,`TEMPLATE_NAME`,`TEMPLATE_DESC`,`TAG_CODE`,`LOCALE`,`UPDATE_USER`,`UPDATE_TIME`,`CREATE_USER`,`CREATE_TIME`,`LOGIC_DELETE`) VALUES
(506,'0102020203030415','商品范围满金额邮费减金额','多个商品满金额，邮费减金额','05','zh-CN',NULL,CURRENT_TIMESTAMP(),NULL,CURRENT_TIMESTAMP(),0);

INSERT  INTO `promo_template_function`(`TEMPLATE_ID`,TEMPLATE_CODE,`FUNCTION_TYPE`,`FUNCTION_CODE`,`FUNCTION_NAME`,`UPDATE_USER`,`UPDATE_TIME`,`CREATE_USER`,`CREATE_TIME`,`LOGIC_DELETE`) VALUES
(506,'0102020203030415','01','0102','商品范围',NULL,CURRENT_TIMESTAMP(),NULL,CURRENT_TIMESTAMP(),0),
(506,'0102020203030415','02','0202','满',NULL,CURRENT_TIMESTAMP(),NULL,CURRENT_TIMESTAMP(),0),
(506,'0102020203030415','03','0303','金额',NULL,CURRENT_TIMESTAMP(),NULL,CURRENT_TIMESTAMP(),0),
(506,'0102020203030415','04','0415','邮费减金额',NULL,CURRENT_TIMESTAMP(),NULL,CURRENT_TIMESTAMP(),0);


INSERT INTO `t_masterdata_dd`(`DD_CODE`, `DD_VALUE`, `DD_TEXT`, `DD_TYPE`, `DD_ORDER`, `DD_REMARK`, `TENANT_CODE`, `STATE`, `UPDATE_TIME`, `UPDATE_USER`, `CREATE_TIME`, `CREATE_USER`) VALUES ('PAYMENT_METHOD', 'doku15', 'Doku信用卡支付', NULL, 30, NULL, '100001', 0, '2020-06-24 11:07:47', NULL, '2020-06-24 11:07:13', NULL);
INSERT INTO `t_masterdata_dd`(`DD_CODE`, `DD_VALUE`, `DD_TEXT`, `DD_TYPE`, `DD_ORDER`, `DD_REMARK`, `TENANT_CODE`, `STATE`, `UPDATE_TIME`, `UPDATE_USER`, `CREATE_TIME`, `CREATE_USER`) VALUES ('PAYMENT_METHOD', 'doku53', 'Doku OVO支付', NULL, 40, NULL, '100001', 0, '2020-06-24 11:07:56', NULL, '2020-06-24 11:07:35', NULL);

INSERT INTO `t_masterdata_dd_lang`(`DD_CODE`, `DD_VALUE`, `LANG`, `DD_TEXT`, `TENANT_CODE`, `STATE`, `UPDATE_TIME`, `UPDATE_USER`, `CREATE_TIME`, `CREATE_USER`) VALUES ('PAYMENT_METHOD', 'doku53', 'en-US', 'Doku OVO', '100001', 1, '2020-07-31 14:06:03', NULL, '2020-07-31 14:04:47', NULL);
INSERT INTO `t_masterdata_dd_lang`(`DD_CODE`, `DD_VALUE`, `LANG`, `DD_TEXT`, `TENANT_CODE`, `STATE`, `UPDATE_TIME`, `UPDATE_USER`, `CREATE_TIME`, `CREATE_USER`) VALUES ('PAYMENT_METHOD', 'doku15', 'en-US', 'Doku Credit Card', '100001', 1, '2020-07-31 14:06:39', NULL, '2020-07-31 14:06:39', NULL);