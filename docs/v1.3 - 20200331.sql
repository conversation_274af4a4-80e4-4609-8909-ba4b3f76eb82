insert  into `promo_template`(`ID`,`TEMPLATE_CODE`,`TEMPLATE_NAME`,`TEMPLATE_DESC`,`TAG_CODE`,`LOCALE`,`UPDATE_USER`,`UPDATE_TIME`,`CREATE_USER`,`CREATE_TIME`,`LOGIC_DELETE`) values (1101,'0104020103010401','������Ʒ��ȯ�����','������Ʒ��ȯ����ÿ��ȯ������1����Ʒ','11','zh-cn',NULL,'2020-03-20 06:20:14',NULL,'2020-03-20 06:19:12',0);
insert  into `promo_template`(`ID`,`TEMPLATE_CODE`,`TEMPLATE_NAME`,`TEMPLATE_DESC`,`TAG_CODE`,`LOCALE`,`UPDATE_USER`,`UPDATE_TIME`,`CREATE_USER`,`CREATE_TIME`,`LOGIC_DELETE`) values (1102,'0104020103010402','������Ʒ��ȯ���ۿ�','������Ʒ��ȯ���ۿۣ�ÿ��ȯ������1����Ʒ','11','zh-cn',NULL,'2020-03-20 06:20:10',NULL,'2020-03-20 06:20:10',0);

insert  into `promo_template_function`(`ID`,`TEMPLATE_ID`,`FUNCTION_TYPE`,`FUNCTION_CODE`,`FUNCTION_NAME`,`UPDATE_USER`,`UPDATE_TIME`,`CREATE_USER`,`CREATE_TIME`,`LOGIC_DELETE`) values (161,1101,'01','0104','������Ʒ',NULL,'2020-03-20 06:23:25',NULL,'2020-03-20 06:23:25',0);
insert  into `promo_template_function`(`ID`,`TEMPLATE_ID`,`FUNCTION_TYPE`,`FUNCTION_CODE`,`FUNCTION_NAME`,`UPDATE_USER`,`UPDATE_TIME`,`CREATE_USER`,`CREATE_TIME`,`LOGIC_DELETE`) values (162,1101,'02','0201','��',NULL,'2020-03-20 06:24:13',NULL,'2020-03-20 06:24:13',0);
insert  into `promo_template_function`(`ID`,`TEMPLATE_ID`,`FUNCTION_TYPE`,`FUNCTION_CODE`,`FUNCTION_NAME`,`UPDATE_USER`,`UPDATE_TIME`,`CREATE_USER`,`CREATE_TIME`,`LOGIC_DELETE`) values (163,1101,'03','0301','��',NULL,'2020-03-20 06:25:34',NULL,'2020-03-20 06:25:34',0);
insert  into `promo_template_function`(`ID`,`TEMPLATE_ID`,`FUNCTION_TYPE`,`FUNCTION_CODE`,`FUNCTION_NAME`,`UPDATE_USER`,`UPDATE_TIME`,`CREATE_USER`,`CREATE_TIME`,`LOGIC_DELETE`) values (164,1101,'04','0401','�����',NULL,'2020-03-20 06:28:19',NULL,'2020-03-20 06:27:46',0);
insert  into `promo_template_function`(`ID`,`TEMPLATE_ID`,`FUNCTION_TYPE`,`FUNCTION_CODE`,`FUNCTION_NAME`,`UPDATE_USER`,`UPDATE_TIME`,`CREATE_USER`,`CREATE_TIME`,`LOGIC_DELETE`) values (165,1102,'01','0104','������Ʒ',NULL,'2020-03-20 06:28:13',NULL,'2020-03-20 06:28:13',0);
insert  into `promo_template_function`(`ID`,`TEMPLATE_ID`,`FUNCTION_TYPE`,`FUNCTION_CODE`,`FUNCTION_NAME`,`UPDATE_USER`,`UPDATE_TIME`,`CREATE_USER`,`CREATE_TIME`,`LOGIC_DELETE`) values (166,1102,'02','0201','��',NULL,'2020-03-20 06:28:51',NULL,'2020-03-20 06:28:51',0);
insert  into `promo_template_function`(`ID`,`TEMPLATE_ID`,`FUNCTION_TYPE`,`FUNCTION_CODE`,`FUNCTION_NAME`,`UPDATE_USER`,`UPDATE_TIME`,`CREATE_USER`,`CREATE_TIME`,`LOGIC_DELETE`) values (167,1102,'03','0301','��',NULL,'2020-03-20 06:29:11',NULL,'2020-03-20 06:29:11',0);
insert  into `promo_template_function`(`ID`,`TEMPLATE_ID`,`FUNCTION_TYPE`,`FUNCTION_CODE`,`FUNCTION_NAME`,`UPDATE_USER`,`UPDATE_TIME`,`CREATE_USER`,`CREATE_TIME`,`LOGIC_DELETE`) values (168,1102,'04','0402','���ۿ�',NULL,'2020-03-20 06:29:34',NULL,'2020-03-20 06:29:34',0);

