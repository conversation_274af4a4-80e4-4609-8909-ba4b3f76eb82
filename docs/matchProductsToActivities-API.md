# 商品活动匹配接口文档

## 接口概述

**接口名称**: 商品活动匹配  
**接口路径**: `/promotion/activity/matchProductsToActivities`  
**请求方法**: POST  
**功能描述**: 根据商品列表和活动列表进行匹配，返回活动及其匹配的商品

## 业务场景

此接口适用于以下场景：
1. **商品详情页**：展示某个商品可参与的所有活动
2. **活动页面**：展示指定活动下的所有参与商品
3. **营销分析**：分析商品与活动的匹配关系
4. **批量处理**：批量检查多个商品的活动参与情况

## 与现有接口的区别

| 接口 | 输入 | 输出 | 用途 |
|------|------|------|------|
| `calcShoppingCart` | 购物车商品 | 计算后的优惠结果 | 购物车结算 |
| `queryActivityByShoppingCartProduct` | 购物车商品 | 商品对应的活动列表 | 查询商品活动 |
| **`matchProductsToActivities`** | **商品列表 + 活动列表** | **活动列表（含匹配商品）** | **活动商品匹配** |

## 请求参数

### 基础参数
```json
{
  "domainCode": "DC0001",        // 必填：域编码
  "tenantCode": "100000",        // 必填：租户编码
  "memberCode": "10230703002980", // 可选：会员编码
  "language": "zh-CN",           // 可选：语言
  "orgCodes": ["100001"],        // 可选：组织编码列表
  "qualifications": {            // 可选：会员资格
    "memberLevel": ["VIP", "GOLD"]
  },
  "promotionTime": "20250123120000", // 可选：促销时间
  "products": [...],             // 必填：商品列表
  "activityCodes": [...]         // 可选：活动编码列表（为空则匹配所有活动）
}
```

### 商品信息
```json
{
  "productCode": "SP230704134530",  // 必填：商品编码
  "skuCode": "SK23070400009045",    // 必填：SKU编码
  "productPrice": 500.00,           // 可选：商品价格
  "quantity": 2                     // 可选：数量，默认为1
}
```

## 响应结果

```json
{
  "code": "200",
  "message": "success",
  "data": [
    {
      "activityCode": "AC230704001",
      "activityName": "满减活动",
      "activityType": "01",
      "activityItems": [              // 参与该活动的商品列表
        {
          "productCode": "SP230704134530",
          "skuCode": "SK23070400009045"
        },
        {
          "productCode": "SP230704134531", 
          "skuCode": "SK23070400009046"
        }
      ],
      "funcParams": [...],            // 活动参数
      "giveaways": [...],             // 赠品信息
      "limitedList": [...],           // 限制条件
      "qualifications": [...]         // 资格要求
    }
  ]
}
```

## 核心逻辑

1. **构建购物车对象**：将输入的商品列表转换为购物车格式
2. **活动缓存过滤**：根据活动编码列表过滤活动缓存（如果指定）
3. **商品活动匹配**：复用现有的 `queryActivityByShoppingCartProduct` 逻辑
4. **结果重组**：将结果从"商品->活动"转换为"活动->商品"的结构

## 使用示例

### 示例1：指定活动列表匹配
```bash
curl -X POST "http://localhost:8080/promotion/activity/matchProductsToActivities" \
  -H "Content-Type: application/json" \
  -d '{
    "domainCode": "DC0001",
    "tenantCode": "100000",
    "products": [
      {
        "productCode": "SP001",
        "skuCode": "SK001",
        "productPrice": 100.00
      }
    ],
    "activityCodes": ["AC001", "AC002"]
  }'
```

### 示例2：匹配所有活动
```bash
curl -X POST "http://localhost:8080/promotion/activity/matchProductsToActivities" \
  -H "Content-Type: application/json" \
  -d '{
    "domainCode": "DC0001", 
    "tenantCode": "100000",
    "products": [
      {
        "productCode": "SP001",
        "skuCode": "SK001"
      }
    ]
  }'
```

## 注意事项

1. **性能考虑**：商品数量较多时，建议分批处理
2. **活动过滤**：如果不指定 `activityCodes`，会匹配所有有效活动
3. **商品验证**：每个商品的 `productCode` 和 `skuCode` 都是必填的
4. **结果过滤**：没有匹配到任何活动的商品会被忽略
5. **缓存利用**：接口会充分利用现有的活动缓存机制

## 错误码

| 错误码 | 描述 | 解决方案 |
|--------|------|----------|
| PARAM_EMPTY | 必填参数为空 | 检查 domainCode、tenantCode、products 等必填字段 |
| PARAM_SPECIFICATION_ERROR | 参数格式错误 | 检查商品信息格式是否正确 |

## 扩展性

此接口设计具有良好的扩展性：
1. 可以轻松添加更多的过滤条件
2. 可以支持更复杂的商品信息
3. 可以扩展返回更多的活动详情
4. 可以支持异步处理大批量数据
