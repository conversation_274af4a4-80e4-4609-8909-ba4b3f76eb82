CREATE TABLE `promo_activity_operation_log` (
  `ID` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `TENANT_CODE` VARCHAR(32) NOT NULL COMMENT '系统租户编码，系统拥有者编码',
  `ACTIVITY_CODE` VARCHAR(40) NOT NULL COMMENT '活动编码（纯数字）',
  `OPERATION_CODE` VARCHAR(32) NOT NULL COMMENT '操作编码',
  `OPERATION_TYPE` CHAR(2) NOT NULL COMMENT 'operation type: 01-creation 02-edition 03-activation 04-termination 05-completion 06-extension',
  `UPDATE_USER` VARCHAR(32) DEFAULT NULL COMMENT '更新者',
  `UPDATE_TIME` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `CREATE_USER` VARCHAR(32) DEFAULT NULL COMMENT '创建者',
  `CREATE_TIME` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `LOGIC_DELETE` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除：0-未删除 1-已删除',
  PRIMARY KEY (`ID`)
) ENGINE=INNODB  DEFAULT CHARSET=utf8 COMMENT='活动操作日志表';

CREATE TABLE `promo_activity_operation_log_content` (
  `ID` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `TENANT_CODE` VARCHAR(32) NOT NULL COMMENT '系统租户编码，系统拥有者编码',
  `ACTIVITY_CODE` VARCHAR(40) NOT NULL COMMENT '活动编码（纯数字）',
  `OPERATION_CODE` VARCHAR(32) NOT NULL COMMENT '操作编码',
  `CONTENT` TEXT NOT NULL COMMENT '日志内容',
  `UPDATE_USER` VARCHAR(32) DEFAULT NULL COMMENT '更新者',
  `UPDATE_TIME` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `CREATE_USER` VARCHAR(32) DEFAULT NULL COMMENT '创建者',
  `CREATE_TIME` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `LOGIC_DELETE` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除：0-未删除 1-已删除',
  PRIMARY KEY (`ID`)
) ENGINE=INNODB  DEFAULT CHARSET=utf8 COMMENT='活动操作日志内容表';

INSERT  INTO `promo_template`(`ID`,`TEMPLATE_CODE`,`TEMPLATE_NAME`,`TEMPLATE_DESC`,`TAG_CODE`,`LOCALE`,`UPDATE_USER`,`UPDATE_TIME`,`CREATE_USER`,`CREATE_TIME`,`LOGIC_DELETE`) VALUES
(105,'0101020603020402','单品第数量打折扣','多个商品第几件，打折扣','01','zh-CN',NULL,CURRENT_TIMESTAMP(),NULL,CURRENT_TIMESTAMP(),0);

INSERT  INTO `promo_template_function`(`TEMPLATE_ID`,TEMPLATE_CODE,`FUNCTION_TYPE`,`FUNCTION_CODE`,`FUNCTION_NAME`,`UPDATE_USER`,`UPDATE_TIME`,`CREATE_USER`,`CREATE_TIME`,`LOGIC_DELETE`) VALUES
(105,'0101020603020402','01','0101','单品',NULL,CURRENT_TIMESTAMP(),NULL,CURRENT_TIMESTAMP(),0),
(105,'0101020603020402','02','0206','第',NULL,CURRENT_TIMESTAMP(),NULL,CURRENT_TIMESTAMP(),0),
(105,'0101020603020402','03','0302','数量',NULL,CURRENT_TIMESTAMP(),NULL,CURRENT_TIMESTAMP(),0),
(105,'0101020603020402','04','0402','打折扣',NULL,CURRENT_TIMESTAMP(),NULL,CURRENT_TIMESTAMP(),0);

INSERT  INTO `promo_template`(`ID`,`TEMPLATE_CODE`,`TEMPLATE_NAME`,`TEMPLATE_DESC`,`TAG_CODE`,`LOCALE`,`UPDATE_USER`,`UPDATE_TIME`,`CREATE_USER`,`CREATE_TIME`,`LOGIC_DELETE`) VALUES
(106,'0101020603020401','单品第数量减金额','多个商品第几件，减金额','01','zh-CN',NULL,CURRENT_TIMESTAMP(),NULL,CURRENT_TIMESTAMP(),0);

INSERT  INTO `promo_template_function`(`TEMPLATE_ID`,TEMPLATE_CODE,`FUNCTION_TYPE`,`FUNCTION_CODE`,`FUNCTION_NAME`,`UPDATE_USER`,`UPDATE_TIME`,`CREATE_USER`,`CREATE_TIME`,`LOGIC_DELETE`) VALUES
(106,'0101020603020401','01','0101','单品',NULL,CURRENT_TIMESTAMP(),NULL,CURRENT_TIMESTAMP(),0),
(106,'0101020603020401','02','0206','第',NULL,CURRENT_TIMESTAMP(),NULL,CURRENT_TIMESTAMP(),0),
(106,'0101020603020401','03','0302','数量',NULL,CURRENT_TIMESTAMP(),NULL,CURRENT_TIMESTAMP(),0),
(106,'0101020603020401','04','0401','减金额',NULL,CURRENT_TIMESTAMP(),NULL,CURRENT_TIMESTAMP(),0);

UPDATE promo_template SET TEMPLATE_NAME = '商品范围每第数量打折扣', TEMPLATE_DESC = '多个商品多次满足第几件，打多次折扣' WHERE TEMPLATE_CODE = '0102020403020402';
UPDATE promo_template_function SET FUNCTION_NAME = '每第' WHERE FUNCTION_CODE = '0204';

ALTER TABLE promo_incentive_limited
  CHANGE `LIMITATION_CODE` `LIMITATION_CODE` CHAR(2) CHARSET utf8 COLLATE utf8_general_ci NOT NULL   COMMENT '限制条件编码：01-活动限制总次数 02-活动限制总金额 03-活动限制单日总次数 04-活动限制单日总金额 05-每人限制总次数 06-每人限制总金额；07-每人限制每天总次数；08-每人限制每天总金额; 09-活动限制单日总订单数';

INSERT INTO `idm_resource`(`domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`) VALUES ( 'SYSTEM_DEFAULT', 'MEMBER', 'root', 'Export', 'EXPORT MEMBER', 2, 1, 2, 1, NULL, '2020-02-19 20:17:57', NULL, '2020-06-03 19:21:25', 0, NULL);
INSERT INTO `idm_resource`(`domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`) VALUES ( 'SYSTEM_DEFAULT', 'MEMBER', 'root', 'grantExportFileAccessUrl', 'grantExportFileAccessUrl', 3, 1, 1, 1, NULL, '2020-02-19 20:17:57', NULL, '2020-02-19 20:17:57', 0, NULL);
INSERT INTO `idm_resource`(`domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`) VALUES ( 'SYSTEM_DEFAULT', 'MEMBER', 'root', 'queryExportFile', 'queryExportFile', 3, 1, 1, 1, NULL, '2020-02-19 20:17:57', NULL, '2020-02-19 20:17:57', 0, NULL);
INSERT INTO `idm_resource`(`domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`) VALUES ( 'SYSTEM_DEFAULT', 'MEMBER', 'root', 'exportMember', 'exportMember', 3, 1, 1, 1, NULL, '2020-02-19 20:17:57', NULL, '2020-02-19 20:17:57', 0, NULL);
INSERT INTO `idm_resource`(`domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`) VALUES ( 'SYSTEM_DEFAULT', 'PROMOTION', 'root', 'activityExtend', 'activityExtend', 3, 1, 1, 1, NULL, '2020-02-19 20:17:57', NULL, '2020-02-19 20:17:57', 0, NULL);
INSERT INTO `idm_resource`(`domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`) VALUES ( 'SYSTEM_DEFAULT', 'OPERATION', 'root', 'getMemberDashBoardDatetime', 'getMemberDashBoardDatetime', 3, 1, 1, 1, NULL, '2020-02-19 20:17:57', NULL, '2020-06-01 16:01:31', 0, NULL);
INSERT INTO `idm_resource`(`domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`) VALUES ( 'SYSTEM_DEFAULT', 'OPERATION', 'root', 'getMemberDashBoardTotal', 'getMemberDashBoardTotal', 3, 1, 1, 1, NULL, '2020-02-19 20:17:57', NULL, '2020-06-01 16:01:43', 0, NULL);
INSERT INTO `idm_resource`(`domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`) VALUES ( 'SYSTEM_DEFAULT', 'MEMBER', 'root', 'queryMemberWishlist', 'queryMemberWishlist', 3, 1, 1, 1, NULL, '2020-02-19 20:17:57', NULL, '2020-02-19 20:17:57', 0, NULL);


INSERT INTO `idm_resource_detail`(`domain_code`, `app_code`, `resource_code`, `resource_url`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'MEMBER', 'grantExportFileAccessUrl', 'grantExportFileAccessUrl', NULL, '2020-02-19 20:18:53', NULL, '2020-02-19 20:18:53', 0);
INSERT INTO `idm_resource_detail`(`domain_code`, `app_code`, `resource_code`, `resource_url`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'MEMBER', 'queryExportFile', 'queryExportFile', NULL, '2020-02-19 20:18:53', NULL, '2020-02-19 20:18:53', 0);
INSERT INTO `idm_resource_detail`(`domain_code`, `app_code`, `resource_code`, `resource_url`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'MEMBER', 'exportMember', 'exportMember', NULL, '2020-02-19 20:18:53', NULL, '2020-02-19 20:18:53', 0);
INSERT INTO `idm_resource_detail`(`domain_code`, `app_code`, `resource_code`, `resource_url`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'PROMOTION', 'activityExtend', 'activityExtend', NULL, '2020-02-19 20:18:53', NULL, '2020-02-19 20:18:53', 0);
INSERT INTO `idm_resource_detail`(`domain_code`, `app_code`, `resource_code`, `resource_url`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'OPERATION', 'getMemberDashBoardDatetime', 'getMemberDashBoardDatetime', NULL, '2020-02-19 20:18:53', NULL, '2020-06-01 16:01:57', 0);
INSERT INTO `idm_resource_detail`(`domain_code`, `app_code`, `resource_code`, `resource_url`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'OPERATION', 'getMemberDashBoardTotal', 'getMemberDashBoardTotal', NULL, '2020-02-19 20:18:53', NULL, '2020-06-01 16:02:00', 0);
INSERT INTO `idm_resource_detail`(`domain_code`, `app_code`, `resource_code`, `resource_url`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'MEMBER', 'queryMemberWishlist', 'queryMemberWishlist', NULL, '2020-02-19 20:18:53', NULL, '2020-02-19 20:18:53', 0);

INSERT INTO `idm_menu`(`domain_code`, `app_code`, `menu_parent`, `menu_code`, `resource_code`, `menu_name`, `menu_desc`, `menu_icon`, `display_order`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `menu_name_lang`) VALUES ('SYSTEM_DEFAULT', 'MEMBER', '0', 'ME001695', 'Export', 'Export', '', 'export_3', 50, '/export', 1, NULL, '2020-06-03 18:55:44', NULL, '2020-06-03 18:55:44', 0, NULL);
