-- SQL for 2020.05.14: 
ALTER TABLE `promo_activity` DROP COLUMN `PRODUCT_TYPE`;

-- SQL for 2020.05.28
ALTER TABLE `promo_activity` ADD COLUMN `ITEM_SCOPE_TYPE` TINYINT(1) NOT NULL DEFAULT 1 COMMENT 'Product item scope type: 1-All scope 2-By spu in scope' AFTER `PRODUCT_SELECTION_TYPE`;

ALTER TABLE `promo_template_function` ADD COLUMN `TEMPLATE_CODE` VARCHAR(16) NULL COMMENT 'Template code.' AFTER TEMPLATE_ID;
ALTER TABLE `promo_template_function` MODIFY COLUMN `TEMPLATE_ID` BIGINT(20) NULL COMMENT '促销模板ID';
UPDATE `promo_template_function` tf, `promo_template` t SET tf.`TEMPLATE_CODE`=t.`TEMPLATE_CODE` WHERE tf.`TEMPLATE_ID`=t.`ID`;
CREATE UNIQUE INDEX uidx_templatefunction_template_function ON `promo_template_function`(`TEMPLATE_CODE`,`FUNCTION_CODE`);
DROP INDEX index_function ON `promo_template_function`;

ALTER TABLE `promo_activity_func_rank` ADD COLUMN `TEMPLATE_CODE` VARCHAR(16) NULL COMMENT 'Template code.' AFTER TEMPLATE_ID;
ALTER TABLE `promo_activity_func_rank` MODIFY COLUMN `TEMPLATE_ID` BIGINT(20) NULL COMMENT '促销模板ID';
UPDATE `promo_activity_func_rank` afr, `promo_template` t SET afr.`TEMPLATE_CODE`=t.`TEMPLATE_CODE` WHERE afr.`TEMPLATE_ID`=t.`ID`;
DROP INDEX uidx_activityfuncrank_activity_template_param ON `promo_activity_func_rank`;
CREATE UNIQUE INDEX uidx_activityfuncrank_activity_template_param ON `promo_activity_func_rank`(`ACTIVITY_CODE`,`TEMPLATE_CODE`,`RANK_PARAM`);


