ALTER TABLE `promo_activity_store` CHANGE `STORE_NAME` `STORE_NAME` VARCHAR(100) NOT NULL   COMMENT '店铺名称';

ALTER TABLE `promo_activity` ADD COLUMN `DOMAIN_CODE` VARCHAR(32) DEFAULT 'DEFAULT'  NOT NULL COMMENT '系统域编码，系统拥有者编码' AFTER `ID`;
ALTER TABLE `promo_activity` ADD COLUMN `PRODUCT_CONDITION` CHAR(2) DEFAULT '00' NOT NULL COMMENT '商品条件：00-None；01-买A优惠B，B单价要低于A' AFTER `BACKGROUND_IMAGE`;

ALTER TABLE `marketing`
    CHANGE `ACTIVITY_TYPE` `ACTIVITY_TYPE` CHAR(3) CHARSET utf8 COLLATE utf8_general_ci NOT NULL   COMMENT '活动类型: 03-LUCKY DRAW 04-FLASH SALE',
    ADD COLUMN `OPS_TYPE` CHAR(3) NULL   COMMENT '301-大装盘 302-砸金蛋 401-flash sale' AFTER `ACTIVITY_TYPE`,
    ADD COLUMN `BACKGROUND_IMAGE` VARCHAR(255) NULL   COMMENT '活动图片' AFTER `SPONSORS`,
    ADD COLUMN `RIBBON_IMAGE` VARCHAR(255) NULL   COMMENT 'Ribbon image' AFTER `BACKGROUND_IMAGE`,
    ADD COLUMN `RIBBON_POSITION` VARCHAR(20) NULL   COMMENT 'Ribbon image position' AFTER `RIBBON_IMAGE`,
    ADD COLUMN `RIBBON_TEXT` VARCHAR(255) NULL   COMMENT 'Ribbon text' AFTER `RIBBON_POSITION`;

CREATE TABLE `flash_sale_qualification` (
`ID` bigint(20) NOT NULL AUTO_INCREMENT,
`DOMAIN_CODE` varchar(32) NOT NULL COMMENT '系统域编码，系统拥有者编码',
`TENANT_CODE` varchar(32) NOT NULL COMMENT '系统租户编码，系统拥有者编码',
`ORG_CODE` varchar(32) NOT NULL COMMENT '系统ORG编码',
`ACTIVITY_CODE` varchar(40) NOT NULL COMMENT '活动编码',
`QUALIFICATION_CODE` varchar(32) NOT NULL COMMENT '资格编码',
`QUALIFICATION_VALUE` varchar(100) NOT NULL COMMENT '资格值',
`RELATION` char(2) NOT NULL DEFAULT '01' COMMENT '资格关系，01-OR 02-AND',
`UPDATE_USER` varchar(32) DEFAULT NULL COMMENT '更新者',
`UPDATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
`CREATE_USER` varchar(32) DEFAULT NULL COMMENT '创建者',
`CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
`LOGIC_DELETE` tinyint(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除：0-未删除 1-已删除',
PRIMARY KEY (`ID`),
UNIQUE KEY `UIDX_FLASH_SALE_QUALIFICATION` (`ACTIVITY_CODE`,`QUALIFICATION_CODE`,`QUALIFICATION_VALUE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='秒杀活动参与资格表';

CREATE TABLE `flash_sale_store` (
`ID` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
`DOMAIN_CODE` varchar(32) NOT NULL COMMENT '系统域编码，系统拥有者编码',
`TENANT_CODE` varchar(32) NOT NULL COMMENT '系统租户编码，系统拥有者编码',
`ACTIVITY_CODE` varchar(40) NOT NULL COMMENT 'ACTIVITY CODE',
`ORG_CODE` varchar(32) NOT NULL COMMENT 'Store organization code.',
`STORE_NAME` varchar(50) NOT NULL COMMENT '店铺名称',
`UPDATE_USER` bigint(20) DEFAULT NULL COMMENT '更新者',
`UPDATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
`CREATE_USER` bigint(20) DEFAULT NULL COMMENT '创建者',
`CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
`LOGIC_DELETE` tinyint(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除：0-未删除 1-已删除',
PRIMARY KEY (`ID`),
UNIQUE KEY `uidx_flash_sale_store` (`ACTIVITY_CODE`,`ORG_CODE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='秒杀活动店铺表';

CREATE TABLE `flash_sale_product` (
`ID` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
`DOMAIN_CODE` varchar(32) NOT NULL COMMENT '系统域编码，系统拥有者编码',
`TENANT_CODE` varchar(32) NOT NULL COMMENT '系统租户编码，系统拥有者编码',
`ORG_CODE` varchar(32) NOT NULL COMMENT '系统ORG编码',
`ACTIVITY_CODE` varchar(40) NOT NULL COMMENT '活动编码',
`SKU_CODE` varchar(32) NOT NULL COMMENT 'SKU CODE',
`SKU_NAME` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '商品名称',
`LIST_PRICE` DECIMAL(12,3) NOT NULL COMMENT '商品吊牌价',
`SALE_PRICE` DECIMAL(12,3) NOT NULL COMMENT '商品销售价',
`FLASH_PRICE` DECIMAL(12,3) NOT NULL COMMENT '商品秒杀价',
`SKU_QUOTA` int(11) NOT NULL DEFAULT '1' COMMENT '商品配额',
`SKU_INVENTORY` int(11) NOT NULL COMMENT '商品剩余库存',
`MAX_PER_USER` int(11) NOT NULL COMMENT '商品每人限制秒杀数量',
`UPDATE_USER` varchar(32) DEFAULT NULL COMMENT '更新者',
`UPDATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
`CREATE_USER` varchar(32) DEFAULT NULL COMMENT '创建者',
`CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
`LOGIC_DELETE` tinyint(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除：0-未删除 1-已删除',
PRIMARY KEY (`ID`),
UNIQUE KEY `uidx_flash_sale_prize` (`ACTIVITY_CODE`, SKU_CODE)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='秒杀活动商品表';


CREATE TABLE `flash_sale_order` (
`ID` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
`DOMAIN_CODE` varchar(32) NOT NULL COMMENT '系统域编码，系统拥有者编码',
`TENANT_CODE` varchar(32) NOT NULL COMMENT '系统租户编码，系统拥有者编码',
`ORG_CODE` varchar(32) NOT NULL COMMENT '系统ORG编码',
`ACTIVITY_CODE` varchar(40) NOT NULL COMMENT '活动编码',
`ORDER_ID` varchar(32) NOT NULL COMMENT '用户编码',
`MEMBER_CODE` varchar(32) NOT NULL COMMENT '用户编码',
`SKU_CODE` varchar(32) NOT NULL COMMENT 'SKU CODE',
`SKU_QUALITY` int(11) NOT NULL COMMENT 'SKU QUALITY',
`PROMO_AMOUNT` DECIMAL(12,3) NOT NULL COMMENT '优惠金额',
`ORDER_STATUS` char(2) NOT NULL COMMENT '订单状态，01-未付款 02-已付款 03-已取消',
`UPDATE_USER` varchar(32) DEFAULT NULL COMMENT '更新者',
`UPDATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
`CREATE_USER` varchar(32) DEFAULT NULL COMMENT '创建者',
`CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
`LOGIC_DELETE` tinyint(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除：0-未删除 1-已删除',
PRIMARY KEY (`ID`),
UNIQUE KEY `uidx_flash_sale_record` (`ORDER_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='秒杀活动订单表';


INSERT INTO `idm_resource`(`domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`) VALUES ( 'SYSTEM_DEFAULT', 'PROMOTION', 'root', 'getFlashSale', 'getFlashSale', 3, 1, 1, 1, NULL, '2020-02-19 20:17:57', NULL, '2020-02-19 20:17:57', 0, NULL);
INSERT INTO `idm_resource`(`domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`) VALUES ( 'SYSTEM_DEFAULT', 'PROMOTION', 'root', 'createFlashSale', 'createFlashSale', 3, 1, 1, 1, NULL, '2020-02-19 20:17:57', NULL, '2020-02-19 20:17:57', 0, NULL);
INSERT INTO `idm_resource`(`domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`) VALUES ( 'SYSTEM_DEFAULT', 'PROMOTION', 'root', 'updateFlashSale', 'updateFlashSale', 3, 1, 1, 1, NULL, '2020-02-19 20:17:57', NULL, '2020-02-19 20:17:57', 0, NULL);
INSERT INTO `idm_resource`(`domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`) VALUES ( 'SYSTEM_DEFAULT', 'PROMOTION', 'root', 'queryImportFile', 'queryImportFile', 3, 1, 1, 1, NULL, '2020-02-19 20:17:57', NULL, '2020-02-19 20:17:57', 0, NULL);
INSERT INTO `idm_resource`(`domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`) VALUES ( 'SYSTEM_DEFAULT', 'PROMOTION', 'root', 'createImportFile', 'createImportFile', 3, 1, 1, 1, NULL, '2020-02-19 20:17:57', NULL, '2020-02-19 20:17:57', 0, NULL);
INSERT INTO `idm_resource`(`domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`) VALUES ( 'SYSTEM_DEFAULT', 'PROMOTION', 'root', 'queryImportFileDetail', 'queryImportFileDetail', 3, 1, 1, 1, NULL, '2020-02-19 20:17:57', NULL, '2020-02-19 20:17:57', 0, NULL);
INSERT INTO `idm_resource_detail`(`domain_code`, `app_code`, `resource_code`, `resource_url`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'PROMOTION', 'getFlashSale', 'getFlashSale', NULL, '2020-02-19 20:18:53', NULL, '2020-02-19 20:18:53', 0);
INSERT INTO `idm_resource_detail`(`domain_code`, `app_code`, `resource_code`, `resource_url`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'PROMOTION', 'createFlashSale', 'createFlashSale', NULL, '2020-02-19 20:18:53', NULL, '2020-02-19 20:18:53', 0);
INSERT INTO `idm_resource_detail`(`domain_code`, `app_code`, `resource_code`, `resource_url`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'PROMOTION', 'updateFlashSale', 'updateFlashSale', NULL, '2020-02-19 20:18:53', NULL, '2020-02-19 20:18:53', 0);
INSERT INTO `idm_resource_detail`(`domain_code`, `app_code`, `resource_code`, `resource_url`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'PROMOTION', 'queryImportFile', 'queryImportFile', NULL, '2020-02-19 20:18:53', NULL, '2020-02-19 20:18:53', 0);
INSERT INTO `idm_resource_detail`(`domain_code`, `app_code`, `resource_code`, `resource_url`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'PROMOTION', 'createImportFile', 'createImportFile', NULL, '2020-02-19 20:18:53', NULL, '2020-02-19 20:18:53', 0);
INSERT INTO `idm_resource_detail`(`domain_code`, `app_code`, `resource_code`, `resource_url`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'PROMOTION', 'queryImportFileDetail', 'queryImportFileDetail', NULL, '2020-02-19 20:18:53', NULL, '2020-02-19 20:18:53', 0);
