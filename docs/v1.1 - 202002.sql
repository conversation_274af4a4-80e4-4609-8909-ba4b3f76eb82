-- -------------------------------------------------------------------------------------------------
-- -------------------------------------------------------------------------------------------------
-- promo_activity
ALTER TABLE promo_activity ADD COLUMN TEMPLATE_CODE VARCHAR(40) NOT NULL COMMENT 'Promotion template code.' AFTER TEMPLATE_ID;
UPDATE promo_activity t, promo_template a SET t.TEMPLATE_CODE=a.TEMPLATE_CODE WHERE a.ID=t.TEMPLATE_ID;

-- promo_activity_channel
ALTER TABLE promo_activity_channel ADD COLUMN ACTIVITY_CODE VARCHAR(40) NOT NULL COMMENT 'Activity code.' AFTER ACTIVITY_ID;
UPDATE promo_activity_channel t, promo_activity a SET t.ACTIVITY_CODE=a.ACTIVITY_CODE WHERE a.ID=t.ACTIVITY_ID;

CREATE UNIQUE INDEX uidx_activitychannel_activity_channel ON promo_activity_channel(ACTIVITY_CODE,CHAN_CODE);

-- promo_activity_func_rank
ALTER TABLE promo_activity_func_rank ADD COLUMN ACTIVITY_CODE VARCHAR(40) NOT NULL COMMENT 'Activity code.' AFTER ACTIVITY_ID;
UPDATE promo_activity_func_rank t, promo_activity a SET t.ACTIVITY_CODE=a.ACTIVITY_CODE WHERE a.ID=t.ACTIVITY_ID;

CREATE UNIQUE INDEX uidx_activityfuncrank_activity_template_param ON promo_activity_func_rank(ACTIVITY_CODE,TEMPLATE_ID,RANK_PARAM);

-- promo_activity_gift
ALTER TABLE promo_activity_gift ADD COLUMN ACTIVITY_CODE VARCHAR(40) NOT NULL COMMENT 'Activity code.' AFTER ACTIVITY_ID;
UPDATE promo_activity_gift t, promo_activity a SET t.ACTIVITY_CODE=a.ACTIVITY_CODE WHERE a.ID=t.ACTIVITY_ID;

CREATE UNIQUE INDEX uidx_activitygift_activity_sku ON promo_activity_gift(ACTIVITY_CODE,SKU_CODE);

-- promo_activity_gift_Coupon
ALTER TABLE promo_activity_gift_Coupon ADD COLUMN ACTIVITY_CODE VARCHAR(40) NOT NULL COMMENT 'Activity code.' AFTER ACTIVITY_ID;
UPDATE promo_activity_gift_Coupon t, promo_activity a SET t.ACTIVITY_CODE=a.ACTIVITY_CODE WHERE a.ID=t.ACTIVITY_ID;

-- promo_activity_incentive
ALTER TABLE promo_activity_incentive ADD COLUMN ACTIVITY_CODE VARCHAR(40) NOT NULL COMMENT 'Activity code.' AFTER ACTIVITY_ID;
UPDATE promo_activity_incentive t, promo_activity a SET t.ACTIVITY_CODE=a.ACTIVITY_CODE WHERE a.ID=t.ACTIVITY_ID;

CREATE UNIQUE INDEX uidx_activityincentive_activity_order_incentive ON promo_activity_incentive(ACTIVITY_CODE,PROMO_ORDER_ID,INCENTIVE_TYPE);

-- promo_activity_language
DROP INDEX uniq_promo_activity_language ON promo_activity_language;
CREATE UNIQUE INDEX uidx_activitylanguage_activity_lang ON promo_activity_language(ACTIVITY_CODE,LANG);

-- promo_activity_member
ALTER TABLE promo_activity_member ADD COLUMN ACTIVITY_CODE VARCHAR(40) NOT NULL COMMENT 'Activity code.' AFTER ACTIVITY_ID;
UPDATE promo_activity_member t, promo_activity a SET t.ACTIVITY_CODE=a.ACTIVITY_CODE WHERE a.ID=t.ACTIVITY_ID;

-- promo_activity_member_label
DROP INDEX uniq_activity_member_label ON promo_activity_member_label;

CREATE UNIQUE INDEX uidx_activitymemberlabel_activity_memberlabel ON promo_activity_member_label(ACTIVITY_CODE,MEMBER_LABEL_CODE);

-- promo_activity_product_detail
ALTER TABLE promo_activity_product_detail ADD COLUMN ACTIVITY_CODE VARCHAR(40) NOT NULL COMMENT 'Activity code.' AFTER ACTIVITY_ID;
UPDATE promo_activity_product_detail t, promo_activity a SET t.ACTIVITY_CODE=a.ACTIVITY_CODE WHERE a.ID=t.ACTIVITY_ID;

CREATE UNIQUE INDEX uidx_activityproductdetail_activity_product_sku ON promo_activity_product_detail(ACTIVITY_CODE, PRODUCT_CODE, SKU_CODE);

-- promo_activity_store
ALTER TABLE promo_activity_store ADD COLUMN ACTIVITY_CODE VARCHAR(40) NOT NULL COMMENT 'Activity code.' AFTER ACTIVITY_ID;
UPDATE promo_activity_store t, promo_activity a SET t.ACTIVITY_CODE=a.ACTIVITY_CODE WHERE a.ID=t.ACTIVITY_ID;

CREATE UNIQUE INDEX uidx_activitystore_activity_store_channel ON promo_activity_store(ACTIVITY_CODE,ORG_CODE,CHANNEL_CODE);

-- promo_coupon_activity
ALTER TABLE promo_coupon_activity ADD COLUMN ACTIVITY_CODE VARCHAR(40) NOT NULL COMMENT 'Activity code.' AFTER ACTIVITY_ID;
UPDATE promo_coupon_activity t, promo_activity a SET t.ACTIVITY_CODE=a.ACTIVITY_CODE WHERE a.ID=t.ACTIVITY_ID;

CREATE UNIQUE INDEX uidx_couponactivity_activity ON promo_coupon_activity(ACTIVITY_CODE);

-- promo_coupon_code_user
ALTER TABLE promo_coupon_code_user ADD COLUMN ACTIVITY_CODE VARCHAR(40) NOT NULL COMMENT 'Activity code.' AFTER ACTIVITY_ID;
ALTER TABLE promo_coupon_code_user ADD COLUMN RELEASE_CODE VARCHAR(40) NOT NULL COMMENT 'Coupon release code.' AFTER RELEASE_ID;
UPDATE promo_coupon_code_user t, promo_activity a SET t.ACTIVITY_CODE=a.ACTIVITY_CODE WHERE a.ID=t.ACTIVITY_ID;
UPDATE promo_coupon_code_user t, promo_coupon_release a SET t.RELEASE_CODE=a.RELEASE_CODE WHERE a.ID=t.RELEASE_ID;

CREATE INDEX idx_couponcodeuser_tenant_activity_release ON promo_coupon_code_user(TENANT_CODE, ACTIVITY_CODE, RELEASE_CODE);

-- promo_coupon_inner_code
ALTER TABLE promo_coupon_inner_code ADD COLUMN ACTIVITY_CODE VARCHAR(40) NOT NULL COMMENT 'Activity code.' AFTER ACTIVITY_ID;
ALTER TABLE promo_coupon_inner_code ADD COLUMN RELEASE_CODE VARCHAR(40) NOT NULL COMMENT 'Coupon release code.' AFTER RELEASE_ID;
UPDATE promo_coupon_inner_code t, promo_activity a SET t.ACTIVITY_CODE=a.ACTIVITY_CODE WHERE a.ID=t.ACTIVITY_ID;
UPDATE promo_coupon_inner_code t, promo_coupon_release a SET t.RELEASE_CODE=a.RELEASE_CODE WHERE a.ID=t.RELEASE_ID;

CREATE INDEX idx_couponinnercode_tenant_activity_release ON promo_coupon_inner_code(TENANT_CODE, ACTIVITY_CODE, RELEASE_CODE);

-- promo_coupon_release
ALTER TABLE promo_coupon_release ADD COLUMN ACTIVITY_CODE VARCHAR(40) NOT NULL COMMENT 'Activity code.' AFTER ACTIVITY_ID;
UPDATE promo_coupon_release t, promo_activity a SET t.ACTIVITY_CODE=a.ACTIVITY_CODE WHERE a.ID=t.ACTIVITY_ID;

CREATE INDEX idx_couponrelease_activity ON promo_coupon_release(ACTIVITY_CODE);

-- promo_incentive_limited
ALTER TABLE promo_incentive_limited ADD COLUMN ACTIVITY_CODE VARCHAR(40) NOT NULL COMMENT 'Activity code.' AFTER ACTIVITY_ID;
UPDATE promo_incentive_limited t, promo_activity a SET t.ACTIVITY_CODE=a.ACTIVITY_CODE WHERE a.ID=t.ACTIVITY_ID;

CREATE UNIQUE INDEX uidx_incentivelimited_activity_limitation ON promo_incentive_limited(ACTIVITY_CODE,LIMITATION_CODE);

