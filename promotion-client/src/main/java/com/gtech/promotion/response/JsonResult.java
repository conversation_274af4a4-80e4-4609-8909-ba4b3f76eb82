package com.gtech.promotion.response;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class JsonResult<T> {
    private String code;
    private Boolean success;
    private String message;
    private T data;

    public static <T> JsonResult<T> success(T data) {
        JsonResult<T> result = new JsonResult();
        result.setCode("200");
        result.setSuccess(true);
        result.setData(data);
        return result;
    }

    public static <T> JsonResult<T> success() {
        JsonResult<T> result = new JsonResult();
        result.setCode("200");
        result.setSuccess(true);
        return result;
    }

    public static <T> JsonResult<T> error(String code, String message) {
        JsonResult<T> result = new JsonResult();
        result.setCode(code);
        result.setSuccess(false);
        result.setMessage(message);
        return result;
    }
}
