package com.gtech.promotion.client;

import com.gtech.promotion.request.QueryFlashSalePriceByProductSyncParam;
import com.gtech.promotion.response.JsonResult;
import com.gtech.promotion.response.QueryFlashSalePriceByProductResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(value = "PROMOTION", url = "${titan.gateway.url:}")
public interface PromotionClient {

    @PostMapping("/promotion/marketing/flashSale/price/sync")
    JsonResult<List<QueryFlashSalePriceByProductResult>> queryFlashSalePriceByProductSync(@RequestBody QueryFlashSalePriceByProductSyncParam param);
}
