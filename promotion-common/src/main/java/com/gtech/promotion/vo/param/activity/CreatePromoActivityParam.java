/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.vo.param.activity;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.commons.utils.Joiner;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.api.ApiConstants;
import com.gtech.promotion.checker.activity.TPromoActivityChecker;
import com.gtech.promotion.checker.activity.TPromoProductChecker;
import com.gtech.promotion.code.PromotionConstants;
import com.gtech.promotion.code.activity.*;
import com.gtech.promotion.exception.Check;
import com.gtech.promotion.exception.ErrorCodes;
import com.gtech.promotion.utils.Constants;
import com.gtech.promotion.utils.DateValidUtil;
import com.gtech.promotion.vo.bean.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 创建活动入参
 * 
 */
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("CreatePromoActivityRequest")
public class CreatePromoActivityParam implements Serializable{

    private static final long serialVersionUID = 5030143857890629183L;

    @ApiModelProperty( value="Domain code.", example="DC0001",required=true)
    private String domainCode;

    @ApiModelProperty( value="Tenant code.", example="TC0001",required=true)
    private String tenantCode;

    @ApiModelProperty( value="orgCode.", example="default",required=true)
    private String orgCode;

    @ApiModelProperty(value = "Activity Name",required = true)
    private String activityName;

    @ApiModelProperty("Activity sponsors")
    private String sponsors;

    @ApiModelProperty("ops create type")
    private String opsType;

    @ApiModelProperty("product in activity sort method: 01-price asc 02-price desc")
    private String productSort;

    @ApiModelProperty(value = "Activity type：01-Activity 02-Coupon",required = true)
    private String activityType;
    
    @ApiModelProperty(value = "Show flag: 1-show, 2-hidden")
    private Integer showFlag;

    @ApiModelProperty(value = "Activity warm-up begin time.")
    private String warmBegin;

    @ApiModelProperty(value = "Activity warm-up end time. is warmBegin not blank, warmEnd same as activityBegin")
    private String warmEnd;

    @ApiModelProperty(value = "Activity begin time.",required = true)
    private String activityBegin;

    @ApiModelProperty(value = "Activity end time.",required = true)
    private String activityEnd;

    @ApiModelProperty(value = "Activity url.")
    private String activityUrl;

    @ApiModelProperty(value = "Custom conditions.")
    private List<CustomCondition> customConditions;

    @ApiModelProperty(value = "Activity priority.(1-100, default is 999)")
    @Builder.Default
    private Integer priority = PromotionConstants.DEF_PRIORITY;
    public void setPriority(Integer priority) {
        this.priority = (null == priority ? PromotionConstants.DEF_PRIORITY: priority);
    }

    @ApiModelProperty(value = "Unique identification of goods to be uploaded (this parameter must be filled when uploading files) ")
    private String skuToken;

    @Builder.Default
    @ApiModelProperty(value = ApiConstants.PRODUCT_SELECTION_TYPE)
    private String productSelectionType = ProductSelectionEnum.SELECT.code();
    public void setProductSelectionType(String productSelectionType) {
        this.productSelectionType = StringUtils.isBlank(productSelectionType) ? ProductSelectionEnum.SELECT.code() : productSelectionType;
    }

    @Builder.Default
    @ApiModelProperty(value = "Product item scope type: 1-All scope 2-By spu in scope")
    private String itemScopeType = ItemScopeTypeEnum.ALL_SCOPE.code();
    public void setItemScopeType(String itemScopeType) {
        this.itemScopeType = StringUtils.isBlank(itemScopeType) ? ItemScopeTypeEnum.ALL_SCOPE.code() : itemScopeType;
    }

    @ApiModelProperty(value = "Template code.",required = true)
    private String templateCode;

    @ApiModelProperty(value = ApiConstants.PRODUCT_TYPE,required = true)
    private String conditionProductType;
    public void setConditionProductType(String conditionProductType) {
        this.conditionProductType = conditionProductType;
    }

    @ApiModelProperty(value = ApiConstants.PRODUCT_TYPE)
    private String incentiveProductType;

    @ApiModelProperty(value = "Product scope list. seqNum equals 1 means condition product. seqNum equals 2 means incentive product.")
    private List<ProductScope> products;

    @ApiModelProperty(value = "Product detail list. seqNum equals 1 means condition product. seqNum equals 2 means incentive product.")
    private List<ProductDetail> productDetails;

    @ApiModelProperty(value = "Product detail list. seqNum equals 1 means condition product. seqNum equals 2 means incentive product.")
    private List<ProductDetail> productDetailBlackList;

    @ApiModelProperty(value = "Incentive limited mark: 00-unlimited 01-limited")
    private String incentiveLimitedFlag;

    @ApiModelProperty(value = "Incentive limited list. It can't be empty while incentiveLimitedFlag is '01'.")
    private List<IncentiveLimited> incentiveLimiteds;

    /**
     * @deprecated
     */
    @ApiModelProperty("Deprecated. Member level list. Empty means not limited.")
    @Deprecated
    private List<MemberLevel> memberLevels; //NOSONAR

    /**
     * @deprecated
     */
    @ApiModelProperty("Deprecated. Member label list. Empty means not limited.")
    @Deprecated
    private List<MemberLabel> memberLabels; //NOSONAR

    @ApiModelProperty(value = "Qualification list. Empty means not limited.")
    private List<Qualification> qualifications;

    @ApiModelProperty(value = "Store limited type: 00-Unlimited, 01-Specified stores.",required = true)
    private String storeType;

    @ApiModelProperty(value = "Specified store list while storeType equals 01.")
    private List<ActivityStore> channelStores;

    @ApiModelProperty(value = "Giving-gift list of the activity.")
    private List<Giveaway> giveaways;

    @ApiModelProperty(value = "Parameters list of the activity template function.",required = true)
    private List<FunctionParam> funcParams;

    @ApiModelProperty(value = "Multilingual attributes.")
    private List<ActivityLanguage> activityLanguages;

    @ApiModelProperty(value = "Activity period.")
    private ActivityPeriod activityPeriod;

    @ApiModelProperty(value = "User code of the operator.", example="U00001")
    private String operateUser;

    @ApiModelProperty(value = "User last name of the operator.")
    private String operateLastName;

    @ApiModelProperty(value = "User first name of the operator.")
    private String operateFirstName;

    @ApiModelProperty(value = "Activity cool down time.")
    private String coolDown;

    @ApiModelProperty(value = "Activity background image.")
    private String backgroundImage;

    @ApiModelProperty(value = ApiConstants.PRODUCT_CONDITION)
    private String productCondition;

    @ApiModelProperty(value = "Ribbon image.")
    private String ribbonImage;

    @ApiModelProperty(value = "Position of Ribbon image .")
    private String ribbonPosition;

    @ApiModelProperty(value = "Ribbon text.")
    private String ribbonText;


    /**
     * Parameter validation.
     */
    public void voucherValidate() {

        CheckUtils.isNotBlank(this.domainCode, ErrorCodes.PARAM_EMPTY, "domainCode");
        CheckUtils.isNotBlank(this.tenantCode, ErrorCodes.PARAM_EMPTY, "tenantCode");
        CheckUtils.isNotBlank(this.activityType, ErrorCodes.PARAM_EMPTY, "activityType");

        // Validate the multi-language information.
        CheckUtils.isTrue(CollectionUtils.isNotEmpty(this.activityLanguages), ErrorCodes.PARAM_EMPTY, "activityLanguages");
        for (ActivityLanguage e : this.activityLanguages) {
            e.validate();
        }

        // Validate the store information
        this.setStoreType(StringUtil.isBlank(this.getStoreType()) ? "00" : this.getStoreType());
        if (StoreParamTypeEnum.STORE_CUSTOM.equalsCode(this.getStoreType())){
            CheckUtils.isTrue(CollectionUtils.isNotEmpty(this.channelStores), ErrorCodes.PARAM_EMPTY, "channelStores");
            for (ActivityStore e : this.channelStores) {
                e.validate();
            }
        }

        if (CollectionUtils.isNotEmpty(this.qualifications)){
            for (Qualification qualification : qualifications) {
                qualification.validate();
            }
        }

        if (StringUtil.isBlank(this.incentiveLimitedFlag)) {
            this.incentiveLimitedFlag = IncentiveLimitedFlagEnum.NO.code();
        }
        CheckUtils.isTrue(IncentiveLimitedFlagEnum.exist(this.incentiveLimitedFlag), ErrorCodes.PARAM_ERROR, "incentiveLimitedFlag");
        if (IncentiveLimitedFlagEnum.YES.equalsCode(this.incentiveLimitedFlag)) {
            CheckUtils.isTrue(CollectionUtils.isNotEmpty(this.incentiveLimiteds), ErrorCodes.PARAM_EMPTY, "incentiveLimiteds");
            for(IncentiveLimited e : this.incentiveLimiteds) {
                e.validate();
            }
        }

        CheckUtils.isTrue(priority >= 1 && priority <= 999, ErrorCodes.PARAM_ERROR, "priority");


        if (null != activityPeriod) {
            activityPeriod.validate();
        }
        this.checkTime();
    }

    /**
     * Parameter validation.
     */
    public void validate() {

        CheckUtils.isNotBlank(this.domainCode, ErrorCodes.PARAM_EMPTY, "domainCode");
        CheckUtils.isNotBlank(this.tenantCode, ErrorCodes.PARAM_EMPTY, "tenantCode");
        CheckUtils.isNotBlank(this.activityType, ErrorCodes.PARAM_EMPTY, "activityType");
        CheckUtils.isNotBlank(this.templateCode, ErrorCodes.PARAM_EMPTY, "templateCode");

        // Validate the multi-language information.
        CheckUtils.isTrue(CollectionUtils.isNotEmpty(this.activityLanguages), ErrorCodes.PARAM_EMPTY, "activityLanguages");
        for (ActivityLanguage e : this.activityLanguages) {
            e.validate();
        }

        // Validate the store information
        this.setStoreType(StringUtil.isBlank(this.getStoreType()) ? "00" : this.getStoreType());
        if (StoreParamTypeEnum.STORE_CUSTOM.equalsCode(this.getStoreType())){
            CheckUtils.isTrue(CollectionUtils.isNotEmpty(this.channelStores), ErrorCodes.PARAM_EMPTY, "channelStores");
            for (ActivityStore e : this.channelStores) {
                e.validate();
            }
        }

        if (CollectionUtils.isNotEmpty(this.qualifications)){
            for (Qualification qualification : qualifications) {
                qualification.validate();
            }
        }

        if (StringUtil.isBlank(this.incentiveLimitedFlag)) {
            this.incentiveLimitedFlag = IncentiveLimitedFlagEnum.NO.code();
        }
        CheckUtils.isTrue(IncentiveLimitedFlagEnum.exist(this.incentiveLimitedFlag), ErrorCodes.PARAM_ERROR, "incentiveLimitedFlag");
        if (IncentiveLimitedFlagEnum.YES.equalsCode(this.incentiveLimitedFlag)) {
            CheckUtils.isTrue(CollectionUtils.isNotEmpty(this.incentiveLimiteds), ErrorCodes.PARAM_EMPTY, "incentiveLimiteds");
            for(IncentiveLimited e : this.incentiveLimiteds) {
                e.validate();
            }
        }

        CheckUtils.isTrue(priority >= 1 && priority <= 999, ErrorCodes.PARAM_ERROR, "priority");
        CheckUtils.isTrue(ProductSelectionEnum.exist(this.productSelectionType), ErrorCodes.PARAM_ERROR, "productSelectionType");

        // Validate the product condition
        CheckUtils.isNotBlank(this.conditionProductType, ErrorCodes.PARAM_EMPTY, "conditionProductType");
        CheckUtils.isTrue(ProductTypeEnum.exist(this.conditionProductType), ErrorCodes.PARAM_ERROR, "conditionProductType");
        if (StringUtils.isNotBlank(this.incentiveProductType)) {
            CheckUtils.isTrue(ProductTypeEnum.exist(this.incentiveProductType), ErrorCodes.PARAM_ERROR, "incentiveProductType");
        }

        if (null != activityPeriod) {
            activityPeriod.validate();
        }
        this.validate0411();

        this.checkTime();
        this.checkProduct();
    }

    private void validate0411() {
        
        if (!FuncTypeEnum.IncentiveEnum.EACH_FIXED_MONEY.equalsCode(this.templateCode.substring(12, 16))){
            return;
        }

        CheckUtils.isTrue(ProductSelectionEnum.SELECT.equalsCode(this.productSelectionType), ErrorCodes.PARAM_ERROR, "productSelectionType");
        Check.check(!ProductTypeEnum.CUSTOM_PRODUCT.equalsCode(this.getConditionProductType()), TPromoProductChecker.ERROR_PRODUCT_TYPE_EACH_PRICE);//每个不同特价活动商品范围只能是指定商品
    }

    /**
     * 校验 预热时间和活动时间的比较，以及时间格式校验必须是yyyyMMddHHmmss
     */
    public void checkTime(){

        long current = Long.parseLong(DateUtil.getCurrentDateAsString(DateUtil.FORMAT_YYYYMMDDHHMISS_14));

        // 时间格式校验
        CheckUtils.isTrue(DateValidUtil.isValidDate(this.activityBegin), ErrorCodes.PARAM_EMPTY, "activityBegin");
        CheckUtils.isTrue(DateValidUtil.isValidDate(this.activityEnd), ErrorCodes.PARAM_EMPTY, "activityEnd");

        long activityBeginAsLong = Long.parseLong(this.activityBegin);
        long activityEndAsLong = Long.parseLong(this.activityEnd);
        Check.check(activityBeginAsLong > activityEndAsLong, TPromoActivityChecker.ACTIVITY_BEGIN_END_TIME);
        Check.check(activityEndAsLong <= current, TPromoActivityChecker.ACTIVITY_END_BEGIN_TIME);

        //活动结束时间最大限制：redis的key过期时间不能太大,毫秒值不能超过integer最大值
        String maxDate = DateUtil.format(DateUtil.addMonth(Constants.MAX_TIME), DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        Check.check(activityEndAsLong >= Long.parseLong(maxDate), TPromoActivityChecker.REDIS_TIME_LIMIT,maxDate);

        // 当预热开始时间为空,那么结束时间也要为空
        if (StringUtil.isNotBlank(this.warmBegin)){
            this.warmEnd = this.activityBegin;

            CheckUtils.isTrue(DateValidUtil.isValidDate(this.warmBegin), ErrorCodes.PARAM_EMPTY, "warmBegin");
            CheckUtils.isTrue(DateValidUtil.isValidDate(this.warmEnd), ErrorCodes.PARAM_EMPTY, "activityEnd");

            long warmBeginAsLong = Long.parseLong(this.warmBegin);
            long warmEndAsLong = Long.parseLong(this.warmEnd);
            Check.check(warmBeginAsLong >= warmEndAsLong, TPromoActivityChecker.WARM_BEGIN_TIME);

        }else{
            this.warmBegin = "";
            this.warmEnd = "";
        }


        if (StringUtil.isNotBlank(this.coolDown)){
            CheckUtils.isTrue(DateValidUtil.isValidDate(this.coolDown), ErrorCodes.PARAM_EMPTY, "coolDown");
            long coolDownAsLong = Long.parseLong(this.coolDown);
            Check.check(coolDownAsLong > activityEndAsLong, TPromoActivityChecker.EARLIER_ACTIVITY_END);
            Check.check(coolDownAsLong <= activityBeginAsLong, TPromoActivityChecker.LATER_ACTIVITY_BEGIN);

        }else {
            this.coolDown = "";
        }
    }

    public boolean isAllProducts() {
        
        return CollectionUtils.isEmpty(products) && CollectionUtils.isEmpty(productDetails);
    }

    // 商品校验
    private void checkProduct(){

        //反选时
        if (ProductSelectionEnum.INVERT_SELECT.equalsCode(this.productSelectionType)){
            Check.check(this.isAllProducts(), TPromoProductChecker.ERROR_PRODUCT_SELECT_ALL);//不能反选全部商品
        }

        if (this.isAllProducts()) {
            return;
        }
        boolean seq1 = false;
        if (CollectionUtils.isNotEmpty(this.products)) {
            this.checkProductScopes(products);
            seq1 = products.stream().anyMatch(x->x.getSeqNum() == 1);
        }
        if (CollectionUtils.isNotEmpty(this.productDetails)) {
            this.skuToken = null;
            seq1 = seq1 || productDetails.stream().anyMatch(x -> null != x.getSeqNum() && 1 == x.getSeqNum());
        }

        if (!seq1){ // 如果没有商品池1， 添加一个全分类的商品池1
            if (null == products){
                products = new ArrayList<>();
            }
            products.add(ProductScope.builder().categoryCode(PromotionConstants.UNLIMITED).build());
        }
    }

    public void checkProductAll(ProductScope x){
        Check.check(null == x.getSeqNum(), TPromoProductChecker.NOT_NULL_PRODUCT_SEQ);
        Check.check(x.getSeqNum() < 1 || x.getSeqNum() > 2, TPromoProductChecker.TOO_BIG_SKUNUM);//买A优惠B seqNum只能为1或2  1=A;2=B
    }

    /**
     * 各商品池不能有一摸一样的商品范围
     */
    public void checkProductScopes(List<ProductScope> products){

        HashSet<String> seqNumProducts = new HashSet<>();
        for (ProductScope product : products){

            addIfNullProduct(product);

            String[] categoryCode = product.getCategoryCode().split(",");
            String[] brandCode = product.getBrandCode().split(",");
            String[] attributeCodes = new String[0];

            List<ProductAttribute> productAttributes = product.getAttributes();
            if (CollectionUtils.isNotEmpty(productAttributes)) {
                attributeCodes = productAttributes.stream().map(m -> (m.getAttributeCode() + ":" + m.getAttributeValues())).collect(Collectors.toList()).toArray(attributeCodes);
            }
            Arrays.sort(categoryCode);
            Arrays.sort(brandCode);
            Arrays.sort(attributeCodes);

            Joiner joiner = Joiner.on(",");
            seqNumProducts.add(joiner.appendTo(joiner.appendTo(joiner.appendTo(new StringBuilder(), categoryCode), brandCode), attributeCodes).toString());
        }
        Check.check(products.size() > seqNumProducts.size(), TPromoProductChecker.ERROR_PRODUCT_SCOPE);
    }

    private void addIfNullProduct(ProductScope product){
        if (StringUtil.isBlank(product.getCategoryCode())){
            product.setCategoryCode(PromotionConstants.UNLIMITED);
        }
        if (StringUtil.isBlank(product.getBrandCode())){
            product.setBrandCode(PromotionConstants.UNLIMITED);
        }
        List<ProductAttribute> productAttributes = product.getAttributes();
        if (CollectionUtils.isNotEmpty(productAttributes)) {
            productAttributes.forEach(attr -> {
                if (StringUtil.isBlank(attr.getAttributeCode())) {
                    attr.setAttributeCode(PromotionConstants.UNLIMITED);
                }
                if (StringUtil.isBlank(attr.getAttributeValues())) {
                    attr.setAttributeValues(PromotionConstants.UNLIMITED);
                }
            });
        }
    }

}