/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.vo.result.flashsale;

import com.gtech.promotion.api.ApiConstants;
import com.gtech.promotion.vo.bean.ActivityPeriod;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 根据活动查商品 出参
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("FlashSaleQueryListResponse")
public class FlashSaleQueryListResult extends ActivityLanguageResult {

    @ApiModelProperty(value = "Activity Code")
    private String activityCode;

    @ApiModelProperty(value = "Activity type.03-Lucky draw 04-Flash sale")
    private String activityType;

    @ApiModelProperty(value = "Activity start time")
    private String activityBegin;

    @ApiModelProperty(value = "Activity end time")
    private String activityEnd;

    @ApiModelProperty(value = "Activity url")
    private String activityUrl;

    @ApiModelProperty(value = ApiConstants.ACTIVITY_STATUS)
    private String activityStatus;

    @ApiModelProperty(value = "Sponsors")
    private String sponsors;

    @ApiModelProperty(value = "Activity warm-up begin time.")
    private String warmBegin;

    @ApiModelProperty(value = "Activity cool down time.")
    private String coolDown;

    @ApiModelProperty(value = "Ops create activity type")
    private String opsType;

    @ApiModelProperty(value = "Background image")
    private String backgroundImage;

    @ApiModelProperty(value = "Ribbon image")
    private String ribbonImage;

    @ApiModelProperty(value = "Ribbon position")
    private String ribbonPosition;

    @ApiModelProperty(value = "Ribbon text")
    private String ribbonText;

    @ApiModelProperty(value = "Activity period.")
    private ActivityPeriod activityPeriod;

    @ApiModelProperty(value = "pre sale pay type")
    private String preSalePayType;

    @ApiModelProperty(value = "shipping time")
    private String shippingTime;

    @ApiModelProperty(value = "import no")
    private String importNo;
}
