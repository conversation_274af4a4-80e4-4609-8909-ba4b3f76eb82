package com.gtech.promotion.vo.param.activity;

import com.gtech.promotion.page.RequestPage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/7/13 10:01
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("ActivityGroupRelationRequest")
public class ActivityGroupRelationParam extends RequestPage implements Serializable {

    private static final long serialVersionUID = -7256079983819897634L;

    @ApiModelProperty(value = "租户编码", example = "880001", required = true)
    private String tenantCode;

    @ApiModelProperty(value = "互斥叠加关系列表",  required = true)
    private List<GroupRelationParam> relations;


}
