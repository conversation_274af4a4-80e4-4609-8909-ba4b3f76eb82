package com.gtech.promotion.vo.param.purchaseconstraint;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class PcRuleCatchRefreshRequest {
    @NotBlank
    private String tenantCode;
    @NotNull
    @Size(min = 1,max = 50)
    private List<String> pcCodeList;
}
