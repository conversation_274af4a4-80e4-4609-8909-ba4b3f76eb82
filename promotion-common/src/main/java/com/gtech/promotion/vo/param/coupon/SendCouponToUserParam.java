/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.vo.param.coupon;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.api.ApiConstants;
import com.gtech.promotion.exception.ErrorCodes;
import com.gtech.promotion.vo.bean.Qualification;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.List;

@Getter
@Setter
@ToString
@ApiModel("SendCouponToUserRequest")
public class SendCouponToUserParam implements Serializable{

    private static final long serialVersionUID = 616863850706013457L;

    @ApiModelProperty(value = "Domain code.",example = "D00001",required = true)
    private String domainCode;

    @ApiModelProperty(value = "Tenant code", required = true)
    private String tenantCode;

    @ApiModelProperty(value = "Language id")
    private String language;
    
    @ApiModelProperty(value="Channel code.")
    private String channelCode;

    @ApiModelProperty(value = "Store organization code.")
    private String orgCode;

    @ApiModelProperty(value = "Promotion activity code.", required = true)
    private String activityCode;

    @ApiModelProperty(value = "Member user code.", required = true)
    private String userCode;

    @ApiModelProperty(value = "Qualification list")
    private List<Qualification> qualifications;

    @ApiModelProperty(value = "Frozen Status.1-UnFrozen,2-Frozened",example = "1")
    private Integer frozenStatus ;

    /**
     * @deprecated
     */
    @ApiModelProperty(value = "Deprecated. Member level code.",required = false)
    @Deprecated
    private String memberLevelCode; //NOSONAR

    /**
     * @deprecated
     */
    @ApiModelProperty(value = "Deprecated. Member label codes.",required = false)
    @Deprecated
    private String memberLabelCodes; //NOSONAR
    
    @ApiModelProperty(value = ApiConstants.TAKE_LABEL, required = true)
    private String takeLabel;

    @ApiModelProperty(value = "Coupon source")
    private String couponSource;

    @ApiModelProperty(value = "Receive count for per member.", required = true)
    private Integer receiveCount = 1;

    /**
     * Parameter validation.
     */
    public void validate() {

        CheckUtils.isNotBlank(this.domainCode, ErrorCodes.PARAM_EMPTY, "domainCode");
        CheckUtils.isNotBlank(this.tenantCode, ErrorCodes.PARAM_EMPTY, "tenantCode");
        CheckUtils.isNotBlank(this.activityCode, ErrorCodes.PARAM_EMPTY, "activityCode");
        CheckUtils.isNotBlank(this.takeLabel, ErrorCodes.PARAM_EMPTY, "takeLabel");
        CheckUtils.isNotBlank(this.userCode, ErrorCodes.PARAM_EMPTY, "userCode");
        if (!CollectionUtils.isEmpty(qualifications)){
            for (Qualification qualification : qualifications) {
                qualification.validate();
            }
        }
        CheckUtils.isTrue(this.receiveCount > 0 && this.receiveCount < 100, ErrorCodes.PARAM_SPECIFICATION_ERROR, "receiveCount must greate than 0 and less than 100.");
    }
}
