package com.gtech.promotion.vo.result.activity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/6/14 15:08
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("ActivityGroupResponse")
public class ActivityGroupResult implements Serializable {

    private static final long serialVersionUID = 2726549637094250508L;

    @ApiModelProperty(value = "租户编码", example = "880001")
    private String tenantCode;

    @ApiModelProperty(value = "分组编码", example = "1231209")
    private String groupCode;

    @ApiModelProperty(value = "Domain code",example = "DC0001")
    private String domainCode;

    @ApiModelProperty(value = "分组名称", example = "自定义满减分组")
    private String groupName;

    @ApiModelProperty(value = "分组类型, 01:默认分组 02：自定义分组", example = "01")
    private String type;

    @ApiModelProperty(value = "优先级，数字越大越优先", example = "20")
    private Integer priority;

    @ApiModelProperty(value = "是否删除：0——未删除 ， 1——已删除", example = "0")
    private String logicDelete;

    @ApiModelProperty(value = "创建时间" , example = "20200501000000")
    private Date createTime;
    @ApiModelProperty(value = "扩展字段" , example = "{\"language\": {\"en-US\": \"Discount for select products\"}}")
    private String extParam;
}
