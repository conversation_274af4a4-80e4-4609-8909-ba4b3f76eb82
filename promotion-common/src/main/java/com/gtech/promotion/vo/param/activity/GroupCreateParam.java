package com.gtech.promotion.vo.param.activity;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.exception.ErrorCodes;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/6/14 9:56
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("GroupCreateRequest")
public class GroupCreateParam implements Serializable {

    @ApiModelProperty(value = "Tenant code", example = "880001", required = true)
    private String tenantCode;

    @ApiModelProperty(value = "Domain code",required = true)
    private String domainCode;

    @ApiModelProperty(value = "Group name", example = "Group name", required = true)
    private String groupName;

    @ApiModelProperty(value = "Ext param", example = "Ext param")
    private String extParam;


    @ApiModelProperty(value = "Operator user", example = "12344")
    private String operatorUser;

    public void checkParam(){

        CheckUtils.isNotBlank(this.tenantCode, ErrorCodes.PARAM_EMPTY, "tenantCode");
        CheckUtils.isNotBlank(this.domainCode, ErrorCodes.PARAM_EMPTY, "domainCode");
        CheckUtils.isNotBlank(this.groupName, ErrorCodes.PARAM_EMPTY, "groupName");

    }

}
