package com.gtech.promotion.vo.result.flashsale;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/8/8 13:38
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("MarketingGroupUserListResponse")
public class MarketingGroupUserListResult implements Serializable {

    private static final long serialVersionUID = 739341005221991443L;

    @ApiModelProperty(value = "activity code.",example = "12556568987",required = true)
    private String activityCode;

    /**
     * 拼团业务编码
     */
    @ApiModelProperty(value = "Marketing group code.",example = "12556568987",required = true)
    private String marketingGroupCode;

    /**
     * 会员编码
     */
    @ApiModelProperty(value = "User code.",example = "12556568987",required = true)
    private String userCode;

    //微信昵称
    @ApiModelProperty(value = "Nick name.")
    private String nickName;

    //微信头像
    @ApiModelProperty(value = "WeChat img.")
    private String wechatImg;

    /**
     * 是否团长 1 是， 0 否
     */
    @ApiModelProperty(value = "Team leader.是否团长 1 是， 0 否",example = "12556568987",required = true)
    private String teamLeader;

    /**
     * 有效截止时间
     */
    @ApiModelProperty(value = "Effective time.",example = "20200810154932",required = true)
    private String effectiveTime;

    /**
     * 拼团状态 01进行中，02 拼团成功,03取消拼团 04已支付
     */
    @ApiModelProperty(value = "Group status. 01进行中，02 拼团成功,03取消拼团，04已支付",example = "12556568987",required = true)
    private String groupStatus;

    @ApiModelProperty(value = "Remaining quantity.",example = "1",required = true)
    private Integer remainingQuantity;

    @ApiModelProperty(value = "product code.",example = "1")
    private String productCode;

    @ApiModelProperty(value = "sku code.",example = "1")
    private String skuCode;



}
