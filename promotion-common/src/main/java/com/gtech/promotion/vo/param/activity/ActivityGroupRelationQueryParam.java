package com.gtech.promotion.vo.param.activity;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.exception.ErrorCodes;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/7/12 13:40
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("ActivityGroupRelationQueryRequest")
public class ActivityGroupRelationQueryParam implements Serializable {

    private static final long serialVersionUID = 5343446950553247093L;

    @ApiModelProperty(value = "租户编码", example = "880001", required = true)
    private String tenantCode;

    @ApiModelProperty(value = "Domain code.",required = true)
    private String domainCode;

    public void validate() {

        CheckUtils.isNotBlank(this.tenantCode, ErrorCodes.PARAM_EMPTY, "tenantCode");

        CheckUtils.isNotBlank(this.domainCode, ErrorCodes.PARAM_EMPTY, "domainCode");


    }
}
