package com.gtech.promotion.vo.param.activity;

import com.gtech.promotion.vo.bean.ProductAttribute;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("QueryAfterDiscountItemParam")
public class QueryAfterDiscountItemParam implements Serializable {

    private static final long serialVersionUID = -7732244140096712209L;

    // Product category code list.
    @ApiModelProperty(value = "Product category code list.")
    private List<String> categoryCodes;

    @ApiModelProperty(value = "Product brand code.")
    private String brandCode;

    @ApiModelProperty(value = "Product attribute information list.")
    private List<ProductAttribute> attributes;

    @ApiModelProperty(value = "Product code.")
    private String productCode;

    @ApiModelProperty(value = "Sku code.")
    private String skuCode;

    @ApiModelProperty(value = "sale price.")
    private BigDecimal salePrice;

    @ApiModelProperty(value = "Product tag.")
    private String productTag;
}
