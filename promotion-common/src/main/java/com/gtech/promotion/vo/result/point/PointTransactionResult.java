package com.gtech.promotion.vo.result.point;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

@Getter
@Setter
@ToString
public class PointTransactionResult implements Serializable {

    private static final long serialVersionUID = -5839079873382121231L;

    @ApiModelProperty(value = "Tenant code",required = true)
    private String tenantCode;

    @ApiModelProperty(value = "Transaction serial number.")
    private String transactionSn;

    @ApiModelProperty(value = "Point account code. (UserCode or OrgCode)")
    private String accountCode;

    @ApiModelProperty(value = "Point account type. (1-User 2-Organization)")
    private Integer accountType;

    @ApiModelProperty(value = "Point campaign code.")
    private String campaignCode;

    @ApiModelProperty(value = "Program name.")
    private String programName;

    @ApiModelProperty(value = "Point transaction type. (1-Increase points 2-Deduct points)")
    private Integer transactionType;

    @ApiModelProperty(value = "Point transaction remarks.")
    private String transactionRemarks;

    @ApiModelProperty(value = "Point spent/earned in the transaction.")
    private Integer transactionAmount;

    @ApiModelProperty(value = "Transaction date. (yyyyMMddHHmmss)")
    private String transactionDate;

    @ApiModelProperty(value = "Refer transaction serial number.")
    private String referTransactionSn;

    @ApiModelProperty(value = "Refer order number.")
    private String referOrderNumber;

    @ApiModelProperty(value = "Create user.")
    private String createUser;

    @ApiModelProperty(value = "Create time.")
    private String createTime;

    @ApiModelProperty(value = "Operation.")
    private String operation;

}