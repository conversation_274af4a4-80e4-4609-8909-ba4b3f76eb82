package com.gtech.promotion.vo.result.activity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2021/6/2 13:22
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("ActivityItemResponse")
public class ActivityItemResult implements Serializable {

    private static final long serialVersionUID = 3460724302382891881L;

    @ApiModelProperty(value = "Product sku code.")
    private String skuCode;
    @ApiModelProperty(value = "Product code.")
    private String productCode;

}
