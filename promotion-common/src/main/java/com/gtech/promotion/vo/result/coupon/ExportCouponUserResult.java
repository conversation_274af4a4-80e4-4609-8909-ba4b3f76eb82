package com.gtech.promotion.vo.result.coupon;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2021/3/29 13:36
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ExportCouponUserResult implements Serializable {
    private static final long serialVersionUID = 7883661377007382536L;

    private String id;

    private String tenantCode;

    private String activityCode;

    private String userCode;

    private String account;

    private String firstName;

    private String lastName;

    private String usedQty;

    private String unusedQty;

    private String totalQty;

}
