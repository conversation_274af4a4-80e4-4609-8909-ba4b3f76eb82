package com.gtech.promotion.vo.result.purchaseconstraint;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.gtech.promotion.vo.bean.PurchaseConstraintRule;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.util.List;

/**
 * 检查不符合限购规则的限购结果详细信息
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel("CheckPurchaseConstraintResponse")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CheckPurchaseConstraintResult {

    /**
     * 校验后是否可以购买
     */
    @ApiModelProperty(value = "Purchase constraint can buy")
    private Boolean canBuy;

    /**
     * 是否命中限购，不能购买，只要命中也需要返回true
     */
    @ApiModelProperty(value = "Purchase constraint hit")
    private Boolean hitPurchaseConstraint;

    /**
     * 不能购买的错误code
     */
    @ApiModelProperty(value = "Purchase constraint error code.")
    private String errorCode;

    /**
     * 不能购买的错误信息
     */
    @ApiModelProperty(value = "Purchase constraint error message.")
    private String errorMessage;


    /**
     * 限购触发到所有商品集合
     */
    @ApiModelProperty(value = "Purchase constraint product list.")
    private List<CheckPurchaseConstraintProductResult> products;


    /**
     * 限购条件值 可能是金额也可能是数量
     */
    @ApiModelProperty(value = "Purchase constraint code.")
    private String purchaseConstraintCode;

    /**
     * 触发不能购买的限购规则
     */
    @ApiModelProperty(value = "Purchase constraint rule.")
    private PurchaseConstraintRule purchaseConstraintRule;

    /**
     * 命中的可购买的限购
     */
    @ApiModelProperty(value = "Check Purchase constraint hit results.")
    private List<CheckPurchaseConstraintHitResult> checkPurchaseConstraintHitResults;


    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class CheckPurchaseConstraintHitResult {
        /**
         * 限购Code
         */
        @ApiModelProperty(value = "Purchase constraint code")
        private String purchaseConstraintCode;
        /**
         * 限购名称
         */
        @ApiModelProperty(value = "Purchase constraint name")
        private String purchaseConstraintName;
        /**
         * 命中的限购规则
         */
        @ApiModelProperty(value = "Hit Purchase constraint rules.")
        private List<PurchaseConstraintRule> purchaseConstraintRuleList;
    }

}
