package com.gtech.promotion.vo.param.marketing;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.code.coupon.CouponFrozenStatusEnum;
import com.gtech.promotion.exception.ErrorCodes;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2021/4/26 15:15
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("UpdateFrozenStatusRequest")
public class UpdateFrozenStatusParam {


    @ApiModelProperty(value = "Domain code.",example = "D00001",required = true)
    private String domainCode;

    @ApiModelProperty(value = "Tenant code.",example = "T00001",required = true)
    private String tenantCode;

    @ApiModelProperty(value = "Store organization code. (Blank value means unlimited.)")
    private String orgCode;

    @ApiModelProperty(value = "Activity code",required = true)
    private String activityCode;

    @ApiModelProperty(value = "Member code", required = true)
    private String memberCode;

    @ApiModelProperty(value = "Member code", required = true)
    private Integer quality;

    @ApiModelProperty(value = "Frozen status 01-UnFrozen 02-Frozen",required = true)
    private String frozenStatus;
    //只有在发错券的时候才传这个参数
    @ApiModelProperty(name = "logicDelete",value = "Logic Delete",example = "0-unDelete,1-delete")
    private Integer logicDelete;

    /**
     * Parameter validation.
     */
    public void validate() {

        CheckUtils.isNotBlank(this.domainCode, ErrorCodes.PARAM_EMPTY, "domainCode");
        CheckUtils.isNotBlank(this.tenantCode, ErrorCodes.PARAM_EMPTY, "tenantCode");
        CheckUtils.isNotBlank(this.activityCode, ErrorCodes.PARAM_EMPTY, "activityCode");
        CheckUtils.isNotBlank(this.memberCode, ErrorCodes.PARAM_EMPTY, "memberCode");
        CheckUtils.isNotNull(this.quality, ErrorCodes.PARAM_EMPTY, "quality");
        CheckUtils.isNotBlank(this.frozenStatus, ErrorCodes.PARAM_EMPTY, "frozenStatus");
        if (StringUtil.isNotBlank(frozenStatus)){
            CheckUtils.isTrue(CouponFrozenStatusEnum.exist(frozenStatus), ErrorCodes.PARAM_ERROR, "frozenStatus");
        }
        CheckUtils.isTrue(quality > 0, ErrorCodes.PARAM_ERROR, "quality");
    }

}
