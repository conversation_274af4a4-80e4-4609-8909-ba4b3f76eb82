package com.gtech.promotion.vo.result.marketing;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LuckyDrawFindResult extends MarketingFindResult implements Serializable {

    private static final long serialVersionUID = 7103694320399903976L;

    @ApiModelProperty(value = "Prize list.",required = true)
    private List<MarketingPrizeResult> marketingPrizes;
}
