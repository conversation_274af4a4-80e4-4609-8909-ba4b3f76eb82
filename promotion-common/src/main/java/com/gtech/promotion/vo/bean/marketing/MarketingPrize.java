package com.gtech.promotion.vo.bean.marketing;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.code.marketing.PrizeTypeEnum;
import com.gtech.promotion.exception.ErrorCodes;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("MarketingPrize")
public class MarketingPrize implements Serializable {

    private static final long serialVersionUID = -6679659356842154489L;

    @ApiModelProperty(value = "prize code", required = true)
    private String prizeCode;

    @ApiModelProperty(value = "prize name", required = true)
    private String prizeName;

    @ApiModelProperty(value = "prize image")
    private String prizeImage;

    @ApiModelProperty(value = "prize order")
    private Integer prizeOrder;

    @ApiModelProperty(value = "prize quota")
    private Integer prizeQuota;

    @ApiModelProperty(value = "prize number per prize. eg. (coupon * 2)")
    private Integer prizeNum;
    // 奖品类型，01-券
    @ApiModelProperty(value = "prize type. 00-no prize, 01-coupon", required = true)
    private String prizeType;

    @ApiModelProperty(value = "prize Probability. (percent)  eg. 0.1 = 0.1%")
    private BigDecimal prizeProbability;

    @ApiModelProperty(value = "Multilingual prize display name.", required = true)
    private List<MarketingPrizeLanguage> marketingPrizeLanguages;

    public void validate() {
        CheckUtils.isNotBlank(this.prizeCode, ErrorCodes.PARAM_EMPTY, "prizeCode");
        CheckUtils.isNotBlank(this.prizeName, ErrorCodes.PARAM_EMPTY, "prizeName");
        CheckUtils.isNotBlank(this.prizeType, ErrorCodes.PARAM_EMPTY, "prizeType");
        CheckUtils.isTrue(PrizeTypeEnum.exist(this.prizeType), ErrorCodes.PARAM_ERROR, "prizeType");

        if (PrizeTypeEnum.DEFAULT.equalsCode(prizeType)){
            prizeQuota = 0;
            prizeProbability = BigDecimal.ZERO;
            prizeNum = 0;
        }else {
            CheckUtils.isTrue(null != this.prizeNum, ErrorCodes.PARAM_EMPTY, "prizeNum");
            CheckUtils.isTrue(null != this.prizeQuota, ErrorCodes.PARAM_EMPTY, "prizeQuota");
            CheckUtils.isTrue(0 < this.prizeQuota, ErrorCodes.PARAM_ERROR, "prizeQuota");
            String prizeProbability1 = "prizeProbability";
            CheckUtils.isTrue(null != this.prizeProbability, ErrorCodes.PARAM_EMPTY, prizeProbability1);
            CheckUtils.isTrue(BigDecimal.ZERO.compareTo(this.prizeProbability) < 0, ErrorCodes.PARAM_ERROR, prizeProbability1);
            CheckUtils.isTrue(new BigDecimal(100).compareTo(this.prizeProbability) > 0, ErrorCodes.PARAM_ERROR, prizeProbability1);
        }
        CheckUtils.isTrue(!CollectionUtils.isEmpty(marketingPrizeLanguages), ErrorCodes.PARAM_EMPTY, "marketingPrizeLanguages");

        for (MarketingPrizeLanguage marketingPrizeLanguage : marketingPrizeLanguages) {
            marketingPrizeLanguage.validate();
        }
    }
}
