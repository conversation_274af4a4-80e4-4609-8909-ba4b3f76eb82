package com.gtech.promotion.vo.param.activity;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.exception.ErrorCodes;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("CreateSharingRecordRequest")
public class CreateSharingRecordParam {


    @ApiModelProperty(value = "DomainCode", required = true)
    private String domainCode;
    @ApiModelProperty(value = "TenantCode", required = true)
    private String tenantCode;
    @ApiModelProperty(value = "OrgCode", required = true)
    private String orgCode;
    @ApiModelProperty(value = "ActivityCode", required = true)
    private String activityCode;
    @ApiModelProperty(value = "sharingMemberCode", required = true)
    private String sharingMemberCode;





    public void validate() {
        CheckUtils.isNotBlank(this.domainCode, ErrorCodes.PARAM_EMPTY, "domainCode");
        CheckUtils.isNotBlank(this.tenantCode, ErrorCodes.PARAM_EMPTY, "tenantCode");
        CheckUtils.isNotBlank(this.orgCode, ErrorCodes.PARAM_EMPTY, "orgCode");
        CheckUtils.isNotBlank(this.activityCode, ErrorCodes.PARAM_EMPTY, "activityCode");
        CheckUtils.isNotBlank(this.sharingMemberCode, ErrorCodes.PARAM_EMPTY, "sharingMemberCode");
    }


}
