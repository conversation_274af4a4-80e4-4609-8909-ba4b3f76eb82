/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WA<PERSON><PERSON>NTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.vo.bean;

import com.gtech.promotion.vo.param.marketing.flashsale.SkuParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.util.List;


/**
 * ProductCodes
 *
 * <AUTHOR>
 * @Date 2020-05-18
 */
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("ProductCodes")
public class ProductCodes implements Serializable {

    private static final long serialVersionUID = -8187081929977859601L;

    // Product brand code.
    private String brandCode;

    // Product code.
    private String productCode;

    // Product sku code.
    private String skuCode;

    // Combine product sku code.
    private String combineSkuCode;

    private List<SkuParam> skuList;//NOSONAR

	// Product tag code
    private String productTag;

    // Product category code list.
    private List<String> categoryCodes;

    // Product attribute information list.
    private List<ProductAttribute> attributes;

    @ApiModelProperty(value = "Product attribute information list.")
    private List<ProductAttribute> spuAttributes;

}
