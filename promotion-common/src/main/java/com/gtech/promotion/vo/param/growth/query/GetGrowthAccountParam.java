package com.gtech.promotion.vo.param.growth.query;

import java.io.Serializable;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import org.hibernate.validator.constraints.Range;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class GetGrowthAccountParam implements Serializable{

    private static final long serialVersionUID = 4003128923341198096L;

    @ApiModelProperty(value = "Domain code", required = true)
    @NotEmpty(message = "domainCode can not be empty")
    private String domainCode;

    @ApiModelProperty(value = "tenantCode", required = true)
    @NotEmpty(message = "tenantCode can not be empty")
    private String tenantCode;

    @ApiModelProperty(value = "Growth account type. (1-User 2-Organization)", required = true)
    @Range(min = 1, max = 2, message = "accountType is error")
    @NotNull(message = "accountType can not be null")
    private Integer accountType;

    @ApiModelProperty(value = "account code. (UserCode or OrgCode)", required = true)
    @NotEmpty(message = "accountCode can not be empty")
    private String accountCode;

    /**
     * Parameter validation.
     */
    public void validate() {
        // to do something
    }
}
