/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.vo.param.marketing;

import com.gtech.commons.page.PageParam;
import com.gtech.commons.utils.CheckUtils;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.api.ApiConstants;
import com.gtech.promotion.code.activity.ActivityStatusEnum;
import com.gtech.promotion.exception.ErrorCodes;
import com.gtech.promotion.utils.DateValidUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("MarketingQueryRequest")
public class MarketingQueryParam extends PageParam implements Serializable {

    private static final long serialVersionUID = -3856798448266805646L;

    @ApiModelProperty(value = "Domain code.",example = "D00001",required = true)
    private String domainCode;

    @ApiModelProperty(value = "Tenant code.",example = "T00001",required = true)
    private String tenantCode;

    @ApiModelProperty(value = "Store organization code. (Blank value means unlimited.)",required = true)
    private String orgCode;

    @ApiModelProperty(value = "Group code")
    private String groupCode;


    @ApiModelProperty(value = "Activity code")
    private String activityCode;

    @ApiModelProperty(value = "Activity type. 03-lucky draw 04 flash sale")
    private String activityType;
    @ApiModelProperty(value = "Activity ops type.  301-Slyder Adventures 302-Egg frenzy  401-flash sale")
    private String opsType;

    @ApiModelProperty(value= ApiConstants.ACTIVITY_STATUS)
    private String activityStatus;

    @ApiModelProperty(value = "Activity name")
    private String activityName;

    @ApiModelProperty(value = "Activity sponsors")
    private String sponsors;

    @ApiModelProperty(value = "Activity begin time from.")
    private String activityBeginFrom;

    @ApiModelProperty(value = "Activity begin time to.")
    private String activityBeginTo;

    @ApiModelProperty(value = "Activity end time from.")
    private String activityEndFrom;

    @ApiModelProperty(value = "Activity end time to.")
    private String activityEndTo;

    @ApiModelProperty(value = "warm begin time from")
    private String warmBeginFrom;

    @ApiModelProperty(value = "warm begin time to")
    private String warmBeginTo;

    @ApiModelProperty(value = "warm end time from")
    private String warmEndFrom;

    @ApiModelProperty(value = "warm end time to")
    private String warmEndTo;

    @ApiModelProperty(value = "luckyDrawRuleFlag")
    private String luckyDrawRuleFlag;
    @ApiModelProperty(value = "defaultFlag default = false")
    private Boolean defaultFlag = true;
    /**
     * Parameter validation.
     */
    public void validate() {

        CheckUtils.isNotBlank(this.domainCode, ErrorCodes.PARAM_EMPTY, "domainCode");
        CheckUtils.isNotBlank(this.tenantCode, ErrorCodes.PARAM_EMPTY, "tenantCode");
        CheckUtils.isNotBlank(this.orgCode, ErrorCodes.PARAM_EMPTY, "orgCode");

        CheckUtils.isTrue(StringUtil.isBlank(this.activityBeginFrom) || DateValidUtil.isValidDate(this.activityBeginFrom), ErrorCodes.PARAM_ERROR, "activityBeginFrom");
        CheckUtils.isTrue(StringUtil.isBlank(this.activityBeginTo) || DateValidUtil.isValidDate(this.activityBeginTo), ErrorCodes.PARAM_ERROR, "activityBeginTo");
        CheckUtils.isTrue(StringUtil.isBlank(this.activityEndFrom) || DateValidUtil.isValidDate(this.activityEndFrom), ErrorCodes.PARAM_ERROR, "activityEndFrom");
        CheckUtils.isTrue(StringUtil.isBlank(this.activityEndTo) || DateValidUtil.isValidDate(this.activityEndTo), ErrorCodes.PARAM_ERROR, "activityEndTo");

        CheckUtils.isTrue(StringUtil.isBlank(this.activityStatus) || ActivityStatusEnum.exist(activityStatus), ErrorCodes.PARAM_ERROR, "activityType");

        if (StringUtil.isNotBlank(this.activityBeginFrom) && StringUtil.isNotBlank(this.activityBeginTo)) {
            CheckUtils.isTrue(this.activityBeginFrom.compareTo(this.activityBeginTo) < 0, ErrorCodes.PARAM_SPECIFICATION_ERROR,
                    "activityBeginFrom must be earlier than activityBeginTo");
        }
        if (StringUtil.isNotBlank(this.activityEndFrom) && StringUtil.isNotBlank(this.activityEndTo)) {
            CheckUtils.isTrue(this.activityEndFrom.compareTo(this.activityEndTo) < 0, ErrorCodes.PARAM_SPECIFICATION_ERROR,
                    "activityEndFrom must be earlier than activityEndTo");
        }
    }
}
