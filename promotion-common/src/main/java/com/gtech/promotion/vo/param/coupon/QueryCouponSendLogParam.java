package com.gtech.promotion.vo.param.coupon;

import com.gtech.commons.page.PageParam;
import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.exception.ErrorCodes;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "QueryCouponSendLogRequest")
public class QueryCouponSendLogParam extends PageParam implements Serializable {

    private static final long serialVersionUID = -5066908595557951188L;

    @ApiModelProperty(value = "Tenant code", required = true)
    private String tenantCode;

    @ApiModelProperty(value = "release code", required = true)
    private String releaseCode;

    @ApiModelProperty(value = "activityCode code", required = true)
    private String activityCode;

    /**
     * Parameter validation.
     */
    public void validate() {
        CheckUtils.isNotBlank(this.tenantCode, ErrorCodes.PARAM_EMPTY, "tenantCode");
//        CheckUtils.isNotBlank(this.releaseCode, ErrorCodes.PARAM_EMPTY, "releaseCode")
        CheckUtils.isNotBlank(this.activityCode, ErrorCodes.PARAM_EMPTY, "activityCode");
    }

}
