package com.gtech.promotion.vo.param.marketing.flashsale;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.exception.ErrorCodes;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("FindGroupUserRequest")
public class FindGroupUserParam implements Serializable {

    private static final long serialVersionUID = -3565804759277866817L;

    @ApiModelProperty(value = "Domain code.",example = "D00001",required = true)
    private String domainCode;

    @ApiModelProperty(value = "Tenant code.",required = true)
    private String tenantCode;

    @ApiModelProperty(value = "Marketing group code.",example = "12556568987",required = true)
    private String marketingGroupCode;

    @ApiModelProperty(value = "user code.",example = "12556568987",required = true)
    private String userCode;

    @ApiModelProperty(value = "product code.",example = "1")
    private String productCode;

    @ApiModelProperty(value = "sku code.",example = "1")
    private String skuCode;


    public void validate() {

        CheckUtils.isNotBlank(this.domainCode, ErrorCodes.PARAM_EMPTY, "domainCode");
        CheckUtils.isNotBlank(this.tenantCode, ErrorCodes.PARAM_EMPTY, "tenantCode");
        CheckUtils.isNotBlank(this.marketingGroupCode, ErrorCodes.PARAM_EMPTY, "marketingGroupCode");
        CheckUtils.isNotBlank(this.userCode, ErrorCodes.PARAM_EMPTY, "userCode");

    }


}