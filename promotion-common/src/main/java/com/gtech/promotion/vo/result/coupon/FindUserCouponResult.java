/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.vo.result.coupon;

import com.gtech.promotion.api.ApiConstants;
import com.gtech.promotion.vo.bean.ActivityPeriod;
import com.gtech.promotion.vo.bean.ActivityStore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 
 *
 * <AUTHOR>
 * @Date 2020-02-08
 */
@Getter
@Setter
@ToString
@ApiModel("FindUserCouponResponse")
public class FindUserCouponResult implements Serializable {

    private static final long serialVersionUID = -1966858874981363216L;

    @ApiModelProperty(value = "Coupon activity name.")
    private String activityName;
    
    @ApiModelProperty(value = "Coupon activity label.")
    private String activityLabel;
    
    @ApiModelProperty(value = "Coupon activity code.")
    private String activityCode;

    @ApiModelProperty(value = "Coupon activity status.",example = ApiConstants.ACTIVITY_STATUS)
    private String activityStatus;

    @ApiModelProperty(value = "Coupon code.")
    private String couponCode;

    @ApiModelProperty(ApiConstants.COUPON_TYPE)
    private String couponType;

    @ApiModelProperty(value = "OPS type.201,202,203,204",example = "201")
    private String opsType;

    @ApiModelProperty(value = "Coupon face value.")
    private BigDecimal faceValue;
    
    @ApiModelProperty(ApiConstants.FACE_UNIT)
    private String faceUnit;
    
    @ApiModelProperty(ApiConstants.CONDITION_UNIT)
    private String conditionUnit;
    
    @ApiModelProperty(value = "Condition value")
    private BigDecimal conditionValue;
    
    @ApiModelProperty(value = "Activity URL")
    private String activityUrl;

    @ApiModelProperty(ApiConstants.COUPON_STATUS)
    private String status;

    @ApiModelProperty(value= "Frozen status：01-UnFrozen 02-Frozen")
    private String frozenStatus;
    
    @ApiModelProperty(value = "Received time")
    private String receivedTime;
    
    @ApiModelProperty(value = "Used time")
    private String usedTime;

    @ApiModelProperty(value = "Valid start time")
    private String validStartTime;

    @ApiModelProperty(value = "Valid end time")
    private String validEndTime;

    @ApiModelProperty(ApiConstants.TAKE_LABEL)
    private String takeLabel;

    @ApiModelProperty(value = "Coupon source")
    private String couponSource;

    @ApiModelProperty(value = "Order code")
    private String usedRefId;

    @ApiModelProperty(value = "Activity desc")
    private String activityDesc;
    
    @ApiModelProperty(ApiConstants.REWARD_TYPE)
    private String rewardType;
    
    @ApiModelProperty("List of stores related to the activity")
    private List<ActivityStore> stores;
    
    @ApiModelProperty("Activity Remark")
    private String activityRemark;

    @ApiModelProperty(value = "Activity period.")
    private ActivityPeriod activityPeriod;
}
