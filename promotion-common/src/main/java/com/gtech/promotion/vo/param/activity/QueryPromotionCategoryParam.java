package com.gtech.promotion.vo.param.activity;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.exception.ErrorCodes;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/5/25 10:13
 */
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("QueryPromotionCategoryRequest")
public class QueryPromotionCategoryParam implements Serializable {

    private static final long serialVersionUID = 1310709027970213811L;

    @ApiModelProperty(value = "Tenant code.",example = "T00001",required = true)
    private String tenantCode;

    @ApiModelProperty(value = "Promotion category",required = true)
    private String promotionCategory;

    /**
     * Parameter validation.
     */
    public void validate() {

        CheckUtils.isNotBlank(this.tenantCode, ErrorCodes.PARAM_EMPTY, "tenantCode");
        CheckUtils.isNotBlank(this.promotionCategory, ErrorCodes.PARAM_EMPTY, "promotionCategory");
    }

}
