package com.gtech.promotion.vo.param.point.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class PointAccountUniqueParam implements Serializable {

	private static final long serialVersionUID = 5825657452236955730L;

    @ApiModelProperty(value = "Domain code", required = true)
    @NotEmpty(message = "domainCode can not be empty")
    private String domainCode;

    @ApiModelProperty(value = "tenantCode", required = true)
    @NotEmpty(message = "tenantCode can not be empty")
    private String tenantCode;

	@ApiModelProperty(value = "pointAccountCode", required = true)
	@NotEmpty(message = "pointAccountCode can not be empty")
	private String pointAccountCode;

	@ApiModelProperty(value = "campaignCode", required = true)
	@NotEmpty(message = "campaignCode can not be empty")
	private String campaignCode;

	@EqualsAndHashCode(callSuper=false)
	@Data
	public static class PointAccountStatusUniqueVo extends PointAccountUniqueParam {

	    private static final long serialVersionUID = -8612717153118651895L;

	    @ApiModelProperty(value = "status", required = true)
		@NotNull(message = "status can not be null")
		private Integer status;

	    @ApiModelProperty(value = "oldStatus", required = true)
		@NotNull(message = "oldStatus can not be null")
		private Integer oldStatus;
	}

    /**
     * Parameter validation.
     */
    public void validate() {
		// to do something
    }
}
