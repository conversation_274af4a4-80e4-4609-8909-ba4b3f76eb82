package com.gtech.promotion.vo.param.growth;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.Date;

@Data
public class GrowthTransactionParam {

	/**
	 * Domain code.
	 */
	@ApiModelProperty(value = "Domain code", required = true)
	@NotEmpty(message = "domainCode can not be empty")
	private String domainCode;

	/**
	 * Tenant code.
	 */
	@ApiModelProperty(value = "Tenant code", required = true)
	@NotEmpty(message = "tenantCode can not be empty")
	private String tenantCode;

	/**
	 * Transaction serial number.
	 */
	@ApiModelProperty(value = "Transaction serial number.")
	private String transactionSn;

	/**
	 * Growth account code. (UserCode or OrgCode)
	 */
	@ApiModelProperty(value = "Growth account code. (UserCode or OrgCode)")
	private String accountCode;

	/**
	 * Growth account type. (1-User 2-Organization)
	 */
	@ApiModelProperty(value = "Growth account type. (1-User 2-Organization)")
	private Integer accountType;

	/**
	 * Growth transaction type. (1-Increase points 2-Deduct points)
	 */
	@ApiModelProperty(value = "Growth transaction type. (1-Increase points 2-Deduct points)")
	private Integer transactionType;

	/**
	 * Growth transaction remarks.
	 */
	@ApiModelProperty(value = "Growth transaction remarks.")
	private String transactionRemarks;

	/**
	 * Growth spent/earned in the transaction.
	 */
	@ApiModelProperty(value = "Growth spent/earned in the transaction.")
	private Integer transactionAmount;

	/**
	 * Before balance.
	 */
	@ApiModelProperty(value = "before balance.")
	private Integer beforeBalance;

	/**
	 * After balance.
	 */
	@ApiModelProperty(value = "After balance.")
	private Integer afterBalance;

	/**
	 * Transaction date. (yyyyMMddHHmmss)
	 */
	@ApiModelProperty(value = " Transaction date. (yyyyMMddHHmmss)")
	private Long transactionDate;

	/**
	 * Refer transaction serial number.
	 */
	@ApiModelProperty(value = "Refer transaction serial number.")
	private String referTransactionSn;

	/**
	 * Refer order number.
	 */
	@ApiModelProperty(value = "Refer order number.")
	private String referOrderNumber;

	/**
	 * Create time
	 */
	@ApiModelProperty(value = "create time")
	private Date createTime;

	/**
	 * Create user
	 */
	@ApiModelProperty(value = "create user")
	private String createUser;

    @ApiModelProperty(value = "Origin",example = "1-导入的数据；2-...")
    private Integer origin;

    /**
     * Parameter validation.
     */
    public void validate() {
        // to do something
    }

}