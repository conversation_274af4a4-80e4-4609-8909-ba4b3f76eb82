package com.gtech.promotion.vo.param.activity;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.checker.activity.TPromoActivityChecker;
import com.gtech.promotion.exception.Check;
import com.gtech.promotion.exception.ErrorCodes;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serializable;
import java.util.List;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/7/16 9:24
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel("GroupBindingActivityRequest")
public class GroupBindingActivityParam implements Serializable {

    private static final long serialVersionUID = -729540004764784421L;

    @ApiModelProperty(value = "租户编码", example = "880001", required = true)
    private String tenantCode;

    @ApiModelProperty(value = "Domain code",required = true)
    private String domainCode;

    @ApiModelProperty(value = "Activity codes", example = "自定义满减分组",required = true)
    private List<String> activityCodes;

    @ApiModelProperty(value = "Group code", example = "123",required = true)
    private String groupCode;


    public void checkParam(){

        CheckUtils.isNotBlank(this.tenantCode, ErrorCodes.PARAM_EMPTY, "tenantCode");

        CheckUtils.isNotBlank(this.domainCode, ErrorCodes.PARAM_EMPTY, "domainCode");



        CheckUtils.isNotBlank(this.groupCode, ErrorCodes.PARAM_EMPTY, "groupCode");

        Check.check(CollectionUtils.isEmpty(activityCodes), TPromoActivityChecker.NOT_NULL_ACTIVITY_CODE);

        for (String activityCode : activityCodes) {
            Check.check(StringUtil.isEmpty(activityCode), TPromoActivityChecker.NOT_NULL_ACTIVITY_CODE);
        }
        
    }

}
