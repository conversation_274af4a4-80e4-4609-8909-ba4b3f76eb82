/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.vo.param.marketing.flashsale;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.api.ApiConstants;
import com.gtech.promotion.code.marketing.ActivityTypeEnum;
import com.gtech.promotion.exception.ErrorCodes;
import com.gtech.promotion.vo.bean.ShoppingCartStore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 购物车查询计算的参数实体 属性有”租户信息“和”订单活动列表“以及“商品列表，商品里面包含其所属活动” 等
 */
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("FlashSaleOrderCalaRequest")
public class FlashSaleOrderCalaParam implements Serializable {

    private static final long serialVersionUID = 2986124484100533156L;

    @ApiModelProperty( value="Domain code.", example="D00001",required=true)
    private String domainCode;

    @ApiModelProperty( value="Tenant code.", example="T00001",required=true)
    private String tenantCode;

    @ApiModelProperty(value = "Flash sale activity code.", example = "87234943952324", required = true)
    private String activityCode;

    @ApiModelProperty(value = "Language id")
    private String language;

    @ApiModelProperty(value = "Member code",required = true)
    private String memberCode;

    @ApiModelProperty(value = ApiConstants.QUALIFICATIONS)
    private Map<String, List<String>> qualifications;

    @ApiModelProperty(value = "Cart item list by store.",required = true)
    private List<ShoppingCartStore> cartStoreList;

    @ApiModelProperty(value = "activity type")
    private String activityType = ActivityTypeEnum.FLASH_SALE.code();

    /**
     * Parameter validation.
     */
    public void validate() {

        CheckUtils.isNotBlank(this.domainCode, ErrorCodes.PARAM_EMPTY, "domainCode");
        CheckUtils.isNotBlank(this.tenantCode, ErrorCodes.PARAM_EMPTY, "tenantCode");
        CheckUtils.isNotBlank(this.memberCode, ErrorCodes.PARAM_EMPTY, "memberCode");
        CheckUtils.isNotBlank(this.activityCode, ErrorCodes.PARAM_EMPTY, "activityCode");

        CheckUtils.isTrue(!CollectionUtils.isEmpty(this.cartStoreList), ErrorCodes.PARAM_EMPTY, "cartStoreList");
        for(ShoppingCartStore cs : this.cartStoreList) {
            cs.validate();
        }
    }

}
