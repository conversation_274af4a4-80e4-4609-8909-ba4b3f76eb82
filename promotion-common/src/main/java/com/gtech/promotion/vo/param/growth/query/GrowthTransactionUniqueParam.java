package com.gtech.promotion.vo.param.growth.query;

import java.io.Serializable;

import javax.validation.constraints.NotEmpty;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class GrowthTransactionUniqueParam implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 5825657452236955730L;

	/**
	 * 积分流水代码
	 */
	@ApiModelProperty(value = "transactionSn", required = true)
	@NotEmpty(message = "transactionSn can not be empty")
	private String transactionSn;

	@ApiModelProperty(value = "tenantCode", required = true)
	@NotEmpty(message = "tenantCode can not be empty")
	private String tenantCode;

    /**
     * Parameter validation.
     */
    public void validate() {
		// to do something
    }

}
