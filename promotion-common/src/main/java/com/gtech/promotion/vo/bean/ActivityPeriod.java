package com.gtech.promotion.vo.bean;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.exception.ErrorCodes;
import com.gtech.promotion.utils.CronUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ActivityPeriod implements Serializable {

    private static final long serialVersionUID = -747929298594818203L;

    @ApiModelProperty(value = "Activity period begin. (corn expression)", example="0 0 12 * * ? *", required = true)
    private String beginPeriod;

    @ApiModelProperty(value = "Activity period end. (corn expression)", example="0 0 12 * * ? *", required = true)
    private String endPeriod;

    @ApiModelProperty(value = "Activity period interval week", example="1")
    private Integer intervalWeek;

    public void validate() {
        CheckUtils.isNotBlank(this.beginPeriod, ErrorCodes.PARAM_EMPTY, "beginPeriod");
        CheckUtils.isNotBlank(this.endPeriod, ErrorCodes.PARAM_EMPTY, "endPeriod");
        CheckUtils.isTrue(CronUtil.validate(beginPeriod), ErrorCodes.PARAM_SPECIFICATION_ERROR, "beginPeriod");
        CheckUtils.isTrue(CronUtil.validate(endPeriod), ErrorCodes.PARAM_SPECIFICATION_ERROR, "endPeriod");
        CheckUtils.isTrue(null == intervalWeek || intervalWeek > 0, ErrorCodes.PARAM_SPECIFICATION_ERROR, "intervalWeek");
    }

}
