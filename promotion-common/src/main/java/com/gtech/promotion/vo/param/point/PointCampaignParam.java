package com.gtech.promotion.vo.param.point;

import com.gtech.promotion.vo.bean.CampaignTitleLanguage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Collections;
import java.util.List;

@Data
@ApiModel(value = "PointCampaignRequest")
public class PointCampaignParam implements Serializable {

	private static final long serialVersionUID = -5545910437370478345L;
	/**
	 * Domain code.
	 */
	@ApiModelProperty(value = "Domain code", required = true)
	@NotEmpty(message = "domainCode can not be empty")
	private String domainCode;

	/**
	 * Tenant code.
	 */
	@ApiModelProperty(value = "Tenant code", required = true)
	@NotEmpty(message = "tenantCode can not be empty")
	private String tenantCode;
    /**
     * campaign code
     */
	@ApiModelProperty(value = "campaign code")
    private String campaignCode;

	/**
	 * program name
	 */
	@ApiModelProperty(value = "program name")
	private String programName;

    /**
     * campaign title
     */
	@ApiModelProperty(value = "campaign title")
    private String campaignTitle;

	/**
	 * campaign title
	 */
	@ApiModelProperty(value = "campaign title")
	private String campaignTitleLanguage;


	@ApiModelProperty(value = "RankName by language")
	private List<CampaignTitleLanguage> campaignTitleLanguages;

    /**
     * campaign desc
     */
	@ApiModelProperty(value = "campaign desc")
    private String campaignDesc;

    /**
     * Campaingn sponsor information.
     */
	@ApiModelProperty(value = "Campaingn sponsor information.")
    private String sponsor;

    /**
     * Point campaign begin time.
     */
	@ApiModelProperty(value = "Point campaign begin time.")
    private String beginTime;

    /**
     * Point campaign end time.
     */
	@ApiModelProperty(value = "Point campaign end time.")
    private String endTime;

    /**
     * Activity total points.
     */
	@ApiModelProperty(value = "Activity total points.", required = true)
	@NotNull(message = "totalPoints can not be null")
    private Integer totalPoints;

    /**
     * Activity remaining points.
     */
	@ApiModelProperty(value = "Activity remaining points.")
    private Integer remainingPoints;

    /**
     * campaignstatus.( 0-inactive 1-active)
     */
	@ApiModelProperty(value = "campaignstatus.( 0-inactive 1-active)")
	private Integer status;


	/**
	 * create user.
	 */
	@ApiModelProperty(value = "Create user.")
	private String createUser;


	public List<CampaignTitleLanguage> getLanguageList() {
		if (CollectionUtils.isEmpty(campaignTitleLanguages)){
			campaignTitleLanguages = Collections.emptyList();
		}
		return campaignTitleLanguages;
	}





	/**
     * Parameter validation.
     */
    public void validate() {
		// to do something

    }
}