/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.vo.param.activity;

import com.gtech.commons.page.PageParam;
import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.exception.ErrorCodes;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serializable;
import java.util.List;

/**
 * QueryListParam
 *
 */
@Getter
@Setter
@ToString
@ApiModel("QueryListRequest")
public class QueryListParam extends PageParam implements Serializable {

    private static final long serialVersionUID = 5173678198258870668L;

    @ApiModelProperty(value = "Domain code",required = true)
    private String domainCode;

    @ApiModelProperty(value = "Tenant code",required = true)
    private String tenantCode;

    @ApiModelProperty( value="orgCode.", example="default",required=true)
    private String orgCode;

    @ApiModelProperty(value = "Activity type List：(type:01-Activity 02-Coupon 03-lucky draw 04-flash sale)")
    private List<String> activityTypes;

    @ApiModelProperty(value = "Activity name.")
    private String activityName;

    @ApiModelProperty(value = "Activity code.")
    private String activityCode;

    @ApiModelProperty(value = "warm begin time from")
    private String warmBeginFrom;

    @ApiModelProperty(value = "warm begin time to")
    private String warmBeginTo;

    @ApiModelProperty(value = "warm end time from")
    private String warmEndFrom;

    @ApiModelProperty(value = "warm end time to")
    private String warmEndTo;

    /**
     * Parameter validation.
     */
    public void validate() {

        CheckUtils.isNotBlank(this.domainCode, ErrorCodes.PARAM_EMPTY, "domainCode");
        CheckUtils.isNotBlank(this.tenantCode, ErrorCodes.PARAM_EMPTY, "tenantCode");
        if (CollectionUtils.isEmpty(activityTypes)) {
            activityTypes = null;
        }

    }
}
