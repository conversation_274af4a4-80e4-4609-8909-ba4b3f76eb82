package com.gtech.promotion.vo.result.purchaseconstraint;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.util.List;

@ToString
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@ApiModel("CalAvailableQtyByProductListResponse")
public class CalAvailableQtyByProductListResult implements Serializable {

    private static final long serialVersionUID = -1100832434278447410L;
    @ApiModelProperty(value = "Sku code")
    private String spuCode;
    @ApiModelProperty(value = "Spu code")
    private String skuCode;

    @ApiModelProperty(value = "calAvailableQtyResultList")
    List<CalAvailableQtyResult> calAvailableQtyResultList;



}
