package com.gtech.promotion.vo.result.coupon;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/12/22 13:57
 */
@Data
public class ExportCouponRelationResult implements Serializable {

    private static final long serialVersionUID = 8147761283231813893L;

    private String maxCouponCode;

    private String tenantCode;

    private String activityCode;

    private String userCode;

    private String account;

    private String orderCode;

    private String couponCode;

    private String status;


    // 可用开始时间(yyyyMMddhhmmss)
    private String receiveStartTime;

    // 可用开始时间(yyyyMMddhhmmss)
    private String receiveEndTime;

    // 可用开始时间(yyyyMMddhhmmss)
    private String validStartTime;

    // 可用结束时间(yyyyMMddhhmmss)
    private String validEndTime;

    private Integer validDays;
}
