package com.gtech.promotion.vo.result.coupon;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2021/3/29 17:25
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ExportCouponUserDetailResult implements Serializable {
    private static final long serialVersionUID = 4355344036272923736L;


    private String id;

    private String orderCode;

    private String userCode;

    private String couponCode;

    private String status;


}
