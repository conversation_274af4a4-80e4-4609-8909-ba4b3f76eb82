package com.gtech.promotion.vo.param.activity;


import com.gtech.commons.utils.CheckUtils;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.code.marketing.ActivityTypeEnum;
import com.gtech.promotion.exception.ErrorCodes;
import com.gtech.promotion.vo.bean.ProductAttribute;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serializable;
import java.util.List;

/**
 * 根据商品查询促销列表
 * 默认查询营销+促销
 *
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("QueryMarketingActivityListByProductListRequest")
public class QueryPromotionOrMarketingNoFilterParam implements Serializable {


    private static final long serialVersionUID = 617256235121327047L;



    @ApiModelProperty( value="Domain code.", example="DC0001",required=true)
    private String domainCode;

    @ApiModelProperty( value="Tenant code.", example="TC0001",required=true)
    private String tenantCode;
    @ApiModelProperty(value = "Language id")
    private String language;

    @ApiModelProperty(value = "activity type", example = "[01,02,03,04,05,06,07]")
    private List<String> activityTypeList;

    @ApiModelProperty(value = "product list", example = "[123,123]")
    private List<Product> productList;



    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Product implements Serializable {

        private static final long serialVersionUID = 7643926171938415849L;

        @ApiModelProperty(value = "Product category code list.")
        private List<String> categoryCodes;

        @ApiModelProperty(value = "Product brand code. If combineSkuCode is empty, brandCode can not be empty")
        private String brandCode;

        @ApiModelProperty(value = "Product tag.")
        // Product tag code
        private String productTag;
        @ApiModelProperty(value = "Combine sku code.")
        private String combineSkuCode;

        @ApiModelProperty(value = "Attributes.")
        // Product attribute information list.
        private List<ProductAttribute> attributes;

        @ApiModelProperty(value = "Product attribute information list.")
        private List<ProductAttribute> spuAttributes;

        @ApiModelProperty(value = "Product code. If combineSkuCode is empty, productCode can not be empty")
        private String productCode;

        @ApiModelProperty(value = "Product sku code.")
        private String skuCode;

        @ApiModelProperty(value = "Org code.",required = true)
        private String orgCode;


        public void validate() {
            //product或者sku必须有一个
            CheckUtils.isTrue(StringUtil.isNotBlank(this.productCode) || StringUtil.isNotBlank(this.skuCode), ErrorCodes.PARAM_EMPTY, "productCode or skuCode");
            CheckUtils.isNotBlank(this.orgCode, ErrorCodes.PARAM_EMPTY, "orgCode");
        }
    }

    public void validate() {

        CheckUtils.isNotBlank(this.domainCode, ErrorCodes.PARAM_EMPTY, "domainCode");
        CheckUtils.isNotBlank(this.tenantCode, ErrorCodes.PARAM_EMPTY, "tenantCode");
        CheckUtils.isTrue(CollectionUtils.isNotEmpty(productList), ErrorCodes.PARAM_EMPTY, "productList");
        if (CollectionUtils.isEmpty(this.activityTypeList)) {
            //默认营销+促销
            activityTypeList = ActivityTypeEnum.getCodes();
            activityTypeList.addAll(com.gtech.promotion.code.activity.ActivityTypeEnum.getCodes());
        }
        for (QueryPromotionOrMarketingNoFilterParam.Product product : productList) {
            product.validate();
        }

    }


}
