package com.gtech.promotion.vo.param.activity;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.exception.ErrorCodes;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/7/16 16:22
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("GroupSettingPriorityRequest")
public class GroupSettingPriorityParam {

    @ApiModelProperty(value = "tenant code", example = "880001", required = true)
    private String tenantCode;

    @ApiModelProperty(value = "domain code", example = "880001", required = true)
    private String domainCode;

    @ApiModelProperty(value = "group code list", required = true)
    private List<ActivityGroupParam> groups;


    public void validate(){

        CheckUtils.isNotBlank(this.tenantCode, ErrorCodes.PARAM_EMPTY, "tenantCode");
        CheckUtils.isNotBlank(this.domainCode, ErrorCodes.PARAM_EMPTY, "domainCode");
        CheckUtils.isTrue(CollectionUtils.isNotEmpty(groups), ErrorCodes.GROUP_CODE_INTEGER, "groups");

        for (ActivityGroupParam group : groups) {
            CheckUtils.isNotBlank(group.getGroupCode(), ErrorCodes.PARAM_EMPTY, "groupCode");
        }

    }

}
