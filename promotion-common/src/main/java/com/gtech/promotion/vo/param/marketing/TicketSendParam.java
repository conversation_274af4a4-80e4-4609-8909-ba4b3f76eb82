package com.gtech.promotion.vo.param.marketing;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.checker.marketing.MarketingChecker;
import com.gtech.promotion.code.coupon.CouponFrozenStatusEnum;
import com.gtech.promotion.exception.Check;
import com.gtech.promotion.exception.ErrorCodes;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class TicketSendParam implements Serializable {

    private static final long serialVersionUID = 1263853628185588082L;

    @ApiModelProperty(value = "Domain code.",example = "D00001",required = true)
    private String domainCode;

    @ApiModelProperty(value = "Tenant code.",required = true)
    private String tenantCode;

    @ApiModelProperty(value = "Org code.",required = true)
    private String orgCode;

    @ApiModelProperty(value="Activity code",required=true)
    private String activityCode;

    @ApiModelProperty(value="Member code list, memberCode/tagCode/mobile list must have one")
    private List<String> memberCodes;

    @ApiModelProperty(value="Member tag list, memberCode/tagCode/mobile list must have one")
    private List<String> memberTagCodes;

    @ApiModelProperty(value="Member mobile list, memberCode/tagCode/mobile list must have one")
    private List<String> memberMobiles;

    @ApiModelProperty(value = "Frozen status 01-UnFrozen 02-Frozen")
    String frozenStatus;

    @ApiModelProperty(value="Quality each member",required=true)
    private Integer quality;

    public void validate() {
        CheckUtils.isNotBlank(this.domainCode, ErrorCodes.PARAM_EMPTY, "domainCode");
        CheckUtils.isNotBlank(this.tenantCode, ErrorCodes.PARAM_EMPTY, "tenantCode");
        CheckUtils.isNotBlank(this.orgCode, ErrorCodes.PARAM_EMPTY, "orgCode");
        CheckUtils.isNotBlank(this.activityCode, ErrorCodes.PARAM_EMPTY, "activityCode");
        Check.check(CollectionUtils.isEmpty(this.memberCodes)
                && CollectionUtils.isEmpty(this.memberTagCodes)
                && CollectionUtils.isEmpty(this.memberMobiles), MarketingChecker.TICKET_SEND_NO_MEMBER);
        CheckUtils.isTrue(null != this.quality, ErrorCodes.PARAM_EMPTY, "quality");
        CheckUtils.isTrue(0 < this.quality, ErrorCodes.PARAM_ERROR, "quality");
        if (StringUtil.isNotBlank(frozenStatus)){
            CheckUtils.isTrue(CouponFrozenStatusEnum.exist(frozenStatus), ErrorCodes.PARAM_ERROR, "frozenStatus");
        }
    }
}
