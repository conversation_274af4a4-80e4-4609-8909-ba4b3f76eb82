package com.gtech.promotion.vo.param.activity;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.code.ErrorCodes;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 商品活动匹配参数
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("MatchProductsToActivitiesRequest")
public class MatchProductsToActivitiesParam implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "Domain code", example = "DC0001", required = true)
    private String domainCode;

    @ApiModelProperty(value = "Tenant code", example = "100000", required = true)
    private String tenantCode;

    @ApiModelProperty(value = "Member code")
    private String memberCode;

    @ApiModelProperty(value = "Language id")
    private String language;

    @ApiModelProperty(value = "Org codes")
    private List<String> orgCodes;

    @ApiModelProperty(value = "Qualifications")
    private Map<String, List<String>> qualifications;

    @ApiModelProperty(value = "Promotion time")
    private String promotionTime;

    @ApiModelProperty(value = "Product list", required = true)
    private List<ProductInfo> products;

    @ApiModelProperty(value = "Activity codes to filter (optional, if empty will match all activities)")
    private List<String> activityCodes;

    /**
     * 商品信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel("ProductInfo")
    public static class ProductInfo implements Serializable {

        private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "Product code", required = true)
        private String productCode;

        @ApiModelProperty(value = "SKU code", required = true)
        private String skuCode;

        @ApiModelProperty(value = "Product price")
        private BigDecimal productPrice;

        @ApiModelProperty(value = "Quantity", example = "1")
        private Integer quantity;

        public void validate() {
            CheckUtils.isNotBlank(productCode, ErrorCodes.PARAM_EMPTY, "productCode");
            CheckUtils.isNotBlank(skuCode, ErrorCodes.PARAM_EMPTY, "skuCode");
        }
    }

    public void validate() {
        CheckUtils.isNotBlank(domainCode, ErrorCodes.PARAM_EMPTY, "domainCode");
        CheckUtils.isNotBlank(tenantCode, ErrorCodes.PARAM_EMPTY, "tenantCode");
        CheckUtils.isTrue(CollectionUtils.isNotEmpty(products), ErrorCodes.PARAM_EMPTY, "products");
        
        // 验证每个商品信息
        for (ProductInfo product : products) {
            product.validate();
        }
    }
}
