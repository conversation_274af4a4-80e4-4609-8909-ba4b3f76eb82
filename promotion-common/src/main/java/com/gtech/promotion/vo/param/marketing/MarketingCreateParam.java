package com.gtech.promotion.vo.param.marketing;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.promotion.api.ApiConstants;
import com.gtech.promotion.checker.activity.TPromoActivityChecker;
import com.gtech.promotion.code.activity.SelectorProductTypeEnum;
import com.gtech.promotion.code.marketing.ActivityOpsTypeEnum;
import com.gtech.promotion.code.marketing.ActivityTypeEnum;
import com.gtech.promotion.exception.Check;
import com.gtech.promotion.exception.ErrorCodes;
import com.gtech.promotion.utils.DateValidUtil;
import com.gtech.promotion.vo.bean.ActivityPeriod;
import com.gtech.promotion.vo.bean.ExtImage;
import com.gtech.promotion.vo.bean.marketing.MarketingLanguage;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MarketingCreateParam implements Serializable {

    private static final BigDecimal HUNDRED = new BigDecimal("100");

    private static final long serialVersionUID = -3573626362263013524L;

    @ApiModelProperty(value = "Domain code.",example = "D00001",required = true)
    private String domainCode;

    @ApiModelProperty(value = "Tenant code.",required = true)
    private String tenantCode;

    @ApiModelProperty(value = "org code.",required = true)
    private String orgCode;

    @ApiModelProperty(value = "Activity type. 03:lucky draw 04:flash sale 05:pre-sale,06-group",required = true)
    private String activityType;

    @ApiModelProperty(value = "Activity type. 301-Slyder Adventures 302-Egg frenzy  303-Speed Dial 401-flash sale",required = true)
    private String opsType;

    @ApiModelProperty(value = "group code. default = opsType")
    private String groupCode = opsType;

    @ApiModelProperty(value = "Activity name",required = true)
    private String activityName;

    @ApiModelProperty(value = "Activity begin",required = true)
    private String activityBegin;

    @ApiModelProperty(value = "Activity end",required = true)
    private String activityEnd;

    @ApiModelProperty(value = "Activity url")
    private String activityUrl;

    @ApiModelProperty(value = "Activity sponsors",required = true)
    private String sponsors;

    @ApiModelProperty(value = "User code of the operator.", example="U00001", required = true)
    private String operateUser;

    @ApiModelProperty(value = "Activity background image.")
    private String backgroundImage;

    @ApiModelProperty(value = "Multilingual attributes.",required = true)
    private List<MarketingLanguage> marketingLanguages;

    @ApiModelProperty(value = "Activity cool down time.")
    private String coolDown;

    @ApiModelProperty(value = "Activity warm-up begin time.")
    private String warmBegin;

    @ApiModelProperty(value = "Activity warm-up end time. is warmBegin not blank, warmEnd same as activityBegin")
    private String warmEnd;

    @ApiModelProperty(value = "User last name of the operator.")
    private String operateLastName;

    @ApiModelProperty(value = "User first name of the operator.")
    private String operateFirstName;

    @ApiModelProperty(value = "Activity period.")
    private ActivityPeriod activityPeriod;

    @ApiModelProperty(value = ApiConstants.INCENTIVE_LIMITED_FLAG)
    private String incentiveLimitedFlag;

    @ApiModelProperty(value = "luckyDrawRuleFlag")
    private String luckyDrawRuleFlag;

    @ApiModelProperty("sku 01 spu 02. default 01")
    private String selectProductType = SelectorProductTypeEnum.SELECT_SKU.code();

    @ApiModelProperty(value = "ext images")
    private List<ExtImage> extImages;


    public void validate() {


        CheckUtils.isNotBlank(this.domainCode, ErrorCodes.PARAM_EMPTY, "domainCode");
        CheckUtils.isNotBlank(this.tenantCode, ErrorCodes.PARAM_EMPTY, "tenantCode");
        CheckUtils.isNotBlank(this.orgCode, ErrorCodes.PARAM_EMPTY, "orgCode");
        CheckUtils.isNotBlank(this.activityType, ErrorCodes.PARAM_EMPTY, "activityType");
        CheckUtils.isNotBlank(this.opsType, ErrorCodes.PARAM_EMPTY, "opsType");
        CheckUtils.isNotBlank(this.activityName, ErrorCodes.PARAM_EMPTY, "activityName");
        CheckUtils.isNotBlank(this.activityBegin, ErrorCodes.PARAM_EMPTY, "activityBegin");
        CheckUtils.isNotBlank(this.activityEnd, ErrorCodes.PARAM_EMPTY, "activityEnd");
        CheckUtils.isNotBlank(this.sponsors, ErrorCodes.PARAM_EMPTY, "sponsors");
        CheckUtils.isNotBlank(this.operateUser, ErrorCodes.PARAM_EMPTY, "operateUser");
        CheckUtils.isTrue(!CollectionUtils.isEmpty(marketingLanguages), ErrorCodes.PARAM_EMPTY, "marketingLanguages");

        // 时间查询条件验证
        CheckUtils.isTrue(DateValidUtil.isValidDate(this.activityBegin), ErrorCodes.PARAM_ERROR, "activityBegin");
        CheckUtils.isTrue(DateValidUtil.isValidDate(this.activityEnd), ErrorCodes.PARAM_ERROR, "activityEnd");

        long activityBeginAsLong = Long.parseLong(this.activityBegin);
        long activityEndAsLong = Long.parseLong(this.activityEnd);
        long current = Long.parseLong(DateUtil.getCurrentDateAsString(DateUtil.FORMAT_YYYYMMDDHHMISS_14));

        Check.check(activityBeginAsLong > activityEndAsLong, TPromoActivityChecker.ACTIVITY_BEGIN_END_TIME);
        Check.check(activityEndAsLong <= current, TPromoActivityChecker.ACTIVITY_END_BEGIN_TIME);

        CheckUtils.isTrue(ActivityTypeEnum.exist(this.activityType), ErrorCodes.PARAM_ERROR, "activityType");
        CheckUtils.isTrue(ActivityOpsTypeEnum.exist(this.opsType), ErrorCodes.PARAM_ERROR, "opsType");

        CheckUtils.isTrue(SelectorProductTypeEnum.exist(this.selectProductType), ErrorCodes.PARAM_ERROR, "selectProductType");

        for (MarketingLanguage marketingLanguage : marketingLanguages) {
            marketingLanguage.validate();
        }

        if (null != activityPeriod) {
            activityPeriod.validate();
        }
    }
}
