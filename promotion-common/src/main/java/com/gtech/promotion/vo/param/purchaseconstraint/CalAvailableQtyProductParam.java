package com.gtech.promotion.vo.param.purchaseconstraint;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.exception.ErrorCodes;
import com.gtech.promotion.vo.bean.ProductAttribute;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.util.List;

@Data
@ToString
@ApiModel("CalAvailableQtyProduct")
public class CalAvailableQtyProductParam {
    private List<String> categories;// 此list为categoryPath 格式A1>B1>C 如果以后需要时 新增List<String> categoryCodes
    @ApiModelProperty(value = "Product category code list.")
    private List<String> categoryCodes;
    @ApiModelProperty(value = "Product category name.")
    private List<String> categoryNames;
    @ApiModelProperty(value = "Product brand code.")
    private String brandCode;
    @ApiModelProperty(value = "Product brand name.")
    private String brandName;
    @ApiModelProperty(value = "SKU attribute information list.")
    private List<ProductAttribute> attributes; // sku属性
    @ApiModelProperty(value = "Product code.")
    private String productCode;
    @ApiModelProperty(value = "Product name.")
    private String productName;
    @ApiModelProperty(value = "Product sku code.")
    private String skuCode;
    @ApiModelProperty(value = "Product sku name.")
    private String skuName;

    @ApiModelProperty(value = "Product main product number.")
    private String mainProductNo;

    @ApiModelProperty(value = "Product attribute information list.")
    private List<ProductAttribute> spuAttributes; // spu属性，用于核算促销价时，处理通过spu属性标记的活动

    @ApiModelProperty(value = "Product tag code.")
    private String productTag;

    public void validate(){
        CheckUtils.isNotBlank(this.productCode, ErrorCodes.PARAM_EMPTY, "productCode");
    }
}
