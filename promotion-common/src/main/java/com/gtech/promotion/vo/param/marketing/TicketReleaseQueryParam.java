package com.gtech.promotion.vo.param.marketing;

import com.gtech.commons.page.PageParam;
import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.exception.ErrorCodes;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TicketReleaseQueryParam extends PageParam implements Serializable {

    private static final long serialVersionUID = -9136161853249956267L;
    @ApiModelProperty(value = "Domain code.",example = "D00001",required = true)
    private String domainCode;

    @ApiModelProperty(value = "Tenant code.",required = true)
    private String tenantCode;

    @ApiModelProperty(value = "Org code.",required = true)
    private String orgCode;

    @ApiModelProperty(value="Activity code",required=true)
    private String activityCode;

    public void validate() {
        CheckUtils.isNotBlank(this.domainCode, ErrorCodes.PARAM_EMPTY, "domainCode");
        CheckUtils.isNotBlank(this.tenantCode, ErrorCodes.PARAM_EMPTY, "tenantCode");
        CheckUtils.isNotBlank(this.orgCode, ErrorCodes.PARAM_EMPTY, "orgCode");
        CheckUtils.isNotBlank(this.activityCode, ErrorCodes.PARAM_EMPTY, "activityCode");
    }
}
