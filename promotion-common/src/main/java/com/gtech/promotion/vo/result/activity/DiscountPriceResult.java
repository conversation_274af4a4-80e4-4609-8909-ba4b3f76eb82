package com.gtech.promotion.vo.result.activity;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class DiscountPriceResult implements Serializable {

    private static final long serialVersionUID = 6222469763407213883L;

    @ApiModelProperty(value = "discount price item list.")
    private List<DiscountPriceItemResult> discountPriceItemList;
}
