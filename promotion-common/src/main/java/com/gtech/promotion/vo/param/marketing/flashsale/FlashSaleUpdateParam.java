package com.gtech.promotion.vo.param.marketing.flashsale;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.exception.ErrorCodes;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FlashSaleUpdateParam extends FlashSaleCreateParam implements Serializable {

    private static final long serialVersionUID = 4039072003226122010L;
    @ApiModelProperty(value = "activity code.",example = "12556568987",required = true)
    private String activityCode;

    @Override
    public void validate() {

        CheckUtils.isNotBlank(this.activityCode, ErrorCodes.PARAM_EMPTY, "activityCode");
        super.validate();
    }
}
