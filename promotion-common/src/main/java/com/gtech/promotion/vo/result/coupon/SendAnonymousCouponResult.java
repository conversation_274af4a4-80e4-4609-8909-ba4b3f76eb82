package com.gtech.promotion.vo.result.coupon;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2022/11/7 11:18
 */
@Data
public class SendAnonymousCouponResult implements Serializable {

    private static final long serialVersionUID = -86256270161445577L;
    @ApiModelProperty(value = "Coupon code.")
    private String couponCode;

    // 可用开始时间(yyyyMMddhhmmss)
    @ApiModelProperty(value = "Valid start time.")
    private String validStartTime;

    // 可用结束时间(yyyyMMddhhmmss)
    @ApiModelProperty(value = "Valid end time.")
    private String validEndTime;
}
