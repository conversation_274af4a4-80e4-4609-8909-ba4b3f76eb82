package com.gtech.promotion.vo.param.purchaseconstraint;

import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.checker.purchaseconstraint.PurchaseConstraintChecker;
import com.gtech.promotion.exception.Check;
import com.gtech.promotion.vo.bean.PurchaseConstraintPriority;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

@Data
@ToString
@ApiModel("UpdatePurchaseConstraintPriorityRequest")
public class UpdatePurchaseConstraintPriorityParam {
    @ApiModelProperty(value="Tenant code",required=true)
    private String tenantCode;

    @ApiModelProperty(value="Purchase constraint priority list",required=true)
    private List<PurchaseConstraintPriority> purchaseConstraintPriorityList;

    @ApiModelProperty(value = "User code of the operator.", example="U00001", required = true)
    private String operateUser;

    @ApiModelProperty(value = "User last name of the operator.")
    private String operateLastName;

    @ApiModelProperty(value = "User first name of the operator.")
    private String operateFirstName;

    public void validate(){
        Check.check(StringUtil.isBlank(tenantCode), PurchaseConstraintChecker.NOT_NULL_TENANT_CODE);
        Check.check(CollectionUtils.isEmpty(purchaseConstraintPriorityList),
                                                PurchaseConstraintChecker.NOT_EMPTY_PURCHASE_CONSTRAINT_PRIORITY);

        for(PurchaseConstraintPriority purchaseConstraintPriority : purchaseConstraintPriorityList){
            purchaseConstraintPriority.validate();
        }
    }
}
