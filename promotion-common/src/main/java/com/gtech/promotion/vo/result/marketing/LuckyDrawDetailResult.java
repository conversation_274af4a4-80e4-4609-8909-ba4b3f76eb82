package com.gtech.promotion.vo.result.marketing;

import com.gtech.promotion.api.ApiConstants;
import com.gtech.promotion.vo.bean.ActivityPeriod;
import com.gtech.promotion.vo.bean.IncentiveLimited;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class LuckyDrawDetailResult implements Serializable {

    private static final long serialVersionUID = -3283799111316112799L;

    @ApiModelProperty(value = "Domain code.",example = "D00001",required = true)
    private String domainCode;

    @ApiModelProperty(value = "Tenant code.",required = true)
    private String tenantCode;

    @ApiModelProperty(value = "org code.",required = true)
    private String orgCode;

    @ApiModelProperty(value = "group code.")
    private String groupCode;

    @ApiModelProperty(value = "activity code.",example = "12556568987",required = true)
    private String activityCode;

    // 活动类型: 301-大转盘 302-砸金蛋
    @ApiModelProperty(value = "opsType 301-Slyder Adventures 302-Egg frenzy  303-Speed Dial")
    private String opsType;

    @ApiModelProperty(value = "Activity type. 01-Slyder Adventures 02-Egg frenzy",required = true)
    private String activityType;

    @ApiModelProperty(value = "Activity name",required = true)
    private String activityName;

    @ApiModelProperty(value = ApiConstants.ACTIVITY_STATUS,required = true)
    private String activityStatus;

    @ApiModelProperty(value = "Activity begin",required = true)
    private String activityBegin;

    @ApiModelProperty(value = "Activity end",required = true)
    private String activityEnd;

    @ApiModelProperty(value = "Activity url")
    private String activityUrl;

    @ApiModelProperty(value = "Activity sponsors",required = true)
    private String sponsors;

    @ApiModelProperty(value = "Activity label.", required = true)
    private String activityLabel;

    @ApiModelProperty(value = "Activity desc.")
    private String activityDesc;

    @ApiModelProperty(value = "Activity short desc.")
    private String activityShortDesc;

    @ApiModelProperty(value = "prize list.",required = true)
    private List<LuckyDrawPrizeResult> prizes;

    @ApiModelProperty(value = "Activity period.")
    private ActivityPeriod activityPeriod;

    @ApiModelProperty(value = "Activity background image.")
    private String backgroundImage;

    @ApiModelProperty(value = "Incentive limited list. It can't be empty while incentiveLimitedFlag is '01'.",required = true)
    private List<IncentiveLimited> incentiveLimiteds;
}
