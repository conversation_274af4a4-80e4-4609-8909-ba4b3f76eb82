package com.gtech.promotion.vo.result.growth;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

@Getter
@Setter
@ToString
public class GrowthTransactionResult implements Serializable {

    private static final long serialVersionUID = 2482556440481846923L;

    @ApiModelProperty(value = "Tenant code")
    private String tenantCode;

	@ApiModelProperty(value = "Transaction serial number.")
	private String transactionSn;

	@ApiModelProperty(value = "Growth account code. (UserCode or OrgCode)")
	private String accountCode;

	@ApiModelProperty(value = "Growth account type. (1-User 2-Organization)")
	private Integer accountType;

	@ApiModelProperty(value = "Growth transaction type. (1-Increase points 2-Deduct points)")
	private Integer transactionType;

	@ApiModelProperty(value = "Growth transaction remarks.")
	private String transactionRemarks;

	@ApiModelProperty(value = "Growth spent/earned in the transaction.")
	private Integer transactionAmount;

	@ApiModelProperty(value = " Transaction date. (yyyyMMddHHmmss)")
	private String transactionDate;

	@ApiModelProperty(value = "Refer transaction serial number.")
	private String referTransactionSn;

	@ApiModelProperty(value = "Refer order number.")
	private String referOrderNumber;

	@ApiModelProperty(value = "Before balance.")
	private Integer beforeBalance;

	@ApiModelProperty(value = "After balance.")
	private Integer afterBalance;

	@ApiModelProperty(value = "Create time")
	private Date createTime;

	@ApiModelProperty(value = "Create time")
	private String createTimeString;

	@ApiModelProperty(value = "Create user")
	private String createUser;

	@ApiModelProperty(value = "Update time")
	private Date updateTime;

    @ApiModelProperty(value = "Create time")
    private String updateTimeString;

    @ApiModelProperty(value = "Origin",example = "1-导入的数据；2-...")
    private Integer origin;
}