/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.vo.bean;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.checker.activity.GiveawayChecker;
import com.gtech.promotion.code.activity.GiveawayTypeEnum;
import com.gtech.promotion.exception.Check;
import com.gtech.promotion.exception.ErrorCodes;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * ActivityGift
 */
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("Giveaway")
public class Giveaway implements Serializable {

    private static final long serialVersionUID = -5269174358794690756L;

    @ApiModelProperty(value = "giveaway code.",required = true)
    private String giveawayCode;

    @ApiModelProperty(value = "giveaway name.",required = true)
    private String giveawayName;

    @ApiModelProperty(value = "giveaway quantity.",required = true)
    private Integer giveawayNum;

    @ApiModelProperty(value = "Giveaway type. 1:sku 2:coupon 3:custom product",required = true)
    private Integer giveawayType;

    @ApiModelProperty(value = "OpsType. 201-product Discount,202-order discount(coupon),203- order discount(code),204- voucher")
    private String opsType;

    @ApiModelProperty(value = "Level (numbers 1-99)",required = true)
    private Integer rankParam;
    /**
     * 赠品序号
     */
    private Integer giveawaySort;

    public void validate(){
        CheckUtils.isTrue(null != this.giveawayType, ErrorCodes.PARAM_EMPTY, "giveawayType");
        CheckUtils.isTrue(GiveawayTypeEnum.exist(String.valueOf(giveawayType)), ErrorCodes.PARAM_ERROR, "giveawayType");
        if (GiveawayTypeEnum.CUSTOM_PRODUCT.code().equals(String.valueOf(giveawayType))){
            giveawayCode = "CUSTOM_PRODUCT";
            giveawayName = "CUSTOM_PRODUCT";
            giveawayNum = 1;
        }
        CheckUtils.isNotBlank(this.giveawayCode, ErrorCodes.PARAM_EMPTY, "giveawayCode");
        CheckUtils.isNotBlank(this.giveawayName, ErrorCodes.PARAM_EMPTY, "giveawayName");
        Check.check(giveawayName.length() > 256, GiveawayChecker.TOO_LONG_SKU_NAME);
        CheckUtils.isTrue(null != this.giveawayNum, ErrorCodes.PARAM_EMPTY, "giveawayNum");
        CheckUtils.isTrue(this.giveawayNum >= 0, ErrorCodes.PARAM_ERROR, "giveawayNum");

    }
}
