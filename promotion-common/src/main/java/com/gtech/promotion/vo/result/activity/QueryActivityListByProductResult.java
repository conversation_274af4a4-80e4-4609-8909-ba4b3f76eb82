/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.vo.result.activity;

import com.gtech.promotion.api.ApiConstants;
import com.gtech.promotion.vo.bean.ActivityPeriod;
import com.gtech.promotion.vo.bean.FunctionParam;
import com.gtech.promotion.vo.bean.Giveaway;
import com.gtech.promotion.vo.bean.Qualification;
import com.gtech.promotion.vo.result.flashsale.MarketingLanguageResult;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 根据商品查询促销列表出参实体(出参)
 */
@Getter
@Setter
@ToString
@ApiModel("QueryActivityListByProductResponse")
public class QueryActivityListByProductResult implements Serializable {

    private static final long serialVersionUID = -5362972009823135022L;

    @ApiModelProperty(value = "Promo price.")
    private String promoPrice;

    @ApiModelProperty(value = "Activity type：01-Activity 02-Coupon")
    private String activityType;

    @ApiModelProperty(value = "Activity code.")
    private String activityCode;

    @ApiModelProperty(value = "Activity name.")
    private String activityName;

    @ApiModelProperty(value = "Group code.")
    private String groupCode;

    @ApiModelProperty(value = "Activity status.")
    private String activityStatus;

    @ApiModelProperty(value = "Activity description.")
    private String activityDesc;

    @ApiModelProperty("Activity remark")
    private String activityRemark;//

    @ApiModelProperty("Activity label")
    private String activityLabel;//

    @ApiModelProperty(ApiConstants.ACTIVITY_SORT)
    private String activitySort;//

    @ApiModelProperty(value = "Custom conditions.")
    private String customCondition;

    @ApiModelProperty("Activity begin")
    private String activityBegin;//

    @ApiModelProperty("Activity end")
    private String activityEnd;//

    @ApiModelProperty("create time")
    private Date createTime;//

    @ApiModelProperty(value = "Activity warm-up begin time.")
    private String warmBegin;

    @ApiModelProperty(value = "Activity cool down time.")
    private String coolDown;

    @ApiModelProperty(value = "ops create Activity type")
    private String opsType;

    // Member level codes list.
    @ApiModelProperty(value = "Qualification list.")
    private List<Qualification> qualifications;

    /**
     * @deprecated
     */
    @ApiModelProperty(value = "Deprecated. Member level codes list.")
    @Deprecated
    private List<String> memberLevelCodes; //NOSONAR

    /**
     * @deprecated
     */
    @ApiModelProperty(value = "Deprecated. Member label codes list.")
    @Deprecated
    private List<String> memberLabelCodes; //NOSONAR

    @ApiModelProperty(value = ApiConstants.PROMO_SCOPE)
    private String promoScope;//

//    预热结束时间
    @ApiModelProperty("Warm end")
    private String warmEnd;//

    @ApiModelProperty("Activity URL")
    private String activityUrl;//

    @ApiModelProperty(ApiConstants.PRODUCT_SELECTION_TYPE)
    private String productSelectionType;

    @ApiModelProperty(value = "Product item scope type: 1-All scope 2-By spu in scope")
    private String itemScopeType;

    //赠品赠送最大限制数量
    @ApiModelProperty("Maximum number of gifts")
    private String giftLimitMax;//

    @ApiModelProperty("Giveaways")
    private List<Giveaway> giveaways;

    //商品池序号
    @ApiModelProperty("Seq num")
    private String seqNum;

    @ApiModelProperty(value = "Activity background image.")
    private String backgroundImage;

    @ApiModelProperty(value = "Ribbon image.")
    private String ribbonImage;

    @ApiModelProperty(value = "Position of ribbon image .")
    private String ribbonPosition;

    @ApiModelProperty(value = "Ribbon text.")
    private String ribbonText;

    @ApiModelProperty(value = "Activity period.")
    private ActivityPeriod activityPeriod;

    @ApiModelProperty(value = "Priority.")
    private Integer priority;

    @ApiModelProperty(value = "Parameters list of the activity template function.")
    private List<FunctionParam> funcParams;

    @ApiModelProperty(value = "Languages activity.")
    private List<MarketingLanguageResult> languageResults;
}
