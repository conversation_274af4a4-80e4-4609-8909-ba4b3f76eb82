package com.gtech.promotion.vo.bean.marketing.flashsale;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.exception.ErrorCodes;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("FlashSaleLanguage")
public class FlashSaleLanguage implements Serializable {

    private static final long serialVersionUID = -5560200398456475717L;
    @ApiModelProperty(value = "Activity name.")
    private String activityName;

    @ApiModelProperty(value = "Activity label.")
    private String activityLabel;

    @ApiModelProperty(value = "Activity desc.")
    private String activityDesc;

    @ApiModelProperty(value = "Activity short desc.")
    private String activityShortDesc;

    @ApiModelProperty(value = "Language id. (en-US/id-ID/zh-CN/...)", required = true)
    private String language;

    public void validate() {
        CheckUtils.isNotBlank(this.language, ErrorCodes.PARAM_EMPTY, "language");
    }
}
