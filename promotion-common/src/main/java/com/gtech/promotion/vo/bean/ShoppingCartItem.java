/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.vo.bean;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.api.ApiConstants;
import com.gtech.promotion.exception.ErrorCodes;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;


/**
 * 
 *
 * <AUTHOR>
 * @Date 2020-02-13
 */
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("ShoppingCartItem")
public class ShoppingCartItem implements Serializable {

    private static final long serialVersionUID = -7732244140096712209L;

    // Product category code list.
    @ApiModelProperty(value = "Product category code list.")
    private List<String> categoryCodes;

    @ApiModelProperty(value = "Product brand code.")
    private String brandCode;

	@ApiModelProperty(value = "SKU attribute information list.")
    private List<ProductAttribute> attributes;

	@ApiModelProperty(value = "Product attribute information list.")
	private List<ProductAttribute> spuAttributes;

    @ApiModelProperty(value = "Product tag code.")
    private String productTag;

    @ApiModelProperty(value = "Product code.")
    private String productCode;

    @ApiModelProperty(value = "Product sku code.Separate multiple commas")
    private String skuCode;

    @ApiModelProperty(value = ApiConstants.COMBINESKUCODE)
    private String combineSkuCode;

    @ApiModelProperty(value = "Product original price.",required = true)
    private BigDecimal productPrice;

    @ApiModelProperty(value = "Product original list price.")
    private BigDecimal productListPrice;

    @ApiModelProperty(value = "Product quantity.",required = true)
    private Integer quantity;

    @ApiModelProperty(value = ApiConstants.SELECT_FLAG,required = true)
    private String selectionFlag;




    public BigDecimal getProductAmount() {

        if (null == this.quantity || null == this.productPrice) {
            return BigDecimal.ZERO;
        }

        return new BigDecimal(this.quantity).multiply(this.productPrice);
    }

	public List<ProductAttribute> getAttributes() {
		List<ProductAttribute> list = new ArrayList<>();
		if (!CollectionUtils.isEmpty(this.attributes)) {
			list.addAll(this.attributes);
		}
		if (!CollectionUtils.isEmpty(this.spuAttributes)) {
			list.addAll(this.spuAttributes);
		}
		return list;
	}

    /**
     * Parameter validation.
     */
    public void validate() {
        CheckUtils.isNotBlank(this.selectionFlag, ErrorCodes.PARAM_EMPTY, "selectionFlag");

        CheckUtils.isNotNull(this.productPrice, ErrorCodes.PARAM_EMPTY, "productPrice");
        CheckUtils.isNotNull(this.quantity, ErrorCodes.PARAM_EMPTY, "quantity");

        CheckUtils.isTrue(this.quantity > 0, ErrorCodes.PARAM_ERROR, "quantity");
        CheckUtils.isTrue(this.productPrice.compareTo(BigDecimal.ZERO) >= 0, ErrorCodes.PARAM_ERROR, "productPrice");


    }


}
