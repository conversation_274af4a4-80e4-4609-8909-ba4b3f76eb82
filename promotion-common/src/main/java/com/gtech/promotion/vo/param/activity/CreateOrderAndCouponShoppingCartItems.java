package com.gtech.promotion.vo.param.activity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2021/6/15 11:03
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("CreateOrderAndCouponShoppingCartItemsRequest")
public class CreateOrderAndCouponShoppingCartItems implements Serializable {

    private static final long serialVersionUID = -5956245073625133964L;
    // Product code.
    @ApiModelProperty(value = "Product code.", required = true)
    private String productCode;

    @ApiModelProperty(value = "skuCode." ,required = true)
    private String skuCode;

}
