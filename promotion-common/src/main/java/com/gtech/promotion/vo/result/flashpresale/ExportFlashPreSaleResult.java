package com.gtech.promotion.vo.result.flashpresale;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/12/26 13:55
 */
@Data
public class ExportFlashPreSaleResult implements Serializable {

    private static final long serialVersionUID = -1676244961469144986L;

//   日期 订单总数 已付款订单 订单总金额 已付款订单总金额

    private String date;

    private Integer totalOfOrders;

    private Long ofPaidOrders;

    private Long ofCancelledOrders;

    private Double totalAmount;

    private Double amountOfPaidOrders;

    private String maxOrderCode;

}
