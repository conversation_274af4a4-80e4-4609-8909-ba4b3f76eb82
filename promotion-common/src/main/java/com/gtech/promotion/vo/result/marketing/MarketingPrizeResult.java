package com.gtech.promotion.vo.result.marketing;

import com.gtech.promotion.vo.bean.marketing.MarketingPrize;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MarketingPrizeResult extends MarketingPrize {

    private static final long serialVersionUID = 3258730029372308327L;
    @ApiModelProperty(value = "Prize no", required = true)
    private String prizeNo;
}
