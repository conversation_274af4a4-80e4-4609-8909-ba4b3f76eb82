package com.gtech.promotion.vo.param.activity;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("QueryAfterDiscountPriceRequest")
public class QueryAfterDiscountPriceParam  implements Serializable {
    private String tenantCode;

    private String orgCode;

    private String language;

    private List<QueryAfterDiscountItemParam> itemList;
}
