/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WA<PERSON>RANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.vo.result.coupon;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * FindCouponReleaseResult
 *
 * <AUTHOR>
 * @Date 2020-02-17
 */
@Getter
@Setter
@ToString
@ApiModel("FindCouponReleaseResponse")
public class FindCouponReleaseResult implements Serializable {

    private static final long serialVersionUID = 3442110891061739354L;

    @ApiModelProperty(value = "Activity code.",required = true)
    private String activityCode;

    @ApiModelProperty(value = "Coupon type:  01-General coupon 02-Anonymous coupon 03-Single fixed promotion code.",required = true)
    private String couponType;

    @ApiModelProperty(value = "Coupon release code.",required = true)
    private String releaseCode;

    @ApiModelProperty(value = "Coupon release status: 01-Waiting released 02-Already released 03-Imported 04-Cancel released",required = true)
    private String releaseStatus;

    @ApiModelProperty(value = "Release coupon quantity: releaseQuantity fixed 1 while couponType=03",required = true)
    private String releaseQuantity;

    @ApiModelProperty(value = "Remaining available stock.")
    private String inventory;

    @ApiModelProperty(value = "User received coupon quantity.")
    private String receivedQuantity;

    @ApiModelProperty(value = "User used coupon quantity.")
    private String usedTotal;

    @ApiModelProperty(value = "User locked coupon quantity.")
    private String locked;

    @ApiModelProperty(value = "Receive coupon start time: yyyyMMddhhmmss",required = true)
    private String receiveStartTime;

    @ApiModelProperty(value = "Receive coupon end time: yyyyMMddhhmmss",required = true)
    private String receiveEndTime;

    @ApiModelProperty(value = "Validity days from received date.",required = false)
    private String validDays;

    @ApiModelProperty(value = "Validity date begin: yyyyMMddhhmmss",required = false)
    private String validStartTime;

    @ApiModelProperty(value = "Validity date end: yyyyMMddhhmmss",required = false)
    private String validEndTime;

    @ApiModelProperty(value = "Appointment time of coupon release: yyyyMMddhhmmss, required while releaseType=02",required = false)
    private String releaseTime;

    @ApiModelProperty(value = "时间同步活动时间 0-不同步 1-同步",required = false)
    private String timeSameActivity;

    @ApiModelProperty(value = "领取时间是否与活动时间相同 0:否 1:是",required = false)
    private String receiveTimeSameActivity;

    @ApiModelProperty(name = "couponRuleType, default 01 (couponType 为 01或02; 03忽略该字段. )",value = "券码生成规则 01 数字 02 数字和字母")
    private String couponRuleType;

    @ApiModelProperty(value = "The promotion type")
    private String promotionType;

    @ApiModelProperty(value = "The remark")
    private String remark;

    @ApiModelProperty(value = "Create time",required = false)
    private Date createTime;




}
