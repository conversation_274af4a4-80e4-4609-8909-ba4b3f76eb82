package com.gtech.promotion.vo.result.boostsharding;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel(value = "ExportBoostSharingDetailResponse")
@Data
public class ExportBoostSharingDetailResult implements Serializable {

    private static final long serialVersionUID = 5214726434812995036L;
    /*发起分享的ID
        发起分享的时间
        发起人会员账号
        发起人昵称
        发起人手机号
        助力时间
        助力人会员账号
        助力人昵称
        助力人手机号*/
    @ApiModelProperty(value = "Sharing record code")
    private String sharingRecordCode;
    @ApiModelProperty(value = "Sharing time")
    private String sharingTime;
    @ApiModelProperty(value = "Sharing member code")
    private String sharingMemberCode;
    @ApiModelProperty(value = "Sharing member name")
    private String sharingMemberName;
    @ApiModelProperty(value = "Sharing member phone")
    private String sharingMemberPhone;
    @ApiModelProperty(value = "Boost time")
    private String boostTime;
    @ApiModelProperty(value = "Boost member code")
    private String boostMemberCode;
    @ApiModelProperty(value = "Boost member name")
    private String boostMemberName;
    @ApiModelProperty(value = "Boost member phone")
    private String boostMemberPhone;




}
