package com.gtech.promotion.vo.result.point;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

@Getter
@Setter
@ToString
public class PointAccountCampaignResult implements Serializable {

    private static final long serialVersionUID = -7215249207371277089L;

    @ApiModelProperty(value = "Tenant code")
    private String tenantCode;

    @ApiModelProperty(value = "Point account code.")
    private String pointAccountCode;

    @ApiModelProperty(value = "account code. (UserCode or OrgCode)")
    private String accountCode;

    @ApiModelProperty(value = "Point account type. (1-User 2-Organization)")
    private Integer accountType;

    @ApiModelProperty(value = "Point account description.")
    private String accountDesc;

    @ApiModelProperty(value = "Campaign balance list.")
    private List<CampaignBalance> campaignBalances;

    @ApiModelProperty(value = "Point account status.(0-Inactive 1-Active)")
    private Integer status;

    @ApiModelProperty(value = "Extends parameters. (JSON String)")
    private String extParams;

    @ApiModelProperty(value = "Expiring days")
    private Long expiringDays;

    @ApiModel(value = "Campaign balance")
    @Data
    public static class CampaignBalance implements Serializable{

        private static final long serialVersionUID = -2180592806203421218L;
        @ApiModelProperty(value = "Campaign code")
        private String campaignCode;

        @ApiModelProperty(value = "Account balance")
        private Integer accountBalance;

        @ApiModelProperty(value = "Expiring point")
        private Integer expiringPoint;

    }
}