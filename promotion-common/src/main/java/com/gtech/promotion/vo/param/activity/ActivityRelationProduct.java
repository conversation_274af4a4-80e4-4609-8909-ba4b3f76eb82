package com.gtech.promotion.vo.param.activity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2021/6/15 11:09
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel("ActivityRelationProductRequest")
public class ActivityRelationProduct implements Serializable {

    private static final long serialVersionUID = 9043609424449272561L;

    @ApiModelProperty(value = "Activity code.",required = true)
    private String activityCode;

    @ApiModelProperty(value = "Total discount amount (including promoRewardPostage) ")
    private BigDecimal promoRewardAmount;

    @ApiModelProperty(value = "Total amount of postage deduction")
    @Builder.Default
    private BigDecimal promoRewardPostage = BigDecimal.ZERO;

    @ApiModelProperty(value = "WinTogether shopping cart items.",required = true)
    private List<CreateOrderAndCouponShoppingCartItems> shoppingCartItems;

}
