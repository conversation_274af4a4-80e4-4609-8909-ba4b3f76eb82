package com.gtech.promotion.vo.bean;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.promotion.exception.ErrorCodes;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@ApiModel("PurchaseConstraintPriority")
public class PurchaseConstraintPriority {
    @ApiModelProperty(value="Purchase constraint code",required=true)
    private String purchaseConstraintCode;

    @ApiModelProperty(value= "Purchase constraint priority",required=true)
    private Integer purchaseConstraintPriority;

    public void validate(){
        CheckUtils.isNotBlank(this.purchaseConstraintCode, ErrorCodes.PARAM_EMPTY, "purchaseConstraintCode");
        CheckUtils.isNotNull(this.purchaseConstraintPriority, ErrorCodes.PARAM_EMPTY, "purchaseConstraintPriority");
    }
}
