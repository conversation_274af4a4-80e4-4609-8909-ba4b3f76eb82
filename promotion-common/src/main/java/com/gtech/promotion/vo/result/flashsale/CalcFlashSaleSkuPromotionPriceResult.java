/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.vo.result.flashsale;

import com.gtech.promotion.vo.bean.ActivityPeriod;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 单品活动活动价出参对象
 */
@Getter
@Setter
@ToString
@ApiModel("CalcFlashSaleSkuPromotionPriceResponse")
public class CalcFlashSaleSkuPromotionPriceResult implements Serializable {

    private static final long serialVersionUID = -5443508116298038834L;
    @ApiModelProperty(value = "Activity code.", example = "32424234234")
    private String activityCode;

    @ApiModelProperty(value = "03-lucky draw 04-flash sale")
    private String activityType;

    @ApiModelProperty(value = "Activity name.", example = "activity")
    private String activityName;

    @ApiModelProperty("Activity start time. (yyyyMMddHHmmss)")
    private String activityStartTime;

    @ApiModelProperty("Activity end time. (yyyyMMddHHmmss)")
    private String activityEndTime;

    @ApiModelProperty(value = "Promotion price", example = "80.00")
    private BigDecimal promotionPrice;

    @ApiModelProperty(value = "Activity warm-up begin time.")
    private String warmBegin;

    @ApiModelProperty(value = "Activity cool down time.")
    private String coolDown;

    @ApiModelProperty(value = "ops create Activity type")
    private String opsType;

    @ApiModelProperty(value = "sku inventory", example = "1000")
    private int inventory;

    @ApiModelProperty(value = "Max per user qty", example = "2")
    private int maxPerUser;

    @ApiModelProperty(value = "Background image")
    private String backgroundImage;

    @ApiModelProperty(value = "Ribbon image")
    private String ribbonImage;

    @ApiModelProperty(value = "Ribbon position")
    private String ribbonPosition;

    @ApiModelProperty(value = "Ribbon text")
    private String ribbonText;

    @ApiModelProperty(value = "Activity sponsors")
    private String sponsors;

    @ApiModelProperty(value = "Activity period.")
    private ActivityPeriod activityPeriod;

    @ApiModelProperty(value = "pre sale pay type")
    private String preSalePayType;

    @ApiModelProperty(value = "shipping time")
    private String shippingTime;

    @ApiModelProperty(value = "import no")
    private String importNo;

    @ApiModelProperty(value = "sku code")
    private String skuCode;
}