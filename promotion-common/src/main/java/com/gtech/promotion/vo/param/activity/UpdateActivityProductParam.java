/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.vo.param.activity;

import com.gtech.commons.utils.CheckUtils;
import com.gtech.commons.utils.Joiner;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.api.ApiConstants;
import com.gtech.promotion.checker.activity.TPromoProductChecker;
import com.gtech.promotion.code.PromotionConstants;
import com.gtech.promotion.code.activity.FuncTypeEnum;
import com.gtech.promotion.code.activity.ItemScopeTypeEnum;
import com.gtech.promotion.code.activity.ProductSelectionEnum;
import com.gtech.promotion.code.activity.ProductTypeEnum;
import com.gtech.promotion.exception.Check;
import com.gtech.promotion.exception.ErrorCodes;
import com.gtech.promotion.vo.bean.ProductAttribute;
import com.gtech.promotion.vo.bean.ProductDetail;
import com.gtech.promotion.vo.bean.ProductScope;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;

@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("UpdateActivityProductRequest")
public class UpdateActivityProductParam implements Serializable{

    private static final long serialVersionUID = -5049130707590042983L;
    @ApiModelProperty( value="Domain code.", example="DC0001",required=true)
    private String domainCode;

    @ApiModelProperty( value="Tenant code.", example="TC0001",required=true)
    private String tenantCode;

    @ApiModelProperty( value="orgCode.", example="default",required=true)
    private String orgCode;

    @ApiModelProperty(value = "Activity code.", required=true)
    private String activityCode;

    @ApiModelProperty(value = "Template code.",required = true)
    private String templateCode;

    @Builder.Default
    @ApiModelProperty(value = ApiConstants.PRODUCT_SELECTION_TYPE)
    private String productSelectionType = ProductSelectionEnum.SELECT.code();
    public void setProductSelectionType(String productSelectionType) {
        this.productSelectionType = StringUtils.isBlank(productSelectionType) ? ProductSelectionEnum.SELECT.code() : productSelectionType;
    }

    @Builder.Default
    @ApiModelProperty(value = "Product item scope type: 1-All scope 2-By spu in scope")
    private String itemScopeType = ItemScopeTypeEnum.ALL_SCOPE.code();
    public void setItemScopeType(String itemScopeType) {
        this.itemScopeType = StringUtils.isBlank(itemScopeType) ? ItemScopeTypeEnum.ALL_SCOPE.code() : itemScopeType;
    }

    @ApiModelProperty(value = ApiConstants.PRODUCT_TYPE,required = true)
    private String conditionProductType;
    public void setProductType(String productType) {
        this.conditionProductType = productType;
    }

    @ApiModelProperty(value = ApiConstants.PRODUCT_TYPE)
    private String incentiveProductType;

    @ApiModelProperty(value = "Product scope list. seqNum equals 1 means condition product. seqNum equals 2 means incentive product.")
    private List<ProductScope> products;

    @ApiModelProperty(value = "Product detail list. seqNum equals 1 means condition product. seqNum equals 2 means incentive product.")
    private List<ProductDetail> productDetails;

    @ApiModelProperty(value = "Product detail list. seqNum equals 1 means condition product. seqNum equals 2 means incentive product.")
    private List<ProductDetail> productDetailBlackList;

    @ApiModelProperty(value = "User code of the operator.", example="U00001")
    private String operateUser;

    @ApiModelProperty(value = "User last name of the operator.")
    private String operateLastName;

    @ApiModelProperty(value = "User first name of the operator.")
    private String operateFirstName;

    @ApiModelProperty(value = ApiConstants.PRODUCT_CONDITION)
    private String productCondition;
    /**
     * Parameter validation.
     */
    public void validate() {

        CheckUtils.isNotBlank(this.domainCode, ErrorCodes.PARAM_EMPTY, "domainCode");
        CheckUtils.isNotBlank(this.tenantCode, ErrorCodes.PARAM_EMPTY, "tenantCode");
        CheckUtils.isNotBlank(this.activityCode, ErrorCodes.PARAM_EMPTY, "activityCode");
        CheckUtils.isNotBlank(this.templateCode, ErrorCodes.PARAM_EMPTY, "templateCode");

        CheckUtils.isTrue(ProductSelectionEnum.exist(this.productSelectionType), ErrorCodes.PARAM_ERROR, "productSelectionType");

        // Validate the product condition
        CheckUtils.isNotBlank(this.conditionProductType, ErrorCodes.PARAM_EMPTY, "conditionProductType");
        CheckUtils.isTrue(ProductTypeEnum.exist(this.conditionProductType), ErrorCodes.PARAM_ERROR, "conditionProductType");
        if (StringUtils.isNotBlank(this.incentiveProductType)) {
            CheckUtils.isTrue(ProductTypeEnum.exist(this.incentiveProductType), ErrorCodes.PARAM_ERROR, "incentiveProductType");
        }

        this.validate0411();

        this.checkProduct();
    }

    private void validate0411() {
        if (!FuncTypeEnum.IncentiveEnum.EACH_FIXED_MONEY.equalsCode(this.templateCode.substring(12, 16))){
            return;
        }
        CheckUtils.isTrue(ProductSelectionEnum.SELECT.equalsCode(this.productSelectionType), ErrorCodes.PARAM_ERROR, "productSelectionType");
        Check.check(!ProductTypeEnum.CUSTOM_PRODUCT.equalsCode(this.getConditionProductType()), TPromoProductChecker.ERROR_PRODUCT_TYPE_EACH_PRICE);//每个不同特价活动商品范围只能是指定商品
    }

    public boolean isAllProducts() {
        
        return CollectionUtils.isEmpty(products) && CollectionUtils.isEmpty(productDetails);
    }

    // 商品校验
    private void checkProduct(){

        //反选时
        if (ProductSelectionEnum.INVERT_SELECT.equalsCode(this.productSelectionType)){
            Check.check(this.isAllProducts(), TPromoProductChecker.ERROR_PRODUCT_SELECT_ALL);//不能反选全部商品
        }

        if (this.isAllProducts()) {
            return;
        }
        boolean seq1 = false;
        if (CollectionUtils.isNotEmpty(this.products)) {
            this.checkProductScopes(products);
            seq1 = products.stream().anyMatch(x->x.getSeqNum() == 1);
        }
        if (CollectionUtils.isNotEmpty(this.productDetails)) {
            seq1 = seq1 || productDetails.stream().anyMatch(x -> null != x.getSeqNum() && 1 == x.getSeqNum());
        }

        if (!seq1){ // 如果没有商品池1， 添加一个全分类的商品池1
            if (null == products){
                products = new ArrayList<>();
            }
            products.add(ProductScope.builder().categoryCode(PromotionConstants.UNLIMITED).build());
        }
    }

    public void checkProductAll(ProductScope x){
        Check.check(null == x.getSeqNum(), TPromoProductChecker.NOT_NULL_PRODUCT_SEQ);
        Check.check(x.getSeqNum() < 1 || x.getSeqNum() > 2, TPromoProductChecker.TOO_BIG_SKUNUM);//买A优惠B seqNum只能为1或2  1=A;2=B
    }

    /**
     * 各商品池不能有一摸一样的商品范围
     */
    public void checkProductScopes(List<ProductScope> products){

        HashSet<String> seqNumProducts = new HashSet<>();
        for (ProductScope product : products){

            addIfNullProduct(product);

            String[] categoryCode = product.getCategoryCode().split(",");
            String[] brandCode = product.getBrandCode().split(",");
            String[] attributeCodes = new String[0];

            List<ProductAttribute> productAttributes = product.getAttributes();
            if (CollectionUtils.isNotEmpty(productAttributes)) {
                attributeCodes = productAttributes.stream().map(m -> (m.getAttributeCode() + ":" + m.getAttributeValues())).collect(Collectors.toList()).toArray(attributeCodes);
            }
            Arrays.sort(categoryCode);
            Arrays.sort(brandCode);
            Arrays.sort(attributeCodes);

            Joiner joiner = Joiner.on(",");
            seqNumProducts.add(joiner.appendTo(joiner.appendTo(joiner.appendTo(new StringBuilder(), categoryCode), brandCode), attributeCodes).toString());
        }
        Check.check(products.size() > seqNumProducts.size(), TPromoProductChecker.ERROR_PRODUCT_SCOPE);
    }

    private void addIfNullProduct(ProductScope product){
        if (StringUtil.isBlank(product.getCategoryCode())){
            product.setCategoryCode(PromotionConstants.UNLIMITED);
        }
        if (StringUtil.isBlank(product.getBrandCode())){
            product.setBrandCode(PromotionConstants.UNLIMITED);
        }
        List<ProductAttribute> productAttributes = product.getAttributes();
        if (CollectionUtils.isNotEmpty(productAttributes)) {
            productAttributes.forEach(attr -> {
                if (StringUtil.isBlank(attr.getAttributeCode())) {
                    attr.setAttributeCode(PromotionConstants.UNLIMITED);
                }
                if (StringUtil.isBlank(attr.getAttributeValues())) {
                    attr.setAttributeValues(PromotionConstants.UNLIMITED);
                }
            });
        }
    }

}