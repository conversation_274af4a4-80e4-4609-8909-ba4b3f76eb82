/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.vo.result.coupon;

import com.gtech.promotion.vo.bean.ActivityPeriod;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;


/**   
 */
@Getter
@Setter
@ToString
@ApiModel("FilterCouponsByCartResponse")
public class FilterCouponsByCartResult implements Serializable {
	
    private static final long serialVersionUID = 4854400167034991071L;

    @ApiModelProperty(value = "Coupon code.")
    private String couponCode;

    @ApiModelProperty(value = "Coupon type:  01-General coupon 02-Anonymous coupon 03-Single fixed promotion code.")
    private String couponType;

    @ApiModelProperty(value = "Coupon status: 1-Useable, 2-Used, 3-Expired. Empty means query all coupons")
    private String couponStatus;
    
    @ApiModelProperty(value = "Valid use begin time.")
    private String validBegin;

    @ApiModelProperty(value = "Valid use end time.")
    private String validEnd;

    @ApiModelProperty(value = "Whether the coupon has reward flag.")
    private Boolean isReward;

    @ApiModelProperty(value = "Activity code")
    private String activityCode;
    
    @ApiModelProperty(value = "Activity name")
    private String activityName;
    
    @ApiModelProperty(value = "Activity label")
    private String activityLabel;

    @ApiModelProperty(value = "Activity description")
    private String activityDesc;

    @ApiModelProperty(value = "Activity short description")
    private String activityShortDesc;

    @ApiModelProperty(value = "User limit max count.")
    private Integer userlimitMax;

    @ApiModelProperty(value = "Coupon face value")
    private BigDecimal faceValue;
    
    @ApiModelProperty(value = "Coupon face value unit: 01-Amount 02-Discount")
    private String faceUnit;
    
    @ApiModelProperty(value = "Coupon use condition value unit: 01-Amount 02-Quantity")
    private String conditionUnit;
    
    @ApiModelProperty(value = "Condition value.")
    private BigDecimal conditionValue;

    @ApiModelProperty(value = "Incentive type: 01-减金额 02-打折扣 03-单件固定金额 04-组合固定金额 05-包邮 06-送赠品 07-买A送A 08-买A送B 12-买A减价B 13-买A打折B")
    private String rewardType;

    @ApiModelProperty(value = "Promotion match failed reason message.")
    private String falseReason;

    @ApiModelProperty(value = "Promotion match failed reason code.")
    private String falseCode;

    @ApiModelProperty(value = "Product type.")
    private String productType;

    @ApiModelProperty(value = "Activity period.")
    private ActivityPeriod activityPeriod;

}
