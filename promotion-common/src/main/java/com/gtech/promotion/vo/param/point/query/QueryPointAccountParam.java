package com.gtech.promotion.vo.param.point.query;

import com.gtech.promotion.page.RequestPage;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = false)
public class QueryPointAccountParam extends RequestPage {
	/**
	 * 
	 */
	private static final long serialVersionUID = 7853385907080119486L;

	/**
	 * Domain code.
	 */
	@ApiModelProperty(value = "Domain code")
	private String domainCode;

	/**
	 * Tenant code.
	 */
	@ApiModelProperty(value = "Tenant code", required = true)
	private String tenantCode;

	/**
	 * Point account code.
	 */
	@ApiModelProperty(value = "Point account code.")
	private String pointAccountCode;

	/**
	 * Point account description.
	 */
	@ApiModelProperty(value = "Point account description.")
	private String accountDesc;

	/**
	 * account code. (UserCode or OrgCode)
	 */
	@ApiModelProperty(value = "account code. (UserCode or OrgCode)")
	private String accountCode;

	/**
	 * Point account type. (1-User 2-Organization)
	 */
	@ApiModelProperty(value = "Point account type. (1-User 2-Organization)")
	private Integer accountType;

	/**
	 * Account balance
	 */
	@ApiModelProperty(value = "Account balance")
	private Integer accountBalance;

	/**
	 * Point account status.(0-Inactive 1-Active)
	 */
	@ApiModelProperty(value = "Point account status.(0-Inactive 1-Active)")
	private Integer status;

    /**
     * Parameter validation.
     */
    public void validate() {
		// to do something
    }

}