package com.gtech.promotion.vo.result.coupon;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2021/7/5 10:59
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "QueryCouponUsedListResponse")
public class QueryCouponUsedListResult implements Serializable {
    private static final long serialVersionUID = -5801790483047890227L;

    //优惠券信息：编号、类别、名称（即创建优惠券时基础信息的促销名称）、状态、时间日期
    //使用人信息：获得优惠券的人、手机号码、邮箱、获取时间、Org Code
    //使用订单信息：订单号、订单时间

    @ApiModelProperty(value = "Activity name",example = "activity")
    private String activityName;

    @ApiModelProperty(value = "Activity code",example = "1413134123")
    private String activityCode;

    @ApiModelProperty(value = "Coupon code",example = "1341413414")
    private String couponCode;

    //01：优惠券 02:匿名券 03：优惠码
    @ApiModelProperty(value = "Coupon type:01-General coupon 02-Anonymous coupon 03-Single fixed promotion code.",example = "01")
    private String couponType;

    //1-未发放 02-已发放 03-已使用 04-已锁定 05-已过期
    @ApiModelProperty(value = "Coupon status: 1-Did not receive 02-Have to receive 03-Used 04-Locked 05-Expired. Empty means query all coupons.")
    private String status;

    @ApiModelProperty(value="create time.Time to get the coupon.")
    private Date createTime;

    @ApiModelProperty(value="User code.")
    private String userCode;

    @ApiModelProperty(value="User name")
    private String userName;

    @ApiModelProperty(value="Last name")
    private String lastName;

    @ApiModelProperty(value="First name")
    private String firstName;

    @ApiModelProperty(value="Valid start time.20210425103227")
    private String validStartTime;

    @ApiModelProperty(value="Valid end time.20210425103227")
    private String validEndTime;

    @ApiModelProperty(value = "Org code.")
    private String ordCode;

    @ApiModelProperty(value = "Mobile.")
    private String mobile;

    @ApiModelProperty(value = "User email.")
    private String email;

    //USED_REF_ID
    @ApiModelProperty(value = "OrderNo.")
    private String usedRefId;

    @ApiModelProperty(value = "TakeLabel.")
    private String takeLabel;

    @ApiModelProperty(value = "CouponSource.")
    private String couponSource;

    @ApiModelProperty(value = "Order used time.")
    private String usedTime;

    // 券面值 01：金额 02：折扣
    @ApiModelProperty(value = "Coupon face value. 01-金额 02-折扣")
    private BigDecimal faceValue;

    // 券面值单位 01：金额 02：折扣
    @ApiModelProperty(value = "Coupon face value unit. 01-金额 02-折扣")
    private String faceUnit;



}
