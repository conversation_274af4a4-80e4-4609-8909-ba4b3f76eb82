package com.gtech.promotion.code.purchaseconstraint;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum PcRuleCalculateTypeEnum {
    INCREMENT("1", "增量加"),
    DECREMENT("2", "增量减"),
    CHECK("3", "检查");


    private final String code;
    private final String desc;

    public static PcRuleCalculateTypeEnum getByCode(String code) {
        PcRuleCalculateTypeEnum[] values = PcRuleCalculateTypeEnum.values();
        for (PcRuleCalculateTypeEnum type : values) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    public boolean equalsCode(String code) {
        return this.getCode().equals(code);
    }
}
