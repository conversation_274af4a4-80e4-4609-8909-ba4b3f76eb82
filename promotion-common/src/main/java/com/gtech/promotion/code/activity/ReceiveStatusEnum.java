/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.code.activity;

/**   
 * 领取状态
 */
public enum ReceiveStatusEnum {
    
    //领取状态：1：可领取；2：未开始 3：已结束；4：已领完；5：已被限领；6：当前会员不符合要求（会员等级或会员标签）；优先级  （7和2级别相同）7263541
    RECEIVE("1","可领取"), 
    NOT_START("2","未开始"), 
    END("3","已结束"), 
    BROUGHT("4","已领完"),
    LIMIT("5","已被限领"),
    NOT_CONDITION("6","当前会员不符合要求"),
    TERMINATION("7","已终止")
    ;

    ReceiveStatusEnum(String code, String desc){
        this.code = code;
        this.desc = desc;
    }

    private final String code;

    private final String desc;

    /**
     * 判断是否是合法的活动状态
     * 
     * @param code
     *            类型code
     * @return true：合法
     */
    public static boolean exist(String code){
        ReceiveStatusEnum[] values = ReceiveStatusEnum.values();
        for (ReceiveStatusEnum type : values){
            if (type.code().equals(code)){
                return true;
            }
        }
        return false;
    }

    public String code(){
        return code;
    }

    public String desc(){
        return desc;
    }

    public boolean equalsCode(String code) {

        return this.code().equals(code);
    }
  
}
