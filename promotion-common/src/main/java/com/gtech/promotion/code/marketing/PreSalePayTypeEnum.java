package com.gtech.promotion.code.marketing;

import com.gtech.commons.code.IEnum;
import com.gtech.commons.utils.EnumUtil;

public enum PreSalePayTypeEnum implements IEnum{

    NO_USE("1","full pay"),
    USED("2","partial pay");

    PreSalePayTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private String code;

    private String desc;

    @Override
    public String code() {

        return code;
    }

    @Override
    public String desc() {

        return desc;
    }

    @Override
    public boolean equalsCode(String code) {

        return this.code().equals(code);
    }

    /**
     * Check whether the enumeration code is valid
     */
    public static boolean exist(String code){

        return EnumUtil.code2Enum(PreSalePayTypeEnum.class, code) != null;
    }

}
