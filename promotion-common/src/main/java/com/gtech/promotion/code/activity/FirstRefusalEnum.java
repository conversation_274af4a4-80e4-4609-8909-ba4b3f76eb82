package com.gtech.promotion.code.activity;

import lombok.Getter;

/**
 * 是否优先可见枚举
 */
@Getter
public enum FirstRefusalEnum {
    NO(0,"NO"),
    YES(1,"YES"),
    ;

    FirstRefusalEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private final Integer code;

    private final String desc;

    /**
     * 判断是否是合法的活动状态
     */
    public static boolean exist(String code) {

        FirstRefusalEnum[] values = FirstRefusalEnum.values();
        for (FirstRefusalEnum type : values) {
            if (type.getCode().equals(code)) {
                return true;
            }
        }
        return false;
    }

    public boolean equalsCode(int code) {

        return this.code == code;
    }

    public int number() {

        return this.code;
    }

}
