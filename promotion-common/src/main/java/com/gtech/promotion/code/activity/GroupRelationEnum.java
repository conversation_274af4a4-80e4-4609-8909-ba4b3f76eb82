package com.gtech.promotion.code.activity;

import com.gtech.commons.code.IEnum;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/7/16 17:25
 */
public enum  GroupRelationEnum implements IEnum {

    RELATION_1("1","叠加"),
    RELATION_2("2","互斥"),

    ;

    GroupRelationEnum(String code, String desc){

        this.code = code;
        this.desc = desc;
    }

    private String code;

    private String desc;

    /**
     * 判断是否是合法的活动店铺类型
     *
     * @param code
     *            类型code
     * @return true：合法
     */
    public static boolean exist(String code){
        GiveawayTypeEnum[] values = GiveawayTypeEnum.values();
        for (GiveawayTypeEnum type : values){
            if (type.code().equals(code)){
                return true;
            }
        }
        return false;
    }

    public String code(){
        return code;
    }

    public String desc(){
        return desc;
    }

    public boolean equalsCode(String code) {

        return this.code().equals(code);
    }
}
