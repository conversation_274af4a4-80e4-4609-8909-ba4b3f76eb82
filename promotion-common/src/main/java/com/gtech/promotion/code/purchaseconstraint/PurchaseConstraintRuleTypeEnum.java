package com.gtech.promotion.code.purchaseconstraint;

import com.google.common.collect.Lists;
import lombok.Getter;

import java.util.List;

/**
 * 限购规则条件类型枚举
 * 0:CUSTOMER_MAX_QTY_PER_PRODUCT,1:CUSTOMER_MAX_QTY_ALL_PRODUCTS,2:CUSTOMER_MAX_AMOUNT,
 * 3:ORDER_MAX_QTY_PER_SKU,4:ORDER_MAX_QUANTITY,5:ORDER_MAX_AMOUNT_PER_SKU,6:ORDER_MAX_AMOUNT
 */
@Getter
public enum PurchaseConstraintRuleTypeEnum {

    CUSTOMER_MAX_QTY_PER_PRODUCT(0, "会员购买单个商品最大数量"),
    CUSTOMER_MAX_QTY_ALL_PRODUCTS(1, "会员购买所有商品最大数量"),
    CUSTOMER_MAX_AMOUNT(2, "会员购买最大金额"),

    ORDER_MAX_QTY_PER_SKU(3, "每个订单中单个商品最大数量"),
    ORDER_MAX_QUANTITY(4, "每个订单中商品数量最大数量"),
    ORDER_MAX_AMOUNT_PER_SKU(5, "每个订单中单个商品最大金额"),
    ORDER_MAX_AMOUNT(6, "每个订单中所有商品最大金额"),

    FIRST_REFUSAL(7, "优先购买"), // 只做错误回显，不存数据库
    CUSTOM_RULES(8, "自定义规则"),

    ;

    private final Integer code;
    private final String desc;

    PurchaseConstraintRuleTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static PurchaseConstraintRuleTypeEnum valueOfCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (int i = 0; i < PurchaseConstraintRuleTypeEnum.values().length; i++) {
            if (PurchaseConstraintRuleTypeEnum.values()[i].getCode().equals(code)) {
                return PurchaseConstraintRuleTypeEnum.values()[i];
            }
        }
        return null;
    }

    /**
     * 累计规则
     */
    public static List<PurchaseConstraintRuleTypeEnum> addUpRuleTypeList() {
        return Lists.newArrayList(CUSTOMER_MAX_QTY_PER_PRODUCT, CUSTOMER_MAX_QTY_ALL_PRODUCTS, CUSTOMER_MAX_AMOUNT);
    }


    public boolean equalsCode(Integer code) {
        return this.getCode().equals(code);
    }

}
