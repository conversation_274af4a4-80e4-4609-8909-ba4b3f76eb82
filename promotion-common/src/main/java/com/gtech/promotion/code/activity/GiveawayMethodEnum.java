package com.gtech.promotion.code.activity;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 促销满赠 赠送方式
 * 1:全送 2:部分送
 */
@Getter
@AllArgsConstructor
public enum GiveawayMethodEnum {

    ALL("1","全部送"),
    PARTIAL("2","部分送"),
    ;


    private final String code;

    private final String desc;


    public static boolean exist(String code){
        GiveawayMethodEnum[] values = GiveawayMethodEnum.values();
        for (GiveawayMethodEnum type : values){
            if (type.getCode().equals(code)){
                return true;
            }
        }
        return false;
    }


    public boolean equalsCode(String code) {

        return this.getCode().equals(code);
    }

}
