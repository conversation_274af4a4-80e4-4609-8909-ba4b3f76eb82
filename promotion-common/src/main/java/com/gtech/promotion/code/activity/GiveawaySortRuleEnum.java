package com.gtech.promotion.code.activity;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 促销满赠 赠品池排序规则
 * 1:有序 2:随机
 */
@Getter
@AllArgsConstructor
public enum GiveawaySortRuleEnum {

    ORDERLY("1","有序"),
    RANDOM("2","随机"),
    ;


    private final String code;

    private final String desc;


    public static boolean exist(String code){
        GiveawaySortRuleEnum[] values = GiveawaySortRuleEnum.values();
        for (GiveawaySortRuleEnum type : values){
            if (type.getCode().equals(code)){
                return true;
            }
        }
        return false;
    }


    public boolean equalsCode(String code) {

        return this.getCode().equals(code);
    }

}
