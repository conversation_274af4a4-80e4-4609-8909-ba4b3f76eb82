/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.code.activity;

import com.gtech.commons.code.IEnum;

/**
 * 函数类型枚举
 */
public enum FuncTypeEnum implements IEnum{

    SCOPE("01","促销范围"), CONDITION("02","促销条件"), PARAM("03","条件参数"), INCENTIVE("04","促销奖励");

    private final String code;

    private final String desc;

    /**
     * 构造函数
     * 
     * @param code
     * @param desc
     */
    private FuncTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String code() {

        return code;
    }

    public String desc() {

        return desc;
    }

    public boolean equalsCode(String code) {

        return this.code().equals(code);
    }

    public enum ScopeEnum implements IEnum{
        ITEM("0101","单品"), PRODUCT("0102","商品"), ORDER("0103","订单"), SINGLE("0104","单件");

        ScopeEnum(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        private String code;

        private String desc;

        public String code() {

            return code;
        }

        public String desc() {

            return desc;
        }

        public boolean equalsCode(String code) {

            return this.code().equals(code);
        }

    }

    public enum IncentiveEnum implements IEnum{

        REDUCE_AMOUNT("0401","减金额"),
        DISCOUNT("0402","打折扣"),
        FIXED_MONEY("0403","单件固定金额"),
        FIXED_AMOUNT("0404","总计固定金额"),
        FREE_SHIPPING("0405","包邮"),
        GIVEAWAY("0406","送赠品"),
        BUY_A_GET_OTHER_A_FREE("0407","买送"),
        BUY_A_GET_B_FREE("0408","买送"),
        EACH_FIXED_MONEY("0411","每件不同特价"),
        BUY_A_REDUCE_B("0412","买A范围B范围减价"),
        BUY_A_DISCOUNT_B("0413","买A范围B范围打折"),
        EVERY_REDUCE_AMOUNT("0414","每件减固定金额");

        IncentiveEnum(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        private String code;

        private String desc;

        public String code() {

            return code;
        }

        public String desc() {

            return desc;
        }

        public String rewardType() {

            return code.substring(2);
        }

        public boolean equalsCode(String code) {

            return this.code().equals(code);
        }

    }
}
