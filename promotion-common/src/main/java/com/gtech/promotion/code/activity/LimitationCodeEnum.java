/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.code.activity;

import com.gtech.commons.code.IEnum;

/**   
 * 活动限制
 */
// 01-活动限制总次数, 02-活动限制总金额, 03-活动限制单日次数, 04-活动限制单日金额, 11-每人限制总次数, 12-每人限制总金额, 13-每人限制单日次数, 14-每人限制单日金额, 21-每单限制优惠金额
public enum LimitationCodeEnum implements IEnum {

    NON("00", "None"),

    ACTIVITY_TOTAL_COUNT("01", "活动限制总次数"),
    ACTIVITY_TOTAL_AMOUNT("02", "活动限制总金额"),
    ACTIVITY_DAY_COUNT("03", "活动限制单日次数"),
    ACTIVITY_DAY_AMOUNT("04", "活动限制单日金额"),

    ACTIVITY_DAY_ORDER_COUNT("09", "活动限制单日订单数"),

    USER_TOTAL_COUNT("11", "每人限制总次数"),
    USER_TOTAL_AMOUNT("12", "每人限制总金额"),
    USER_DAY_COUNT("13", "每人限制单日次数"),
    USER_DAY_AMOUNT("14", "每人限制单日金额"),
    USER_DAY_ORDER_COUNT("15", "每人限制单日订单数"),
    USER_WEEK_COUNT("16", "每人限制单周次数"),
    USER_MONTH_COUNT("17", "每人限制单月次数"),

    ORDER_AMOUNT("21", "活动限制每单优惠金额"),

    USER_SKU_COUNT("31", "活动限制每人单SKU次数"),
    USER_SPU_COUNT("32", "活动限制每人单SPU次数"),
    SKU_COUNT("33", "活动限制单SKU次数"),
    SPU_COUNT("34", "活动限制单SPU次数"),

    USER_TOTAL_COUNT_FLASH_SALE("0411", "限时秒杀每人限制总次数"),

    USER_RECEIVE_COUPON_LIMIT("35", "用户领取优惠券限制次数"),

    USER_RECEIVE_COUPON_LIMIT_DAY("36", "用户单日领取优惠券限制次数"),

    ;

    LimitationCodeEnum(String code, String desc){
        this.code = code;
        this.desc = desc;
    }

    private final String code;
    private final String desc;

    public String code(){
        return code;
    }

    public String desc(){
        return desc;
    }

    public boolean equalsCode(String code) {

        return this.code().equals(code);
    }

    /**
     * 判断是否是合法的类型
     */
    public static boolean exist(String code){
        LimitationCodeEnum[] values = LimitationCodeEnum.values();
        for (LimitationCodeEnum type : values){
            if (type.code().equals(code)){
                return true;
            }
        }
        return false;
    }

    public static boolean isTimes(String code){

        return USER_TOTAL_COUNT.equalsCode(code)
                || ACTIVITY_TOTAL_COUNT.equalsCode(code)
                || USER_DAY_COUNT.equalsCode(code)
                || USER_WEEK_COUNT.equalsCode(code)
                || USER_MONTH_COUNT.equalsCode(code)
                || ACTIVITY_DAY_COUNT.equalsCode(code)
                || ACTIVITY_DAY_ORDER_COUNT.equalsCode(code)
                || USER_TOTAL_COUNT_FLASH_SALE.equalsCode(code)
                || USER_DAY_ORDER_COUNT.equalsCode(code)
                || USER_SKU_COUNT.equalsCode(code)
                || SKU_COUNT.equalsCode(code);
    }
}
