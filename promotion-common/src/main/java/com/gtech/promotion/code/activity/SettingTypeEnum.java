/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.code.activity;

/**
 * 活动店铺类型
 */
public enum SettingTypeEnum {

    ALL("0","重新加载数据库中的所有配置"),PRICE_SORT("1","购物车计算价格排序");

    SettingTypeEnum(String code, String desc){
        this.code = code;
        this.desc = desc;
    }

    private String code;

    private String desc;

    /**
     * 判断是否是合法的类型
     * 
     * @param code
     *            类型code
     * @return true：合法
     */
    public static boolean exist(String code){
        SettingTypeEnum[] values = SettingTypeEnum.values();
        for (SettingTypeEnum type : values){
            if (type.code().equals(code)){
                return true;
            }
        }
        return false;
    }

    public String code(){
        return code;
    }

    public String desc(){
        return desc;
    }

    public boolean equalsCode(String code) {

        return this.code().equals(code);
    }

    public enum PriceSortEnum {

        DESC("1","降序"), ASC("2","升序");

        PriceSortEnum(String code, String desc){
            this.code = code;
            this.desc = desc;
        }

        private String code;

        private String desc;

        /**
         * 判断是否是合法的类型
         * 
         * @param code
         *            类型code
         * @return true：合法
         */
        public static boolean exist(String code){
            PriceSortEnum[] values = PriceSortEnum.values();
            for (PriceSortEnum type : values){
                if (type.code().equals(code)){
                    return true;
                }
            }
            return false;
        }

        public String code(){
            return code;
        }

        public String desc(){
            return desc;
        }

        public boolean equalsCode(String code) {

            return this.code().equals(code);
        }

    }

    public enum PrecisionEnum {
        YUAN("0","元"), JIAO("1","角"),FEN("2","分");

        PrecisionEnum(String code, String desc){
            this.code = code;
            this.desc = desc;
        }

        private String code;

        private String desc;

        /**
         * 判断是否是合法的类型
         * 
         * @param code
         *            类型code
         * @return true：合法
         */
        public static boolean exist(String code){
            PrecisionEnum[] values = PrecisionEnum.values();
            for (PrecisionEnum type : values){
                if (type.code().equals(code)){
                    return true;
                }
            }
            return false;
        }

        public String code(){
            return code;
        }

        public String desc(){
            return desc;
        }

        public boolean equalsCode(String code) {

            return this.code().equals(code);
        }

    }
    
}
