/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.code.activity;

import com.gtech.commons.code.IEnum;

/**
 * 商品属性enum
 */
public enum TurnOnAndOffEnum implements IEnum {

    TURN_OFF("0","关闭"),
    TURN_ON("1","打开")
    ;

    TurnOnAndOffEnum(String code, String desc){
        this.code = code;
        this.desc = desc;
    }

    private final String code;

    private final String desc;

    /**
     * 判断是否是合法的类型
     * 
     * @param code
     *            类型code
     * @return true：合法
     */
    public static boolean exist(String code){
        TurnOnAndOffEnum[] values = TurnOnAndOffEnum.values();
        for (TurnOnAndOffEnum type : values){
            if (type.code().equals(code)){
                return true;
            }
        }
        return false;
    }

    public String code(){
        return code;
    }

    public String desc(){
        return desc;
    }

    public boolean equalsCode(String code) {

        return this.code().equals(code);
    }

}
