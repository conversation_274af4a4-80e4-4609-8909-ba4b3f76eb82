
/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.code.coupon;

/**
 * 
 */
public enum CouponTypeEnum {
    PROMOTION_COUPON("01","优惠券"),
    ANONYMITY_COUPON("02","匿名券"),
    PROMOTION_CODE("03","优惠码"),
    ;

    CouponTypeEnum(String code,String desc){
        this.code = code;
        this.desc = desc;
    }

    private String code;

    private String desc;

    /**
     * 判断是否是合法的类型
     *
     * @param code
     *            类型code
     * @return true：合法
     */
    public static boolean exist(String code){
        CouponTypeEnum[] values = CouponTypeEnum.values();
        for (CouponTypeEnum type : values){
            if (type.code().equals(code)){
                return true;
            }
        }
        return false;
    }
    
    public static CouponStatusEnum getByCode(String code){
        CouponStatusEnum[] values = CouponStatusEnum.values();
        for (CouponStatusEnum value : values){
            if(value.code().equals(code)){
                return value;
            }
        }
        return null;
    }

    public String code(){
        return code;
    }

    public String desc(){
        return desc;
    }

    public boolean equalsCode(String code) {

        return this.code().equals(code);
    }

}
  
