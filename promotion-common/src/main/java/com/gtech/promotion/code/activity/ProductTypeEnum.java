/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.code.activity;

/** 
 * 活动商品类型
 */
public enum ProductTypeEnum {

    ALL("00","全部商品"), 
    CUSTOM_RANGE("01","指定范围"),
    CUSTOM_PRODUCT("02","指定商品"),
    CUSTOM_SEQ("03","指定多个商品范围"),
    ;

    ProductTypeEnum(String code, String desc){
        this.code = code;
        this.desc = desc;
    }

    private final String code;
    private final String desc;
    
    
    /**
     * 判断是否是合法的活动状态
     * 
     * @param code
     *            类型code
     * @return true：合法
     */
    public static boolean exist(String code){
        ProductTypeEnum[] values = ProductTypeEnum.values();
        for (ProductTypeEnum type : values){
            if (type.code().equals(code)){
                return true;
            }
        }
        return false;
    }

    public String code(){
        return code;
    }

    public String desc(){
        return desc;
    }

    public boolean equalsCode(String code) {

        return this.code().equals(code);
    }

}
