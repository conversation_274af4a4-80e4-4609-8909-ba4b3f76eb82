package com.gtech.promotion.code.marketing;

import com.gtech.commons.code.IEnum;
import com.gtech.commons.utils.EnumUtil;

public enum RightOfFirstRefusalStatusEnum implements IEnum {
    UN_USED("01","unused"),
    LOCK("02","lock"),
    USED("03","used")
    ;

    private String code;
    private String desc;
    RightOfFirstRefusalStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    @Override
    public String code() {
        return this.code;
    }

    @Override
    public String desc() {
        return this.desc;
    }

    @Override
    public boolean equalsCode(String code) {

        return this.code().equals(code);
    }

    /**
     * Check whether the enumeration code is valid
     */
    public static boolean exist(String code){

        return EnumUtil.code2Enum(RecordStatusEnum.class, code) != null;
    }

}
