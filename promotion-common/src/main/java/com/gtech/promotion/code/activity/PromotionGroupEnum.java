package com.gtech.promotion.code.activity;

import com.alibaba.fastjson.JSONObject;

public enum PromotionGroupEnum {


    /**
     * 101	打折促销
     * 102	买A优惠B
     * 103	订单满减满折
     * 104	一口价
     * 105	买多优惠
     * 106	满赠促销
     * 107	运费促销
     * 108	单品特价促销
     * 201	打折券
     * 202	满减满折券
     * 203	满减满折码
     * 204	兑换券
     * 205	运费促销券
     * 206	买多优惠券
     * 207	满赠促销券
     * 301	抽奖-大转盘
     * 302	抽奖-砸金蛋
     * 303	抽奖-九宫格
     * 401	秒杀
     * 501	预售
     * 601	多人拼团
     * 701	分享助力
     */
    DISCOUNT("101", "打折促销", "Discount for select products"),
    BUY_A_DISCOUNT_B("102", "买A优惠B", "Buy A get B discounted"),
    ORDER_FULL_REDUCE_FULL_DISCOUNT("103", "订单满减满折", "Order Discount"),
    ONE_PRICE("104", "一口价", "Special Offer"),
    BUY_MORE_DISCOUNT("105", "买多优惠", "Purchase with Purchase"),
    FULL_GIFT_PROMOTION("106", "满赠促销", "Gift with Purchase"),
    FREIGHT_PROMOTION("107", "运费促销", "Shipping Promotion"),
    SINGLE_SPECIAL_PRICE_PROMOTION("108", "单品特价促销", "Single-item Discount"),
    DISCOUNT_COUPON("201", "打折券", "Product discount"),
    FULL_REDUCE_FULL_DISCOUNT_COUPON("202", "满减满折券", "Order discount (coupon)"),
    FULL_REDUCE_FULL_DISCOUNT_CODE("203", "满减满折码", "Order discount (code)"),
    EXCHANGE_COUPON("204", "兑换券", "Voucher"),
    FREIGHT_PROMOTION_COUPON("205", "运费促销券", "Shipping Promotion"),
    BUY_MORE_DISCOUNT_COUPON("206", "买多优惠券", "Purchase with Purchase"),
    FULL_GIFT_PROMOTION_COUPON("207", "满赠促销券", "Complimentary voucher"),
    SINGLE_SPECIAL_PRICE_COUPON("208", "单品特价促销券", "Single-item Discount"),
    TURNTABLE("301", "抽奖-大转盘", "Lucky Draw"),
    SMASH_GOLDEN_EGG("302", "抽奖-砸金蛋", "Lucky Draw"),
    NINE_GRID("303", "抽奖-九宫格", "Lucky Draw"),
    SECKILL("401", "秒杀", "Flash Sale"),
    PRESALE("501", "预售", "Preorder"),
    MULTI_PERSON_GROUP("601", "多人拼团", "Group buying"),
    BOOST_SHARING("701", "分享助力", "Boost Sharing");

    private final String code;
    private final String desc;
    private final String enUS;
    PromotionGroupEnum(String code, String desc, String enUS){
        this.code = code;
        this.desc = desc;
        this.enUS = enUS;
    }

    public String getDesc() {
        return desc;
    }
    public String getCode() {
        return code;
    }
    public String getEnUS() {
        return enUS;
    }

    public static PromotionGroupEnum getPromotionGroupEnum(String code) {
        for (PromotionGroupEnum promotionGroupEnum : PromotionGroupEnum.values()) {
            if (promotionGroupEnum.getCode().equals(code)) {
                return promotionGroupEnum;
            }
        }
        return null;
    }

    public static String getEnUSNameByCode(String code){
        String languageCode = "en-US";
        String languageValue = DISCOUNT.getEnUS();

        JSONObject languageObject = new JSONObject();
        languageObject.put(languageCode, languageValue);

        JSONObject json = new JSONObject();
        json.put("language", languageObject);
        //{"language": {"en-US": "Discount for select products"}}
        return json.toString();
    }







}
