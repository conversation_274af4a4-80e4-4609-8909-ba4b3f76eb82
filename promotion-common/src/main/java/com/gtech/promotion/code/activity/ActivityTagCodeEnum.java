/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.code.activity;

import com.gtech.commons.code.IEnum;

/**   
 * 活动类型
 */
public enum ActivityTagCodeEnum implements IEnum {
/*
 * 1-单品促销 2-满减促销 3-满折促销 4-特价促销 5-包邮促销 6-赠品促销
 */

    SINGLE("01","单品促销"), 
    SUBTRACTION("02","满减促销"), 
    DISCOUNT("03","满折促销"), 
    SPECIAL("04","特价促销"),
    PINKAGE("05","包邮促销"),
    GIVEAWAY("06","giveaway promotion"),
    BINDING("07","捆绑"),
    SEND_FULL("08","Send for reach"),
    SEND_COUPON("09","Send coupon"),
    BUY_A_DISCOUNT_B("10","Buy A promotion B"),
    PRODUCT_COUPON("11", "Product coupon");

    ActivityTagCodeEnum(String code, String desc){
        this.code = code;
        this.desc = desc;
    }

    private final String code;
    private final String desc;

    public String code(){
        return code;
    }

    public String desc(){
        return desc;
    }

    public boolean equalsCode(String code) {

        return this.code().equals(code);
    }

}
  
