package com.gtech.promotion.code.purchaseconstraint;

import com.gtech.promotion.code.activity.ActivityStatusEnum;
import lombok.Getter;

/**
 * 限购状态
 */
@Getter
public enum  PurchaseConstraintStatusEnum {

    PENDING("01","待提交"),
    IN_AUDIT("02","审核中"),
    REJECTED("03","已拒绝"),
    EFFECTIVE("04","已生效"),
    CLOSURE("05","已结束"),
    SUSPEND("06","暂停中"),
    END("07","已终止");

    PurchaseConstraintStatusEnum(String code, String desc){
        this.code = code;
        this.desc = desc;
    }

    private final String code;

    private final String desc;

    /**
     * 判断是否是合法的活动状态
     *
     * @param code
     *            类型code
     * @return true：合法
     */
    public static boolean exist(String code){
        ActivityStatusEnum[] values = ActivityStatusEnum.values();
        for (ActivityStatusEnum type : values){
            if (type.code().equals(code)){
                return true;
            }
        }
        return false;
    }

    public String code(){
        return code;
    }

    public String desc(){
        return desc;
    }

    public boolean equalsCode(String code) {

        return this.code().equals(code);
    }
}
