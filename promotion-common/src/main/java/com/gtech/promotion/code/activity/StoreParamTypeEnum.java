/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.code.activity;

/**
 * 店铺各属性的类型
 */
public enum StoreParamTypeEnum {
    
    STORE_ALL("00","全店铺"),//该系统商户的下的全部商铺
    STORE_CUSTOM("01","自定义"),//该系统商户的下自定义商铺
    STORE_ALL_AREA("0000","全部区域"),
    STORE_ALL_STORE("0000","全体店铺"),//区域下的的全部店铺
    STORE_ALL_MERC("0000","全体商户")
    ;
    
    StoreParamTypeEnum(String code, String desc){
        this.code = code;
        this.desc = desc;
    }

    private final String code;
    private final String desc;

    public String code(){
        return code;
    }

    public String desc(){
        return desc;
    }

    public boolean equalsCode(String code) {

        return this.code().equals(code);
    }

}
