package com.gtech.promotion.code.marketing;

import com.gtech.commons.code.IEnum;
import com.gtech.commons.utils.EnumUtil;


public enum SelectFlagEnum implements IEnum{

    //00-unchecked 01-check";//是否勾选，00=非勾选，01=勾选
    SELECT_FLAG_NO("00","非勾选"),//非勾选
    SELECT_FLAG_YES("01","勾选");//勾选

    SelectFlagEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private String code;

    private String desc;

    @Override
    public String code() {

        return code;
    }

    @Override
    public String desc() {

        return desc;
    }

    @Override
    public boolean equalsCode(String code) {

        return this.code().equals(code);
    }

    /**
     * Check whether the enumeration code is valid
     */
    public static boolean exist(String code){

        return EnumUtil.code2Enum(SelectFlagEnum.class, code) != null;
    }

}
