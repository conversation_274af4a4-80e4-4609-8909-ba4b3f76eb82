/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WA<PERSON>RANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.code.coupon;

/**
 * 
 */
public enum CouponFrozenStatusEnum {

    UN_FROZEN("01","未冻结"),
    FROZENED("02","已冻结");

    CouponFrozenStatusEnum(String code,String desc){
        this.code = code;
        this.desc = desc;
    }


    private String code;

    private String desc;

    public String code(){
        return code;
    }

    public String desc(){
        return desc;
    }

    public boolean equalsCode(String code) {

        return this.code().equals(code);
    }

    /**
     * 判断是否是合法的类型
     */
    public static boolean exist(String code){
        ReleaseTypeEnum[] values = ReleaseTypeEnum.values();
        for (ReleaseTypeEnum type : values){
            if (type.code().equals(code)){
                return true;
            }
        }
        return false;
    }

}
