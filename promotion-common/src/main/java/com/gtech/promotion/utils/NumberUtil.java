/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR <PERSON><PERSON><PERSON>NTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.utils;

import java.math.BigDecimal;
import java.util.Random;

import com.gtech.promotion.checker.SystemChecker;
import com.gtech.promotion.exception.PromotionException;

/**
 * 数字类工具类
 * 
 */
public class NumberUtil{

    private NumberUtil(){
        throw new PromotionException(SystemChecker.CAN_NOT_INSTANCE_ERROR);
    }

    private static final char[] numberChar = { '0', '1', '2', '3', '4', '5', '6', '7', '8', '9' };

    private static final Random random = new Random();

    private static final char[] numberCharArray = { '1', '2', '3', '4', '5', '6', '7', '8', '9' };


    public static String getRandomNumbers(int length){

        StringBuilder sb = new StringBuilder();

        // 随机产生，验证码由几个数字、几个字母组成
        for (int i = 0; i < length; i++){
            // 得到随机产生的验证码字母。
            String strRand = String.valueOf(numberChar[random.nextInt(numberChar.length)]);
            // 将产生的随机数组合在一起。
            sb.append(strRand);
        }
        return sb.toString();
    }


    public static String getRandomNumber(int length){

        StringBuilder sb = new StringBuilder();

        // 随机产生，验证码由几个数字、几个字母组成
        for (int i = 0; i < length; i++){
            // 得到随机产生的验证码字母。
            String strRand = String.valueOf(numberCharArray[random.nextInt(numberCharArray.length)]);
            // 将产生的随机数组合在一起。
            sb.append(strRand);
        }
        return sb.toString();
    }



    /**
     * 匹配：>0的整数
     */
    public static Boolean isPositiveLongValue(String value) {

        try {
            return Long.parseLong(value) > 0;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
	 * 折扣数字检测（0.0001-0.9999）
	 */
    public static Boolean checkDiscount(String value) {

        try {
            BigDecimal decimal = new BigDecimal(value);
            if (decimal.compareTo(BigDecimal.ZERO) < 0 || decimal.compareTo(BigDecimal.ONE) > 0) {
                return false;
            }

			return decimal.scale() <= 4;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 金额数字检测：大于0的2位小数
     * 
     * @param scale -- 小数位数限制
     */
    public static Boolean checkMoney(String value, Integer scale){
        try {
            BigDecimal decimal = new BigDecimal(value);
            if (decimal.compareTo(BigDecimal.ZERO) <= 0) {
                return false;
            }
            
            if (null == scale) {
                scale = 2;
            }

            return decimal.scale() <= scale;
        } catch (NumberFormatException e) {
            return false;
        }
    }


}
