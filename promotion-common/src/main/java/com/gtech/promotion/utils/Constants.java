/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.utils;

/**
 * 全局常量定义
 */
public abstract class Constants{


    public static final String REGEX = "=";


    public static final String APP_KEY = "PRO";

    public static final String MARKETING_GROUP_SIZE = "MARKETING_GROUP_SIZE";

    // 项目错误码的前缀
    public static final String ERR_CODE_PREFIX = "1014";

    // 促销活动券投放主体常量
    public static final String AGENCY_NO = "GTECH";

    /**
     * @deprecated (Will be removed on 2020.08.08.)
     */
    @Deprecated
    public static final String ALL_CODE = "0000"; // NOSONAR

    // 券码领取后的redis的key的前缀
    public static final String PROMOTION_COUPON_USER = "PROMOTION_COUPON_USER";
    
    //创建couponCode的缓存key的前缀
    public static final String PROMOTION_COUPON_CODE_CACHE = "PROMOTION_COUPON_CODE_CACHE";
    
    //预约投放时间到期
    public static final String PROMOTION_COUPON_RELEASE_TIME = "PROMOTION_COUPON_RELEASE_TIME";
    //优先级叠加互斥表达式的redis的key前缀
    public static final String PROMOTION_ACTIVITY_EXPRESSION = "PROMOTION_ACTIVITY_EXPRESSION";
    
    public static final String PROMOTION_ACTIVITY_SETTING = "PROMOTION_ACTIVITY_SETTING";

    // 券码每人限领的redis的key的前缀
    public static final String PROMOTION_COUPON_LIMIT_USER = "PROMOTION_COUPON_LIMIT_USER";

    // 券码投放限领的redis的key的前缀
    public static final String PROMOTION_COUPON_LIMIT_RELEASE = "PROMOTION_COUPON_LIMIT_RELEASE";

    // 促销奖励限制的redis的key的前缀
    public static final String INCENTIVE_LIMITED = "INC_LIM";

    //促销模板标签的redis的key的前缀
    public static final String PROMOTION_TEMPLATE_TAG = "PROMOTION_TEMPLATE_TAG";

    //促销活动缓存前缀
    public static final String PROMOTION_ACTIVITY_CACHE = "PROMOTION_ACTIVITY_CACHE";
    public static final String PROMOTION_ACTIVITY_CACHE_KEY_SET = "PROMOTION_ACTIVITY_CACHE_KEY_SET";

    // 促销活动本地缓存判断是否有效的前缀
    public static final String PROMOTION_ACTIVITY_CACHE_LEVEL_TWO = "PROMOTION_ACTIVITY_CACHE_LEVEL_TWO";

    //促销spu与活动对应关系缓存前缀(plp)
    public static final String PROMOTION_SPU_ACTIVITY_CACHE = "PROMOTION_SPU_ACTIVITY_CACHE";

    //创建sku商品的缓存key的前缀
    public static final String PROMOTION_ACTIVITY_SKU_CREATE = "PROMOTION_ACTIVITY_SKU_CREATE";

    //couponCode分布式锁的key
    public static final String PROMOTION_COUPON_CODE_LOCK_KEY = "PROMOTION_COUPON_CODE_LOCK_KEY";

    //状态修改的定时任务分布式锁的key
    public static final String PROMOTION_TASK_LOCK_KEY = "PROMOTION_TASK_LOCK_KEY:";

    // 优惠券券码默认的业务编号（订单号）
    public static final String DEFAULT_USED_REF_ID = "DEFAULT";

    public static final String DEFAULT_ORG = "default";

    // 活动三层审核和操作人权限设置系统变量Key
    public static final String ACTIVITY_AUDIT_CONFIG = "ACTIVITY_AUDIT_CONFIG";

    public static final String SYSTEM_DEFAULT = "SYSTEM_DEFAULT";

    public static final String NEED_AUDIT_YES = "1";

    public static final String NEED_AUDIT_NO = "0";

    public static final String NEED_DIFFERENT_YES = "1";

    public static final String NEED_DIFFERENT_NO = "0";
    
    //redis的key最大过期时间限制
    public static final Integer MAX_TIME = 12*30;


    //活动分组间关系
    public static final String PROMOTION_GROUP_RELATION_KEY = "PROMOTION_GROUP_RELATION_KEY:";

    // 限购记录缓存key前缀
    public static final String PURCHASE_CONSTRAINT_CACHE_KEY_PREFIX = "PURCHASE_CONSTRAINT_CACHE";
    // redis key的分隔符
    public static final String REDIS_KEY_SEPARATOR = ":";
    // @分隔符
    public static final String AT_SEPARATOR = "@";
    public static final String COMMA_SEPARATOR = ",";

    public static final String SYSTEM_USER = "system"; // 系统用户

    public static final String MEMBER_TAG_CODE = "memberTagCode";

    public static final String MEMBER_ORG_CODE = "memberOrgCode";
    public static final String IS_MEMBER = "isMember";
    public static final String STRING_ONE = "1";
    public static final String STRING_ZERO = "0";
    public static final String CHANNEL_CODE = "channelCode";
    // 子分类的具体的前缀(与类名相同)
    public enum ErrorCodePrefix{
        SYSTEM("01"), //系统异常，非业务性异常
        // 02 用于ErrorCodes类
        INCENTIVE_LIMITED("03"), //奖励限制表
        RULE_CHECKER("05"), //促销相关接口规则
        PRODUCT_CHECKER("07"), //促销商品接口规则
        TEMPLATE_CHECKER("08"), //模板校验规则
        ACTIVITY_FUNC_PARAM("09"), //模板函数参数校验规则
        SHOPPING_CART("14"), //标签购物车查询计算参数规则
        ACTIVITY_COMP_PARAM("15"), //活动叠加规则
        ORDER_CHECKER("16"), //促销活动订单生产规则
        GIFT_CHECKER("20"), //促销赠品规则
        PRODUCT_DETAIL("21"), //促销商品详情接口规则
        GIFT_COUPON_CHECKER("22"), //促销赠券规则
        COUPON_CODE_USER("23"), //用户优惠券表
        COUPON_QUERY_LIST_CHECKER("24"), //优惠券列表查询
        ACTIVITY_QUERY("25"), //活动查询
        FEIGN_PRODUCT_CHECKER("26"), //商品系统接口的校验
        MEMBER_CHECKER("27"), //会员接口的校验
        ACTIVITY_TAG_CHECKER("28"),//活动标签
        COUPON_ACTIVITY_CHECKER("29"),//券活动
        COUPON_RECEIVE_CHECKER("30"),//领券
        COUPON_EXPORT_USER("31"),//券码导出
        COUPON_QUERY_CHECKER("32"),//券码查询
        COUPON_ERROR_CHECKER("33"),//购物车券码报错
        MEMBER_LABLE_CHECKER("34"), //会员标签接口的校验
        ACTIVITY_STATISTIC_CHECKER("35"),//活动数据统计
        ACTIVITY_SETTING_CHECKER("36"),//活动设置项
		POINT_ACCOUNT_CHECKER("37"), // 积分账户
		POINT_TRANSACTION_CHECKER("38"), // 积分流水
		POINT_CAMPAIGN_CHECKER("39"), // 积分池
		GROWTH_ACCOUNT_CHECKER("40"), // 成长账户
		GROWTH_TRANSACTION_CHECKER("41"),// 成长流水
        PURCHASE_CONSTRAINT_CHECKER("42"),// 限购
        PURCHASE_CONSTRAINT_RULE_CHECKER("43"),// 限购规则
        ;

        private String codePrefix;

        ErrorCodePrefix(String codePrefix){
            this.codePrefix = ERR_CODE_PREFIX + codePrefix;
        }

        public String getCodePrefix(){
            return codePrefix;
        }

    }

}
