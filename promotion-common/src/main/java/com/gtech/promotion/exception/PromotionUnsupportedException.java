/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR <PERSON><PERSON><PERSON>NTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.exception;

import com.gtech.promotion.checker.Checker;

/**
 * 项目异常类
 */
public class PromotionUnsupportedException extends RuntimeException {

    private static final long serialVersionUID = -6340854242522959142L;

    private final String code;

    public String getCode() {

        return code;
    }

    public PromotionUnsupportedException(String message) {

        super(message);
        this.code = null;
    }

    public PromotionUnsupportedException(String code, String message) {

        super(message);
        this.code = code;
    }

    public PromotionUnsupportedException(Checker checker) {

        this(checker.getCode(), checker.getMessage());
    }

}
