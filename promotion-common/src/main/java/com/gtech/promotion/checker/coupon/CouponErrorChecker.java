/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR <PERSON><PERSON><PERSON>NTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.checker.coupon;

import com.gtech.promotion.checker.Checker;
import com.gtech.promotion.utils.Constants;

/**   
 */
public enum CouponError<PERSON><PERSON>cker implements Checker{
    // 101433
    VALID_TIME_NO_REACH("04","Coupon not available at this time"),
    MEMBER_NOT_COUPON("07","Sorry, you does not have the coupon code."),
    LIMIT("08","Sorry, You can get ${0} coupons at most."),
    FREEZE("09","Coupon status: ${0}, coupon: ${1} was frozen."),
    NO_EFFECTIVE("10","Invalid coupon or coupon not exists."),
    ERROR("11","Coupon status: ${0}, Invalid coupon: ${1}"),
    RECEIVE_NO_REACH("12","Not within the collection time."),
    RECEIVE_NO_REACH_COUPON_CODE("13","Not within the collection time, coupon: ${0}"),
    VALID_TIME_NO_REACH_COUPON_CODE("14","Coupon is not available now, coupon: ${0}"),
    NO_USER_COUPON_CODE("16","Coupon: ${0} and user: ${1} are not bound."),
    NO_STATUS("17","Coupon: ${0}, current status: ${1}, must be received status"),

    NO_RECEIVE_COUPON("18","You can't use the voucher until you collect it"),

    NO_USED_TIME("19","The use time is not up"),
    END_TIME("20","End of Validity period"),


    ;


    private final String code;

    private final String message;

    CouponErrorChecker(String code, String message){
        this.code = Constants.ErrorCodePrefix.COUPON_ERROR_CHECKER.getCodePrefix() + code;
        this.message = message;
    }

    @Override
    public String getCode(){
        return code;
    }
    
    @Override
    public String getMessage(){
        return message;
    }
}
