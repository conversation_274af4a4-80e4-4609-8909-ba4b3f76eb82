/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR <PERSON><PERSON><PERSON>NTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.checker.activity;

import com.gtech.promotion.checker.Checker;
import com.gtech.promotion.utils.Constants;

public enum TPromoOrder<PERSON><PERSON><PERSON> implements Checker{
    NOT_NULL_USER_ID("04","The Parameter member code cannot be empty."),
    NULL_ORDER_AMOUNT("07","The Parameter deducted amount cannot be empty."),
    ERROR_GIVEAWAY_ACTIVITY("09","Giveaways info error"),
    ERROR_CANCEL_ORDER_NO("10","This order is already canceled"),
    ERROR_PAID_ORDER_NO("08","This order is already paid"),
    ERROR_GIFT_NUM("11","There are too many gifts"),
    TENANT_CODE_NO_ACTIVITY("12","No promotion activity"),
    NOT_NULL_GIVEAWAY("13","If have promoGiveaways , giveaways cannot be empty"),
    ERROR_PRICE_LENGTH("14","Keep up to two significant digits after the decimal point"),
    NOT_NULL_GIVEAWAY_ACTIVITY_CODE("16","If have promoGiveaways, activity code cannot be empty"),
    ERROR_FREE_POSTAGE("18","该订单无包邮信息"),
    ERROR_GIVEAWAY_NUM_ACTIVITY("19","Giveaway num is wrong"),
    ERROR_PRICE_QUANTITY("20","The Parameter quantity and price cannot be empty."),

    NULL_ORDER("21","Order not exists"),
    NULL_COUPON_CODE("22","The Parameter:${0} cannot be empty."),
    NO_MARKETING_GROUP_CODE("23","No valid group code."),

    GROUP_SIZE_FULL("24","The number of people is full and cannot participate in this group."),

    GROUP_ORDER_COUNT_FULL("24","The order quantity limit per person is full."),
    GIVEAWAY_POOL_ONLY_ONE("25","When the gift pool is enabled, only one gift can be given"),
    GIVEAWAY_CHOOSE_OVER_LIMIT("26","Choose giveaways over limit"),

    //The group does not exist, or the group has ended, or the group has not yet taken effect
    NO_LEADER_MARKETING_GROUP_CODE("27","拼团不存在，或者拼团已结束，或者拼团尚未生效."),

    NO_MARKETING_EFFECTIVE_TIME("28","Group end."),

    MORE_GROUP_SIZE("29","Sorry, the group is full, please join another group."),

    NO_LEADER_NO_PAY("30","该团不存在或未支付."),

    GROUP_SIZE_ERROR_REDIS("32","拼团人数存在问题，请稍后尝试."),

    NO_ORDER_GROUP_LEADER("33","该拼团已解散或参加商品不符合或拼团不存在."),

    NO_CLOSE_FLAG_URL("34","封闭团开启，closeMarketingUrl参数不能为空."),
    NO_CLOSE_FLAG_URL_LEASER("35","封闭团开启的情况下被分享人参与，必须有该团编码信息."),

    NO_CLOSE_FLAG("36","入参类型不存在."),

    EXIST_ORDER_DUPLICATED("37","记录已存在."),

    QUANTITY_GROUP_SIZE("38","该团人数已满"),

    ONCE_GROUP("39","同一拼团，每人只能参加一次."),

    NO_ALLOW_PAY_GROUP("40","拼团失效,无法支付,请取消订单."),
    NO_PRIZE_PRODUCT("41","没有该奖品."),


    ;

    private String code;

    private String message;

    TPromoOrderChecker(String code, String message){
        this.code = Constants.ErrorCodePrefix.ORDER_CHECKER.getCodePrefix() + code;
        this.message = message;
    }

    @Override
    public String getCode(){
        return code;
    }

    @Override
    public String getMessage(){
        return message;
    }

}
