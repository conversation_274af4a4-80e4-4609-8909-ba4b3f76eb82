/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR <PERSON><PERSON><PERSON>NTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.checker.activity;

import com.gtech.promotion.checker.Checker;
import com.gtech.promotion.utils.Constants;

public enum TPromoOrder<PERSON><PERSON><PERSON> implements Checker{
    NOT_NULL_USER_ID("04","The Parameter member code cannot be empty."),
    NULL_ORDER_AMOUNT("07","The Parameter deducted amount cannot be empty."),
    ERROR_GIVEAWAY_ACTIVITY("09","Giveaways info error"),
    ERROR_CANCEL_ORDER_NO("10","This order is already canceled"),
    ERROR_PAID_ORDER_NO("08","This order is already paid"),
    ERROR_GIFT_NUM("11","There are too many gifts"),
    TENANT_CODE_NO_ACTIVITY("12","No promotion activity"),
    NOT_NULL_GIVEAWAY("13","If have promoGiveaways , giveaways cannot be empty"),
    ERROR_PRICE_LENGTH("14","Keep up to two significant digits after the decimal point"),
    NOT_NULL_GIVEAWAY_ACTIVITY_CODE("16","If have promoGiveaways, activity code cannot be empty"),
    ERROR_FREE_POSTAGE("18","该订单无包邮信息"),
    ERROR_GIVEAWAY_NUM_ACTIVITY("19","Giveaway num is wrong"),
    ERROR_PRICE_QUANTITY("20","The Parameter quantity and price cannot be empty."),

    NULL_ORDER("21","Order not exists"),
    NULL_COUPON_CODE("22","The Parameter:${0} cannot be empty."),


    ;

    private String code;

    private String message;

    TPromoOrderChecker(String code, String message){
        this.code = Constants.ErrorCodePrefix.ORDER_CHECKER.getCodePrefix() + code;
        this.message = message;
    }

    @Override
    public String getCode(){
        return code;
    }

    @Override
    public String getMessage(){
        return message;
    }

}
