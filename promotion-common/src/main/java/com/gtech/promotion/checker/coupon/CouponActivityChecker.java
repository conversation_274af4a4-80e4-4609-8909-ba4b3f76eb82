/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WA<PERSON>RANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.checker.coupon;

import com.gtech.promotion.checker.Checker;
import com.gtech.promotion.utils.Constants;

/**   
 */
public enum CouponActivityChecker implements Checker {
	PROMOTION_CODE_EXIST("01","Promotion code already exists"),
	COUPON_CODE_TOO_LONG("02","Coupon code (${0}) must be upper and lower case letters or numbers, up to 18 digits"),
	VALID_BEGIN_TIME_BEHIND_NOW("03","可用开始时间必须大于等于领取开始时间"),
	VALID_END_TIME_BEHIND_VALID_BEGIN_TIME("04","可用结束时间大于可用开始时间"),
	COUPON_ACTIVIY_NOT_EXIST("07","优惠券活动编码不存在"),
	COUPON_ACTIVITY_INVALID("09","Coupon activity invalid"),
	RELEASE_QUQNTITY_BEYOND("12","优惠券活动投放数量超出"),
	ACTIVITY_CREATE_FAILED("14","活动创建失败"),
	COUPON_ACTIVITY_CREATE_FAILED("15","券活动创建失败"),
	ACTIVITY_NOT_EXIST("19","Coupon promotion not exist"),
	TOTAL_QUANTITY_OUT("24","投放数量只能大于等于0"),
	NOT_APPOINT_TYPE("25","非预约类型的投放不能取消"),
	RELEASE_HAVE_COMPLETE("26","已经投放完成状态的投放不能取消"),
    DUPLICATE_COUPON_CODE("28","duplicate coupon code: :${0}"),
    ERROR_COUPON_TYPE("29","券类型只能为01、02、03"),
    DUPLICATE_COUPON_CODE_BLANK("30","券码格式错误，存在空格"),
    NULL_FILE("31","请上传文件"),
    NOT_LIMIT_USERLIMITMAX("35","单用户领券上限不能小于0或者大于总投放量"),
	NOT_NULL_RELEASE_TIME("36","预约投放时投放时间不能为空"),
	NOT_NULL_VALID("37","可用时间段和可用天数必须有一个"),
	ERROR_VALID_01("38","已指定可用天数，不需要可用时间段"),
	ERROR_VALID_02("39","没有指定可用天数，可用开始时间和可用结束时间都不能为空"),
	ERROR_ACTIVITY_RELEASE_STATUS("40","只有已生效的活动才能投放券码"),
    TIME_START1("43","活动开始时间小于等于领取开始时间"),
    TIME_END1("44","活动结束时间大于等于可用结束时间"),
    TIME_END2("45","活动结束时间必须大于领取结束时间"),
    TIME_END3("46","领取结束时间小于等于可用结束时间"),
    RELEASE_TIME_END("47","投放时间小于活动结束时间"),
    VALID_TIME("51","可用开始时间小于可用结束时间"),
    VALID_TIME1("52","可用开始时间大于等于活动开始时间"),
    PROMOTION_CODE_ACTICVITY("56","促销优惠码活动不能进行投放"),
    NOM_TIME1("57","领取结束时间小于当前时间不能进行投放"),
    NOM_TIME2("58","可用结束时间小于当前时间不能进行投放"),
    RELEASE_QUANTITY_LIMIT("59","单批次投放上限一万"),
    NULL_PROMOTION_CODE("61","优惠码不能为空"),
    NOT_NULL_RELEASE_END_TIME("63","预约投放时投放时间必须小于领取结束时间"),
    ACTIVITY_STORE_NOT_EXIST("66","The store cannot participate in this activity."),
    COUPON_NO_PRODUCT("70","Coupon: ${0}, not available items in shopping cart"),
    RELEASE_QUANTITY_USED_LIMIT("71","优惠码投放数量只能为正整数，大于等于1时为使用次数上限，0使用次数不限"),
    USED_LIMIT("72","优惠码使用次数已达上限"),
    QUANTITY("73","投放数量只能为正整数"),
    RELEASE_QUANTITY("74","投放数量，不能为负数"),
    RELEASE_LIST("75","No coupons were put in."),
    ACTIVITY_NOT_EFFIECT_EXIST("76","No effective activity was found"),
    VALID_RELEASE_TIME("77","可用开始时间得大于等于领取开始时间"),
    NOT_NULL_RELEASE_END_TIME1("81","预约投放时投放时间必须小于可用结束时间"),
    NOT_COUPON_TYPE_EXIST("82","Coupon type invalid"),
    TOTAL_QUANTITY_OUT_LIMIT("83","投放数量应该小于这个数:1073741823"),
    NOT_NULL_RELEASE_END_TIME3("85","预约投放时投放时间必须大于活动开始时间"),
    ERROR_STATUS("86","Coupon status invalid"),
    TO_MANY_COUPON("87","当前优惠券太多，请手动选择要使用的优惠券"),

    COUPON_ACTIVITY_INVALID_COUPON_CODE("88","券失效或无对应的券活动"),

    ;

	private String code;
    private String message;

    CouponActivityChecker(String code, String message){
        this.code = Constants.ErrorCodePrefix.COUPON_ACTIVITY_CHECKER.getCodePrefix() + code;
        this.message = message;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getMessage() {
        return message;
    }
}
