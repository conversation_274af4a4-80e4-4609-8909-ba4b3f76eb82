/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.checker.activity;

import com.gtech.promotion.checker.Checker;
import com.gtech.promotion.utils.Constants;

public enum  ActivityStatisticChecker implements Checker {

    NOT_NULL_TENANT_CODE("01","租铺编码不能为空"),
    NOT_NULL_ACTIVITY_CODE("02","活动编码不能为空"),
    NOT_NULL_START_TIME("03","查询起始时间不能为空"),
    NOT_NULL_END_TIME("04","查询结束时间不能为空"),
    START_TIME_LOWER_END_TIME("05","查询起始时间不能大于查询结束时间"),
    ONLY_SUPPORT_90_DAYS("06","查询数据范围为90天"),
    NOT_EXIST_ACTIVITY("07","活动不存在"),
    NO_TIME_FORTMAT("08","时间格式不对,例如开始时间20190305000000，结束时间20190305235959")

    ;
    private String code;
    private String message;

    ActivityStatisticChecker(String code, String message){
        this.code = Constants.ErrorCodePrefix.ACTIVITY_STATISTIC_CHECKER.getCodePrefix() + code;
        this.message = message;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getMessage() {
        return message;
    }
}
