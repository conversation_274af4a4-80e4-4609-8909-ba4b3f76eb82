/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WA<PERSON>RANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.checker.coupon;

import com.gtech.promotion.checker.Checker;
import com.gtech.promotion.utils.Constants;

/**   
 */
public enum CouponExportChecker implements Checker{
	
	NOT_NULL_TENANTCODE("01","系统商户编码不能为空"),
	NOT_NULL_ACTIVITYCODE("02","活动编码不能为空"),
	NO_RELEASE("03","No release coupon"),
	NOT_ANONYMITY_COUPON("04","Only support anonymity coupon"),
    NOT_ANON_COUPON("08","只能领取优惠券"),;

	
	
	private String code;
	private String message;
	
	
	CouponExportChecker(String code, String message) {
		this.code = Constants.ErrorCodePrefix.COUPON_EXPORT_USER.getCodePrefix() + code;
        this.message = message;		
	}

	@Override
	public String getCode() {
		
		return code;
	}

	@Override
	public String getMessage() {
	
		return message;
	}

}
