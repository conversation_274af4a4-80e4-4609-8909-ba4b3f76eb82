### 创建活动-单品减金额
POST {{ url }}/promotion/activity/create
Content-Type: application/json

{
  "activityBegin": "20230705000000",
  "activityEnd": "20230706235959",
  "activityLanguages": [
    {
      "activityDesc": "",
      "activityLabel": "",
      "activityName": "满赠SPU测试",
      "language": "zh-CN"
    }
  ],
  "activityName": "满赠SPU测试",
  "activityType": "01",
  "activityUrl": "",
  "allProducts": false,
  "backgroundImage": "",
  "conditionProductType": "01",
  "customConditions": [],
  "domainCode": "DC0005",
  "funcParams": [
    {
      "functionCode": "0102",
      "functionType": "01",
      "paramType": "01",
      "paramUnit": "",
      "paramValue": "",
      "rankParam": 1
    },
    {
      "functionCode": "0202",
      "functionType": "02",
      "paramType": "01",
      "paramUnit": "",
      "paramValue": "",
      "rankParam": 1
    },
    {
      "functionCode": "0302",
      "functionType": "03",
      "paramType": "02",
      "paramUnit": "02",
      "paramValue": "1",
      "rankParam": 1
    },
    {
      "functionCode": "0406",
      "functionType": "04",
      "paramType": "02",
      "paramUnit": "02",
      "paramValue": "22",
      "rankParam": 1
    }
  ],
  "giveaways": [
    {
      "giveawayCode": "SP230608057130",
      "giveawayName": "官网男士Project Rock强森Respect短袖T恤- 磷光绿1",
      "giveawayNum": 1,
      "giveawayType": 5,
      "opsType": "",
      "rankParam": 1
    }
  ],
  "incentiveLimitedFlag": "01",
  "incentiveLimiteds": [
    {
      "limitationCode": "11",
      "limitationValue": 2
    },
    {
      "limitationCode": "02",
      "limitationValue": 1
    },
    {
      "limitationCode": "01",
      "limitationValue": 3
    },
    {
      "limitationCode": "09",
      "limitationValue": 6
    },
    {
      "limitationCode": "21",
      "limitationValue": 4
    },
    {
      "limitationCode": "15",
      "limitationValue": 7
    },
    {
      "limitationCode": "31",
      "limitationValue": 5
    }
  ],
  "itemScopeType": "1",
  "operateFirstName": "lenm",
  "operateLastName": "peng",
  "operateUser": "10211209036475",
  "opsType": "106",
  "orgCode": "default",
  "priority": 8,
  "productSelectionType": "01",
  "products": [
    {
      "allProduct": true,
      "attrType": "01",
      "attributes": [],
      "brandCode": "0000",
      "brandName": "",
      "categoryCode": "0000",
      "categoryName": "",
      "orgCode": "default",
      "productTag": "TC20230630122345001072",
      "seqNum": 1,
      "spuAttributes": []
    }
  ],
  "promotionCategory": "",
  "promotionCategoryName": "",
  "qualifications": [],
  "ribbonImage": "",
  "ribbonPosition": "",
  "ribbonText": "",
  "sponsors": "default",
  "storeType": "00",
  "templateCode": "0102020203020406",
  "tenantCode": "200005"
}

### 更新活动
POST {{ url }}/promotion/activity/update
Content-Type: application/json

{
  "domainCode": "1",
  "tenantCode": "1",
  "activityCode": "10101195441357606395",
  "activityName": "testtest1212",
  "sponsors": "zxk",
  "activityType": "01",
  "activityBegin": "20201010100101",
  "activityEnd": "20210730132001",
  "templateCode": "0101020103010401",
  "itemScopeType": 2,
  "conditionProductType": "01",
  "incentiveProductType": "00",
  "incentiveLimitedFlag": "01",
  "storeType": "00",
  "incentiveLimiteds": [
    {
      "limitationCode": "11",
      "limitationValue": 2
    },
    {
      "limitationCode": "01",
      "limitationValue": 100
    },
    {
      "limitationCode": "02",
      "limitationValue": 100000
    },
    {
      "limitationCode": "09",
      "limitationValue": 20
    }
  ],
  "productDetails": [
    {
      "seqNum": "1",
      "productCode": "1",
      "skuCode": "1"
    }
  ],
  "funcParams": [
    {
      "functionType": "01",
      "functionCode": "0101",
      "paramType": "01",
      "rankParam": 1
    },
    {
      "functionType": "02",
      "functionCode": "0201",
      "paramType": "01",
      "rankParam": 1
    },
    {
      "functionType": "03",
      "functionCode": "0301",
      "paramType": "01",
      "rankParam": 1
    },
    {
      "functionType": "04",
      "functionCode": "0401",
      "paramType": "02",
      "paramValue": "10",
      "paramUnit": "01",
      "rankParam": 1
    }
  ],
  "activityLanguages": [
    {
      "activityName": "testtetststset",
      "activityLabel": "dddddd",
      "language": "en-US"
    }
  ]
}

### 更新活动状态
POST {{ url }}/promotion/activity/status/update
Content-Type: application/json

{
  "domainCode": "1",
  "tenantCode": "1",
  "activityCode": "10101195441357606395",
  "activityStatus": "04",
  "operateUser": "12"
}

### 更新活动商品
POST {{ url }}/promotion/activity/product/update
Content-Type: application/json

{
  "activityCode": "11001195442825718775",
  "allProducts": false,
  "conditionProductType": "00",
  "domainCode": "DC0001",
  "incentiveProductType": "01",
  "itemScopeType": "1",
  "operateUser": "10200629006180",
  "productCondition": "01",
  "productSelectionType": "01",
  "products": [
    {
      "allProduct": true,
      "brandCode": "0000",
      "brandName": "UNLIMITED",
      "categoryCode": "0000",
      "categoryName": "UNLIMITED",
      "seqNum": 1
    },
    {
      "allProduct": false,
      "attributes": [],
      "brandCode": "BR00000018",
      "brandName": "",
      "categoryCode": "CA00000076,CA00000077,CA00000079,CA00000080,CA200408000025,CA200415000026",
      "categoryName": "",
      "orgCode": "default",
      "seqNum": 2
    }
  ],
  "templateCode": "0102020203030412",
  "tenantCode": "100001"
}

### 查询活动详情
POST {{ url }}/promotion/activity/find
Content-Type: application/json

{
  "activityCode": "11001195442825718775",
  "domainCode": "DC0001",
  "tenantCode": "100001"
}


### 查询活动详情
POST {{ url }}/promotion/activity/query
Content-Type: application/json

{
  "activityCode": "10101217863451004057",
  "channelCode": "SPEEDWORK",
  "opsType": "101,102",
  "activityStatus": "01,04",
  "domainCode": "DC0001",
  "tenantCode": "100001"
}