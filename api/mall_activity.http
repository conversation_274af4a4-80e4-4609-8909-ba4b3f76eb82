### 查询用户券码列表
POST {{ url }}/promotion/activity/queryActivityListByProductList
Content-Type: application/json

{
  "domainCode":"DC0001",
  "tenantCode":"100000",
  "products": [
    {
      "skuCode": "1"
    },
    {
      "skuCode": "2"
    }
  ]
}

### 查询用户券码列表
POST {{ url }}/promotion/activity/queryActivityListByProduct
Content-Type: application/json

{
  "brandCode":"BR200908000056",
  "domainCode":"DC0002",
  "productCode":"SP200921164012",
  "tenantCode":"100003"
}

###  购物车和结算
POST {{ url }}/promotion/activity/calcShoppingCart
Content-Type: application/json

{
  "domainCode": "DC0005",
  "tenantCode": "200005",
  "language": "zh-cn",
  "channelCode": 0,
  "memberCode": "1",
  "qualifications": {
    "": [
      ""
    ]
  },
  "customConditionMap": {
    "": ""
  },
  "memberLevelCode": "",
  "memberLabelCode": "",
  "couponCodes": "",
  "promotionTime": "",
  "cartStoreList": [
    {
      "orgCode": "default",
      "storeName": "",
      "cartItemList": [
        {
          "categoryCodes": [
            ""
          ],
          "brandCode": "",
          "attributes": [
            {
              "attributeCode": "",
              "attributeValues": "",
              "operator": ""
            }
          ],
          "productTag": "",
          "productCode": "SP230608057130",
          "skuCode": "SK23060800001781",
          "combineSkuCode": "",
          "productPrice": 0.0,
          "quantity": 1,
          "selectionFlag": "1",
          "productAmount": 0.0
        }
      ]
    }
  ],
  "postage": 0.0
}