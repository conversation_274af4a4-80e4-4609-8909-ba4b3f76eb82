POST {{ url }}/promotion/activity/calcShoppingCart
Content-Type: application/json

{
  "channelCode": "100001",
  "domainCode": "DC0001",
  "memberCode": "10230703002980",
  "tenantCode": "100001",
  "couponCodes":"400479119000060902",
  "cartStoreList": [
    {
      "cartItemList": [
        {
          "productCode": "SP230704134530",
          "skuCode": "SK23070400009045",
          "productPrice": "500",
          "quantity": "01",
          "selectionFlag": "01"
        }
      ]
    }
  ]
}

###

POST {{ url }}/promotion/order/createOrder
Content-Type: application/json

{
  "cartStoreList": [
    {
      "cartItemList": [
        {
          "productCode": "SP230704134530",
          "skuCode": "SK23070400009045",
          "productPrice": "500",
          "quantity": "01",
          "selectionFlag": "01"
        }
      ]
    }
  ],
  "couponCodes": "400479119000060902",
  "domainCode": "DC0001",
  "freePostage": 0,
  "language": "id-ID",
  "memberCode": "10230703002980",
  "orderNo": "10230703002980",
  "postage": 0.0,
  "promoDeductedAmount": 0,
  "promoRewardPostage": 0.0,
  "tenantCode": "100001",
  "promoGiveaways": [
    {
      "activityCode": "20602054989087554814",
      "giveaways": [
        {
          "giveawayCode": "SK23070400009046",
          "giveawayName": "skuNam11688464867",
          "giveawayNum": 1,
          "giveawayType": 1,
          "opsType": "",
          "rankParam": 1
        }
      ]
    }
  ]
}
