### 商品活动匹配接口测试
POST {{ url }}/promotion/activity/matchProductsToActivities
Content-Type: application/json

{
  "domainCode": "DC0001",
  "tenantCode": "100000",
  "memberCode": "10230703002980",
  "language": "zh-CN",
  "orgCodes": ["100001"],
  "qualifications": {
    "memberLevel": ["VIP", "GOLD"]
  },
  "promotionTime": "20250123120000",
  "products": [
    {
      "productCode": "SP230704134530",
      "skuCode": "SK23070400009045",
      "productPrice": 500.00,
      "quantity": 2
    },
    {
      "productCode": "SP230704134531",
      "skuCode": "SK23070400009046",
      "productPrice": 300.00,
      "quantity": 1
    },
    {
      "productCode": "SP230704134532",
      "skuCode": "SK23070400009047",
      "productPrice": 200.00,
      "quantity": 3
    }
  ],
  "activityCodes": [
    "AC230704001",
    "AC230704002"
  ]
}

### 商品活动匹配接口测试 - 不指定活动列表（匹配所有活动）
POST {{ url }}/promotion/activity/matchProductsToActivities
Content-Type: application/json

{
  "domainCode": "DC0001",
  "tenantCode": "100000",
  "memberCode": "10230703002980",
  "language": "zh-CN",
  "products": [
    {
      "productCode": "SP230704134530",
      "skuCode": "SK23070400009045",
      "productPrice": 500.00,
      "quantity": 1
    },
    {
      "productCode": "SP230704134531",
      "skuCode": "SK23070400009046",
      "productPrice": 300.00,
      "quantity": 2
    }
  ]
}

### 商品活动匹配接口测试 - 最简参数
POST {{ url }}/promotion/activity/matchProductsToActivities
Content-Type: application/json

{
  "domainCode": "DC0001",
  "tenantCode": "100000",
  "products": [
    {
      "productCode": "SP230704134530",
      "skuCode": "SK23070400009045"
    }
  ]
}
