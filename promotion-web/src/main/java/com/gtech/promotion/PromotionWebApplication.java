/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.gtech.promotion.mq.MQConsumerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableAsync;
import tk.mybatis.spring.annotation.MapperScan;

@SpringBootApplication(scanBasePackages = { "com.gtech" })
@EnableCaching
@EnableFeignClients(basePackages = { "com.gtech" })
@EnableDiscoveryClient
@EnableAsync
@EnableApolloConfig(value={"application"})
@MapperScan(basePackages = "com.gtech.promotion.dao.mapper")
public class PromotionWebApplication {

    public static void main(String[] args) {

        SpringApplication.run(PromotionWebApplication.class, args);

        // 启动mq consumer
        MQConsumerFactory.getInstance().getConsumer();
    }

}
