/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.controller.activity;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.checker.activity.TPromoActivityChecker;
import com.gtech.promotion.code.CodeHelper;
import com.gtech.promotion.code.activity.ActivityTypeEnum;
import com.gtech.promotion.component.activity.ActivityComponentDomain;
import com.gtech.promotion.controller.BaseController;
import com.gtech.promotion.domain.activity.ActivityDomain;
import com.gtech.promotion.dto.in.activity.*;
import com.gtech.promotion.dto.out.activity.TPromoActivityListMallOutDTO;
import com.gtech.promotion.dto.out.activity.TPromoActivityListOutDTO;
import com.gtech.promotion.dto.out.activity.TPromoActivityOutDTO;
import com.gtech.promotion.exception.Check;
import com.gtech.promotion.page.RequestPage;
import com.gtech.promotion.utils.Constants;
import com.gtech.promotion.vo.param.UpdateWinTogetherStatus;
import com.gtech.promotion.vo.param.activity.*;
import com.gtech.promotion.vo.result.activity.FindActivityResult;
import com.gtech.promotion.vo.result.activity.QueryActivityListResult;
import com.gtech.promotion.vo.result.activity.QueryListResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.io.Serializable;
import java.util.List;

/**
 * 活动 控制层
 */
@Slf4j
@Api(value = "Promotion Activity API", tags = { "Promotion Activity API" })
@RestController
public class ActivityController extends BaseController {

	@Autowired
	private ActivityComponentDomain activityComponentDomain;

	@ApiOperation(value = "Win together to create promotions", notes = "Win together to create promotions")
	@PostMapping(value = "/activity/updateBatchWinTogetherActivity")
	@Transactional
	public Result updateBatchWinTogetherActivity(@RequestBody WinTogetherParam param) {
		log.info("updateBatchWinTogetherActivity: {}", JSONObject.toJSONString(param));
		UpdatePromoActivityParam updatePromoActivityParam = new UpdatePromoActivityParam();
		// 公共属性
		BeanUtils.copyProperties(param, updatePromoActivityParam);
		List<WinTogetherDifferentParam> activityParams = param.getActivityParams();
		for (WinTogetherDifferentParam activityParam : activityParams) {
			// 每个活动持有不同属性
			BeanUtils.copyProperties(activityParam, updatePromoActivityParam);
			updatePromoActivityParam.validate();
			// 赋值给传入参数
			ActivityUpdateDTO activityDomain = BeanCopyUtils.jsonCopyBean(updatePromoActivityParam,
					ActivityUpdateDTO.class);
			activityDomain.setUpdateUser(param.getOperateUser());
			activityDomain.setActivityType(ActivityTypeEnum.ACTIVITY.code());
			activityComponentDomain.updatePromoActivity(activityDomain);
		}
		return Result.ok();
	}

	@ApiOperation(value = "Update wintogether promotion status", notes = "Update wintogether promotion status")
	@PostMapping(value = "/activity/updateWinTogetherActivityStatus")
	@Transactional
	public Result updateWinTogetherActivityStatus(@RequestBody UpdateWinTogetherStatus param) {
		param.validate();
		log.info("updateWinTogetherActivityStatus: {}", JSONObject.toJSONString(param));
		UpdateActivityStatusInDTO dto = BeanCopyUtils.jsonCopyBean(param, UpdateActivityStatusInDTO.class);
		List<String> activityCodes = param.getActivityCodes();
		activityCodes.forEach(x -> {
			dto.setActivityCode(x);
			this.activityComponentDomain.updateActivityStatus(dto);
		});
		return Result.ok();
	}

	@ApiOperation(value = "Create a new promotion activity", notes = "Create a new promotion activity")
	@PostMapping(value = "/activity/create")
	public Result<String> createPromoActivity(@RequestBody CreatePromoActivityParam param) {
		log.info("createPromoActivity: {}", JSONObject.toJSONString(param));
		param.validate();
		ActivityDomain activityDomain = BeanCopyUtils.jsonCopyBean(param, ActivityDomain.class);
		activityDomain.setCreateUser(param.getOperateUser());
		activityDomain.setActivityType(ActivityTypeEnum.ACTIVITY.code());
		if (StringUtils.isEmpty(param.getOrgCode())) {
			activityDomain.setOrgCode(Constants.DEFAULT_ORG);
		}
		return Result.ok(activityComponentDomain.createPromoActivity(activityDomain));
	}

	@ApiOperation(value = "Update an existing promotion activity", notes = "Update an existing promotion activity")
	@PostMapping(value = "/activity/update")
	public Result<String> updatePromoActivity(@RequestBody UpdatePromoActivityParam param) {
		log.info("updatePromoActivity: {}", JSONObject.toJSONString(param));
		param.validate();
		ActivityUpdateDTO activityDomain = BeanCopyUtils.jsonCopyBean(param, ActivityUpdateDTO.class);
		activityDomain.setUpdateUser(param.getOperateUser());
		activityDomain.setActivityType(ActivityTypeEnum.ACTIVITY.code());

		int update = activityComponentDomain.updatePromoActivity(activityDomain);
		return Result.ok(update >= 1 ? "Update succeeded." : "Update failed.");
	}

	@ApiOperation(value = "Update status for an existing promotion activity", notes = "Update status for an existing promotion activity.")
	@PostMapping(value = "/activity/status/update")
	public Result<String> updateActivityStatus(@RequestBody UpdateActivityStatusInDTO updateActivityStatusInDTO) {
		log.info("修改活动状态：{}", updateActivityStatusInDTO);
		String tenantCode = updateActivityStatusInDTO.getTenantCode();
		String activityCode = updateActivityStatusInDTO.getActivityCode();
		String activityStatus = updateActivityStatusInDTO.getActivityStatus();
		Check.check(StringUtil.isBlank(tenantCode), TPromoActivityChecker.NOT_NULL_TENANT_CODE);
		Check.check(StringUtil.isBlank(activityCode), TPromoActivityChecker.NOT_NULL_ACTIVITYCODE);
		Check.check(StringUtil.isBlank(activityStatus), TPromoActivityChecker.NOT_NULL_ACTIVITY_STATUS);
		this.activityComponentDomain.updateActivityStatus(updateActivityStatusInDTO);
		return Result.ok("更新成功");
	}

	@ApiOperation(value = "Delete an existing promotion activity", notes = "Delete an existing promotion activity")
	@PostMapping(value = "/activity/delete")
	public Result<Object> deleteActivity(@RequestBody TenantCodeActivityIdDTO tenantCodeActivityIdDTO) {
		log.info("删除活动：{}", JSONObject.toJSONString(tenantCodeActivityIdDTO));
		Check.check(StringUtil.isBlank(tenantCodeActivityIdDTO.getTenantCode()),
				TPromoActivityChecker.NOT_NULL_TENANT_CODE);
		Check.check(StringUtil.isBlank(tenantCodeActivityIdDTO.getActivityCode()),
				TPromoActivityChecker.NOT_NULL_ACTIVITYCODE);
		activityComponentDomain.deleteActivityCorrelation(tenantCodeActivityIdDTO.getTenantCode(),
				tenantCodeActivityIdDTO.getActivityCode());

		return Result.ok();
	}

	@ApiOperation(value = "Find an existing promotion activity detail", notes = "Find an existing promotion activity detail.")
	@PostMapping(value = "/activity/find")
	public Result<FindActivityResult> findActivity(@RequestBody FindActivityParam param) {
		log.info("查询促销活动:{{}}", param);
		param.validate();
		ActivityParamDTO activityParamDTO = BeanCopyUtils.jsonCopyBean(param, ActivityParamDTO.class);
		TPromoActivityOutDTO result = activityComponentDomain.findActivityDetail(activityParamDTO.getTenantCode(),
				activityParamDTO.getActivityCode(), activityParamDTO.getOrgCode());

		return Result.ok(BeanCopyUtils.jsonCopyBean(result, FindActivityResult.class));
	}

	@ApiOperation(value = "Query existing promotion activities list", notes = "Query existing promotion activities list")
	@PostMapping(value = "/activity/query")
	public PageResult<QueryActivityListResult> queryActivityList(@RequestBody QueryActivityListParam param) {
		log.info("查询活动列表:{}", JSON.toJSONString(param));
		param.validate();
		TPromoActivityListInDTO paramDto = BeanCopyUtils.jsonCopyBean(param, TPromoActivityListInDTO.class);
		paramDto.setOrgCode(CodeHelper.getOrgCode(param.getOrgCode()));
		RequestPage page = new RequestPage(param.getPageNum(), param.getPageSize());
		PageInfo<TPromoActivityListOutDTO> pageInfoList = activityComponentDomain.queryAllActivity(paramDto, page);
		return PageResult.ok(BeanCopyUtils.jsonCopyList(pageInfoList.getList(), QueryActivityListResult.class),
				pageInfoList.getTotal());
	}

	@ApiOperation(value = "Mall query activity list", notes = "Mall query activity list")
	@PostMapping(value = "/mall/activity/query")
	public Result<List<TPromoActivityListMallOutDTO>> getQueryAllActivityMall(
			@RequestBody TPromoActivityListMallInDTO inDTO) {
		log.info("商城查询活动列表:{{}}", inDTO);
		inDTO.setOrgCode(CodeHelper.getOrgCode(inDTO.getOrgCode()));
		Check.check(StringUtil.isBlank(inDTO.getTenantCode()), TPromoActivityChecker.NOT_NULL_TENANT_CODE);
		return Result.ok(activityComponentDomain.queryMallAllActivity(inDTO));
	}

	@ApiOperation(value = "Extend period for an active", notes = "Extend period for an active")
	@PostMapping(value = "/activity/extend")
	public Result<String> activityExtend(@RequestBody ActivityExtendParam param) {
		log.info("活动结束时间延期参数:{{}}", param);
		param.validate();
		ActivityExtendInDTO dto = BeanCopyUtils.jsonCopyBean(param, ActivityExtendInDTO.class);
		int update = activityComponentDomain.activityExtend(dto);
		return Result.ok(update == 1 ? "Update succeeded." : "Update failed.");
	}

	@ApiOperation(value = "Query existing promotion and marketing activities list", notes = "Query existing promotion and marketing activities list")
	@PostMapping(value = "/query/all")
	public PageResult<QueryListResult> queryList(@RequestBody QueryListParam param) {
		log.info("查询活动列表:{}", JSON.toJSONString(param));
		param.validate();
		PageInfo<QueryListResult> pageInfoList = activityComponentDomain.queryList(param);
		return PageResult.ok(pageInfoList.getList(), pageInfoList.getTotal());
	}

	@ApiOperation(value = "Update product for an existing promotion", notes = "Update product for an existing promotion.")
	@PostMapping(value = "/activity/product/update")
	public Result<Object> updateActivityProduct(@RequestBody UpdateActivityProductParam param) {
		log.info("更新活动商品:{}", JSON.toJSONString(param));
		param.validate();
		ActivityUpdateDTO activityDomain = BeanCopyUtils.jsonCopyBean(param, ActivityUpdateDTO.class);
		activityDomain.setUpdateUser(param.getOperateUser());
		activityComponentDomain.updateActivityProduct(activityDomain);
		return Result.ok();
	}

	@ApiOperation(value = "Close expired activities periodically", notes = "Close expired activities periodically")
	@PostMapping(value = "/activity/expire")
	public Result<Serializable> expireActivity() {
		activityComponentDomain.expireActivity();
		return Result.ok();
	}

	@ApiOperation(value = "Batch update product blacklist", notes = "Batch update product blacklist.")
	@PostMapping(value = "/activity/updateBlackList")
	public Result<Serializable> updateActivityProductDetailBlackList(
			@RequestBody UpdateActivityProductDetailBlackListParam param) {
		log.info("批量更新商品黑名单入参:{{}}", JSONObject.toJSONString(param));
		param.validate();
		activityComponentDomain.updateActivityProductDetailBlackList(param.getTenantCode(),
				param.getProductDetailBlackList());
		return Result.ok();
	}
}
