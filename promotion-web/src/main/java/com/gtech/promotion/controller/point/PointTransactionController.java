package com.gtech.promotion.controller.point;

import com.alibaba.fastjson.JSON;
import com.gtech.commons.page.PageData;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.promotion.component.coupon.RegularDeductionOfExpiredPointsComponent;
import com.gtech.promotion.dto.in.point.PointTransactionDto;
import com.gtech.promotion.service.point.PointTransactionService;
import com.gtech.promotion.vo.param.point.query.GetPointTransactionParam;
import com.gtech.promotion.vo.param.point.query.QueryPointTransactionParam;
import com.gtech.promotion.vo.result.point.PointTransactionResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.io.Serializable;

@Slf4j
@RequestMapping(value = "/point")
@RestController
@ApiIgnore
@Api(value = "Point Transaction API",tags = "Point Transaction API")
public class PointTransactionController {

    @Resource
    private PointTransactionService pointTransactionService;

    @Autowired
    private RegularDeductionOfExpiredPointsComponent regularDeductionOfExpiredPointsComponent;


    /**
     * 获取积分流水列表
     */
    @SuppressWarnings("unchecked")
    @ApiOperation(value = "queryPointTransactionList")
    @PostMapping(value = "/queryPointTransactionList")
    public PageResult<PointTransactionResult> queryPointTransactionList(@RequestBody QueryPointTransactionParam param) {
        log.info("promotion_point:queryPointTransactionList:{{}}", JSON.toJSONString(param));

        param.validate();

        //check参数

        PointTransactionDto pointTransactionDto = BeanCopyUtils.jsonCopyBean(param, PointTransactionDto.class);

        PageData<PointTransactionResult> pageResults = pointTransactionService.queryPointTransactionPage(pointTransactionDto);
        return PageResult.ok(BeanCopyUtils.jsonCopyList(pageResults.getList(), PointTransactionResult.class), pageResults.getTotal());
    }

    /**
     * 获取积分流水详情
     */
    @ApiOperation(value = "getPointTransaction")
    @PostMapping(value = "/getPointTransaction")
    public Result<PointTransactionResult> getPointTransaction(@RequestBody GetPointTransactionParam param) {
        log.info("promotion_point:getPointTransaction:{{}}", JSON.toJSONString(param));

        param.validate();

        return Result.ok(pointTransactionService.getPointTransaction(param));
    }

    /**
     * 定时任务--扣除过期的积分
     */
    @ApiOperation(value = "regularDeductionOfExpiredPoints")
    @PostMapping(value = "/regularDeductionOfExpiredPoints")
    public Result<Serializable> regularDeductionOfExpiredPoints() {
        log.info("promotion_point:regularDeductionOfExpiredPoints");
        regularDeductionOfExpiredPointsComponent.regularDeductionOfExpiredPoints();
        return Result.ok();
    }




}
