package com.gtech.promotion.controller.growth;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableMap;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.DateUtil;
import com.gtech.promotion.service.growth.GrowthAccountSaveService;
import com.gtech.promotion.service.growth.GrowthAccountService;
import com.gtech.promotion.vo.param.growth.CreateGrowthAccountParam;
import com.gtech.promotion.vo.param.growth.UpdateGrowthParam;
import com.gtech.promotion.vo.param.growth.query.GetGrowthAccountParam;
import com.gtech.promotion.vo.param.growth.query.GrowthAccountUniqueParam.GrowthAccountStatusUniqueVo;
import com.gtech.promotion.vo.param.growth.query.QueryGrowthAccountParam;
import com.gtech.promotion.vo.result.growth.GrowthAccountResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.util.Map;

@RequestMapping(value = "/growth")
@RestController
@ApiIgnore
@Api(value = "Growth Account API", tags = "Growth Account API")
public class GrowthAccountController {

    @Resource
	private GrowthAccountService growthAccountService;
	@Resource
	private GrowthAccountSaveService growthAccountSaveService;

	/**
	 * @Description: 获取成长值列表
	 */
	@SuppressWarnings("unchecked")
	@ApiOperation(value = "query growth accountList")
	@PostMapping(value = "/queryGrowthAccountList")
	public PageResult<GrowthAccountResult> queryGrowthAccountList(@RequestBody @Validated QueryGrowthAccountParam growthAccountQueryVo) {
    	//check参数
		Map<String, Object> paramMap = JSON.parseObject(JSON.toJSONString(growthAccountQueryVo), Map.class);
		PageResult<GrowthAccountResult> resultPageResult = growthAccountService.queryGrowthAccountPage(paramMap);
		for (GrowthAccountResult result : resultPageResult.getData().getList()) {
			result.setCreateTimeString(DateUtil.format(result.getCreateTime(),DateUtil.FORMAT_YYYYMMDDHHMISS));
			result.setUpdateTimeString(DateUtil.format(result.getUpdateTime(),DateUtil.FORMAT_YYMMDDHHMISS));
			result.setValidBeginTimeString(DateUtil.format(result.getValidBeginTime(),DateUtil.FORMAT_YYYYMMDDHHMISS));
			result.setValidEndTimeString(DateUtil.format(result.getValidEndTime(),DateUtil.FORMAT_YYYYMMDDHHMISS));
		}
		return resultPageResult;
	}

	/**
	 * @Description: 获取成长值详情
	 */
	@ApiOperation(value = "get growth account")
	@PostMapping(value = "/getGrowthAccount")
	public Result<GrowthAccountResult> getGrowthAccount(@RequestBody @Validated GetGrowthAccountParam param) {
		GrowthAccountResult growthAccount = growthAccountService.getGrowthAccount(param);
		if (null == growthAccount){
			return Result.ok(growthAccount);
		}
		growthAccount.setCreateTimeString(DateUtil.format(growthAccount.getCreateTime(),DateUtil.FORMAT_YYYYMMDDHHMISS));
		growthAccount.setUpdateTimeString(DateUtil.format(growthAccount.getUpdateTime(),DateUtil.FORMAT_YYYYMMDDHHMISS));
		growthAccount.setValidBeginTimeString(DateUtil.format(growthAccount.getValidBeginTime(),DateUtil.FORMAT_YYYYMMDDHHMISS));
		growthAccount.setValidEndTimeString(DateUtil.format(growthAccount.getValidEndTime(),DateUtil.FORMAT_YYYYMMDDHHMISS));
		return Result.ok(growthAccount);
	}

	/**
	 * @Description: 修改成长值状态
	 */
	@ApiOperation(value = "update growth account status")
	@PostMapping(value = "/updateGrowthAccountStatus")
	public Result<Void> updateGrowthAccountStatus(@RequestBody @Validated GrowthAccountStatusUniqueVo accountStatusUniqueVo) {
		growthAccountService.updateGrowthAccountStatus(accountStatusUniqueVo);
		return Result.ok();
	}

	/**
	 * @Description:新建成长值
	 */
	@ApiOperation(value = "create growth account")
	@PostMapping(value = "/createGrowthAccount")
	public Result<Map<String, String>> createGrowthAccount(@RequestBody @Validated CreateGrowthAccountParam growthAccountVo) {
		String growthAccountCode = growthAccountSaveService.saveGrowthAccount(growthAccountVo);
		return Result.ok(ImmutableMap.of("growthAccountCode", growthAccountCode));
	}

	/**
	 * @Description: 编辑成长值
	 */
	@ApiOperation(value = "edit growth account")
	@PostMapping(value = "/editGrowthAccount")
	public Result<Void> editGrowthAccount(@RequestBody @Validated CreateGrowthAccountParam growthAccountVo) {
		growthAccountService.updateGrowthAccount(growthAccountVo);
		return Result.ok();
	}

	/**
	 * 成长值变更
	 */
	@ApiOperation(value = "growth change")
	@PostMapping(value = "/updateGrowth")
	public Result<Void> changeGrowth(@RequestBody UpdateGrowthParam param) {

	    param.validate();

	    growthAccountService.updateGrowth(param);

	    return Result.ok();
	}
}
