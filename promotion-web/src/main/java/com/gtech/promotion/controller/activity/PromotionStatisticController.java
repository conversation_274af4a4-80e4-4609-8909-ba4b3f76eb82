/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.controller.activity;

import com.gtech.commons.result.Result;
import com.gtech.promotion.component.activity.ActivityStatisticDomain;
import com.gtech.promotion.controller.BaseController;
import com.gtech.promotion.dto.in.activity.CouponOrderStatisticInDTO;
import com.gtech.promotion.dto.out.activity.OrderActivityOutDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@Api(value = "Promotion Statistic API",tags = {"Promotion Statistic API"})
@RestController
public class PromotionStatisticController extends BaseController{

    @Autowired
    private ActivityStatisticDomain statisticDomain;

    //查询订单数据
    @ApiOperation(value = "查询订单数据 wtcn-2.5.0")
    @PostMapping(value = "activity/order/data")
    public Result<List<OrderActivityOutDTO>> queryCouponOrderData(@RequestBody CouponOrderStatisticInDTO statisticInDTO){
        return Result.ok(statisticDomain.queryCouponOrderData(statisticInDTO));
    }

}
