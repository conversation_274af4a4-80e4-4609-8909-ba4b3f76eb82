/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.controller.coupon;

import com.alibaba.fastjson.JSONObject;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.checker.activity.TPromoActivityChecker;
import com.gtech.promotion.checker.coupon.CouponAllocateChecker;
import com.gtech.promotion.checker.coupon.CouponExportChecker;
import com.gtech.promotion.component.coupon.CouponCodeUserComponent;
import com.gtech.promotion.component.coupon.CouponInnerCodeComponent;
import com.gtech.promotion.controller.BaseController;
import com.gtech.promotion.dto.in.coupon.BindingCouponInDTO;
import com.gtech.promotion.dto.in.coupon.ExportCouponUserDto;
import com.gtech.promotion.exception.Check;
import com.gtech.promotion.utils.DateValidUtil;
import com.gtech.promotion.vo.param.coupon.ExportCouponDetailParam;
import com.gtech.promotion.vo.result.coupon.ExportCouponUserDetailResult;
import com.gtech.promotion.vo.result.coupon.ExportCouponUserResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;

/**
 * 用户优惠券接口
 */
@RestController
@Slf4j
@Api(value = "Coupon Code User API",tags = {"Coupon Code User API"})
public class CouponCodeUserController extends BaseController{

    @Autowired
    private CouponInnerCodeComponent couponInnerCodeDomain;

    @Autowired
    private CouponCodeUserComponent couponCodeUserComponent;

    @ApiOperation(value = "Bind external coupon code",notes = "Bind external coupon code")
    @PostMapping(value = "/v1/binding/coupon/user")
    public Result<Object> bindingCoupon(@RequestBody BindingCouponInDTO bindingCouponInDTO){
        log.info("领取优惠券参数:{{}}", JSONObject.toJSONString(bindingCouponInDTO));
        Check.check(StringUtil.isBlank(bindingCouponInDTO.getTenantCode()), CouponAllocateChecker.NOT_NULL_MERCCODE);
        Check.check(StringUtil.isBlank(bindingCouponInDTO.getActivityCode()), CouponExportChecker.NOT_NULL_ACTIVITYCODE);
        Check.check(StringUtil.isBlank(bindingCouponInDTO.getValidEndTime()), CouponAllocateChecker.VALID_TIME);
        Check.check(StringUtil.isBlank(bindingCouponInDTO.getValidStartTime()), CouponAllocateChecker.VALID_TIME);
        Check.check(!DateValidUtil.isValidDate(bindingCouponInDTO.getValidStartTime()), TPromoActivityChecker.ERROR_TIME_FORMAT);
        Check.check(!DateValidUtil.isValidDate(bindingCouponInDTO.getValidEndTime()), TPromoActivityChecker.ERROR_TIME_FORMAT);
        Check.check(bindingCouponInDTO.getValidEndTime().compareTo(bindingCouponInDTO.getValidStartTime()) <= 0, CouponAllocateChecker.START_END_TIME);
        Check.check(CollectionUtils.isEmpty(bindingCouponInDTO.getUserCoupons()), CouponAllocateChecker.NOT_USER_COUPONS);
       return Result.ok(couponInnerCodeDomain.bindingUserAndCoupon(bindingCouponInDTO));

    }


    @ApiOperation(value = "Export coupon details",notes = "Export coupon details")
    @PostMapping(value = "/coupon/code/user/exportCouponDetail")
    public Result<List<ExportCouponUserResult>> exportCouponDetail(@RequestBody ExportCouponDetailParam param){
        param.validate();
        log.info("导出优惠券详情：{}", JSONObject.toJSONString(param));
        ExportCouponUserDto dto = BeanCopyUtils.jsonCopyBean(param, ExportCouponUserDto.class);
        List<ExportCouponUserResult> results = couponCodeUserComponent.exportCouponDetail(dto);
        results.removeAll(Collections.singleton(null));
        return Result.ok(results);
    }


    @ApiOperation(value = "Export voucher order details",notes = "Export voucher order details")
    @PostMapping(value = "/coupon/code/user/exportCouponOrderDetail")
    public Result<List<ExportCouponUserDetailResult>> exportCouponOrderDetail(@RequestBody ExportCouponDetailParam param){
        param.validate();
        log.info("导出优惠券订单相关详情：{}", JSONObject.toJSONString(param));
        ExportCouponUserDto dto = BeanCopyUtils.jsonCopyBean(param, ExportCouponUserDto.class);
        List<ExportCouponUserDetailResult> results = couponCodeUserComponent.exportCouponOrderDetail(dto);
        results.removeAll(Collections.singleton(null));
        return Result.ok(results);
    }





}
