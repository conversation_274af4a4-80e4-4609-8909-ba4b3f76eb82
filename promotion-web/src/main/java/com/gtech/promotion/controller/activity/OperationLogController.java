package com.gtech.promotion.controller.activity;

import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.promotion.dto.in.activity.QueryOperationLogsByActivityCodeDTO;
import com.gtech.promotion.dto.out.activity.QueryOperationLogsByActivityCodeOutDTO;
import com.gtech.promotion.service.activity.OperationLogService;
import com.gtech.promotion.vo.param.activity.QueryOperationLogsByActivityCodeParam;
import com.gtech.promotion.vo.result.activity.QueryOperationLogsByActivityCodeResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@Api(value = "Promotion Operation Log API",tags = { "Promotion Operation Log API" })
@RestController
@Slf4j
public class OperationLogController {

    @Autowired
    private OperationLogService operationLogService;

    @ApiOperation(value = "Extend period for an active",notes = "Extend period for an active")
    @PostMapping(value = "/operation/log/queryByCode")
    public Result<List<QueryOperationLogsByActivityCodeResult>> queryOperationLogsByActivityCode(@RequestBody QueryOperationLogsByActivityCodeParam param){
        param.validate();

        QueryOperationLogsByActivityCodeDTO dto = BeanCopyUtils.jsonCopyBean(param, QueryOperationLogsByActivityCodeDTO.class);
        List<QueryOperationLogsByActivityCodeOutDTO> list = operationLogService.queryOperationLogsByActivityCode(dto);
        return Result.ok(BeanCopyUtils.jsonCopyList(list, QueryOperationLogsByActivityCodeResult.class));
    }
}
