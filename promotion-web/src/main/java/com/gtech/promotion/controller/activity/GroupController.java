package com.gtech.promotion.controller.activity;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.promotion.component.activity.PromoGroupDomain;
import com.gtech.promotion.dto.out.activity.GroupActivityVO;
import com.gtech.promotion.vo.param.activity.*;
import com.gtech.promotion.vo.result.activity.ActivityGroupResult;
import com.gtech.promotion.vo.result.activity.QueryActivityGroupListResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/6/14 9:54
 */
@Slf4j
@Api(value = "Promotion Group API", tags = { "Promotion Group API" })
@RestController
public class GroupController {

    @Autowired
    private PromoGroupDomain promoGroupDomain;


    @ApiOperation(value = "Initialize group", notes = "Initialize group.")
    @PostMapping(value = "/activity/group/initialize")
    public Result<String> initGroup(@RequestBody GroupInitParam groupInitParam){
        log.info("initGroup:{{}}", JSON.toJSONString(groupInitParam));
        groupInitParam.checkParam();
        promoGroupDomain.initGroupDomain(groupInitParam);
        return Result.ok();
    }


    @ApiOperation(value = "Initialize group", notes = "Initialize group.")
    @PostMapping(value = "/activity/group/init")
    public Result<String> init(@RequestBody GroupInitParam groupInitParam){
        log.info("initGroup:{{}}", JSON.toJSONString(groupInitParam));
        groupInitParam.checkParam();
        promoGroupDomain.init(groupInitParam.getDomainCode(), groupInitParam.getTenantCode());
        return Result.ok();
    }

    @ApiOperation(value = "Create group", notes = "Create activity group.")
    @PostMapping(value = "/activity/group/create")
    public Result<String> createGroup(@RequestBody GroupCreateParam groupCreateParam){
        log.info("createGroup:{{}}", JSON.toJSONString(groupCreateParam));
        groupCreateParam.checkParam();
        return Result.ok(promoGroupDomain.createGroupDomain(groupCreateParam));
    }


    @ApiOperation(value = "Update group", notes = "Update activity group.")
    @PostMapping(value = "/activity/group/update")
    public Result<Integer> updateGroup(@RequestBody GroupUpdateParam groupUpdateParam){
        log.info("groupUpdateParam:{{}}", JSON.toJSONString(groupUpdateParam));
        groupUpdateParam.checkParam();
        return Result.ok(promoGroupDomain.updateGroupDomain(groupUpdateParam));
    }

    @ApiOperation(value = "Delete group", notes = "Delete activity group")
    @PostMapping(value = "/activity/group/delete")
    public Result<Integer> deleteGroup(@RequestBody GroupDeleteParam groupDeleteParam){
        groupDeleteParam.checkParam();
        log.info("deleteGroup:{{}}", JSON.toJSONString(groupDeleteParam));
        return Result.ok(promoGroupDomain.deleteGroupDomain(groupDeleteParam));
    }

    @ApiOperation(value = "Query group list", notes = "Query activity group list")
    @PostMapping(value = "/activity/group/query")
    public Result<List<ActivityGroupResult>> queryActivityGroupList(@RequestBody GroupQueryParam groupQueryParam){

        groupQueryParam.checkParam();
        log.info("queryActivityGroupList:{{}}", JSON.toJSONString(groupQueryParam));

        List<ActivityGroupResult> result = promoGroupDomain.queryActivityGroupListDomain(groupQueryParam);

        return Result.ok(result);
    }


    @ApiOperation(value = "Binding activity to group", notes = "Binding activity to group")
    @PostMapping(value = "/activity/binding/group")
    public Result<Integer> bindingActivityToGroup(@RequestBody GroupBindingActivityParam bindingActivityParam){

        bindingActivityParam.checkParam();
        log.info("bindingActivityToGroup:{{}}", JSON.toJSONString(bindingActivityParam));

        return Result.ok(promoGroupDomain.bindingActivityToGroupDomain(bindingActivityParam));
    }

    @ApiOperation(value = "Query group list", notes = "Query group list")
    @PostMapping(value = "/activity/group/query/list")
    public PageResult<QueryActivityGroupListResult> queryActivityListUnderGroup(@RequestBody GroupQueryListParam param){

        param.checkParam();

        log.info("queryActivityListUnderGroup:{{}}", JSON.toJSONString(param));

        PageInfo<GroupActivityVO> pageInfo = promoGroupDomain.queryActivityListUnderGroup(param);

        return PageResult.ok(BeanCopyUtils.jsonCopyList(pageInfo.getList(), QueryActivityGroupListResult.class), pageInfo.getTotal());
    }

    @ApiOperation(value = "设置活动分组优先级")
    @PostMapping(value = "/activity/group/priority")
    public Result<Integer> updateGroupPriority(@RequestBody GroupSettingPriorityParam priorityParam){

        return Result.ok(promoGroupDomain.updateGroupPriorityDomain(priorityParam));
    }


}
