/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.controller.activity;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.gtech.commons.exception.ErrorCode;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.CheckUtils;
import com.gtech.commons.utils.StringUtil;
import com.gtech.promotion.calc.CalcExecuter;
import com.gtech.promotion.calc.model.CalcShoppingCart;
import com.gtech.promotion.checker.activity.TPromoProductChecker;
import com.gtech.promotion.checker.coupon.CouponErrorChecker;
import com.gtech.promotion.code.CodeHelper;
import com.gtech.promotion.code.activity.ActivityStoreEnum;
import com.gtech.promotion.code.activity.ActivityTypeEnum;
import com.gtech.promotion.component.activity.*;
import com.gtech.promotion.dao.model.activity.ActivityFunctionParamRankModel;
import com.gtech.promotion.dao.model.activity.ActivityModel;
import com.gtech.promotion.dao.model.activity.FunctionParamModel;
import com.gtech.promotion.dto.cache.ActivityCacheDTO;
import com.gtech.promotion.dto.cache.ProductSkuDetailDTO;
import com.gtech.promotion.dto.in.activity.*;
import com.gtech.promotion.dto.out.activity.*;
import com.gtech.promotion.exception.Check;
import com.gtech.promotion.exception.ErrorCodes;
import com.gtech.promotion.exception.PromotionException;
import com.gtech.promotion.page.RequestPage;
import com.gtech.promotion.service.activity.ActivityProductDetailService;
import com.gtech.promotion.utils.ActivityFilterUtil;
import com.gtech.promotion.utils.ActivityProductCheckUtil;
import com.gtech.promotion.vo.bean.FunctionParam;
import com.gtech.promotion.vo.bean.IncentiveLimited;
import com.gtech.promotion.vo.bean.Qualification;
import com.gtech.promotion.vo.param.activity.*;
import com.gtech.promotion.vo.result.activity.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.collections4.map.HashedMap;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;

import com.gtech.commons.utils.DateUtil;
import com.gtech.promotion.service.coupon.PromoCouponInnerCodeService;
import com.gtech.promotion.service.coupon.PromoCouponCodeUserService;
import com.gtech.promotion.vo.bean.ProductAttribute;
import com.gtech.promotion.dao.model.activity.TPromoActivityProductVO;
import com.gtech.promotion.dto.in.activity.ShoppingCartItem;

/**
 * 
 *Shopping mall activities and goods query and shopping cart calculation
 * <AUTHOR>
 * @Date 2020-02-12
 */
@Slf4j
@Api(value = "Mall Activity API",tags = {"Mall Activity API"})
@RestController
public class MallActivityController {

    @Autowired
    private ActivityCacheDomain activityCacheDomain;

    @Autowired
    private ActivityQueryDomain queryDomain;

    @Autowired
    private CalcExecuter calcExecuter;

    @Autowired
    private ProductDomain productDomain;

    @Autowired
    private ShoppingCartDomain shoppingCartDomain;

    @Autowired
    private ActivityComponentDomain tPromoActivityDomain;
    @Autowired
    private ActivityProductDetailService productDetailService;

    @Autowired
    @Qualifier("queryActivityListByProductListThreadPool")
    private ExecutorService promotionThreadPoolConfig;

    @Autowired
    private PromoCouponInnerCodeService couponInnerCodeService;

    @Autowired
    private PromoCouponCodeUserService couponCodeUserService;

    /**
     * 单品活动价格计算： 根据商品信息查询并计算其参与的单品促销价格
     */
    @ApiOperation(value = "(PDP) Calculate single product promotion price",notes = "(PDP) Calculate single product promotion price") //店铺待定
    @PostMapping(value = "/activity/calcSkuPromotionPrice")
    public Result<CalcSkuPromotionPriceResult> calcSkuPromotionPrice(@RequestBody CalcSkuPromotionPriceParam param) {

        log.info("商品信息:{}", JSONObject.toJSONString(param));

        // Parameter validation.
        param.validate();

        SingleProductDTO product = BeanCopyUtils.jsonCopyBean(param, SingleProductDTO.class);

        ActivityProductCheckUtil.checkProductCombine(product);
        product.setOrgCodes(CodeHelper.getOrgCodes(product.getOrgCodes()));

        Map<String, ActivityCacheDTO> activityCacheMap = activityCacheDomain.getActivityCacheMap(product.getTenantCode(), param.getLanguage(), 2, ActivityTypeEnum.ACTIVITY);

        activityCacheMap = ActivityFilterUtil.filterActivityByOrgCodes(activityCacheMap, product.getOrgCodes());
        activityCacheMap = ActivityFilterUtil.filterActivityByQualifications(activityCacheMap, product.getQualifications());

        SkuActivityPriceDTO skuActivityPriceDTO = tPromoActivityDomain.activitySkuPrice(product, activityCacheMap);

        if (null == skuActivityPriceDTO || CollectionUtils.isEmpty(skuActivityPriceDTO.getActivity())) {
            return Result.ok();
        }

        ActivityPriceDTO activityPriceDTO = skuActivityPriceDTO.getActivity().get(0);

        return Result.ok(BeanCopyUtils.jsonCopyBean(activityPriceDTO, CalcSkuPromotionPriceResult.class));
    }

    @ApiOperation(value = "(PDP) Query activity list by product",notes = "(PDP) Query activity list by product") //店铺待定
    @PostMapping("/activity/queryActivityListByProduct")
    public Result<List<QueryActivityListByProductResult>> queryActivityListByProduct(@RequestBody QueryActivityListByProductParam param){

        log.info("根据商品查询活动列表入参:{}", JSONObject.toJSONString(param));

        param.validate();

        TenantProductDTO product = BeanCopyUtils.jsonCopyBean(param, TenantProductDTO.class);

        if (StringUtils.isNotBlank(param.getOrgCodes())) {
            product.setOrgCodes(Arrays.asList(param.getOrgCodes().split(",")));
        } else {
            product.setOrgCodes(Arrays.asList(ActivityStoreEnum.ALL.code()));
        }

        ActivityProductCheckUtil.checkProductCombine(product);

        //该商户已审核成功的活动
        Map<String, ActivityCacheDTO> activityCacheMap = activityCacheDomain.getActivityCacheMap(product.getTenantCode(), product.getLanguage(), 2, ActivityTypeEnum.ACTIVITY);

        // 过滤符合的活动
        Map<String, ActivityCacheDTO> activityCacheMap1 = ActivityFilterUtil.filterActivityByActivityType(activityCacheMap, ActivityTypeEnum.ACTIVITY);
        Map<String, ActivityCacheDTO> activityCacheMap2 = ActivityFilterUtil.filterActivityByTime(activityCacheMap1, null);
        Map<String, ActivityCacheDTO> activityCacheMap3 = ActivityFilterUtil.filterActivityByQualifications(activityCacheMap2, product.getQualifications());
        List<ProductSkuDetailDTO> productSkuDetailDTOS = productDetailService.queryListByActivityCodesAndProductCode(activityCacheMap3.keySet(), product.getProductCode());
        return Result.ok(BeanCopyUtils.jsonCopyList(queryDomain.getActivityByProduct(product, activityCacheMap3, productSkuDetailDTOS), QueryActivityListByProductResult.class));
    }

    @ApiOperation(value = "(PLP) Query activity list by product list") //店铺待定
    @PostMapping("/activity/queryActivityListByProductList")
    public Result<List<QueryActivityListByProductListResult>> queryActivityListByProductList(@RequestBody QueryActivityListByProductListParam param){

        log.info("根据批量商品查询活动列表入参:{}", JSONObject.toJSONString(param));

        param.validate();

        //该商户已审核成功的活动
        Map<String, ActivityCacheDTO> activityCacheMap = activityCacheDomain.getActivityCacheMap(param.getTenantCode(), param.getLanguage(), 2, ActivityTypeEnum.ACTIVITY);

        // 过滤符合的活动
        activityCacheMap = ActivityFilterUtil.filterActivityByActivityType(activityCacheMap, ActivityTypeEnum.ACTIVITY);
        activityCacheMap = ActivityFilterUtil.filterActivityByTime(activityCacheMap, null);
        activityCacheMap = ActivityFilterUtil.filterActivityByQualifications(activityCacheMap, param.getQualifications());
        Map<String, ActivityCacheDTO> copyMap = new HashedMap<>();
        copyMap.putAll(activityCacheMap);
        List<QueryActivityListByProductListResult> list = new CopyOnWriteArrayList<>();
        List<TenantProductDTO> list1 = new CopyOnWriteArrayList<>();
        List<String> productCodes = new CopyOnWriteArrayList<>();
        for (QueryActivityListByProductListParam.Product product : param.getProducts()) {
            TenantProductDTO productDTO = BeanCopyUtils.jsonCopyBean(product, TenantProductDTO.class);
            ActivityProductCheckUtil.checkProductCombine(productDTO);
            if (StringUtils.isNotBlank(product.getOrgCodes())) {
                productDTO.setOrgCodes(Arrays.asList(product.getOrgCodes().split(",")));
            } else {
                productDTO.setOrgCodes(Collections.singletonList(ActivityStoreEnum.ALL.code()));
            }
            list1.add(productDTO);
            if (!productCodes.contains(productDTO.getProductCode())){
                productCodes.add(productDTO.getProductCode());
            }
        }
        List<ProductSkuDetailDTO> productSkuDetailDTOS = productDetailService.queryListByActivityCodesAndProductCodes(copyMap.keySet(), productCodes);
        CountDownLatch countDownLatch = new CountDownLatch(list1.size());
        for (TenantProductDTO productDTO : list1) {
            queryActivityListByProductListThread(copyMap, list, productSkuDetailDTOS, countDownLatch, productDTO);
        }
        try {
            countDownLatch.await();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        return Result.ok(list);
    }

    public void queryActivityListByProductListThread(Map<String, ActivityCacheDTO> copyMap, List<QueryActivityListByProductListResult> list, List<ProductSkuDetailDTO> productSkuDetailDTOS, CountDownLatch countDownLatch, TenantProductDTO productDTO) {
        promotionThreadPoolConfig.execute(() -> {
            try {
                List<ActivityInfoDTO> activityByProduct = queryDomain.getActivityByProduct(productDTO, copyMap, productSkuDetailDTOS);
                List<QueryActivityListByProductResult> queryActivityListByProductResults = BeanCopyUtils.jsonCopyList(activityByProduct, QueryActivityListByProductResult.class);
                QueryActivityListByProductListResult listResult = new QueryActivityListByProductListResult();
                listResult.setProductCode(productDTO.getProductCode());
                listResult.setSkuCode(productDTO.getSkuCode());
                listResult.setCombineSkuCode(productDTO.getCombineSkuCode());
                listResult.setActivityList(queryActivityListByProductResults);
                list.add(listResult);
            } catch (Exception e){
                log.error("queryActivityListByProductList:{}",e.getMessage());
            } finally {
                countDownLatch.countDown();
            }
        });
    }


    @ApiOperation(value = "Query product list by activity",notes = "Query product list by activity")
    @PostMapping("/activity/queryProductListByActivity")
    public Result<QueryProductListByActivityResult> queryProductListByActivity(@RequestBody QueryProductListByActivityParam param){

        log.info("根据活动编码查询商品信息:{{}}", JSONObject.toJSONString(param));
        param.validate();
        RequestPage page = new RequestPage(param.getPageNum(), param.getPageSize());
        Integer seqNum = param.getSeqNum();
        seqNum = seqNum == null ? 1 : seqNum;

        ActivityProductOutDTO activityProductOutDTO = productDomain.queryActivityProductByCode(param.getTenantCode(), param.getLanguage(), param.getActivityCode(), seqNum, page);

        return Result.ok(BeanCopyUtils.jsonCopyBean(activityProductOutDTO, QueryProductListByActivityResult.class));
    }

    @ApiOperation(value = "Calc shopping cart",notes = "Calc shopping cart")
    @PostMapping(value = "/activity/calcShoppingCart")
    public Result<List<CalcShoppingCartResult>> calcShoppingCart(@RequestBody CalcShoppingCartParam param) {

        log.info("购物车入参：{}", JSONObject.toJSONString(param));
        param.validate();
        ShoppingCartDTO shoppingCart = BeanCopyUtils.jsonCopyBean(param, ShoppingCartDTO.class);
        shoppingCart.setUserCode(param.getMemberCode());
        //获取规则缓存
        Map<String, ActivityCacheDTO> ruleCacheMap = activityCacheDomain.getActivityCacheMap(shoppingCart.getTenantCode(), param.getLanguage(), 2, null);
        if (MapUtils.isEmpty(ruleCacheMap)) {
            String couponCodes = shoppingCart.getCouponCodes();
            if (StringUtil.isNotBlank(couponCodes)) {
                Check.check(true, CouponErrorChecker.NO_EFFECTIVE);
            }
            return Result.ok(new ArrayList<>());
        }

        // 查询
        shoppingCart = shoppingCartDomain.queryActivity(shoppingCart, ruleCacheMap);

        // 计算
        List<ShoppingCartOutDTO> scoDtoList = calcExecuter.calc(new CalcShoppingCart(shoppingCart), ruleCacheMap);
        if (CollectionUtils.isEmpty(scoDtoList)) {
            log.info("scoDtoList is null");
            Result.ok();
        }

        List<CalcShoppingCartResult> resultList = new ArrayList<>();
        for(ShoppingCartOutDTO sco : scoDtoList) {
            CalcShoppingCartResult scResult = BeanCopyUtils.jsonCopyBean(sco, CalcShoppingCartResult.class);
            ErrorCode failedReason = sco.getFailedReason();
            if (null != failedReason) {
                scResult.setFalseReason(failedReason.getMessage());
                scResult.setFalseCode(failedReason.getCode());
            }
            resultList.add(scResult);
        }

        return Result.ok(resultList);
    }

    @ApiOperation(value = "Query activity list by online store",notes = "Query activity list by online store")
    @PostMapping("/activity/queryPromoListByStore")
    public PageResult<QueryPromoListByStoreResult> queryPromoListByStore(@RequestBody QueryPromoListByStoreParam param){

        log.info("根据店铺编码查询活动列表:{{}}", JSONObject.toJSONString(param));
        param.validate();
        PageInfo<TPromoActivityOutDTO> activityOutDTOs = tPromoActivityDomain.queryPromoListByStore(param);
        return PageResult.ok(BeanCopyUtils.jsonCopyList(activityOutDTOs.getList(), QueryPromoListByStoreResult.class), activityOutDTOs.getTotal());
    }

    @ApiOperation(value = "Query activities based on shopping cart items",notes = "Query activities based on shopping cart items")
    @PostMapping(value = "/activity/queryActivityByShoppingCartProduct")
    public Result<List<QueryActivityByProductListResult>> queryActivityByShoppingCartProduct(@RequestBody CalcShoppingCartParam param) {
        log.info("根据购物车商品查询活动信息入参：{}", JSONObject.toJSONString(param));
        param.validate();
        ShoppingCartDTO shoppingCart = BeanCopyUtils.jsonCopyBean(param, ShoppingCartDTO.class);
        shoppingCart.setUserCode(param.getMemberCode());
        //获取规则缓存
        Map<String, ActivityCacheDTO> ruleCacheMap = activityCacheDomain.getActivityCacheMap(shoppingCart.getTenantCode(), param.getLanguage(), 2, null);
        if (MapUtils.isEmpty(ruleCacheMap)) {
            return Result.ok(new ArrayList<>());
        }
        // 查询
        shoppingCart = shoppingCartDomain.queryActivityByShoppingCartProduct(shoppingCart, ruleCacheMap);
        //活动编码-活动实体
        Map<String,ActivityModel> activityMap = new HashMap<>();
        //活动编码对应所属sku
        Map<String,List<ActivityItemResult>> skuMap = new HashMap<>();
        for (ShoppingCartItem shoppingCartItem : shoppingCart.getPromoProducts()) {
            List<ShoppingCartActivity> activityList = shoppingCartItem.getUsedActivitys();
            if (CollectionUtils.isNotEmpty(activityList)) {
                setActivityRelationProduct(activityMap, skuMap, shoppingCartItem, activityList);
            }
        }
        //出参结果封装
        List<QueryActivityByProductListResult> results = Lists.newArrayList();
        //活动遍历
        for (Map.Entry<String, List<ActivityItemResult>> stringListEntry : skuMap.entrySet()) {
            String activityCode = stringListEntry.getKey();
            QueryActivityByProductListResult result = new QueryActivityByProductListResult();
            ActivityModel activityModel = activityMap.get(activityCode);
            List<ActivityItemResult> activityItems = skuMap.get(activityCode);
            BeanCopyUtils.copyProps(activityModel,result);
            result.setActivityItems(activityItems);

            ActivityCacheDTO activityCacheDTO = ruleCacheMap.get(activityCode);

            List<ActivityFunctionParamRankModel> rankModelList = activityCacheDTO.getPromoFuncRanks();
            List<FunctionParamModel> promoFuncParams = activityCacheDTO.getPromoFuncParams();
            setRankAndFunctionParam(rankModelList, promoFuncParams);
            result.setFuncParams(BeanCopyUtils.jsonCopyList(promoFuncParams, FunctionParam.class));
            result.setLimitedList(BeanCopyUtils.jsonCopyList(activityCacheDTO.getIncentiveLimiteds(), IncentiveLimited.class));
            result.setQualifications(BeanCopyUtils.jsonCopyList(activityCacheDTO.getQualificationModels(), Qualification.class));
            //赠品
            result.setGiveaways(activityCacheDTO.getGiveaways());
            results.add(result);
        }
        return Result.ok(results);
    }

    public void setRankAndFunctionParam(List<ActivityFunctionParamRankModel> rankModelList, List<FunctionParamModel> promoFuncParams) {
        for (ActivityFunctionParamRankModel rankModel : rankModelList) {
            for (FunctionParamModel promoFuncParam : promoFuncParams) {
                if (rankModel.getId().equals(promoFuncParam.getRankId())){
                    promoFuncParam.setRankParam(rankModel.getRankParam());
                }
            }
        }
    }

    public void setActivityRelationProduct(Map<String, ActivityModel> activityMap, Map<String, List<ActivityItemResult>> skuMap, ShoppingCartItem shoppingCartItem, List<ShoppingCartActivity> activityList) {
        //遍历商品下活动
        for (ShoppingCartActivity usedActivity : activityList) {
            if (skuMap.containsKey(usedActivity.getActivityCode())){
                List<ActivityItemResult> skuProducts = new ArrayList<>();
                List<ActivityItemResult> activityItemList = skuMap.get(usedActivity.getActivityCode());
                ActivityItemResult activityItem = new ActivityItemResult();
                activityItem.setSkuCode(shoppingCartItem.getSkuCode());
                activityItem.setProductCode(shoppingCartItem.getProductCode());
                skuProducts.add(activityItem);
                skuProducts.addAll(activityItemList);
                //填充活动对应多个sku
                skuMap.put(usedActivity.getActivityCode(),skuProducts);
            }else {
                ActivityItemResult activityItem = new ActivityItemResult();
                //存储活动对应商品
                List<ActivityItemResult> activityItems = Lists.newArrayList();
                activityItem.setSkuCode(shoppingCartItem.getSkuCode());
                activityItem.setProductCode(shoppingCartItem.getProductCode());
                activityItems.add(activityItem);
                skuMap.put(usedActivity.getActivityCode(),activityItems);
            }
            //对应每一个活动实体
            if (!activityMap.containsKey(usedActivity.getActivityCode())){
                ActivityModel activityModel = usedActivity.getActivityModel();
                activityMap.put(usedActivity.getActivityCode(),activityModel);
            }
        }
    }

    @ApiOperation(value = "Query activity discount price",notes = "Query activity discount price")
    @PostMapping("/activity/queryAfterDiscountPrice")
    public Result<DiscountPriceResult> queryAfterDiscountPrice(@RequestBody QueryAfterDiscountPriceParam param){
        log.info("start queryAfterDiscountPrice!param:{}", JSONObject.toJSONString(param));
        CheckUtils.isNotBlank(param.getTenantCode(), ErrorCodes.PARAM_EMPTY, "tenantCode");
        if(CollectionUtils.isEmpty(param.getItemList())){
            throw new PromotionException(TPromoProductChecker.NULL_SKU);
        }
        for(QueryAfterDiscountItemParam item : param.getItemList()){
            if(null == item.getSalePrice()){
                throw new PromotionException(TPromoProductChecker.ERROR_SALE_PRICE_EMPTY);
            }
            if(StringUtil.isEmpty(item.getSkuCode())){
                throw new PromotionException(TPromoProductChecker.NOT_NULL_SKU_CODE);
            }
            if(StringUtil.isEmpty(item.getProductCode())){
                throw new PromotionException(TPromoProductChecker.NOT_NULL_PRODUCT_CODE);
            }
        }
        return tPromoActivityDomain.queryAfterDiscountPrice(param);
    }



	/**
	 * 根据商品列表和活动列表进行匹配，返回活动及其匹配的商品
	 * @param param 包含商品列表和活动列表的参数
	 * @return 活动列表，每个活动下挂参与该活动的商品
	 */
	@ApiOperation(value = "Match products to activities", notes = "Match products to activities and return activities with matched products")
	@PostMapping(value = "/activity/matchProductsToActivities")
	public Result<List<QueryActivityByProductListResult>> matchProductsToActivities(@RequestBody MatchProductsToActivitiesParam param) {
		log.info("商品活动匹配入参：{}", JSONObject.toJSONString(param));
		param.validate();

		// 1. 构建购物车对象（直接从参数转换）
		ShoppingCartDTO shoppingCart = BeanCopyUtils.jsonCopyBean(param, ShoppingCartDTO.class);
		shoppingCart.setUserCode(param.getMemberCode());

		// 2. 获取活动缓存，如果指定了活动列表则进行过滤
		Map<String, ActivityCacheDTO> ruleCacheMap = getFilteredActivityCache(param);
		if (MapUtils.isEmpty(ruleCacheMap)) {
			return Result.ok(new ArrayList<>());
		}

		// 3. 执行商品活动匹配
		shoppingCart = shoppingCartDomain.queryActivityByShoppingCartProduct(shoppingCart, ruleCacheMap);

		// 4. 组织返回结果：以活动为主体，商品为子项
		List<QueryActivityByProductListResult> results = buildActivityProductResults(shoppingCart, ruleCacheMap);

		return Result.ok(results);
	}



	/**
	 * 获取过滤后的活动缓存
	 */
	private Map<String, ActivityCacheDTO> getFilteredActivityCache(MatchProductsToActivitiesParam param) {
		// 获取所有活动缓存
		Map<String, ActivityCacheDTO> allActivityCache = activityCacheDomain.getActivityCacheMap(
			param.getTenantCode(), param.getLanguage(), 2, null);

		// 如果指定了活动列表，则进行过滤
		if (CollectionUtils.isNotEmpty(param.getActivityCodes())) {
			Map<String, ActivityCacheDTO> filteredCache = new HashMap<>();
			for (String activityCode : param.getActivityCodes()) {
				if (allActivityCache.containsKey(activityCode)) {
					filteredCache.put(activityCode, allActivityCache.get(activityCode));
				}
			}
			return filteredCache;
		}

		return allActivityCache;
	}

	/**
	 * 构建活动商品匹配结果
	 */
	private List<QueryActivityByProductListResult> buildActivityProductResults(
			ShoppingCartDTO shoppingCart, Map<String, ActivityCacheDTO> ruleCacheMap) {

		// 活动编码-活动实体
		Map<String, ActivityModel> activityMap = new HashMap<>();
		// 活动编码对应所属sku
		Map<String, List<ActivityItemResult>> skuMap = new HashMap<>();

		// 遍历商品，收集活动和商品的关系
		for (ShoppingCartItem shoppingCartItem : shoppingCart.getPromoProducts()) {
			List<ShoppingCartActivity> activityList = shoppingCartItem.getUsedActivitys();
			if (CollectionUtils.isNotEmpty(activityList)) {
				setActivityRelationProduct(activityMap, skuMap, shoppingCartItem, activityList);
			}
		}

		// 构建返回结果
		List<QueryActivityByProductListResult> results = new ArrayList<>();
		for (Map.Entry<String, List<ActivityItemResult>> entry : skuMap.entrySet()) {
			String activityCode = entry.getKey();
			QueryActivityByProductListResult result = new QueryActivityByProductListResult();

			// 设置活动基本信息
			ActivityModel activityModel = activityMap.get(activityCode);
			BeanCopyUtils.copyProps(activityModel, result);
			result.setActivityItems(entry.getValue());

			// 设置活动详细信息
			ActivityCacheDTO activityCacheDTO = ruleCacheMap.get(activityCode);
			if (activityCacheDTO != null) {
				List<ActivityFunctionParamRankModel> rankModelList = activityCacheDTO.getPromoFuncRanks();
				List<FunctionParamModel> promoFuncParams = activityCacheDTO.getPromoFuncParams();
				setRankAndFunctionParam(rankModelList, promoFuncParams);
				result.setFuncParams(BeanCopyUtils.jsonCopyList(promoFuncParams, FunctionParam.class));
				result.setLimitedList(BeanCopyUtils.jsonCopyList(activityCacheDTO.getIncentiveLimiteds(), IncentiveLimited.class));
				result.setQualifications(BeanCopyUtils.jsonCopyList(activityCacheDTO.getQualificationModels(), Qualification.class));
				result.setGiveaways(activityCacheDTO.getGiveaways());
			}

			results.add(result);
		}

		return results;
	}



	/**
	 * 调试商品活动匹配 - 详细分析匹配失败原因
	 * @param param 商品活动匹配参数
	 * @return 详细的调试信息
	 */
	@ApiOperation(value = "Debug product activity matching", notes = "Detailed analysis of why products don't match activities")
	@PostMapping(value = "/activity/debugProductMatching")
	public Result<Map<String, Object>> debugProductMatching(@RequestBody MatchProductsToActivitiesParam param) {
		log.info("开始调试商品活动匹配，参数: {}", param);

		Map<String, Object> result = new HashMap<>();
		List<Map<String, Object>> debugDetails = new ArrayList<>();

		try {
			// 1. 检查基本参数
			result.put("inputParam", param);
			result.put("debugTime", DateUtil.getCurrentDateAsString(DateUtil.FORMAT_YYYYMMDDHHMISS_14));

			// 2. 获取活动缓存
			Map<String, ActivityCacheDTO> activityCacheMap = activityCacheDomain.getActivityCacheMap(param.getTenantCode(), param.getLanguage(), 2, null);
			result.put("totalActivitiesInCache", activityCacheMap.size());
			result.put("requestedActivityCodes", param.getActivityCodes());

			// 3. 检查请求的活动是否存在
			List<String> foundActivities = new ArrayList<>();
			List<String> missingActivities = new ArrayList<>();

			if (param.getActivityCodes() != null) {
				for (String activityCode : param.getActivityCodes()) {
					if (activityCacheMap.containsKey(activityCode)) {
						foundActivities.add(activityCode);
					} else {
						missingActivities.add(activityCode);
					}
				}
			}

			result.put("foundActivities", foundActivities);
			result.put("missingActivities", missingActivities);

			// 4. 分析每个商品的匹配情况
			if (param.getCartStoreList() != null) {
				for (com.gtech.promotion.vo.bean.ShoppingCartStore cartStore : param.getCartStoreList()) {
					if (cartStore.getCartItemList() != null) {
						for (com.gtech.promotion.vo.bean.ShoppingCartItem cartItem : cartStore.getCartItemList()) {
							Map<String, Object> productDebug = debugSingleProduct(cartItem, activityCacheMap, foundActivities, param);
							debugDetails.add(productDebug);
						}
					}
				}
			}

			result.put("productDebugDetails", debugDetails);
			result.put("success", true);

		} catch (Exception e) {
			log.error("调试商品活动匹配失败", e);
			result.put("success", false);
			result.put("error", e.getMessage());
			result.put("stackTrace", Arrays.toString(e.getStackTrace()));
		}

		return Result.ok(result);
	}

	private Map<String, Object> debugSingleProduct(com.gtech.promotion.vo.bean.ShoppingCartItem cartItem, Map<String, ActivityCacheDTO> activityCacheMap,
			List<String> activityCodes, MatchProductsToActivitiesParam param) {
		Map<String, Object> productDebug = new HashMap<>();

		productDebug.put("productCode", cartItem.getProductCode());
		productDebug.put("skuCode", cartItem.getSkuCode());
		productDebug.put("brandCode", cartItem.getBrandCode());
		productDebug.put("categoryCodes", cartItem.getCategoryCodes());
		productDebug.put("attributes", cartItem.getAttributes());
		productDebug.put("orgCode", "default"); // ShoppingCartItem 没有 orgCode 字段

		List<Map<String, Object>> activityMatches = new ArrayList<>();

		// 检查每个活动的匹配情况
		for (String activityCode : activityCodes) {
			ActivityCacheDTO activityCache = activityCacheMap.get(activityCode);
			if (activityCache == null) {
				continue;
			}

			Map<String, Object> activityMatch = new HashMap<>();
			activityMatch.put("activityCode", activityCode);
			activityMatch.put("activityName", activityCache.getActivityModel().getActivityName());

			// 检查时间匹配
			boolean timeMatch = checkTimeMatch(activityCache, param.getPromotionTime());
			activityMatch.put("timeMatch", timeMatch);

			// 检查渠道匹配
			boolean orgMatch = checkOrgMatch(activityCache, "default");
			activityMatch.put("orgMatch", orgMatch);

			// 检查商品匹配
			Map<String, Object> productMatch = checkProductMatch(activityCache, cartItem);
			activityMatch.put("productMatch", productMatch);

			activityMatches.add(activityMatch);
		}

		productDebug.put("activityMatches", activityMatches);
		return productDebug;
	}

	private boolean checkTimeMatch(ActivityCacheDTO activityCache, String promotionTime) {
		try {
			long longTime = Long.parseLong(promotionTime);
			ActivityModel activity = activityCache.getActivityModel();
			return Long.parseLong(activity.getActivityBegin()) < longTime && Long.parseLong(activity.getActivityEnd()) > longTime;
		} catch (Exception e) {
			return false;
		}
	}

	private boolean checkOrgMatch(ActivityCacheDTO activityCache, String orgCode) {
		// 简化的渠道匹配检查
		return true; // 暂时返回true，实际逻辑较复杂
	}

	private Map<String, Object> checkProductMatch(ActivityCacheDTO activityCache, com.gtech.promotion.vo.bean.ShoppingCartItem cartItem) {
		Map<String, Object> match = new HashMap<>();

		// 获取活动的商品范围配置
		List<TPromoActivityProductVO> promoProducts = activityCache.getPromoProducts();
		match.put("activityProductScopes", promoProducts);

		if (CollectionUtils.isEmpty(promoProducts)) {
			match.put("isAllProduct", true);
			match.put("matchResult", true);
			return match;
		}

		match.put("isAllProduct", false);

		// 检查每个商品范围
		List<Map<String, Object>> scopeMatches = new ArrayList<>();
		boolean anyMatch = false;

		for (TPromoActivityProductVO scope : promoProducts) {
			Map<String, Object> scopeMatch = new HashMap<>();
			scopeMatch.put("seqNum", scope.getSeqNum());

			// 品类匹配
			boolean categoryMatch = checkCategoryMatch(scope.getCategoryCode(), cartItem.getCategoryCodes());
			scopeMatch.put("categoryMatch", categoryMatch);
			scopeMatch.put("activityCategories", scope.getCategoryCode());
			scopeMatch.put("productCategories", cartItem.getCategoryCodes());

			// 品牌匹配
			boolean brandMatch = checkBrandMatch(scope.getBrandCode(), cartItem.getBrandCode());
			scopeMatch.put("brandMatch", brandMatch);
			scopeMatch.put("activityBrand", scope.getBrandCode());
			scopeMatch.put("productBrand", cartItem.getBrandCode());

			// 属性匹配
			Map<String, Object> attributeMatch = checkAttributeMatch(scope.getAttributes(), cartItem.getAttributes());
			scopeMatch.put("attributeMatch", attributeMatch);

			boolean currentScopeMatches = categoryMatch && brandMatch && (Boolean) attributeMatch.get("match");
			scopeMatch.put("overallMatch", currentScopeMatches);

			if (currentScopeMatches) {
				anyMatch = true;
			}

			scopeMatches.add(scopeMatch);
		}

		match.put("scopeMatches", scopeMatches);
		match.put("matchResult", anyMatch);

		return match;
	}

	private boolean checkCategoryMatch(String activityCategories, List<String> productCategories) {
		if ("0000".equals(activityCategories) || StringUtils.isBlank(activityCategories)) {
			return true; // 不限品类
		}

		if (CollectionUtils.isEmpty(productCategories)) {
			return false;
		}

		// 检查是否有任何产品分类匹配活动分类
		String[] activityCats = activityCategories.split(",");
		for (String activityCat : activityCats) {
			for (String productCat : productCategories) {
				if (productCat.concat(">").startsWith(activityCat.concat(">"))) {
					return true;
				}
			}
		}

		return false;
	}

	private boolean checkBrandMatch(String activityBrand, String productBrand) {
		if ("0000".equals(activityBrand) || StringUtils.isBlank(activityBrand)) {
			return true; // 不限品牌
		}

		if (StringUtils.isBlank(productBrand)) {
			return false;
		}

		String[] activityBrands = activityBrand.split(",");
		for (String brand : activityBrands) {
			if (brand.equals(productBrand)) {
				return true;
			}
		}

		return false;
	}

	private Map<String, Object> checkAttributeMatch(List<ProductAttribute> activityAttributes, List<ProductAttribute> productAttributes) {
		Map<String, Object> result = new HashMap<>();
		result.put("activityAttributes", activityAttributes);
		result.put("productAttributes", productAttributes);

		if (CollectionUtils.isEmpty(activityAttributes)) {
			result.put("match", true);
			result.put("reason", "活动无属性限制");
			return result;
		}

		if (CollectionUtils.isEmpty(productAttributes)) {
			result.put("match", false);
			result.put("reason", "商品无属性信息");
			return result;
		}

		// 检查是否有匹配的属性
		for (ProductAttribute activityAttr : activityAttributes) {
			if ("0000".equals(activityAttr.getAttributeCode())) {
				result.put("match", true);
				result.put("reason", "活动属性不限制");
				return result;
			}

			for (ProductAttribute productAttr : productAttributes) {
				if (activityAttr.getAttributeCode().equals(productAttr.getAttributeCode())) {
					// 检查属性值匹配
					if ("0000".equals(activityAttr.getAttributeValues())) {
						result.put("match", true);
						result.put("reason", "活动属性值不限制");
						return result;
					}

					String activityValues = "," + activityAttr.getAttributeValues() + ",";
					String productValue = "," + productAttr.getAttributeValues() + ",";

					if (activityValues.contains(productValue)) {
						result.put("match", true);
						result.put("reason", "属性值匹配成功");
						result.put("matchedAttribute", activityAttr.getAttributeCode());
						result.put("matchedValue", productAttr.getAttributeValues());
						return result;
					}
				}
			}
		}

		result.put("match", false);
		result.put("reason", "无匹配的属性");
		return result;
	}

}
