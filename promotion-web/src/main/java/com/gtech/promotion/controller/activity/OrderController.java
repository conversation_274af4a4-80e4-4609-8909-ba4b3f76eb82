/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.controller.activity;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.promotion.checker.activity.TPromoOrderChecker;
import com.gtech.promotion.component.activity.ActivityCacheDomain;
import com.gtech.promotion.component.activity.OrderDomain;
import com.gtech.promotion.controller.BaseController;
import com.gtech.promotion.dto.cache.ActivityCacheDTO;
import com.gtech.promotion.dto.in.activity.OrderCommitDTO;
import com.gtech.promotion.dto.out.activity.ShoppingCartOutDTO;
import com.gtech.promotion.exception.Check;
import com.gtech.promotion.vo.bean.Giveaway;
import com.gtech.promotion.vo.param.activity.CancelOrderParam;
import com.gtech.promotion.vo.param.activity.ConfirmOrderParam;
import com.gtech.promotion.vo.param.activity.CreateOrderAndCouponParam;
import com.gtech.promotion.vo.param.activity.CreateOrderParam;
import com.gtech.promotion.vo.param.activity.SalesReturnOrderParam;
import com.gtech.promotion.vo.result.activity.CalcShoppingCartResult;
import com.gtech.promotion.vo.result.activity.CreateOrderResult;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * 促销订单
 * 
 */
@Api(value = "Promotion Order API",tags = { "Promotion Order API" })
@RestController
@Slf4j
public class OrderController extends BaseController {


    private static final String LOG_WARN = "{}-{}-{}-{}";

    @Autowired
    private OrderDomain orderDomain;

    @Autowired
    private ActivityCacheDomain activityCacheDomain;

    @ApiOperation(value = "Create promotion order",notes = "Create promotion order")
    @PostMapping("/order/createOrder")
    public Result<CreateOrderResult> createOrder(@RequestBody CreateOrderParam param) {
        log.info("createOrder: {{}}", JSONObject.toJSONString(param));
        param.validate();
        CreateOrderResult createOrderResult = new CreateOrderResult();
        OrderCommitDTO orderCommitDTO = BeanCopyUtils.jsonCopyBean(param, OrderCommitDTO.class);
        orderCommitDTO.setUserCode(param.getMemberCode());
        orderCommitDTO.setOrderId(param.getOrderNo());

        //如果没有赠品没有且促销的扣减总金额为0
        if (orderCommitDTO.getPromoDeductedAmount().doubleValue() == 0 && (CollectionUtils.isEmpty(orderCommitDTO.getPromoGiveaways()))
                && 0 == orderCommitDTO.getPromoRewardPostage().intValue()) {
            log.info("No reward information, just ignore it.");
            return Result.ok();
        }
        if (CollectionUtils.isNotEmpty(orderCommitDTO.getPromoGiveaways())) {
            orderCommitDTO.getPromoGiveaways().forEach(x -> {
                Check.check(null == x.getActivityCode(), TPromoOrderChecker.NOT_NULL_GIVEAWAY_ACTIVITY_CODE);
                Check.check(CollectionUtils.isEmpty(x.getGiveaways()), TPromoOrderChecker.NOT_NULL_GIVEAWAY);
            });
        }
        List<ShoppingCartOutDTO> shoppingCartOutDTOS = new ArrayList<>();
        try {
            //获取活动缓存
            Map<String, ActivityCacheDTO> activityCacheMap = activityCacheDomain.getActivityCacheMap(orderCommitDTO.getTenantCode(), orderCommitDTO.getLanguage(), 2, null);
            //没有缓存活动并且有扣减金额
            Check.check(MapUtils.isEmpty(activityCacheMap), TPromoOrderChecker.TENANT_CODE_NO_ACTIVITY);

            shoppingCartOutDTOS = orderDomain.checkOrder(orderCommitDTO, activityCacheMap);
            shoppingCartOutDTOS = orderDomain.commitOrder(orderCommitDTO, activityCacheMap, shoppingCartOutDTOS);
            createOrderResult.setShoppingCartResults(BeanCopyUtils.jsonCopyList(shoppingCartOutDTOS, CalcShoppingCartResult.class));
        } catch (DuplicateKeyException e) {
            log.warn("createOrder duplicated!", e);
            createOrderResult.setShoppingCartResults(BeanCopyUtils.jsonCopyList(shoppingCartOutDTOS, CalcShoppingCartResult.class));
        } catch (Exception e) {
            log.warn(LOG_WARN, orderCommitDTO.getTenantCode(), 10149903, orderCommitDTO.getTenantCode() + orderCommitDTO.getOrderId(), e.getMessage());
            throw e;
        }
        buildGiveaways(param,createOrderResult);
        return Result.ok(createOrderResult);
    }

    public void buildGiveaways(CreateOrderParam param,CreateOrderResult createOrderResult){
        if(CollectionUtils.isEmpty(param.getPromoGiveaways())){
            return;
        }
        if(CollectionUtils.isEmpty(createOrderResult.getShoppingCartResults())){
            return;
        }

        Map<String,List<Giveaway>> activityGiveawayMap = param.getPromoGiveaways().stream().collect(Collectors.toMap(CreateOrderParam.PromoGiveaway::getActivityCode,CreateOrderParam.PromoGiveaway::getGiveaways));
        Map<String,Map<String,Integer>> activityGiveawayCount = new HashMap<>();
        Iterator<Map.Entry<String,List<Giveaway>>> it = activityGiveawayMap.entrySet().iterator();
        while(it.hasNext()){
            Map.Entry<String,List<Giveaway>> entry=it.next();
            String activityCode = entry.getKey();
            List<Giveaway> giveawayList = entry.getValue();
            if(!CollectionUtils.isEmpty(giveawayList)){
                Map<String,Integer> countMap = giveawayList.stream().collect(Collectors.toMap(Giveaway::getGiveawayCode,Giveaway::getGiveawayNum));
                activityGiveawayCount.put(activityCode,countMap);
            }
        }

        List<CalcShoppingCartResult> shoppingCartResults = createOrderResult.getShoppingCartResults();
        for(CalcShoppingCartResult calcShoppingCartResult : shoppingCartResults){
            String activityCode = calcShoppingCartResult.getActivityCode();
            List<Giveaway> giveaways = calcShoppingCartResult.getGiveaways();
            if(CollectionUtils.isEmpty(giveaways)){
                continue;
            }
            Iterator<Giveaway> giveawayIterator = giveaways.iterator();
            while (giveawayIterator.hasNext()){
                Giveaway giveaway = giveawayIterator.next();
                Map<String,Integer> countMap = activityGiveawayCount.get(activityCode);
                if(null != countMap && null != countMap.get(giveaway.getGiveawayCode())){
                    giveaway.setGiveawayNum(countMap.get(giveaway.getGiveawayCode()));
                }else {
                    giveawayIterator.remove();
                }
            }
        }
    }

    @ApiOperation(value = "Cancel promotion order",notes = "Cancel promotion order")
    @PostMapping(value = "/order/cancelOrder")
    public Result<Object> cancelOrder(@RequestBody CancelOrderParam param) {
        param.validate();
        log.info("cancelOrder: start{}", JSONObject.toJSONString(param));
        try {
            orderDomain.cancelOrder(param.getTenantCode(), param.getOrderNo());
        } catch (Exception e) {
            log.warn(LOG_WARN, param.getTenantCode(), 10149905, param.getTenantCode() + param.getOrderNo(), e.getMessage());
            throw e;
        }
        log.info("cancelOrder:end {}", JSONObject.toJSONString(param));
        return Result.ok();
    }

    @ApiOperation(value = "Confirm promotion order",notes = "Confirm promotion order")
    @PostMapping(value = "/order/confirmOrder")
    public Result<Object> confirmOrder(@RequestBody ConfirmOrderParam param) {

        param.validate();

        log.info("confirmOrder: {}", JSONObject.toJSONString(param));

        try {
            orderDomain.getPayOrderReduceResource(param.getTenantCode(), param.getOrderNo());
        } catch (Exception e) {
            log.warn(LOG_WARN, param.getTenantCode(), 10149904, param.getTenantCode() + param.getOrderNo(), e.getMessage());
            throw e;
        }
        return Result.ok();
    }

    @ApiOperation(value = "Sales return promotion order",notes = "Sales return promotion order")
    @PostMapping(value = "/order/salesReturnOrder")
    public Result<Object> salesReturnOrder(@RequestBody SalesReturnOrderParam param) {
        param.validate();
        log.info("salesReturnOrder: {}", JSONObject.toJSONString(param));
        try {
            orderDomain.salesReturnOrder(param.getTenantCode(), param.getOrderNo());
        } catch (Exception e) {
            log.warn(LOG_WARN, param.getTenantCode(), 10149906, param.getTenantCode() + param.getOrderNo(), e.getMessage());
            throw e;
        }
        return Result.ok();
    }

    @ApiOperation(value = "Create the order and verify the coupon",notes = "Create the order and verify the coupon")
    @PostMapping("/order/createOrderAndVerifyCoupon")
    public Result createOrderAndVerifyCoupon(@RequestBody CreateOrderAndCouponParam param) {
        param.validate();
        log.info("createOrderAndVerifyCoupon: {{}}", JSONObject.toJSONString(param));
        orderDomain.commitOrderAndVerify(param);
        return Result.ok();
    }
}
