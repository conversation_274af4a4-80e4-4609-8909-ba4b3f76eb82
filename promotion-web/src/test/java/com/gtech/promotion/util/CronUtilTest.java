package com.gtech.promotion.util;

import com.gtech.promotion.utils.CronUtil;
import org.junit.Assert;
import org.junit.Test;

public class CronUtilTest {

    @Test
    public void test_validate(){
        boolean validate = CronUtil.validate("0 0 8 1 1/2 ? 2020");
        Assert.assertTrue(validate);

        boolean validate1 = CronUtil.validate("0 0 25 1 1/2 ? 2020");
        Assert.assertFalse(validate1);
    }

    @Test
    public void test_checkDate(){
        // 表达式错误
        boolean b = CronUtil.checkDate("0 0 8 ? * ? *", "0 0 9 ? * ? *", null, "20200101010101", "20990101010101", "20210101083030");
        Assert.assertFalse(b);
        // 不隔周成功
        boolean b0 = CronUtil.checkDate("0 0 8 * * ? *", "0 0 9 * * ? *", null, "20200101010101", "20990101010101", "20210101083030");
        Assert.assertTrue(b0);
        // 指定月份和周几
        boolean b2 = CronUtil.checkDate("0 0 18 ? 1 2 2021", "0 0 19 ? 1 2 2021", null, "20200101010101", "20990101010101", "20210104182030");
        Assert.assertTrue(b2);
        // 不隔周失败
        boolean b1 = CronUtil.checkDate("0 0 8 * * ? *", "0 0 9 * * ? *", null, "20200101010101", "20990101010101", "20210101073030");
        Assert.assertFalse(b1);
        // 隔周在第一周内
        boolean b3 = CronUtil.checkDate("0 0 8 ? * 1 *", "0 0 9 ? * 1 *", 1, "20210103010101", "20990101010101", "20210103083030");
        Assert.assertTrue(b3);
        // 隔周在间隔的那一周
        boolean b4 = CronUtil.checkDate("0 0 8 ? * 1 *", "0 0 9 ? * 1 *", 1, "20210103010101", "20990101010101", "20210110083030");
        Assert.assertFalse(b4);
        // 隔周第二个生效周
        boolean b5 = CronUtil.checkDate("0 0 8 ? * 1 *", "0 0 9 ? * 1 *", 1, "20210103010101", "20990101010101", "20210117083030");
        Assert.assertTrue(b5);
        // 隔周，并且第一个生效周是下一周
        boolean b6 = CronUtil.checkDate("0 0 8 ? * 5 *", "0 0 9 ? * 5 *", 1, "20210101010101", "20990101010101", "20210107083030");
        Assert.assertTrue(b6);
        // 隔周，并且第一个生效周是下一周,当前时间处于隔周中
        boolean b7 = CronUtil.checkDate("0 0 8 ? * 5 *", "0 0 9 ? * 5 *", 1, "20210101010101", "20990101010101", "20210114083030");
        Assert.assertFalse(b7);
        // 隔周，并且第一个生效周是下一周,当前时间处于下一次生效周
        boolean b8 = CronUtil.checkDate("0 0 8 ? * 5 *", "0 0 9 ? * 5 *", 1, "20210101010101", "20990101010101", "20210121083030");
        Assert.assertTrue(b8);
    }
}
