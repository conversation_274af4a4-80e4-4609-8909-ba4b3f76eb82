package com.gtech.promotion;

import com.gtech.promotion.exception.PromotionException;
import io.lettuce.core.RedisException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.*;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @since 2021/8/16 14:09
 */
@RunWith(MockitoJUnitRunner.class)
public class RedisClientTest {
    @InjectMocks
    private RedisClient redisClient;

    @Mock
    private StringRedisTemplate redisTemplate;
    @Mock
    private ValueOperations<String,String> valueOperations;
    @Mock
    private HashOperations hashOperations;
    @Mock
    private ZSetOperations<String,String> zSetOperations;
    @Mock
    private SetOperations<String,String> setOperations;
    @Mock
    private RedisConnectionFactory redisConnectionFactory;
    @Mock
    private RedisConnection redisConnection;

    @Before
    public void before(){
        Mockito.when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        Mockito.when(redisTemplate.opsForSet()).thenReturn(setOperations);
        Mockito.when(redisTemplate.opsForZSet()).thenReturn(zSetOperations);
        Mockito.when(redisTemplate.getConnectionFactory()).thenReturn(redisConnectionFactory);
        Mockito.when(redisTemplate.opsForHash()).thenReturn(hashOperations);
    }


    @Test(expected = PromotionException.class)
    public void deleteFuzzyMatch(){

        Set<String> keys = new HashSet<>();
        Mockito.when(redisTemplate.keys(Mockito.anyString())).thenReturn(keys);
        Mockito.when(redisTemplate.delete(Mockito.anySet())).thenThrow(new RedisException("1"));

        redisClient.deleteFuzzyMatch("1");

    }


    @Test(expected = PromotionException.class)
    public void deleteFuzzyMatch1(){

        Mockito.when(redisTemplate.keys(Mockito.anyString())).thenThrow(new RedisException("!1"));

        redisClient.deleteFuzzyMatch("1");
    }


    @Test
    public void getString(){
        Mockito.when(valueOperations.get(Mockito.any())).thenReturn("1");
        String string = redisClient.getString("1");
        Assert.assertEquals("1",string);
    }

    @Test
    public void getString_exception(){
        Mockito.when(valueOperations.get(Mockito.any())).thenThrow(new NullPointerException());
        String string = redisClient.getString("1");
        Assert.assertNull(string);
    }

    @Test
    public void getInt(){
        Mockito.when(valueOperations.get(Mockito.any())).thenReturn("1");
        int anInt = redisClient.getInt("1");
        Assert.assertEquals(1,anInt);
    }
    @Test
    public void getInt_exception(){
        Mockito.when(valueOperations.get(Mockito.any())).thenThrow(new NullPointerException());
        int anInt = redisClient.getInt("1");
        Assert.assertEquals(0,anInt);
    }

    @Test
    public void getInt1(){
        Mockito.when(valueOperations.get(Mockito.any())).thenReturn("");
        int anInt = redisClient.getInt("1");
        Assert.assertEquals(0,anInt);
    }

    @Test
    public void getZSetSize(){
        Mockito.when(zSetOperations.size(Mockito.any())).thenReturn(1L);
        long zSetSize = redisClient.getZSetSize("1");
        Assert.assertEquals(1L,zSetSize);
    }
    @Test
    public void getZSetSize_exception(){
        Mockito.when(zSetOperations.size(Mockito.any())).thenThrow(new NullPointerException());
        long zSetSize = redisClient.getZSetSize("1");
        Assert.assertEquals(0L,zSetSize);
    }

    @Test
    public void getZSetSize1(){
        long zSetSize = redisClient.getZSetSize("1");
        Assert.assertEquals(0,zSetSize);
    }

    @Test
    public void scan(){
        Mockito.when(redisConnectionFactory.getConnection()).thenReturn(redisConnection);
        Set<String> abc = redisClient.scan("abc");
        Assert.assertEquals(0,abc.size());
    }
    @Test
    public void scan_exception(){
        Mockito.when(redisConnectionFactory.getConnection()).thenReturn(null);
        Set<String> abc = redisClient.scan("abc");
        Assert.assertEquals(0,abc.size());
    }

    @Test
    public void setStringValue(){
        Mockito.doNothing().when(valueOperations).set(Mockito.any(),Mockito.any(),Mockito.anyLong(),Mockito.any());
        redisClient.setStringValue("1","1",10L, TimeUnit.SECONDS);
    }

    @Test
    public void setStringValue1(){
        Mockito.doNothing().when(valueOperations).set(Mockito.any(),Mockito.any());
        redisClient.setStringValue("1","1",null, TimeUnit.SECONDS);
    }

    @Test
    public void setStringValue2(){
        Mockito.doNothing().when(valueOperations).set(Mockito.any(),Mockito.any(),Mockito.anyLong(),Mockito.any());
        redisClient.setStringValue("1","1",-1L, TimeUnit.SECONDS);
    }

    @Test
    public void setStringValue3(){
        Mockito.doNothing().when(valueOperations).set(Mockito.any(),Mockito.any());
        redisClient.setStringValue("1","1");
    }
    @Test(expected = PromotionException.class)
    public void setStringValue_exception(){
        Mockito.doThrow(new NullPointerException()).when(valueOperations).set(Mockito.any(),Mockito.any());
        redisClient.setStringValue("1","1");
    }

    @Test
    public void increment(){
        long increment = redisClient.increment("1", 1L);
        Assert.assertEquals(0,increment);
    }
    @Test(expected = PromotionException.class)
    public void increment_exception(){
        Mockito.when(redisTemplate.opsForValue()).thenThrow(new NullPointerException());
        long increment = redisClient.increment("1", 1L);
        Assert.assertEquals(0,increment);
    }

    @Test
    public void addZSet(){
        long increment = redisClient.addZSet("1", new HashSet());
        Assert.assertEquals(0,increment);
    }
    @Test(expected = PromotionException.class)
    public void addZSet_exception(){
        Mockito.when(redisTemplate.opsForZSet()).thenThrow(new NullPointerException());
        long increment = redisClient.addZSet("1", new HashSet());
        Assert.assertEquals(0,increment);
    }

    @Test
    public void expire(){
        Mockito.when(redisTemplate.expire(Mockito.any(),Mockito.anyLong(),Mockito.any())).thenReturn(true);
        redisClient.expire("1", 1L,TimeUnit.SECONDS);
    }

    @Test
    public void expire1(){
        Mockito.when(redisTemplate.expire(Mockito.any(),Mockito.anyLong(),Mockito.any())).thenReturn(true);
        redisClient.expire("1", -1L,TimeUnit.SECONDS);
    }
    @Test(expected = PromotionException.class)
    public void expire_exception(){
        Mockito.when(redisTemplate.expire(Mockito.any(),Mockito.anyLong(),Mockito.any())).thenThrow(new NullPointerException());
        redisClient.expire("1", -1L,TimeUnit.SECONDS);
    }

    @Test
    public void delete(){
        Mockito.when(redisTemplate.delete(Mockito.anyString())).thenReturn(true);
        redisClient.delete("1");
    }

    @Test(expected = PromotionException.class)
    public void delete_exception(){
        Mockito.when(redisTemplate.delete(Mockito.anyString())).thenThrow(new NullPointerException());
        redisClient.delete("1");
    }

    @Test
    public void getFirstStringValueFormZSet(){
        Mockito.when(zSetOperations.range(Mockito.any(),Mockito.anyLong(),Mockito.anyLong())).thenReturn(new HashSet<>());
        String st = redisClient.getFirstStringValueFormZSet("1");
        Assert.assertNull(st);
    }
    @Test
    public void getFirstStringValueFormZSet_null(){
        Mockito.when(zSetOperations.range(Mockito.any(),Mockito.anyLong(),Mockito.anyLong())).thenReturn(null);
        String st = redisClient.getFirstStringValueFormZSet("1");
        Assert.assertNull(st);
    }
    @Test(expected = PromotionException.class)
    public void getFirstStringValueFormZSet_exception(){
        Mockito.when(zSetOperations.range(Mockito.any(),Mockito.anyLong(),Mockito.anyLong())).thenThrow(new NullPointerException());
        String st = redisClient.getFirstStringValueFormZSet("1");
        Assert.assertNull(st);
    }

    @Test
    public void getFirstStringValueFormZSet1(){
        Set<String> set = new HashSet<>();
        set.add("1");
        Mockito.when(zSetOperations.range(Mockito.any(),Mockito.anyLong(),Mockito.anyLong())).thenReturn(set);
        String st = redisClient.getFirstStringValueFormZSet("1");
        Assert.assertEquals("1",st);
    }

    @Test
    public void addSet(){
        Mockito.when(setOperations.add(Mockito.any(),Mockito.any())).thenReturn(1L);
        redisClient.addSet("1","1");
    }
    @Test(expected = PromotionException.class)
    public void addSet_Exception(){
        Mockito.when(setOperations.add(Mockito.any(),Mockito.any())).thenThrow(new NullPointerException());
        redisClient.addSet("1","1");
    }

    @Test
    public void removeFromSet(){
        Mockito.when(setOperations.remove(Mockito.any(),Mockito.any())).thenReturn(1L);
        redisClient.removeFromSet("1","1");
    }
    @Test(expected = PromotionException.class)
    public void removeFromSet_exception(){
        Mockito.when(setOperations.remove(Mockito.any(),Mockito.any())).thenThrow(new NullPointerException());
        redisClient.removeFromSet("1","1");
    }
    @Test(expected = PromotionException.class)
    public void getHashValue_exception(){
        Mockito.when(redisTemplate.opsForHash()).thenThrow(new NullPointerException());
        redisClient.getHashValue("1","1");
    }
    @Test(expected = PromotionException.class)
    public void getHashMap_exception(){
        Mockito.when(redisTemplate.opsForHash()).thenThrow(new NullPointerException());
        redisClient.getHashMap("1");
    }
    @Test(expected = PromotionException.class)
    public void putAllHash_exception(){
        Mockito.when(redisTemplate.opsForHash()).thenThrow(new NullPointerException());
        redisClient.putAllHash("1", new HashMap<>());
    }
    @Test(expected = PromotionException.class)
    public void putHash_exception(){
        Mockito.when(redisTemplate.opsForHash()).thenThrow(new NullPointerException());
        redisClient.putHash("1", "1", "1");
    }

    @Test
    public void setIfAbsent() {
        redisClient.setIfAbsent(null, 1, null, null);
        redisClient.setIfAbsent(null, 1, 1L, null);
        redisClient.setIfAbsent(null, 1, -1L, null);
    }

    @Test
    public void setIfAbsentIncrement() {
        redisClient.setIfAbsentIncrement(null, 1, null, null);
        redisClient.setIfAbsentIncrement(null, 1, 1L, null);
        redisClient.setIfAbsentIncrement(null, 1, -1L, null);
    }

    @Test
    public void putHash() {
        redisClient.putHash(null, null, null);
    }

    @Test
    public void putAllHash() {
        redisClient.putAllHash(null,null);
    }

    @Test
    public void getHashMap() {
        redisClient.getHashMap(null);
    }

    @Test
    public void getHashValue() {
        redisClient.getHashValue(null, null);
    }
    @Test
    public void incrementByCondition() {
        redisClient.increment(true, "key:001", 1);
    }
}
