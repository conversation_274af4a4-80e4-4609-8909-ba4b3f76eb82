package com.gtech.promotion.component.marketing;

import com.gtech.pim.client.PimClient;
import com.gtech.pim.request.ProductVo;
import com.gtech.pim.response.JsonResult;
import com.gtech.pim.response.ResultSkuCodeVo;
import com.gtech.promotion.component.flashsale.DataSyncComponent;
import com.gtech.promotion.dao.model.activity.ActivityPeriodModel;
import com.gtech.promotion.dao.model.marketing.MarketingModel;
import com.gtech.promotion.dao.model.marketing.flashsale.FlashSaleProductModel;
import com.gtech.promotion.dto.in.flashsale.QueryFlashSalePriceByProductInDto;
import com.gtech.promotion.dto.out.marketing.QueryFlashSalePriceByProductOutDto;
import com.gtech.promotion.service.activity.ActivityPeriodService;
import com.gtech.promotion.service.flashsale.FlashSaleProductService;
import com.gtech.promotion.service.marketing.MarketingService;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@RunWith(MockitoJUnitRunner.class)
public class DataSyncComponentTest {

    @InjectMocks
    private DataSyncComponent flashSaleComponent;

    @Mock
    private FlashSaleProductService flashSaleProductService;
    @Mock
    private MarketingService marketingService;
    @Mock
    private ActivityPeriodService activityPeriodService;
    @Mock
    private PimClient pimClient;

    @Test
    public void queryFlashSalePriceByProduct_empty(){
        QueryFlashSalePriceByProductInDto param = new QueryFlashSalePriceByProductInDto();
        Mockito.when(marketingService.queryCurrentEffectiveMarketingList(Mockito.any(), Mockito.any())).thenReturn(new ArrayList<>());
        List<QueryFlashSalePriceByProductOutDto> result = flashSaleComponent.queryFlashSalePriceByProduct(param);
        Assert.assertEquals(0, result.size());
    }
    @Test
    public void queryFlashSalePriceByProduct_products_empty(){
        QueryFlashSalePriceByProductInDto param = new QueryFlashSalePriceByProductInDto();
        List<QueryFlashSalePriceByProductInDto.Product> products = new ArrayList<>();
        QueryFlashSalePriceByProductInDto.Product product = new QueryFlashSalePriceByProductInDto.Product();
        product.setSkuCode("1");
        products.add(product);
        param.setProducts(products);
        List<MarketingModel> list = new ArrayList<>();
        MarketingModel model = new MarketingModel();
        model.setActivityCode("1");
        list.add(model);
        Mockito.when(marketingService.queryCurrentEffectiveMarketingList(Mockito.any(), Mockito.any())).thenReturn(list);
        Mockito.when(flashSaleProductService.getProductsByActivityCodesAndProducts(Mockito.any(), Mockito.anyList(),Mockito.anyList())).thenReturn(new ArrayList<>());
        List<QueryFlashSalePriceByProductOutDto> result = flashSaleComponent.queryFlashSalePriceByProduct(param);
        Assert.assertEquals(0, result.size());
    }

    @Test
    public void queryFlashSalePriceByProduct(){
        QueryFlashSalePriceByProductInDto param = new QueryFlashSalePriceByProductInDto();
        List<QueryFlashSalePriceByProductInDto.Product> products = new ArrayList<>();
        QueryFlashSalePriceByProductInDto.Product product = new QueryFlashSalePriceByProductInDto.Product();
        product.setSkuCode("1");
        products.add(product);
        param.setProducts(products);
        List<MarketingModel> list = new ArrayList<>();
        MarketingModel model = new MarketingModel();
        model.setActivityCode("1");
        list.add(model);
        List<FlashSaleProductModel> flashSaleProductModels = new ArrayList<>();
        FlashSaleProductModel model1 = new FlashSaleProductModel();
        model1.setSkuCode("1");
        model1.setFlashPrice(new BigDecimal(1));
        flashSaleProductModels.add(model1);
        Mockito.when(marketingService.queryCurrentEffectiveMarketingList(Mockito.any(), Mockito.any())).thenReturn(list);
        Mockito.when(flashSaleProductService.getProductsByActivityCodesAndProducts(Mockito.any(), Mockito.anyList(),Mockito.anyList())).thenReturn(flashSaleProductModels);
        List<QueryFlashSalePriceByProductOutDto> result = flashSaleComponent.queryFlashSalePriceByProduct(param);
        Assert.assertEquals(1, result.size());
    }

    @Test
    public void sendPriceToPim(){
        Mockito.when(flashSaleProductService.findListByActivityCode(Mockito.any())).thenReturn(new ArrayList<>());
        flashSaleComponent.sendPriceToPim("1","1",true);
    }

    @Test
    public void sendPriceToPimSkuCodes_empty(){
        List<String> skuCodes = new ArrayList<>();
        skuCodes.add("1");
        JsonResult<List<ResultSkuCodeVo>> listJsonResult = new JsonResult<>();
        listJsonResult.setSuccess(true);
        listJsonResult.setData(new ArrayList<>());
        List<FlashSaleProductModel> models = new ArrayList<>();
        FlashSaleProductModel model = new FlashSaleProductModel();
        models.add(model);
        Mockito.when(flashSaleProductService.findListByActivityCode(Mockito.any())).thenReturn(models);
        Mockito.when(pimClient.queryProductSkuCodeList(Mockito.any())).thenReturn(listJsonResult);
        flashSaleComponent.sendPriceToPim("1","1",true);
    }
    @Test
    public void sendPriceToPim_not_empty(){
        List<String> skuCodes = new ArrayList<>();
        skuCodes.add("1");
        JsonResult<List<ResultSkuCodeVo>> listJsonResult = new JsonResult<>();
        List<ResultSkuCodeVo> resultSkuCodeVos = new ArrayList<>();
        for (int i=0; i<1003;i++){
            listJsonResult.setSuccess(true);
            ResultSkuCodeVo vo = new ResultSkuCodeVo();
            vo.setSkuCode(String.valueOf(i));
            vo.setProductCode(String.valueOf(i));
            resultSkuCodeVos.add(vo);
        }
        listJsonResult.setData(resultSkuCodeVos);
        List<FlashSaleProductModel> models = new ArrayList<>();
        FlashSaleProductModel model = new FlashSaleProductModel();
        models.add(model);

        QueryFlashSalePriceByProductInDto param = new QueryFlashSalePriceByProductInDto();
        List<QueryFlashSalePriceByProductInDto.Product> products = new ArrayList<>();
        QueryFlashSalePriceByProductInDto.Product product = new QueryFlashSalePriceByProductInDto.Product();
        product.setSkuCode("1");
        product.setProductCode("1");
        products.add(product);
        param.setProducts(products);
        List<MarketingModel> list = new ArrayList<>();
        MarketingModel marketingModel = new MarketingModel();
        marketingModel.setActivityCode("1");
        list.add(marketingModel);
        List<FlashSaleProductModel> flashSaleProductModels = new ArrayList<>();
        for (int i=0; i<1003;i++){
            FlashSaleProductModel model1 = new FlashSaleProductModel();
            model1.setSkuCode(String.valueOf(i));
            model1.setFlashPrice(new BigDecimal(1));
            flashSaleProductModels.add(model1);
        }
        JsonResult<Void> voidJsonResult = new JsonResult<>();
        voidJsonResult.setSuccess(true);
        Mockito.when(marketingService.queryCurrentEffectiveMarketingList(Mockito.any(), Mockito.any())).thenReturn(list);
        Mockito.when(flashSaleProductService.getProductsByActivityCodesAndProducts(Mockito.any(), Mockito.anyList(),Mockito.anyList())).thenReturn(flashSaleProductModels);
        Mockito.when(flashSaleProductService.findListByActivityCode(Mockito.any())).thenReturn(models);
        Mockito.when(pimClient.queryProductSkuCodeList(Mockito.any())).thenReturn(listJsonResult);
        Mockito.when(pimClient.batchUpdateProductToEs(Mockito.any())).thenReturn(voidJsonResult);
        Mockito.when(marketingService.updateByActivityCode(Mockito.any())).thenReturn(1);
        flashSaleComponent.sendPriceToPim("1","1",true);
    }

    @Test
    public void syncPriceTimer_null(){
        List<MarketingModel> marketingModels = new ArrayList<>();
        MarketingModel marketingModel = new MarketingModel();
        marketingModels.add(marketingModel);
        Mockito.when(marketingService.queryAllTenantEffectiveFlashSaleList(Mockito.any(),Mockito.anyInt())).thenReturn(marketingModels);
        Mockito.when(activityPeriodService.findPeriod(Mockito.any(),Mockito.any())).thenReturn(null);
        flashSaleComponent.syncPriceTimer();
    }
    @Test
    public void syncPriceTimer_status_1(){
        List<MarketingModel> marketingModels = new ArrayList<>();
        MarketingModel marketingModel = new MarketingModel();
        marketingModel.setActivityBegin("20210817151502");
        marketingModel.setActivityEnd("20990817151502");
        marketingModel.setSyncPriceStatus("1");
        ActivityPeriodModel period = new ActivityPeriodModel();
        period.setBeginPeriod("* * 8 ? 12 3 2020");
        period.setEndPeriod("* * 8 ? 12 3 2099");
        period.setIntervalWeek(2);
        marketingModels.add(marketingModel);
        Mockito.when(marketingService.queryAllTenantEffectiveFlashSaleList(Mockito.any(),Mockito.anyInt())).thenReturn(marketingModels);
        Mockito.when(activityPeriodService.findPeriod(Mockito.any(),Mockito.any())).thenReturn(period);
        flashSaleComponent.syncPriceTimer();
    }

    @Test
    public void syncPriceTimer_11(){
        List<MarketingModel> marketingModels = new ArrayList<>();
        MarketingModel marketingModel = new MarketingModel();
        marketingModel.setActivityBegin("20210817151502");
        marketingModel.setActivityEnd("20990817151502");
        marketingModel.setSyncPriceStatus("1");
        ActivityPeriodModel period = new ActivityPeriodModel();
        period.setBeginPeriod("1");
        period.setEndPeriod("3");
        period.setIntervalWeek(2);
        marketingModels.add(marketingModel);
        Mockito.when(marketingService.queryAllTenantEffectiveFlashSaleList(Mockito.any(),Mockito.anyInt())).thenReturn(marketingModels);
        Mockito.when(activityPeriodService.findPeriod(Mockito.any(),Mockito.any())).thenReturn(period);
        flashSaleComponent.syncPriceTimer();
    }

    @Test
    public void setProductRelationInfo(){
        flashSaleComponent.setProductRelationInfo("1","1",false,new ArrayList<>(),new ArrayList<>(),new ArrayList<>());
    }

    @Test
    public void setProductRelationInfo1(){
        List<ResultSkuCodeVo> resultSkuCodeVos = new ArrayList<>();
        ResultSkuCodeVo vo = new ResultSkuCodeVo();
        vo.setProductCode("1");
        resultSkuCodeVos.add(vo);
        List<QueryFlashSalePriceByProductOutDto> productOutDtoList = new ArrayList<>();
        QueryFlashSalePriceByProductOutDto dto = new QueryFlashSalePriceByProductOutDto();
        dto.setProductCode("2");
        productOutDtoList.add(dto);
        List<ProductVo> productVoList = new ArrayList<>();
        ProductVo productVo = new ProductVo();
        productVoList.add(productVo);
        flashSaleComponent.setProductRelationInfo("1","1",true,resultSkuCodeVos,productOutDtoList,productVoList);
    }

    @Test
    public void setProductRelationInfo2(){
        List<ResultSkuCodeVo> resultSkuCodeVos = new ArrayList<>();
        ResultSkuCodeVo vo = new ResultSkuCodeVo();
        vo.setProductCode("1");
        resultSkuCodeVos.add(vo);
        List<QueryFlashSalePriceByProductOutDto> productOutDtoList = new ArrayList<>();
        QueryFlashSalePriceByProductOutDto dto = new QueryFlashSalePriceByProductOutDto();
        dto.setProductCode("2");
        productOutDtoList.add(dto);
        List<ProductVo> productVoList = new ArrayList<>();
        for (int i = 0; i < 1000 ; i++) {
            ProductVo productVo = new ProductVo();
            productVoList.add(productVo);
        }
        JsonResult<Void> voidJsonResult = new JsonResult<>();
        voidJsonResult.setSuccess(false);
        Mockito.when(pimClient.batchUpdateProductToEs(Mockito.anyList())).thenReturn(voidJsonResult);
        flashSaleComponent.setProductRelationInfo("1","1",true,resultSkuCodeVos,productOutDtoList,productVoList);
    }


}
