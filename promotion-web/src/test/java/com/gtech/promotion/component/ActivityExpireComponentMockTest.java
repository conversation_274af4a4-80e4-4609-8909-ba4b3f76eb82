package com.gtech.promotion.component;

import com.gtech.promotion.component.activity.ActivityExpireComponentDomain;
import com.gtech.promotion.dao.model.activity.ActivityModel;
import com.gtech.promotion.helper.ActivityRedisHelpler;
import com.gtech.promotion.service.activity.ActivityService;
import com.gtech.promotion.service.activity.OperationLogService;
import com.gtech.promotion.service.coupon.PromoCouponCodeUserService;
import com.gtech.promotion.service.coupon.PromoCouponInnerCodeService;
import com.gtech.promotion.service.coupon.PromoCouponReleaseService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-09-18 9:52
 */
@RunWith(MockitoJUnitRunner.class)
public class ActivityExpireComponentMockTest {
    @InjectMocks
    private ActivityExpireComponentDomain activityExpireComponentDomain;
    @Mock
    private ActivityService activityService;
    @Mock
    private StringRedisTemplate redisTemplate;
    @Mock
    private ActivityRedisHelpler activityRedisHelper;
    @Mock
    private PromoCouponReleaseService couponReleaseService;
    @Mock
    private PromoCouponInnerCodeService couponInnerCodeService;
    @Mock
    private PromoCouponCodeUserService couponCodeUserService;
    @Mock
    private OperationLogService operationLogService;

    @Test
    public void expireActivity(){
        List<ActivityModel> activityModels = new ArrayList<>();
        activityExpireComponentDomain.expireActivity(activityModels);
    }

    @Test
    public void expireActivity1(){
        List<ActivityModel> activityModels = new ArrayList<>();
        ActivityModel activityModel = new ActivityModel();
        activityModel.setActivityStatus("01");
        activityModels.add(activityModel);
        activityExpireComponentDomain.expireActivity(activityModels);
    }

    @Test
    public void expireActivity2(){
        ActivityModel activityByActivityCode = new ActivityModel();
        activityByActivityCode.setActivityStatus("01");
        Mockito.when(activityService.findActivityByActivityCode(Mockito.any(), Mockito.any())).thenReturn(activityByActivityCode);
        activityExpireComponentDomain.expireActivity("1","1");
    }

    @Test
    public void expireActivity3(){
        ActivityModel activityByActivityCode = new ActivityModel();
        activityByActivityCode.setActivityStatus("04");
        activityByActivityCode.setActivityEnd("20210918100000");
        Mockito.when(activityService.findActivityByActivityCode(Mockito.any(), Mockito.any())).thenReturn(activityByActivityCode);
        activityExpireComponentDomain.expireActivity("1","2");
    }

    @Test
    public void expireActivity4(){
        ActivityModel activityByActivityCode = new ActivityModel();
        activityByActivityCode.setActivityStatus("04");
        activityByActivityCode.setActivityEnd("20210918100000");
        Mockito.when(activityService.findActivityByActivityCode(Mockito.any(), Mockito.any())).thenReturn(activityByActivityCode);
        Mockito.when(activityService.updateActivityStatus(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenThrow(NullPointerException.class);
        activityExpireComponentDomain.expireActivity("1","1");
    }

    @Test
    public void expireActivity6(){
        ActivityModel activityByActivityCode = new ActivityModel();
        activityByActivityCode.setActivityStatus("04");
        activityByActivityCode.setActivityEnd("20210918100000");
        activityByActivityCode.setTenantCode("1");
        activityByActivityCode.setActivityCode("2");
        Mockito.when(activityService.findActivityByActivityCode(Mockito.any(), Mockito.any())).thenReturn(activityByActivityCode);
        activityExpireComponentDomain.expireActivity(activityByActivityCode);
    }

    @Test
    public void expireActivity7(){
        ActivityModel activityByActivityCode = new ActivityModel();
        activityByActivityCode.setActivityStatus("04");
        activityByActivityCode.setActivityEnd("20210918100000");
        activityByActivityCode.setTenantCode("1");
        activityByActivityCode.setActivityCode("2");
        ActivityModel activityByActivity = new ActivityModel();
        activityByActivity.setActivityStatus("01");
        Mockito.when(activityService.findActivityByActivityCode(Mockito.any(), Mockito.any())).thenReturn(activityByActivity);
        activityExpireComponentDomain.expireActivity(activityByActivityCode);
    }
}
