package com.gtech.promotion.component.coupon;

import com.gtech.basic.idm.web.vo.result.QueryUserResult;
import com.gtech.commons.page.PageData;
import com.gtech.commons.result.Result;
import com.gtech.member.web.vo.result.GetMemberProfileResult;
import com.gtech.member.web.vo.result.QueryMemberListByConditionResult;
import com.gtech.promotion.code.coupon.CouponFrozenStatusEnum;
import com.gtech.promotion.code.coupon.CouponStatusEnum;
import com.gtech.promotion.code.coupon.CouponTemplateEnum;
import com.gtech.promotion.code.coupon.CouponTypeEnum;
import com.gtech.promotion.component.activity.QualificationDomain;
import com.gtech.promotion.dao.model.activity.ActivityModel;
import com.gtech.promotion.dao.model.activity.PromoCouponSendDetailModel;
import com.gtech.promotion.dao.model.coupon.CouponReleaseModel;
import com.gtech.promotion.dao.model.coupon.TPromoCouponCodeUserVO;
import com.gtech.promotion.dao.model.coupon.TPromoCouponInnerCodeVO;
import com.gtech.promotion.domain.coupon.CouponReleaseDomain;
import com.gtech.promotion.dto.cache.ActivityCacheDTO;
import com.gtech.promotion.dto.in.coupon.ExportCouponUserDto;
import com.gtech.promotion.dto.in.coupon.QueryCouponUsedInDTO;
import com.gtech.promotion.dto.in.coupon.TCouponCheckAndUseInDTO;
import com.gtech.promotion.exception.PromotionException;
import com.gtech.promotion.feign.IdmFeignClient;
import com.gtech.promotion.feign.MemberFeignClient;
import com.gtech.promotion.helper.ActivityRedisHelpler;
import com.gtech.promotion.service.activity.ActivityService;
import com.gtech.promotion.service.coupon.PromoCouponCodeUserService;
import com.gtech.promotion.service.coupon.PromoCouponReleaseService;
import com.gtech.promotion.service.impl.coupon.PromoCouponActivityServiceImpl;
import com.gtech.promotion.service.impl.coupon.PromoCouponInnerCodeServiceImpl;
import com.gtech.promotion.service.impl.coupon.PromoCouponReleaseServiceImpl;
import com.gtech.promotion.utils.Constants;
import com.gtech.promotion.utils.DateUtil;
import com.gtech.promotion.utils.EasyCacheUtil;
import com.gtech.promotion.vo.param.coupon.SendCouponToUserParam;
import com.gtech.promotion.vo.result.coupon.ExportCouponUserDetailResult;
import com.gtech.promotion.vo.result.coupon.ExportCouponUserResult;
import com.gtech.promotion.vo.result.coupon.QueryCouponUsedListResult;
import org.apache.commons.lang.time.DateUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static org.mockito.ArgumentMatchers.any;

@RunWith(MockitoJUnitRunner.class)
public class CouponCodeUserComponentTest {

    @InjectMocks
    private CouponCodeUserComponent couponCodeUserComponent;

    @Mock
    private PromoCouponInnerCodeServiceImpl couponInnerCodeService;

    @Mock
    private PromoCouponActivityServiceImpl couponActivityService;
    @Mock
    private ActivityRedisHelpler redisService;
    @Mock
    private PromoCouponReleaseServiceImpl promoCouponReleaseService;
    @Mock
    private PromoCouponCodeUserService couponCodeUserService;
    @Mock
    private MemberFeignClient memberFeignClient;
    @Mock
    private ActivityService activityService;
    @Mock
    private IdmFeignClient idmFeignClient;


    @Mock
    private CouponInnerCodeComponent couponInnerCodeDomain;

    @Mock
    private CouponActivityComponent couponActivityDomain;


    @Mock
    private PromoCouponReleaseService couponReleaseService;

    @Mock
    private ReceiveCouponBatchComponent receiveCouponBatchDomain;

    @Mock
    private QualificationDomain qualificationDomain;


    @Test
    public void getInnerCode(){

        String couponCodeKey = 1+":"+1+":"+"couponCodes";

        List<TPromoCouponInnerCodeVO> couponInnerCodeVOS = new ArrayList<>();

        TPromoCouponInnerCodeVO tPromoCouponInnerCodeVO = new TPromoCouponInnerCodeVO();
        tPromoCouponInnerCodeVO.setCouponCode("1");
        tPromoCouponInnerCodeVO.setActivityCode("1");
        tPromoCouponInnerCodeVO.setTenantCode("1");


        couponInnerCodeVOS.add(tPromoCouponInnerCodeVO);


        EasyCacheUtil.set(couponCodeKey, couponInnerCodeVOS, 2);

        couponCodeUserComponent.getInnerVO("1","1","1", false);

    }

    @Test
    public void testCheckAnonCoupon_Success() {
        long now = System.currentTimeMillis();
        TPromoCouponInnerCodeVO couponInnerCode = new TPromoCouponInnerCodeVO();
        couponInnerCode.setCouponType("02");
        couponInnerCode.setValidStartTime(String.valueOf(now + 10000));
        couponInnerCode.setValidEndTime(String.valueOf(now + 20000));

        CouponReleaseDomain couponReleaseById = new CouponReleaseDomain();
        couponReleaseById.setValidDays(10);

        Assert.assertThrows(PromotionException.class,  () -> couponCodeUserComponent.checkAnonCoupon(now, couponInnerCode, couponReleaseById));
    }

    @Test
    public void testCheckAnonCoupon_NoReceiveCoupon() {
        long now = System.currentTimeMillis();
        TPromoCouponInnerCodeVO couponInnerCode = new TPromoCouponInnerCodeVO();
        couponInnerCode.setCouponType("02");
        couponInnerCode.setValidStartTime(""); // 空字符串应引发异常

        CouponReleaseDomain couponReleaseById = new CouponReleaseDomain();
        couponReleaseById.setValidDays(10);

        Assert.assertThrows(PromotionException.class, () -> couponCodeUserComponent.checkAnonCoupon(now, couponInnerCode, couponReleaseById));
    }

    @Test
    public void testCheckAnonCoupon_NoUsedTime() {
        long now = System.currentTimeMillis();
        TPromoCouponInnerCodeVO couponInnerCode = new TPromoCouponInnerCodeVO();
        couponInnerCode.setCouponType("02");
        couponInnerCode.setValidStartTime(String.valueOf(now - 10000)); // 过去的时间应引发异常
        couponInnerCode.setValidEndTime(String.valueOf(now - 10000)); // 过去的时间应引发异常

        CouponReleaseDomain couponReleaseById = new CouponReleaseDomain();
        couponReleaseById.setValidDays(10);

        Assert.assertThrows(PromotionException.class,()->couponCodeUserComponent.checkAnonCoupon(now, couponInnerCode, couponReleaseById));
    }

    @Test
    public void testCheckAnonCoupon_EndTime() {
        long now = System.currentTimeMillis();
        TPromoCouponInnerCodeVO couponInnerCode = new TPromoCouponInnerCodeVO();
        couponInnerCode.setCouponType("02");
        couponInnerCode.setValidEndTime(String.valueOf(now -1000)); // 过去的时间应引发异常

        CouponReleaseDomain couponReleaseById = new CouponReleaseDomain();
        couponReleaseById.setValidDays(10);

        Assert.assertThrows(PromotionException.class, () -> couponCodeUserComponent.checkAnonCoupon(now, couponInnerCode, couponReleaseById));
    }

    @Test(expected = PromotionException.class)
    public void checkUserLimit_check(){

        ActivityModel activityModel = new ActivityModel();
        activityModel.setUserLimitMax(1);

        TPromoCouponInnerCodeVO couponInnerCode = new TPromoCouponInnerCodeVO();
        couponInnerCode.setActivityCode("1");
        couponInnerCode.setCouponCode("1");
        couponInnerCode.setActivityCode("1");
        Mockito.when(couponCodeUserService.getUserUsedCouponCountByActivityCode(any(),any(),any())).thenReturn(1);
        couponCodeUserComponent.checkUserLimit("userCode",couponInnerCode,activityModel);

    }


    @Test
    public void checkUserLimit(){

        ActivityModel activityModel = new ActivityModel();
        activityModel.setUserLimitMax(10);

        TPromoCouponInnerCodeVO couponInnerCode = new TPromoCouponInnerCodeVO();
        couponInnerCode.setActivityCode("1");
        couponInnerCode.setCouponCode("1");
        couponInnerCode.setActivityCode("1");
        Mockito.when(couponCodeUserService.getUserUsedCouponCountByActivityCode(any(),any(),any())).thenReturn(1);
        couponCodeUserComponent.checkUserLimit("userCode",couponInnerCode,activityModel);

    }


    @Test(expected = PromotionException.class)
    public void isCanUse_coupon_not_exist(){
        // given
        String tenantCode = "1";
        String couponCode = "1";
        String userCode = "1";
        String activityCode = "1";
        Map<String, ActivityCacheDTO> activityCacheMap = new HashMap<>();
        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();
        ActivityModel activityModel = new ActivityModel();
        activityModel.setTotalQuantity(10);
        activityModel.setActivityCode("111");
        activityCacheDTO.setActivityModel(activityModel);
        activityCacheMap.put("111",activityCacheDTO);

        // when
        // then
        String canUse = couponCodeUserComponent.isCanUse(tenantCode, couponCode, userCode, activityCacheMap, false);
        Assert.assertEquals(activityCode, canUse);
    }

    @Test(expected = PromotionException.class)
    public void isCanUse_no_activity(){
        // given
        String tenantCode = "1";
        String couponCode = "1";
        String userCode = "1";
        String activityCode = "1";
        Map<String, ActivityCacheDTO> activityCacheMap = new HashMap<>();
        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();
        ActivityModel activityModel = new ActivityModel();
        activityModel.setTotalQuantity(10);
        activityModel.setActivityCode("1");
        activityCacheDTO.setActivityModel(activityModel);
        activityCacheMap.put("111",activityCacheDTO);
        CouponReleaseDomain couponReleaseById = new CouponReleaseDomain();
        couponReleaseById.setValidDays(1);
        couponReleaseById.setReceiveStartTime("1");

        // when
        TPromoCouponInnerCodeVO couponInnerCode = new TPromoCouponInnerCodeVO();
        couponInnerCode.setCouponCode("111");
        couponInnerCode.setActivityCode("1");
        couponInnerCode.setStatus(CouponStatusEnum.UN_GRANT.code());

        couponInnerCode.setFrozenStatus(CouponFrozenStatusEnum.UN_FROZEN.code());
        //Mockito.when(couponInnerCodeService.findCouponByCouponCodeOrPassword(Mockito.anyString(), Mockito.anyString())).thenReturn(couponInnerCode);
//        Mockito.when(promoCouponReleaseService.findCouponReleaseByReleaseCode(any(), any())).thenReturn(couponReleaseById);
        // then
        String canUse = couponCodeUserComponent.isCanUse(tenantCode, couponCode, userCode, activityCacheMap, false);
        Assert.assertEquals(activityCode, canUse);
    }

    @Test
    public void isCanUse_优惠码(){
        // given
        String tenantCode = "isCanUse_优惠码:tenantCode:1";
        String couponCode = "1";
        String userCode = "1";
        Map<String, ActivityCacheDTO> activityCacheMap = new HashMap<>();
        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();
        ActivityModel activityModel = new ActivityModel();
        activityModel.setTotalQuantity(10);
        activityModel.setActivityCode("111");
        activityCacheDTO.setActivityModel(activityModel);
        activityCacheMap.put("111",activityCacheDTO);
        // when
        TPromoCouponInnerCodeVO couponInnerCode = new TPromoCouponInnerCodeVO();
        couponInnerCode.setStatus(CouponStatusEnum.GRANTED.code());
        couponInnerCode.setActivityCode("111");
        ActivityModel couponActivity = new ActivityModel();
        CouponReleaseDomain couponReleaseById = new CouponReleaseDomain();
        couponReleaseById.setValidDays(1);
        couponReleaseById.setReceiveStartTime("1");
        couponActivity.setUserLimitMax(0);
        Mockito.when(couponInnerCodeService.findCouponByCouponCodeOrPassword(Mockito.anyString(), Mockito.anyString())).thenReturn(couponInnerCode);
//        Mockito.when(couponActivityService.findCouponActivity(Mockito.anyString(), any())).thenReturn(couponActivity);
        Mockito.when(redisService.getCouponReleaseLimit(Mockito.anyString(), any(), any())).thenReturn(0);
        Mockito.when(promoCouponReleaseService.findCouponReleaseByReleaseCode(any(), any())).thenReturn(couponReleaseById);
        // then
        String canUse = couponCodeUserComponent.isCanUse(tenantCode, couponCode, userCode, activityCacheMap, false);
        Assert.assertEquals("111",canUse);
    }



    @Test
    public void isCanUse_优惠券_是否领取_1_内存(){


        String couponCodeUserVoKey = "2" + ":" + "2" + ":" + "couponCodeUserVOs";
        List<TPromoCouponCodeUserVO> list = new ArrayList<>();
        TPromoCouponCodeUserVO tPromoCouponCodeUserVO = new TPromoCouponCodeUserVO();
        tPromoCouponCodeUserVO.setCouponCode("1");  //
        tPromoCouponCodeUserVO.setActivityCode("1");
        tPromoCouponCodeUserVO.setCouponType(CouponTemplateEnum.COUPON.code());
        tPromoCouponCodeUserVO.setStatus(CouponStatusEnum.GRANTED.code());
        tPromoCouponCodeUserVO.setFrozenStatus(CouponFrozenStatusEnum.UN_FROZEN.code());
        tPromoCouponCodeUserVO.setTenantCode("1");  //
        tPromoCouponCodeUserVO.setUserCode("1");
        tPromoCouponCodeUserVO.setValidStartTime("20210425000000");
        Date date = DateUtils.addDays(new Date(), 1);
        String dateString = DateUtil.getDateString(date, DateUtil.DATETIMESTOREFORMAT);
        tPromoCouponCodeUserVO.setValidEndTime(dateString);
        tPromoCouponCodeUserVO.setUsedRefId(Constants.DEFAULT_USED_REF_ID);
        tPromoCouponCodeUserVO.setActivityCode("111");
        list.add(tPromoCouponCodeUserVO);
        EasyCacheUtil.set(couponCodeUserVoKey,list,2);

        // given
        String tenantCode = "isCanUse_优惠券_是否领取_1_内存:tenantCode:1";
        String couponCode = "1";
        String userCode = "1";
        Map<String, ActivityCacheDTO> activityCacheMap = new HashMap<>();
        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();
        ActivityModel activityModel = new ActivityModel();
        activityModel.setTotalQuantity(10);
        activityModel.setActivityCode("111");
        activityCacheDTO.setActivityModel(activityModel);
        activityCacheMap.put("111",activityCacheDTO);
        // when
        TPromoCouponInnerCodeVO couponInnerCode = new TPromoCouponInnerCodeVO();
        couponInnerCode.setStatus(CouponStatusEnum.GRANTED.code());
        couponInnerCode.setCouponType(CouponTemplateEnum.COUPON.code());
        couponInnerCode.setCouponCode("1");
        ActivityModel couponActivity = new ActivityModel();
        CouponReleaseDomain couponReleaseById = new CouponReleaseDomain();
        couponReleaseById.setValidDays(1);
        couponReleaseById.setReceiveStartTime("1");
        couponActivity.setUserLimitMax(0);
        couponInnerCode.setActivityCode("111");
        Mockito.when(couponInnerCodeService.findCouponByCouponCodeOrPassword(Mockito.anyString(), Mockito.anyString())).thenReturn(couponInnerCode);
        TPromoCouponCodeUserVO userVO = new TPromoCouponCodeUserVO();
        userVO.setStatus(CouponStatusEnum.GRANTED.code());
        userVO.setUserCode("12112");

        userVO.setValidStartTime("20210425000000");
        userVO.setValidEndTime(dateString);
        userVO.setUsedRefId(Constants.DEFAULT_USED_REF_ID);
        userVO.setActivityCode("111");
        Mockito.when(couponCodeUserService.getUserCouponInfo(any(), any(), any())).thenReturn(userVO);
        // then
        String canUse = couponCodeUserComponent.isCanUse(tenantCode, couponCode, userCode, activityCacheMap, false);
        Assert.assertNotNull("111",canUse);
    }


    @Test
    public void isCanUse_优惠券_是否领取_1_内存2(){
        String couponCodeUserVoKey = "3" + ":" + "3" + ":" + "couponCodeUserVOs";
        List<TPromoCouponCodeUserVO> list = new ArrayList<>();
        TPromoCouponCodeUserVO tPromoCouponCodeUserVO = new TPromoCouponCodeUserVO();
        tPromoCouponCodeUserVO.setCouponCode("1");
        tPromoCouponCodeUserVO.setActivityCode("1");
        tPromoCouponCodeUserVO.setCouponType(CouponTemplateEnum.COUPON.code());
        tPromoCouponCodeUserVO.setStatus(CouponStatusEnum.GRANTED.code());
        tPromoCouponCodeUserVO.setFrozenStatus(CouponFrozenStatusEnum.UN_FROZEN.code());
        tPromoCouponCodeUserVO.setTenantCode("1");
        tPromoCouponCodeUserVO.setUserCode("1");
        tPromoCouponCodeUserVO.setValidStartTime("20210425000000");
        Date date = DateUtils.addDays(new Date(), 1);
        String dateString = DateUtil.getDateString(date, DateUtil.DATETIMESTOREFORMAT);
        tPromoCouponCodeUserVO.setValidEndTime(dateString);
        tPromoCouponCodeUserVO.setUsedRefId(Constants.DEFAULT_USED_REF_ID);
        tPromoCouponCodeUserVO.setActivityCode("111");
        list.add(tPromoCouponCodeUserVO);
        EasyCacheUtil.set(couponCodeUserVoKey,list,5);
        // given
        String tenantCode = "1";
        String couponCode = "1";
        String userCode = "1";
        Map<String, ActivityCacheDTO> activityCacheMap = new HashMap<>();
        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();
        ActivityModel activityModel = new ActivityModel();
        activityModel.setTotalQuantity(10);
        activityModel.setActivityCode("111");
        activityCacheDTO.setActivityModel(activityModel);
        activityCacheMap.put("111",activityCacheDTO);
        // when
        TPromoCouponInnerCodeVO couponInnerCode = new TPromoCouponInnerCodeVO();
        couponInnerCode.setStatus(CouponStatusEnum.GRANTED.code());
        couponInnerCode.setCouponType(CouponTemplateEnum.COUPON.code());
        couponInnerCode.setCouponCode("1");
        ActivityModel couponActivity = new ActivityModel();
        CouponReleaseDomain couponReleaseById = new CouponReleaseDomain();
        couponReleaseById.setValidDays(1);
        couponReleaseById.setReceiveStartTime("1");
        couponActivity.setUserLimitMax(0);
        couponInnerCode.setActivityCode("111");
        Mockito.when(couponInnerCodeService.findCouponByCouponCodeOrPassword(Mockito.anyString(), Mockito.anyString())).thenReturn(couponInnerCode);
        TPromoCouponCodeUserVO userVO = new TPromoCouponCodeUserVO();
        userVO.setStatus(CouponStatusEnum.GRANTED.code());
        userVO.setUserCode("12112");

        userVO.setValidStartTime("20210425000000");
        userVO.setValidEndTime(dateString);
        userVO.setUsedRefId(Constants.DEFAULT_USED_REF_ID);
        userVO.setActivityCode("111");
        Mockito.when(couponCodeUserService.getUserCouponInfo(any(), any(), any())).thenReturn(userVO);
        // then
        String canUse = couponCodeUserComponent.isCanUse(tenantCode, couponCode, userCode, activityCacheMap, false);
        Assert.assertNotNull("111",canUse);
    }


    @Test
    public void isCanUse_优惠券(){


        String couponCodeUserVoKey = "1" + ":" + "1" + ":" + "couponCodeUserVOs";
        List<TPromoCouponCodeUserVO> list = new ArrayList<>();
        TPromoCouponCodeUserVO tPromoCouponCodeUserVO = new TPromoCouponCodeUserVO();
        tPromoCouponCodeUserVO.setCouponCode("1");
        tPromoCouponCodeUserVO.setActivityCode("1");
        tPromoCouponCodeUserVO.setCouponType(CouponTemplateEnum.ANON_COUPON.code());
        tPromoCouponCodeUserVO.setStatus(CouponStatusEnum.UN_GRANT.code());
        tPromoCouponCodeUserVO.setFrozenStatus(CouponFrozenStatusEnum.UN_FROZEN.code());
        tPromoCouponCodeUserVO.setTenantCode("1");
        tPromoCouponCodeUserVO.setUserCode("1");
        tPromoCouponCodeUserVO.setValidStartTime("20210425000000");
        Date date = DateUtils.addDays(new Date(), 1);
        String dateString = DateUtil.getDateString(date, DateUtil.DATETIMESTOREFORMAT);
        tPromoCouponCodeUserVO.setValidEndTime(dateString);
        tPromoCouponCodeUserVO.setUsedRefId(Constants.DEFAULT_USED_REF_ID);
        tPromoCouponCodeUserVO.setActivityCode("111");
        list.add(tPromoCouponCodeUserVO);
        EasyCacheUtil.set(couponCodeUserVoKey,list,5);
        // given
        String tenantCode = "isCanUse_优惠券:tenantCode:1";
        String couponCode = "1";
        String userCode = "1";
        Map<String, ActivityCacheDTO> activityCacheMap = new HashMap<>();
        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();
        ActivityModel activityModel = new ActivityModel();
        activityModel.setTotalQuantity(10);
        activityModel.setActivityCode("111");
        activityCacheDTO.setActivityModel(activityModel);
        activityCacheMap.put("111",activityCacheDTO);
        // when
        TPromoCouponInnerCodeVO couponInnerCode = new TPromoCouponInnerCodeVO();
        couponInnerCode.setStatus(CouponStatusEnum.GRANTED.code());
        couponInnerCode.setCouponType(CouponTemplateEnum.ANON_COUPON.code());
        couponInnerCode.setCouponCode("1");
        ActivityModel couponActivity = new ActivityModel();
        CouponReleaseDomain couponReleaseById = new CouponReleaseDomain();
        couponReleaseById.setValidDays(1);
        couponReleaseById.setReceiveStartTime("1");
        couponActivity.setUserLimitMax(0);
        couponInnerCode.setActivityCode("111");
        Mockito.when(couponInnerCodeService.findCouponByCouponCodeOrPassword(Mockito.anyString(), Mockito.anyString())).thenReturn(couponInnerCode);
        TPromoCouponCodeUserVO userVO = new TPromoCouponCodeUserVO();
        userVO.setStatus(CouponStatusEnum.GRANTED.code());
        userVO.setUserCode("12112");

        userVO.setValidStartTime("20210425000000");
        userVO.setValidEndTime(dateString);
        userVO.setUsedRefId(Constants.DEFAULT_USED_REF_ID);
        userVO.setActivityCode("111");
        Mockito.when(couponCodeUserService.getUserCouponInfo(any(), any(), any())).thenReturn(userVO);
        // then
        String canUse = couponCodeUserComponent.isCanUse(tenantCode, couponCode, userCode, activityCacheMap, false);
        Assert.assertNotNull("111",canUse);
    }

    @Test
    public void isCanUse_优惠券_是否领取_1(){
        // given
        String tenantCode = "isCanUse_优惠券_是否领取_1:tenantCode:1";
        String couponCode = "1";
        String userCode = "1";
        Map<String, ActivityCacheDTO> activityCacheMap = new HashMap<>();
        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();
        ActivityModel activityModel = new ActivityModel();
        activityModel.setTotalQuantity(10);
        activityModel.setActivityCode("111");
        activityCacheDTO.setActivityModel(activityModel);
        activityCacheMap.put("111",activityCacheDTO);
        // when
        TPromoCouponInnerCodeVO couponInnerCode = new TPromoCouponInnerCodeVO();
        couponInnerCode.setStatus(CouponStatusEnum.GRANTED.code());
        couponInnerCode.setCouponType(CouponTemplateEnum.COUPON.code());
        ActivityModel couponActivity = new ActivityModel();
        CouponReleaseDomain couponReleaseById = new CouponReleaseDomain();
        couponReleaseById.setValidDays(1);
        couponReleaseById.setReceiveStartTime("1");
        couponActivity.setUserLimitMax(0);
        couponInnerCode.setActivityCode("111");
        Mockito.when(couponInnerCodeService.findCouponByCouponCodeOrPassword(Mockito.any(), Mockito.any())).thenReturn(couponInnerCode);
        TPromoCouponCodeUserVO userVO = new TPromoCouponCodeUserVO();
        userVO.setStatus(CouponStatusEnum.GRANTED.code());
        userVO.setUserCode("12112");
        Date date = DateUtils.addDays(new Date(), 1);
        String dateString = DateUtil.getDateString(date, DateUtil.DATETIMESTOREFORMAT);
        userVO.setValidStartTime("20210425000000");
        userVO.setValidEndTime(dateString);
        userVO.setUsedRefId(Constants.DEFAULT_USED_REF_ID);
        userVO.setActivityCode("111");
        Mockito.when(couponCodeUserService.getUserCouponInfo(any(), any(), any())).thenReturn(userVO);
        Mockito.when(couponInnerCodeService.findCouponByCouponCodeOrPassword(any(), any())).thenReturn(couponInnerCode);
        // then
        String canUse = couponCodeUserComponent.isCanUse(tenantCode, couponCode, userCode, activityCacheMap, false);
        Assert.assertNotNull("111",canUse);
    }

    @Test
    public void isCanUse_优惠券_是否领取_2(){
        // given
        String tenantCode = "isCanUse_优惠券_是否领取_2:tenantCode:1";
        String couponCode = "1";
        String userCode = "1";
        Map<String, ActivityCacheDTO> activityCacheMap = new HashMap<>();
        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();
        ActivityModel activityModel = new ActivityModel();
        activityModel.setTotalQuantity(10);
        activityModel.setActivityCode("111");
        activityCacheDTO.setActivityModel(activityModel);
        activityCacheMap.put("111",activityCacheDTO);
        // when
        TPromoCouponInnerCodeVO couponInnerCode = new TPromoCouponInnerCodeVO();
        couponInnerCode.setStatus(CouponStatusEnum.GRANTED.code());
        couponInnerCode.setCouponType(CouponTemplateEnum.COUPON.code());
        couponInnerCode.setActivityCode("111");
        ActivityModel couponActivity = new ActivityModel();
        CouponReleaseDomain couponReleaseById = new CouponReleaseDomain();
        couponReleaseById.setValidDays(1);
        couponReleaseById.setReceiveStartTime("1");
        couponActivity.setUserLimitMax(0);
        Mockito.when(couponInnerCodeService.findCouponByCouponCodeOrPassword(Mockito.any(), Mockito.any())).thenReturn(couponInnerCode);
        TPromoCouponCodeUserVO userVO = new TPromoCouponCodeUserVO();
        userVO.setStatus(CouponStatusEnum.GRANTED.code());
        userVO.setUserCode("12112");
        Date date = DateUtils.addDays(new Date(), 1);
        String dateString = DateUtil.getDateString(date, DateUtil.DATETIMESTOREFORMAT);
        userVO.setValidStartTime("20210425000000");
        userVO.setValidEndTime(dateString);
        userVO.setUsedRefId("121312");
        userVO.setActivityCode("111");
        Mockito.when(couponCodeUserService.getUserCouponInfo(any(), any(), any())).thenReturn(userVO);
        // then
        String canUse = couponCodeUserComponent.isCanUse(tenantCode, couponCode, userCode, activityCacheMap, false);
        Assert.assertNotNull("0",canUse);
    }

    @Test(expected = PromotionException.class)
    public void isCanUse_匿名券_冻结(){
        // given
        String tenantCode = "1";
        String couponCode = "1";
        String userCode = "1";
        Map<String, ActivityCacheDTO> activityCacheMap = new HashMap<>();
        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();
        ActivityModel activityModel = new ActivityModel();
        activityModel.setTotalQuantity(10);
        activityModel.setActivityCode("111");
        activityCacheDTO.setActivityModel(activityModel);
        activityCacheMap.put("111",activityCacheDTO);
        // when
        TPromoCouponInnerCodeVO couponInnerCode = new TPromoCouponInnerCodeVO();
        couponInnerCode.setStatus(CouponStatusEnum.GRANTED.code());
        couponInnerCode.setCouponType(CouponTemplateEnum.ANON_COUPON.code());
        couponInnerCode.setFrozenStatus(CouponFrozenStatusEnum.FROZENED.code());
        couponInnerCode.setActivityCode("111");
        couponInnerCode.setCouponCode("666");
        ActivityModel couponActivity = new ActivityModel();
        CouponReleaseDomain couponReleaseById = new CouponReleaseDomain();
        couponReleaseById.setValidDays(1);
        couponReleaseById.setReceiveStartTime("1");
        couponActivity.setUserLimitMax(0);
//        Mockito.when(couponInnerCodeService.findCouponByCouponCode(Mockito.anyString(), Mockito.anyString())).thenReturn(couponInnerCode);
//        Mockito.when(couponActivityService.findCouponActivity(Mockito.anyString(), any())).thenReturn(couponActivity);
//        Mockito.when(promoCouponReleaseService.findCouponReleaseByReleaseCode(Mockito.any(), Mockito.any())).thenReturn(couponReleaseById);
        TPromoCouponCodeUserVO userVO = new TPromoCouponCodeUserVO();
        userVO.setStatus(CouponStatusEnum.GRANTED.code());
        userVO.setUserCode("12112");
        Date date = DateUtils.addDays(new Date(), 1);
        String dateString = DateUtil.getDateString(date, DateUtil.DATETIMESTOREFORMAT);
        userVO.setValidStartTime("20210425000000");
        userVO.setValidEndTime(dateString);
        userVO.setUsedRefId("121312");
        userVO.setActivityCode("111");
//        Mockito.when(couponCodeUserService.getUserCouponInfo(Mockito.any(), Mockito.any(),Mockito.any())).thenReturn(userVO);
        // then
        String canUse = couponCodeUserComponent.isCanUse(tenantCode, couponCode, userCode, activityCacheMap, false);
        Assert.assertNotNull("0",canUse);
    }

    @Test
    public void isCanUse_匿名券_未冻结(){
        // given
        String tenantCode = "1";
        String couponCode = "1";
        String userCode = "1";
        Map<String, ActivityCacheDTO> activityCacheMap = new HashMap<>();
        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();
        ActivityModel activityModel = new ActivityModel();
        activityModel.setTotalQuantity(10);
        activityModel.setActivityCode("111");
        activityCacheDTO.setActivityModel(activityModel);
        activityCacheMap.put("111",activityCacheDTO);
        // when
        TPromoCouponInnerCodeVO couponInnerCode = new TPromoCouponInnerCodeVO();
        couponInnerCode.setStatus(CouponStatusEnum.GRANTED.code());
        couponInnerCode.setCouponType(CouponTemplateEnum.ANON_COUPON.code());
        couponInnerCode.setFrozenStatus(CouponFrozenStatusEnum.UN_FROZEN.code());
        couponInnerCode.setCouponCode("666");
        couponInnerCode.setActivityCode("111");
        ActivityModel couponActivity = new ActivityModel();
        CouponReleaseDomain couponReleaseById = new CouponReleaseDomain();
        couponReleaseById.setValidDays(1);
        couponReleaseById.setReceiveStartTime("1");
        couponActivity.setUserLimitMax(0);
        Mockito.when(couponInnerCodeService.findCouponByCouponCodeOrPassword(Mockito.anyString(), Mockito.anyString())).thenReturn(couponInnerCode);
        //Mockito.when(couponActivityService.findCouponActivity(Mockito.anyString(), any())).thenReturn(couponActivity);
//        Mockito.when(promoCouponReleaseService.findCouponReleaseByReleaseCode(Mockito.any(), Mockito.any())).thenReturn(couponReleaseById);
        TPromoCouponCodeUserVO userVO = new TPromoCouponCodeUserVO();
        userVO.setStatus(CouponStatusEnum.GRANTED.code());
        userVO.setUserCode("12112");
        Date date = DateUtils.addDays(new Date(), 1);
        String dateString = DateUtil.getDateString(date, DateUtil.DATETIMESTOREFORMAT);
        userVO.setValidStartTime("20210425000000");
        userVO.setValidEndTime(dateString);
        userVO.setUsedRefId("121312");
        userVO.setActivityCode("111");
        Mockito.when(couponCodeUserService.getUserCouponInfo(any(), any(), any())).thenReturn(userVO);
        // then
        String canUse = couponCodeUserComponent.isCanUse(tenantCode, couponCode, userCode, activityCacheMap, false);
        Assert.assertNotNull("0",canUse);
    }

    @Test
    public void isCanUse_匿名券_未冻结1(){
        // given
        String tenantCode = "isCanUse_匿名券_未冻结1:tenantCode:1";
        String couponCode = "1";
        String userCode = "1";
        Map<String, ActivityCacheDTO> activityCacheMap = new HashMap<>();
        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();
        ActivityModel activityModel = new ActivityModel();
        activityModel.setActivityCode("11111");
        activityCacheDTO.setActivityModel(activityModel);
        activityCacheMap.put("11111",activityCacheDTO);
        // when
        TPromoCouponInnerCodeVO couponInnerCode = new TPromoCouponInnerCodeVO();
        couponInnerCode.setStatus(CouponStatusEnum.GRANTED.code());
        couponInnerCode.setCouponType(CouponTemplateEnum.ANON_COUPON.code());
        couponInnerCode.setFrozenStatus(CouponFrozenStatusEnum.UN_FROZEN.code());
        couponInnerCode.setCouponCode("666");
        couponInnerCode.setActivityCode("11111");
        ActivityModel couponActivity = new ActivityModel();
        CouponReleaseDomain couponReleaseById = new CouponReleaseDomain();
        couponReleaseById.setValidDays(1);
        couponReleaseById.setReceiveStartTime("1");
        couponActivity.setUserLimitMax(0);
        Mockito.when(couponInnerCodeService.findCouponByCouponCodeOrPassword(Mockito.anyString(), Mockito.anyString())).thenReturn(couponInnerCode);
//        Mockito.when(couponActivityService.findCouponActivity(Mockito.anyString(), any())).thenReturn(couponActivity);
//        Mockito.when(promoCouponReleaseService.findCouponReleaseByReleaseCode(Mockito.any(), Mockito.any())).thenReturn(couponReleaseById);
        TPromoCouponCodeUserVO userVO = new TPromoCouponCodeUserVO();
        userVO.setStatus(CouponStatusEnum.GRANTED.code());
        userVO.setUserCode("12112");
        Date date = DateUtils.addDays(new Date(), 1);
        String dateString = DateUtil.getDateString(date, DateUtil.DATETIMESTOREFORMAT);
        userVO.setValidStartTime("20210425000000");
        userVO.setValidEndTime(dateString);
        userVO.setUsedRefId("121312");
        userVO.setActivityCode("111");
        Mockito.when(couponCodeUserService.getUserCouponInfo(any(), any(), any())).thenReturn(userVO);
        // then
        String canUse = couponCodeUserComponent.isCanUse(tenantCode, couponCode, userCode, activityCacheMap, false);
        Assert.assertNotNull("0",canUse);
    }

    @Test(expected = PromotionException.class)
    public void isCanUse_优惠码_异常(){
        // given
        String tenantCode = "1";
        String couponCode = "1";
        String userCode = "1";
        Map<String, ActivityCacheDTO> activityCacheMap = new HashMap<>();
        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();
        ActivityModel activityModel = new ActivityModel();
        activityModel.setTotalQuantity(10);
        activityModel.setActivityCode("111");
        activityCacheDTO.setActivityModel(activityModel);
        activityCacheMap.put("111",activityCacheDTO);
        // when
        TPromoCouponInnerCodeVO couponInnerCode = new TPromoCouponInnerCodeVO();
        couponInnerCode.setStatus(CouponStatusEnum.GRANTED.code());
        couponInnerCode.setCouponType(CouponTemplateEnum.COUPON_CODE.code());
        couponInnerCode.setFrozenStatus(CouponFrozenStatusEnum.UN_FROZEN.code());
        couponInnerCode.setCouponCode("666");
        ActivityModel couponActivity = new ActivityModel();
        couponActivity.setTotalQuantity(10);
        CouponReleaseDomain couponReleaseById = new CouponReleaseDomain();

        couponActivity.setUserLimitMax(0);
        TPromoCouponCodeUserVO userVO = new TPromoCouponCodeUserVO();
        userVO.setStatus(CouponStatusEnum.GRANTED.code());
        userVO.setUserCode("12112");
        Date date = DateUtils.addDays(new Date(), 1);
        String dateString = DateUtil.getDateString(date, DateUtil.DATETIMESTOREFORMAT);
        userVO.setValidStartTime("20210425000000");
        userVO.setValidEndTime(dateString);
        userVO.setUsedRefId("121312");
        userVO.setActivityCode("111");
        couponReleaseById.setValidDays(null);
        couponReleaseById.setReceiveStartTime("1");
        couponReleaseById.setValidStartTime(dateString);
        couponReleaseById.setValidEndTime(dateString);
//        Mockito.when(couponInnerCodeService.findCouponByCouponCode(Mockito.anyString(), Mockito.anyString())).thenReturn(couponInnerCode);
//        Mockito.when(couponActivityService.findCouponActivity(Mockito.anyString(), any())).thenReturn(couponActivity);
//        Mockito.when(redisService.getCouponReleaseLimit(Mockito.anyString(), Mockito.any(), Mockito.any())).thenReturn(0);
//        Mockito.when(promoCouponReleaseService.findCouponReleaseByReleaseCode(any(), any())).thenReturn(couponReleaseById);
//        Mockito.when(redisService.getCouponReleaseLimit(any(), any(), any())).thenReturn(2);
//        Mockito.when(promoCouponReleaseService.findCouponReleaseByReleaseCode(any(), any())).thenReturn(couponReleaseById);
        // then
        String canUse = couponCodeUserComponent.isCanUse(tenantCode, couponCode, userCode, activityCacheMap, false);
        Assert.assertNotNull("0",canUse);
    }

    @Test(expected = PromotionException.class)
    public void isCanUse_优惠码_领取时间未到(){
        // given
        String tenantCode = "1";
        String couponCode = "1";
        String userCode = "1";
        Map<String, ActivityCacheDTO> activityCacheMap = new HashMap<>();
        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();
        ActivityModel activityModel = new ActivityModel();
        activityModel.setTotalQuantity(10);
        activityModel.setActivityCode("111");
        activityCacheDTO.setActivityModel(activityModel);
        activityCacheMap.put("111",activityCacheDTO);
        // when
        TPromoCouponInnerCodeVO couponInnerCode = new TPromoCouponInnerCodeVO();
        couponInnerCode.setStatus(CouponStatusEnum.GRANTED.code());
        couponInnerCode.setCouponType(CouponTemplateEnum.COUPON_CODE.code());
        couponInnerCode.setFrozenStatus(CouponFrozenStatusEnum.UN_FROZEN.code());
        couponInnerCode.setCouponCode("666");
        ActivityModel couponActivity = new ActivityModel();
        couponActivity.setTotalQuantity(10);
        CouponReleaseDomain couponReleaseById = new CouponReleaseDomain();

        couponActivity.setUserLimitMax(0);
        TPromoCouponCodeUserVO userVO = new TPromoCouponCodeUserVO();
        userVO.setStatus(CouponStatusEnum.GRANTED.code());
        userVO.setUserCode("12112");
        Date date = DateUtils.addDays(new Date(), 1);
        String dateString = DateUtil.getDateString(date, DateUtil.DATETIMESTOREFORMAT);
        userVO.setValidStartTime("20210425000000");
        userVO.setValidEndTime(dateString);
        userVO.setUsedRefId("121312");
        userVO.setActivityCode("111");
        couponReleaseById.setValidDays(null);
        couponReleaseById.setReceiveStartTime("1");
        couponReleaseById.setValidStartTime("20210425000000");
        couponReleaseById.setValidEndTime(dateString);
        couponReleaseById.setReceiveStartTime(dateString);
//        Mockito.when(couponInnerCodeService.findCouponByCouponCode(Mockito.anyString(), Mockito.anyString())).thenReturn(couponInnerCode);
//        Mockito.when(couponActivityService.findCouponActivity(Mockito.anyString(), any())).thenReturn(couponActivity);
//        Mockito.when(promoCouponReleaseService.findCouponReleaseByReleaseCode(any(), any())).thenReturn(couponReleaseById);
//        Mockito.when(redisService.getCouponReleaseLimit(any(), any(), any())).thenReturn(2);
//        Mockito.when(promoCouponReleaseService.findCouponReleaseByReleaseCode(any(), any())).thenReturn(couponReleaseById);
        // then
        String canUse = couponCodeUserComponent.isCanUse(tenantCode, couponCode, userCode, activityCacheMap, false);
        Assert.assertNotNull("0",canUse);
    }

    @Test(expected = PromotionException.class)
    public void isCanUse_优惠码_超过用户限制校验(){
        // given
        String tenantCode = "1";
        String couponCode = "1";
        String userCode = "1";
        Map<String, ActivityCacheDTO> activityCacheMap = new HashMap<>();
        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();
        ActivityModel activityModel = new ActivityModel();
        activityModel.setTotalQuantity(10);
        activityModel.setActivityCode("111");
        activityCacheDTO.setActivityModel(activityModel);
        activityCacheMap.put("111",activityCacheDTO);
        // when
        TPromoCouponInnerCodeVO couponInnerCode = new TPromoCouponInnerCodeVO();
        couponInnerCode.setStatus(CouponStatusEnum.GRANTED.code());
        couponInnerCode.setCouponType(CouponTemplateEnum.COUPON_CODE.code());
        couponInnerCode.setFrozenStatus(CouponFrozenStatusEnum.UN_FROZEN.code());
        couponInnerCode.setCouponCode("666");
        ActivityModel couponActivity = new ActivityModel();
        couponActivity.setTotalQuantity(10);
        couponActivity.setUserLimitMax(2);
        CouponReleaseDomain couponReleaseById = new CouponReleaseDomain();
        TPromoCouponCodeUserVO userVO = new TPromoCouponCodeUserVO();
        userVO.setStatus(CouponStatusEnum.GRANTED.code());
        userVO.setUserCode("12112");
        Date date = DateUtils.addDays(new Date(), 1);
        String dateString = DateUtil.getDateString(date, DateUtil.DATETIMESTOREFORMAT);
        userVO.setValidStartTime("20210425000000");
        userVO.setValidEndTime(dateString);
        userVO.setUsedRefId("121312");
        userVO.setActivityCode("111");
        couponReleaseById.setValidDays(null);
        couponReleaseById.setReceiveStartTime("1");
        couponReleaseById.setValidStartTime("20210425000000");
        couponReleaseById.setValidEndTime(dateString);
//        Mockito.when(couponInnerCodeService.findCouponByCouponCode(Mockito.anyString(), Mockito.anyString())).thenReturn(couponInnerCode);
//        Mockito.when(couponActivityService.findCouponActivity(Mockito.anyString(), any())).thenReturn(couponActivity);
//        Mockito.when(promoCouponReleaseService.findCouponReleaseByReleaseCode(any(), any())).thenReturn(couponReleaseById);
//        Mockito.when(redisService.getCouponReleaseLimit(any(), any(), any())).thenReturn(2);
//        Mockito.when(promoCouponReleaseService.findCouponReleaseByReleaseCode(any(), any())).thenReturn(couponReleaseById);
//        Mockito.when(couponCodeUserService.getUserUsedCouponCountByActivityCode(any(), any(), any())).thenReturn(2);
        // then
        String canUse = couponCodeUserComponent.isCanUse(tenantCode, couponCode, userCode, activityCacheMap, false);
        Assert.assertNotNull("0",canUse);
    }

    @Test(expected = PromotionException.class)
    public void isCanUse_优惠码_用户限制校验未超过(){
        // given
        String tenantCode = "1";
        String couponCode = "1";
        String userCode = "1";
        Map<String, ActivityCacheDTO> activityCacheMap = new HashMap<>();
        // when
        TPromoCouponInnerCodeVO couponInnerCode = new TPromoCouponInnerCodeVO();
        couponInnerCode.setStatus(CouponStatusEnum.GRANTED.code());
        couponInnerCode.setCouponType(CouponTemplateEnum.COUPON_CODE.code());
        couponInnerCode.setFrozenStatus(CouponFrozenStatusEnum.UN_FROZEN.code());
        couponInnerCode.setCouponCode("666");
        couponInnerCode.setActivityCode("2222");
        ActivityModel couponActivity = new ActivityModel();
        couponActivity.setTotalQuantity(10);
        couponActivity.setUserLimitMax(0);
        couponActivity.setActivityCode("2222");
        CouponReleaseDomain couponReleaseById = new CouponReleaseDomain();
        TPromoCouponCodeUserVO userVO = new TPromoCouponCodeUserVO();
        userVO.setStatus(CouponStatusEnum.GRANTED.code());
        userVO.setUserCode("12112");
        Date date = DateUtils.addDays(new Date(), 1);
        String dateString = DateUtil.getDateString(date, DateUtil.DATETIMESTOREFORMAT);
        userVO.setValidStartTime("20210425000000");
        userVO.setValidEndTime(dateString);
        userVO.setUsedRefId("121312");
        userVO.setActivityCode("111");
        couponReleaseById.setValidDays(null);
        couponReleaseById.setReceiveStartTime("1");
        couponReleaseById.setValidStartTime("20210425000000");
        couponReleaseById.setValidEndTime(dateString);
        Mockito.when(couponInnerCodeService.findCouponByCouponCodeOrPassword(Mockito.anyString(), Mockito.anyString())).thenReturn(couponInnerCode);
//        Mockito.when(couponActivityService.findCouponActivity(Mockito.anyString(), any())).thenReturn(couponActivity);
//        Mockito.when(redisService.getCouponReleaseLimit(any(), any(), any())).thenReturn(2);
//        Mockito.when(promoCouponReleaseService.findCouponReleaseByReleaseCode(any(), any())).thenReturn(couponReleaseById);
        // then
        String canUse = couponCodeUserComponent.isCanUse(tenantCode, couponCode, userCode, activityCacheMap, false);
        Assert.assertNotNull("2222",canUse);
    }

    @Test
    public void isCanUse_优惠码_用户限制校验未超过1(){
        // given
        String tenantCode = "1";
        String couponCode = "1";
        String userCode = "1";
        Map<String, ActivityCacheDTO> activityCacheMap = new HashMap<>();
        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();
        ActivityModel activityModel = new ActivityModel();
        activityModel.setTotalQuantity(10);
        activityModel.setActivityCode("111");
        activityCacheDTO.setActivityModel(activityModel);
        activityCacheMap.put("111",activityCacheDTO);

        // when
        TPromoCouponInnerCodeVO couponInnerCode = new TPromoCouponInnerCodeVO();
        couponInnerCode.setStatus(CouponStatusEnum.GRANTED.code());
        couponInnerCode.setCouponType(CouponTemplateEnum.COUPON_CODE.code());
        couponInnerCode.setFrozenStatus(CouponFrozenStatusEnum.UN_FROZEN.code());
        couponInnerCode.setCouponCode("666");
        couponInnerCode.setActivityCode("111");
        ActivityModel couponActivity = new ActivityModel();
        couponActivity.setTotalQuantity(10);
        couponActivity.setUserLimitMax(0);
        couponActivity.setActivityCode("2222");
        CouponReleaseDomain couponReleaseById = new CouponReleaseDomain();
        TPromoCouponCodeUserVO userVO = new TPromoCouponCodeUserVO();
        userVO.setStatus(CouponStatusEnum.GRANTED.code());
        userVO.setUserCode("12112");
        Date date = DateUtils.addDays(new Date(), 1);
        String dateString = DateUtil.getDateString(date, DateUtil.DATETIMESTOREFORMAT);
        userVO.setValidStartTime("20210425000000");
        userVO.setValidEndTime(dateString);
        userVO.setUsedRefId("121312");
        userVO.setActivityCode("111");
        couponReleaseById.setValidDays(null);
        couponReleaseById.setReceiveStartTime("1");
        couponReleaseById.setValidStartTime("20210425000000");
        couponReleaseById.setValidEndTime(dateString);
        Mockito.when(couponInnerCodeService.findCouponByCouponCodeOrPassword(Mockito.anyString(), Mockito.anyString())).thenReturn(couponInnerCode);
        //Mockito.when(couponActivityService.findCouponActivity(Mockito.anyString(), any())).thenReturn(couponActivity);
        Mockito.when(redisService.getCouponReleaseLimit(any(), any(), any())).thenReturn(2);
        Mockito.when(promoCouponReleaseService.findCouponReleaseByReleaseCode(any(), any())).thenReturn(couponReleaseById);
        // then
        String canUse = couponCodeUserComponent.isCanUse(tenantCode, couponCode, userCode, activityCacheMap, false);
        Assert.assertNotNull("2222",canUse);
    }

    @Test
    public void isCanUse_优惠码_状态检验不通过(){
        // given
        String tenantCode = "1";
        String couponCode = "1";
        String userCode = "1";
        Map<String, ActivityCacheDTO> activityCacheMap = new HashMap<>();
        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();
        ActivityModel activityModel = new ActivityModel();
        activityModel.setTotalQuantity(10);
        activityModel.setActivityCode("111");
        activityCacheDTO.setActivityModel(activityModel);
        activityCacheMap.put("111",activityCacheDTO);
        // when
        TPromoCouponInnerCodeVO couponInnerCode = new TPromoCouponInnerCodeVO();
        couponInnerCode.setStatus(CouponStatusEnum.GRANTED.code());
        couponInnerCode.setCouponType(CouponTemplateEnum.COUPON_CODE.code());
        couponInnerCode.setFrozenStatus(CouponFrozenStatusEnum.FROZENED.code());
        couponInnerCode.setCouponCode("666");
        couponInnerCode.setActivityCode("111");
        ActivityModel couponActivity = new ActivityModel();
        couponActivity.setTotalQuantity(10);
        couponActivity.setUserLimitMax(0);
        couponActivity.setActivityCode("2222");
        CouponReleaseDomain couponReleaseById = new CouponReleaseDomain();
        TPromoCouponCodeUserVO userVO = new TPromoCouponCodeUserVO();
        userVO.setStatus(CouponStatusEnum.GRANTED.code());
        userVO.setUserCode("12112");
        Date date = DateUtils.addDays(new Date(), 1);
        String dateString = DateUtil.getDateString(date, DateUtil.DATETIMESTOREFORMAT);
        userVO.setValidStartTime("20210425000000");
        userVO.setValidEndTime(dateString);
        userVO.setUsedRefId("121312");
        userVO.setActivityCode("111");
        couponReleaseById.setValidDays(null);
        couponReleaseById.setReceiveStartTime("1");
        couponReleaseById.setValidStartTime("20210425000000");
        couponReleaseById.setValidEndTime(dateString);
//        Mockito.when(couponInnerCodeService.findCouponByCouponCode(Mockito.anyString(), Mockito.anyString())).thenReturn(couponInnerCode);
//        Mockito.when(couponActivityService.findCouponActivity(Mockito.anyString(), any())).thenReturn(couponActivity);
//        Mockito.when(redisService.getCouponReleaseLimit(any(), any(), any())).thenReturn(2);
//        Mockito.when(promoCouponReleaseService.findCouponReleaseByReleaseCode(any(), any())).thenReturn(couponReleaseById);
        // then
        try {
            String canUse = couponCodeUserComponent.isCanUse(tenantCode, couponCode, userCode, activityCacheMap, false);
            Assert.assertNotNull("2222", canUse);
        }catch (Exception e){

        }

        try {
            couponCodeUserComponent.isCanUse(tenantCode, couponCode, null, activityCacheMap, false);
        }catch (Exception e){

        }

        couponInnerCode.setFrozenStatus(CouponFrozenStatusEnum.UN_FROZEN.code());
        couponInnerCode.setCouponType(CouponTemplateEnum.ANON_COUPON.code());
        couponReleaseById.setValidDays(1);
        try {
           // Mockito.when(couponInnerCodeService.findCouponByCouponCode(Mockito.anyString(), Mockito.anyString())).thenReturn(couponInnerCode);
//            Mockito.when(promoCouponReleaseService.findCouponReleaseByReleaseCode(any(), any())).thenReturn(couponReleaseById);
            couponCodeUserComponent.isCanUse(tenantCode, couponCode, userCode, activityCacheMap, false);
        }catch (Exception e){

        }


        couponReleaseById.setValidDays(null);
        //yyyyMMddhhmmss
        couponReleaseById.setReceiveStartTime("1");
        try {
//            Mockito.when(promoCouponReleaseService.findCouponReleaseByReleaseCode(any(), any())).thenReturn(couponReleaseById);
            couponCodeUserComponent.isCanUse(tenantCode, couponCode, userCode, activityCacheMap, false);
        }catch (Exception e){

        }

        try {
            userVO.setValidStartTime("20210425000000");
            couponActivity.setUserLimitMax(99999999);
//            Mockito.when(promoCouponReleaseService.findCouponReleaseByReleaseCode(any(), any())).thenReturn(couponReleaseById);
//            Mockito.when(couponActivityService.findCouponActivity(Mockito.anyString(), any())).thenReturn(couponActivity);
            couponCodeUserComponent.isCanUse(tenantCode, couponCode, userCode, activityCacheMap, false);
        }catch (Exception e){

        }
    }


    @Test
    public void queryUsedCouponList_empty(){
        QueryCouponUsedInDTO dto = new QueryCouponUsedInDTO();
        dto.setMobile("111");
        dto.setUserCode("111");
        Mockito.when(memberFeignClient.queryMemberListByMemberCode(any())).thenReturn(null);
        PageData<QueryCouponUsedListResult> data = couponCodeUserComponent.queryUsedCouponList(dto);
        Assert.assertEquals(0L,data.getList().size());
    }
    @Test
    public void queryUsedCouponList_not_empty_is_member(){
        QueryCouponUsedInDTO dto = new QueryCouponUsedInDTO();
        dto.setEmail("111");
        dto.setUserCode("111");
        Result<List<QueryMemberListByConditionResult>> resultPageData =new Result<>();
        List<QueryMemberListByConditionResult> results= new ArrayList<>();
        QueryMemberListByConditionResult result = new QueryMemberListByConditionResult();
        result.setMemberCode("111");
        results.add(result);
        resultPageData.setData(results);
        PageData<TPromoCouponCodeUserVO> userVOPageData = new PageData<>();
        List<TPromoCouponCodeUserVO> userVOS = new ArrayList<>();
        TPromoCouponCodeUserVO userVO = new TPromoCouponCodeUserVO();
        userVO.setActivityCode("1");
        userVO.setUserCode("1");
        userVOS.add(userVO);
        userVOPageData.setList(userVOS);
        List<ActivityModel> activityModels = new ArrayList<>();
        ActivityModel activityModel = new ActivityModel();
        activityModel.setActivityCode("1");
        activityModels.add(activityModel);
        Mockito.when(memberFeignClient.queryMemberListByMemberCode(any())).thenReturn(resultPageData);
        Mockito.when(couponCodeUserService.queryUsedCouponListService(any())).thenReturn(userVOPageData);
        Mockito.when(activityService.queryActivityByActivityCodes(any(), any())).thenReturn(activityModels);

        PageData<QueryCouponUsedListResult> data = couponCodeUserComponent.queryUsedCouponList(dto);
        Assert.assertEquals(1L,data.getList().size());
    }

    @Test
    public void queryUsedCouponList_not_empty_not_is_member(){
        QueryCouponUsedInDTO dto = new QueryCouponUsedInDTO();
        dto.setUserCode("111");
        Result<List<QueryMemberListByConditionResult>> resultPageData =new Result<>();
        List<QueryMemberListByConditionResult> results= new ArrayList<>();
        QueryMemberListByConditionResult result = new QueryMemberListByConditionResult();
        result.setMemberCode("111");
        results.add(result);
        resultPageData.setData(results);
        PageData<TPromoCouponCodeUserVO> userVOPageData = new PageData<>();
        List<TPromoCouponCodeUserVO> userVOS = new ArrayList<>();
        TPromoCouponCodeUserVO userVO = new TPromoCouponCodeUserVO();
        userVO.setActivityCode("1");
        userVOS.add(userVO);
        userVOPageData.setList(userVOS);
        List<ActivityModel> activityModels = new ArrayList<>();
        ActivityModel activityModel = new ActivityModel();
        activityModel.setActivityCode("1");
        activityModels.add(activityModel);
        Mockito.when(couponCodeUserService.queryUsedCouponListService(any())).thenReturn(userVOPageData);
        Mockito.when(activityService.queryActivityByActivityCodes(any(), any())).thenReturn(activityModels);
        Mockito.when(memberFeignClient.queryMemberListByMemberCode(any())).thenReturn(resultPageData);

        PageData<QueryCouponUsedListResult> data = couponCodeUserComponent.queryUsedCouponList(dto);
        Assert.assertEquals(1L,data.getList().size());
    }

    @Test
    public void queryUsedCouponList_not_is_member(){
        QueryCouponUsedInDTO dto = new QueryCouponUsedInDTO();
        Result<List<QueryMemberListByConditionResult>> resultPageData =new Result<>();
        List<QueryMemberListByConditionResult> results= new ArrayList<>();
        QueryMemberListByConditionResult result = new QueryMemberListByConditionResult();
        result.setMemberCode("111");
        results.add(result);
        resultPageData.setData(results);
        PageData<TPromoCouponCodeUserVO> userVOPageData = new PageData<>();
        List<TPromoCouponCodeUserVO> userVOS = new ArrayList<>();
        TPromoCouponCodeUserVO userVO = new TPromoCouponCodeUserVO();
        userVO.setActivityCode("1");
        userVO.setUserCode("1");
        userVOS.add(userVO);
        userVOPageData.setList(userVOS);
        List<ActivityModel> activityModels = new ArrayList<>();
        ActivityModel activityModel = new ActivityModel();
        activityModel.setActivityCode("1");
        activityModels.add(activityModel);
        Mockito.when(couponCodeUserService.queryUsedCouponListService(any())).thenReturn(userVOPageData);
        Mockito.when(activityService.queryActivityByActivityCodes(any(), any())).thenReturn(activityModels);
        Mockito.when(memberFeignClient.queryMemberListByMemberCode(any())).thenReturn(resultPageData);

        PageData<QueryCouponUsedListResult> data = couponCodeUserComponent.queryUsedCouponList(dto);
        Assert.assertEquals(1L,data.getList().size());
    }

    @Test
    public void exportCouponDetail(){
        ExportCouponUserDto dto = new ExportCouponUserDto ();
        List<ExportCouponUserResult> resultPageData = new ArrayList<>();
        ExportCouponUserResult r1 = new ExportCouponUserResult();
        r1.setUserCode("test");
        resultPageData.add(r1);
        Result<GetMemberProfileResult> memberProfile = new Result<>();
        Result<GetMemberProfileResult> memberProfile1 = new Result<>();
        memberProfile.setData(new GetMemberProfileResult());
        Mockito.when(couponCodeUserService.exportCouponUserCode(any())).thenReturn(resultPageData);
        Mockito.when(memberFeignClient.getMemberProfile(any())).thenReturn(memberProfile).thenReturn(memberProfile1);

        Result<List<QueryUserResult>> queryUserResult = new Result<>();
        queryUserResult.setCode("0");
        List<QueryUserResult> queryUserResultList = new ArrayList<>();
        QueryUserResult queryUser = new QueryUserResult();
        queryUser.setUserCode("test");
        queryUser.setAccount("account");
        queryUserResultList.add(queryUser);
        queryUserResult.setData(queryUserResultList);
        Mockito.when(idmFeignClient.queryUserList(any())).thenReturn(queryUserResult);

        List<ExportCouponUserResult> exportCouponUserList = new ArrayList<>();
        ExportCouponUserResult exportCouponUserResult = new ExportCouponUserResult();
        exportCouponUserResult.setUserCode("test");
        exportCouponUserList.add(exportCouponUserResult);
        Mockito.when(couponCodeUserService.exportCouponUserCode(any())).thenReturn(exportCouponUserList);


        List<ExportCouponUserResult> exportCouponUserResults = couponCodeUserComponent.exportCouponDetail(dto);
    }

    @Test
    public void exportCouponOrderDetail(){
        ExportCouponUserDto dto = new ExportCouponUserDto();
        List<ExportCouponUserDetailResult> resultPageData = new ArrayList<>();
        resultPageData.add(new ExportCouponUserDetailResult());
        Mockito.when(couponCodeUserService.exportCouponOrderCode(any())).thenReturn(resultPageData);
        List<ExportCouponUserDetailResult> exportCouponUserDetailResults = couponCodeUserComponent.exportCouponOrderDetail(dto);
        Assert.assertEquals(1L,exportCouponUserDetailResults.size());
    }

    @Test
    public void queryCheckAndUseCouponCode_匿名券优惠码(){
        TCouponCheckAndUseInDTO dto = new TCouponCheckAndUseInDTO();
        dto.setCouponCodes("1,");
        TPromoCouponInnerCodeVO innerCodeVO = new TPromoCouponInnerCodeVO();
        innerCodeVO.setStatus(CouponStatusEnum.UN_GRANT.code());
        ActivityModel activityMode = new ActivityModel();
        CouponReleaseModel releaseModel = new CouponReleaseModel();
        releaseModel.setValidDays(1);
        Mockito.when(couponInnerCodeService.findCouponByCouponCode(any(), any())).thenReturn(innerCodeVO);
        Mockito.when(activityService.findActivity(any(), any(), any())).thenReturn(activityMode);
        Mockito.when(promoCouponReleaseService.findCanReceiveRelease(any(),  any())).thenReturn(releaseModel);
        int i = couponCodeUserComponent.queryCheckAndUseCouponCode(dto);
        Assert.assertEquals(1, i);
    }

    @Test
    public void queryCheckAndUseCouponCode_优惠券(){
        TCouponCheckAndUseInDTO dto = new TCouponCheckAndUseInDTO();
        dto.setCouponCodes("1,");
        TPromoCouponInnerCodeVO innerCodeVO = new TPromoCouponInnerCodeVO();
        innerCodeVO.setCouponType(CouponTypeEnum.PROMOTION_COUPON.code());
        innerCodeVO.setStatus(CouponStatusEnum.GRANTED.code());
        CouponReleaseModel releaseModel = new CouponReleaseModel();
        Date dateEnd = DateUtils.addDays(new Date(), 1);
        String dateEndString = DateUtil.getDateString(dateEnd, DateUtil.DATETIMESTOREFORMAT);
        Date dateBegin = DateUtils.addDays(new Date(), -1);
        String dateBeginString = DateUtil.getDateString(dateBegin, DateUtil.DATETIMESTOREFORMAT);
        releaseModel.setValidEndTime(dateEndString);
        releaseModel.setValidStartTime(dateBeginString);
        Mockito.when(couponInnerCodeService.findCouponByCouponCode(any(), any())).thenReturn(innerCodeVO);
        Mockito.when(promoCouponReleaseService.findCanUseRelease(any(), any())).thenReturn(releaseModel);
        Mockito.when(couponCodeUserService.findUserByCouponCode(any())).thenReturn(new TPromoCouponCodeUserVO());
        int i = couponCodeUserComponent.queryCheckAndUseCouponCode(dto);
        Assert.assertEquals(1, i);
    }

    @Test
    public void getActivityCodeByUserCodeTest(){
        try {
            couponCodeUserComponent.getActivityCodeByUserCode(null,1L,null,null);
        }catch (Exception e){

        }

    }

    @Test
    public void getActivityCodeTest(){
        TPromoCouponCodeUserVO vo = new TPromoCouponCodeUserVO();
        vo.setUsedRefId("test");
        Mockito.when(couponCodeUserService.getUserCouponInfo(any(), any(), any())).thenReturn(vo);
        try {
            couponCodeUserComponent.getActivityCode(null, null, "cc", null, 1L);
        }catch (Exception e){

        }
        try {
            couponCodeUserComponent.getActivityCode(null, null, null, null, 1L);
        }catch (Exception e){

        }
    }

    @Test
    public void foreachQueryCouponUsedResultTest(){
        List<ActivityModel> activityModels = new ArrayList<>();
        Result<List<QueryMemberListByConditionResult>> listResult = new Result<>();
        List<QueryMemberListByConditionResult> queryMemberListByConditionResults = new ArrayList<>();
        QueryMemberListByConditionResult queryMemberListByConditionResult = new QueryMemberListByConditionResult();
        listResult.setData(queryMemberListByConditionResults);
        queryMemberListByConditionResults.add(queryMemberListByConditionResult);
        List<QueryMemberListByConditionResult> members = new ArrayList<>();
        QueryMemberListByConditionResult member = new QueryMemberListByConditionResult();
        member.setMemberCode("test");
        member.setLastName("d");
        member.setFirstName("c");
        members.add(member);
        List<QueryCouponUsedListResult> results = new ArrayList<>();
        QueryCouponUsedListResult queryCouponUsedListResult = new QueryCouponUsedListResult();
        queryCouponUsedListResult.setUserCode("test");
        results.add(queryCouponUsedListResult);
        couponCodeUserComponent.foreachQueryCouponUsedResult(activityModels,listResult,members,results);
    }

    @Test
    public void getActivityNameTest(){
        List<ActivityModel> activityModels = new ArrayList<>();
        ActivityModel activityModel = new ActivityModel();
        activityModel.setActivityCode("test");
        activityModels.add(activityModel);
        QueryCouponUsedListResult result = new QueryCouponUsedListResult();
        result.setActivityCode("test");
        couponCodeUserComponent.getActivityName(activityModels,result);
    }

    @Test
    public void getResultTest(){
        Result<List<QueryMemberListByConditionResult>> listResult = new Result<>();
        List<ActivityModel> activityModels = new ArrayList<>();
        List<QueryCouponUsedListResult> results = new ArrayList<>();
        couponCodeUserComponent.getResult(listResult,activityModels,results);
    }


    @Test
    public void checkUserLimitByCouponSendDetailModelList(){
        couponCodeUserComponent.checkUserLimitByCouponSendDetailModelList(null);
        List<PromoCouponSendDetailModel> couponSendDetailModelList = new ArrayList<>();
        PromoCouponSendDetailModel promoCouponSendDetailModel = new PromoCouponSendDetailModel();
        couponSendDetailModelList.add(promoCouponSendDetailModel);
        couponCodeUserComponent.checkUserLimitByCouponSendDetailModelList(couponSendDetailModelList);

        try {
            promoCouponSendDetailModel.setFailReason("123");
            couponCodeUserComponent.checkUserLimitByCouponSendDetailModelList(couponSendDetailModelList);
        }catch (Exception e){}

        try {
            promoCouponSendDetailModel.setFailReason("day");
            couponCodeUserComponent.checkUserLimitByCouponSendDetailModelList(couponSendDetailModelList);
        }catch (Exception e){}

    }



    @Test
    public void sendCouponToUser() {
        SendCouponToUserParam param = new SendCouponToUserParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setUserCode("1");
        param.setActivityCode("1");
        param.setTakeLabel("01");
        param.setFrozenStatus(1);
        param.setReceiveCount(1);
        ActivityModel activityModel = new ActivityModel();
        activityModel.setCouponType("01");
        List<CouponReleaseDomain> couponRelease = new ArrayList<>();
        CouponReleaseDomain domain = new CouponReleaseDomain();
        domain.setActivityCode("1");
        couponRelease.add(domain);
        Map<String, String> userCouponMap = new HashMap<>();
        userCouponMap.put("1", "1");
        Mockito.when(couponActivityDomain.findSendCouponValidActivity(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(activityModel);
        Mockito.when(qualificationDomain.checkQualificationForSendCoupon(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(true);
        Mockito.when(couponReleaseService.queryCanReceiveReleases(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(couponRelease);
        Mockito.when(couponInnerCodeDomain.allocateCoupon(Mockito.anyList(), Mockito.anyInt(), Mockito.anyString(), Mockito.any(), Mockito.anyList(),
                Mockito.anyInt(), Mockito.any())).thenReturn(userCouponMap);
        couponCodeUserComponent.sendCouponToUserByUserCode(param);

    }



    @Test(expected = Exception.class)
    public void sendCouponToUser_empty_1() {
        SendCouponToUserParam param = new SendCouponToUserParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setUserCode("1");
        param.setActivityCode("1");
        param.setTakeLabel("01");
        param.setFrozenStatus(1);
        param.setReceiveCount(1);
        ActivityModel activityModel = new ActivityModel();
        activityModel.setCouponType("01");
        List<CouponReleaseDomain> couponRelease = new ArrayList<>();
        CouponReleaseDomain domain = new CouponReleaseDomain();
        domain.setActivityCode("1");
        couponRelease.add(domain);
        //Map<String, String> userCouponMap = new HashMap<>();
        //userCouponMap.put("1","1");

//        Mockito.when(couponActivityDomain.findValidActivity(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(activityModel);
//        Mockito.when(qualificationDomain.checkQualificationForSendCoupon(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(true);
//        Mockito.when(couponReleaseService.queryCanReceiveReleases(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(couponRelease);
        couponCodeUserComponent.sendCouponToUserByUserCode(param);

    }
    @Test(expected = Exception.class)
    public void sendCouponToUser_empty() {
        SendCouponToUserParam param = new SendCouponToUserParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setUserCode("1");
        param.setActivityCode("1");
        param.setTakeLabel("01");
        param.setFrozenStatus(1);
        param.setReceiveCount(1);
        ActivityModel activityModel = new ActivityModel();
        activityModel.setCouponType("01");
//        Mockito.when(couponActivityDomain.findValidActivity(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(activityModel);
//        Mockito.when(qualificationDomain.checkQualificationForSendCoupon(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(true);
//        Mockito.when(couponReleaseService.queryCanReceiveReleases(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(new ArrayList<>());
        couponCodeUserComponent.sendCouponToUserByUserCode(param);

    }


}
