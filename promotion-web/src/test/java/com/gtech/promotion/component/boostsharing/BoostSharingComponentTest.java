package com.gtech.promotion.component.boostsharing;

import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.gtech.basic.masterdata.response.JsonResult;
import com.gtech.commons.page.PageData;
import com.gtech.commons.redis.GTechCodeGenerator;
import com.gtech.commons.redis.GTechRedisTemplate;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.member.web.vo.bean.MemberTagBean;
import com.gtech.member.web.vo.result.GetMemberProfileResult;
import com.gtech.promotion.code.activity.ActivityStatusEnum;
import com.gtech.promotion.code.coupon.CouponReleaseSourceEnum;
import com.gtech.promotion.code.coupon.ReleaseTimeSameActivityEnum;
import com.gtech.promotion.code.coupon.ReleaseTypeEnum;
import com.gtech.promotion.code.marketing.*;
import com.gtech.promotion.component.coupon.CouponCodeUserComponent;
import com.gtech.promotion.component.coupon.CouponReleaseComponent;
import com.gtech.promotion.component.flashsale.FlashSaleComponent;
import com.gtech.promotion.component.marketing.MarketingCacheComponent;
import com.gtech.promotion.component.marketing.TicketComponent;
import com.gtech.promotion.dao.entity.marketing.HelpRecordEntity;
import com.gtech.promotion.dao.entity.marketing.RightOfFirstRefusalEntity;
import com.gtech.promotion.dao.entity.marketing.SharingRecordEntity;
import com.gtech.promotion.dao.model.activity.ActivityModel;
import com.gtech.promotion.dao.model.activity.QualificationModel;
import com.gtech.promotion.dao.model.marketing.BoostSharingModel;
import com.gtech.promotion.dao.model.marketing.MarketingModel;
import com.gtech.promotion.dao.model.marketing.SharingRecordModel;
import com.gtech.promotion.dao.model.marketing.flashsale.CacheFlashSaleModel;
import com.gtech.promotion.domain.coupon.CouponReleaseDomain;
import com.gtech.promotion.domain.marketing.TicketReleaseDomain;
import com.gtech.promotion.domain.marketing.TicketSendDomain;
import com.gtech.promotion.dto.boostsharing.ExportBoostSharingDetailDto;
import com.gtech.promotion.dto.boostsharing.ExportBoostSharingTotalDto;
import com.gtech.promotion.dto.boostsharing.FilterNoRightOfFirstRefusalDto;
import com.gtech.promotion.dto.in.activity.ShoppingCartItem;
import com.gtech.promotion.dto.in.coupon.ReleaseCouponInDTO;
import com.gtech.promotion.dto.in.flashsale.HelpRecordDto;
import com.gtech.promotion.dto.in.flashsale.QuerySharingInformationDto;
import com.gtech.promotion.dto.in.flashsale.SharingRecordDto;
import com.gtech.promotion.dto.in.flashsale.WriteOffOfPreEmptiveRightsDto;
import com.gtech.promotion.feign.MasterDataFeignClient;
import com.gtech.promotion.feign.MemberFeignClient;
import com.gtech.promotion.helper.RedisLock;
import com.gtech.promotion.page.RequestPage;
import com.gtech.promotion.service.activity.ActivityService;
import com.gtech.promotion.service.flashsale.FlashSaleQualificationService;
import com.gtech.promotion.service.flashsale.FlashSaleStoreService;
import com.gtech.promotion.service.marketing.*;
import com.gtech.promotion.vo.param.coupon.SendCouponToUserParam;
import com.gtech.promotion.vo.param.marketing.TicketReleaseQueryParam;
import com.gtech.promotion.vo.result.flashsale.BoostSharingRewardsResult;
import com.gtech.promotion.vo.result.flashsale.CreateHelpRecordResult;
import com.gtech.promotion.vo.result.flashsale.QuerySharingInformationResult;
import com.gtech.promotion.vo.result.marketing.TicketReleaseQueryResult;
import com.gtech.promotion.vo.result.marketing.TicketSendResult;
import org.junit.Assert;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class BoostSharingComponentTest {

    @InjectMocks
    private BoostSharingComponent boostSharingComponent;

    @Mock
    private SharingRecordService sharingRecordService;
    @Mock
    private HelpRecordService helpRecordService;
    @Mock
    private BoostSharingService boostSharingService;
    @Mock
    private GTechRedisTemplate gTechRedisTemplate;
    @Mock
    private RedisLock redisLock;
    @Mock
    private GTechCodeGenerator codeGenerator;

    @Mock
    private CouponCodeUserComponent couponCodeUserComponent;

    @Mock
    private FlashSaleQualificationService flashSaleQualificationService;

    @Mock
    private FlashSaleStoreService flashSaleStoreService;
    @Mock
    private TicketComponent ticketComponent;

    @Mock
    private RightOfFirstRefusalService rightOfFirstRefusalService;

    @Mock
    private MarketingService marketingService;

    @Mock
    private MemberFeignClient memberFeignClient;

    @Mock
    private MarketingCacheComponent marketingCacheComponent;
    @Mock
    private FlashSaleComponent flashSaleComponent;

    @Mock
    private CouponReleaseComponent couponReleaseComponent;
    @Mock
    private ActivityService activityService;
    @Mock
    private MasterDataFeignClient masterDataFeignClient;





    @Test
    public void testCreateBoostSharing() {

        SharingRecordDto sharingRecordDto = new SharingRecordDto();
        sharingRecordDto.setActivityCode("activityCode");
        sharingRecordDto.setDomainCode("domainCode");
        sharingRecordDto.setTenantCode("tenantCode");
        sharingRecordDto.setOrgCode("orgCode");

        BoostSharingModel boostSharingModel = new BoostSharingModel();
        boostSharingModel.setNumberOfBoostSharing("numberOfBoostSharing");

        MarketingModel marketingModel = new MarketingModel();
        marketingModel.setActivityCode("activityCode");
        marketingModel.setDomainCode("domainCode");
        marketingModel.setTenantCode("tenantCode");
        marketingModel.setOrgCode("orgCode");
        marketingModel.setActivityBegin("20000101010101");
        marketingModel.setActivityEnd("30000101010101");
        marketingModel.setActivityStatus(ActivityStatusEnum.EFFECTIVE.code());
        Result<GetMemberProfileResult> memberProfileResultResult = new Result<>();

        GetMemberProfileResult getMemberProfileResult = new GetMemberProfileResult();
        MemberTagBean memberTagBean = new MemberTagBean();
        memberTagBean.setTagCode("tagCode");
        memberTagBean.setTagName("tagCode");
        getMemberProfileResult.setTags(Lists.newArrayList(memberTagBean));

        memberProfileResultResult.setData(getMemberProfileResult);
        Mockito.when(memberFeignClient.getMemberProfile(Mockito.any())).thenReturn(memberProfileResultResult);


        Mockito.when(marketingService.findByActivityCode(Mockito.any())).thenReturn(marketingModel);
        Mockito.when(boostSharingService.findBoostShardingInfo(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(boostSharingModel);


        Mockito.when(sharingRecordService.insert(any())).thenReturn(0);
//        Mockito.when(sharingRecordService.findByActivityCode(anyString())).thenReturn(new SharingRecordModel());
//        Mockito.when(helpRecordService.insert(any())).thenReturn(0);
//        Mockito.when(helpRecordService.findByActivityCode(anyString())).thenReturn(new HelpRecordModel());
//        Mockito.when(boostSharingService.insert(any())).thenReturn(0);

        Mockito.when(codeGenerator.generateCode(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn("replaceMeWithExpectedResult");


        String result = boostSharingComponent.createBoostSharing(sharingRecordDto);
        Assert.assertEquals("replaceMeWithExpectedResult", result);
    }

    @Test
    public void testCreateHelpRecord() {

        HelpRecordDto helpRecordDto = new HelpRecordDto();
        helpRecordDto.setActivityCode("activityCode");
        helpRecordDto.setDomainCode("domainCode");
        helpRecordDto.setTenantCode("tenantCode");
        helpRecordDto.setOrgCode("orgCode");
        helpRecordDto.setHelpMemberCode("1234");
        helpRecordDto.setSharingMemberCode("123");

        BoostSharingModel boostSharingModel = new BoostSharingModel();
        boostSharingModel.setNumberOfBoostSharing("2");

        MarketingModel marketingModel = new MarketingModel();
        marketingModel.setActivityCode("activityCode");
        marketingModel.setDomainCode("domainCode");
        marketingModel.setTenantCode("tenantCode");
        marketingModel.setOrgCode("orgCode");
        marketingModel.setActivityBegin("20000101010101");
        marketingModel.setActivityEnd("30000101010101");
        marketingModel.setActivityStatus(ActivityStatusEnum.EFFECTIVE.code());


        Result<GetMemberProfileResult> memberProfileResultResult = new Result<>();

        GetMemberProfileResult getMemberProfileResult = new GetMemberProfileResult();
        MemberTagBean memberTagBean = new MemberTagBean();
        memberTagBean.setTagCode("tagCode");
        memberTagBean.setTagName("tagCode");
        getMemberProfileResult.setTags(Lists.newArrayList(memberTagBean));

        memberProfileResultResult.setData(getMemberProfileResult);
        Mockito.when(memberFeignClient.getMemberProfile(Mockito.any())).thenReturn(memberProfileResultResult);


        Mockito.when(marketingService.findByActivityCode(Mockito.any())).thenReturn(marketingModel);
        Mockito.when(boostSharingService.findBoostShardingInfo(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(boostSharingModel);

        boostSharingModel.setBoostSharingType(BoostSharingTypeEnum.COUPON.code());
        SharingRecordModel sharingRecordModel = new SharingRecordModel();
        sharingRecordModel.setNumberOfPeopleWhoHaveHelped("2");
        sharingRecordModel.setActivityCode("activityCode");
        sharingRecordModel.setDomainCode("domainCode");
        sharingRecordModel.setTenantCode("tenantCode");
        sharingRecordModel.setOrgCode("orgCode");
        sharingRecordModel.setNumberOfPeopleWhoHaveHelped("2");
        sharingRecordModel.setNumberOfBoostSharing("2");
        sharingRecordModel.setActivityStatus("01");
        //Mockito.when(codeGenerator.generateCode(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn("replaceMeWithExpectedResult");
        Mockito.when(sharingRecordService.findBysharingRecordCode(Mockito.any(), Mockito.any())).thenReturn(sharingRecordModel);

//        Mockito.when(sharingRecordService.findByActivityCode(anyString())).thenReturn(sharingRecordModel);
        //Mockito.when(helpRecordService.checkMemberHelpRecord(anyString(), anyString())).thenReturn(new HelpRecordEntity("domainCode", "tenantCode", "orgCode", "activityCode", "sharingRecordCode", "helpRecordCode", "sharingMemberCode", "helpMemberCode", "helpRecordStatus"));
//        Mockito.when(helpRecordService.insert(any())).thenReturn(0);
//        Mockito.when(helpRecordService.findByActivityCode(anyString())).thenReturn(new HelpRecordModel());
//        Mockito.when(boostSharingService.insert(any())).thenReturn(0);
//        Mockito.when(boostSharingService.findByActivityCode(anyString())).thenReturn(new BoostSharingModel());
//        Mockito.when(redisLock.tryLockAndRetry(anyString(), anyLong(), anyInt())).thenReturn("tryLockAndRetryResponse");
//
        CreateHelpRecordResult result = boostSharingComponent.createHelpRecord(helpRecordDto);
        Assert.assertNotEquals(new CreateHelpRecordResult(), result);
    }

    @Test
    public void testAssembleHelpRecordReturnValue() {
        BoostSharingRewardsResult boostSharingRewardsResult = new BoostSharingRewardsResult();
        CreateHelpRecordResult result = boostSharingComponent.assembleHelpRecordReturnValue(new HelpRecordDto(), "helpRecordCode", BoostSharingEnum.PROCESSING, BoostSharingHelpStatusEnum.SUCCESS, boostSharingRewardsResult);
        CreateHelpRecordResult result1 = new CreateHelpRecordResult();
        result1.setHelpRecordCode("helpRecordCode");
        result1.setHelpRecordStatus("01");
        result1.setSharingRecordStatus("01");
        Assert.assertEquals(result1.getHelpRecordCode(), result.getHelpRecordCode());
    }

    @Test
    public void testInsertHelpRecord() {
        BoostSharingModel boostSharingModel = new BoostSharingModel();
        boostSharingModel.setNumberOfBoostSharing("numberOfBoostSharing");
        Mockito.when(codeGenerator.generateCode(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn("replaceMeWithExpectedResult");

//        Mockito.when(boostSharingService.findByActivityCode(Mockito.any())).thenReturn(boostSharingModel);
//        Mockito.when(sharingRecordService.insert(any())).thenReturn(0);
        Mockito.when(helpRecordService.insert(any())).thenReturn(0);
//        Mockito.when(boostSharingService.insert(any())).thenReturn(0);

        String result = boostSharingComponent.insertHelpRecord(new HelpRecordDto());
        Assert.assertEquals("replaceMeWithExpectedResult", result);
    }

    @Test
    public void testUpdateSharingRecordStatus() {
        Mockito.when(sharingRecordService.updateSharingRecord(any())).thenReturn(0);

        boostSharingComponent.updateSharingRecordStatus(new SharingRecordModel("activityCode", "domainCode", "tenantCode", "orgCode", "sharingRecordCode", "sharingMemberCode", "activityStatus", "numberOfBoostSharing", "numberOfPeopleWhoHaveHelped", new Date()));
    }

    @Test
    public void testDistributeRewards() {
        BoostSharingRewardEnum couponForHelpersOnly = BoostSharingRewardEnum.COUPON_FOR_HELPERS_ONLY;

        BoostSharingModel boostSharingModel = new BoostSharingModel();
        boostSharingModel.setNumberOfBoostSharing("numberOfBoostSharing");
//        Mockito.when(boostSharingService.findByActivityCode(Mockito.any())).thenReturn(boostSharingModel);
//        Mockito.when(sharingRecordService.findByActivityCode(anyString())).thenReturn(new SharingRecordModel());
//        Mockito.when(helpRecordService.findByActivityCode(anyString())).thenReturn(new HelpRecordModel());
//        Mockito.when(boostSharingService.findByActivityCode(anyString())).thenReturn(new BoostSharingModel());

        boostSharingComponent.distributeRewards(new HelpRecordDto(), couponForHelpersOnly, boostSharingModel);
    }

    @Test
    public void testQuerySharingInformation() {
        Mockito.when(sharingRecordService.querySharingRecord(any())).thenReturn(Arrays.<SharingRecordEntity>asList(new SharingRecordEntity(1L, "domainCode", "tenantCode", "orgCode", "sharingRecordCode", "sharingMemberCode", "activityStatus", "numberOfBoostSharing", "numberOfPeopleWhoHaveHelped", "activityStatus", new Date(), new Date())));
        Mockito.when(helpRecordService.queryHelpRecord(any(), any())).thenReturn(Arrays.<HelpRecordEntity>asList(new HelpRecordEntity(1L, "tenantCode", "orgCode", "activityCode", "sharingRecordCode", "helpRecordCode", "sharingMemberCode", "helpMemberCode", "helpRecordStatus", "activityStatus", new Date())));

        RequestPage page = new RequestPage(1, 5);
        PageResult<QuerySharingInformationResult> result = boostSharingComponent.querySharingInformation(new QuerySharingInformationDto(), page);


    }


    @Test
    public void distributeRewards_1() {

        HelpRecordDto helpRecordDto = new HelpRecordDto();
        helpRecordDto.setHelpMemberCode("helpMemberCode");
        helpRecordDto.setActivityCode("activityCode");
        helpRecordDto.setSharingRecordCode("sharingRecordCode");
        helpRecordDto.setSharingRecordCode("sharingRecordCode");
        BoostSharingRewardEnum rewardType = BoostSharingRewardEnum.COUPON_FOR_HELPERS_ONLY;
        BoostSharingModel boostSharingModel = new BoostSharingModel();

        boostSharingModel.setHelpToGetCouponActivityCode("activityCode");
        boostSharingModel.setLuckyDrawActivityCode("activityCode");
        boostSharingModel.setShareToGetCouponActivityCode("activityCode");

        PageInfo<CouponReleaseDomain> couponReleaseList = new PageInfo<>();
        PageResult<TicketReleaseQueryResult> result = new PageResult<>();

        getMemberInfo();
//        Mockito.when(couponReleaseComponent.queryCouponRelease(Mockito.any(), Mockito.any())).thenReturn(couponReleaseList);
        boostSharingComponent.distributeRewards(helpRecordDto, rewardType, boostSharingModel);


        rewardType = BoostSharingRewardEnum.SHARE_PEOPLE_AND_HELP_PEOPLE_COUPONS;
        boostSharingComponent.distributeRewards(helpRecordDto, rewardType, boostSharingModel);
        rewardType = BoostSharingRewardEnum.SHARER_RIGHT_OF_FIRST_REFUSAL;
        Mockito.when(ticketComponent.queryRelease(Mockito.any(), Mockito.any())).thenReturn(result);
        boostSharingComponent.distributeRewards(helpRecordDto, rewardType, boostSharingModel);
        rewardType = BoostSharingRewardEnum.SHARER_LUCKY_DRAW_BONUS;
//        Mockito.when(couponReleaseComponent.queryCouponRelease(Mockito.any(), Mockito.any())).thenReturn(couponReleaseList);
//        boostSharingComponent.distributeRewards(helpRecordDto, rewardType, boostSharingModel);

    }
    @Test
    public void distributeRewards_5() {

        HelpRecordDto helpRecordDto = new HelpRecordDto();
        helpRecordDto.setHelpMemberCode("helpMemberCode");
        helpRecordDto.setActivityCode("activityCode");
        helpRecordDto.setSharingRecordCode("sharingRecordCode");
        helpRecordDto.setSharingRecordCode("sharingRecordCode");
        BoostSharingRewardEnum rewardType = BoostSharingRewardEnum.COUPON_FOR_HELPERS_ONLY;
        BoostSharingModel boostSharingModel = new BoostSharingModel();

        boostSharingModel.setHelpToGetCouponActivityCode("activityCode");
        boostSharingModel.setLuckyDrawActivityCode("activityCode");
        boostSharingModel.setShareToGetCouponActivityCode("activityCode");

        PageInfo<CouponReleaseDomain> couponReleaseList = new PageInfo<>();
        PageResult<TicketReleaseQueryResult> result = new PageResult<>();

        getMemberInfo();
        rewardType = BoostSharingRewardEnum.SHARER_RIGHT_OF_FIRST_REFUSAL;
        Mockito.when(ticketComponent.queryRelease(Mockito.any(), Mockito.any())).thenReturn(result);
        boostSharingComponent.distributeRewards(helpRecordDto, rewardType, boostSharingModel);


    }

    @Test
    public void testSendTicket() {
        // Arrange
        HelpRecordDto dto = new HelpRecordDto();
        dto.setDomainCode("domainCode");
        dto.setTenantCode("tenantCode");
        dto.setOrgCode("orgCode");
        dto.setActivityCode("activityCode");
        dto.setSharingRecordCode("sharingRecordCode");

        String helpToGetCouponActivityCode = "helpToGetCouponActivityCode";
        String helpMemberCode = "helpMemberCode";

        PageResult<TicketReleaseQueryResult> ticketReleaseQueryResultPageResult = new PageResult<>();
        List<TicketReleaseQueryResult> list = new ArrayList<>();
        TicketReleaseQueryResult ticketReleaseQueryResult = new TicketReleaseQueryResult();
        ticketReleaseQueryResult.setCreateUser(dto.getActivityCode());
        list.add(ticketReleaseQueryResult);
        ticketReleaseQueryResultPageResult.setData(new PageData<>(list, 1L));

        TicketReleaseDomain ticketReleaseDomain = new TicketReleaseDomain();
        ticketReleaseDomain.setActivityCode(helpToGetCouponActivityCode);
        ticketReleaseDomain.setDomainCode(dto.getDomainCode());
        ticketReleaseDomain.setTenantCode(dto.getTenantCode());
        ticketReleaseDomain.setOrgCode(dto.getOrgCode());
        ticketReleaseDomain.setOperateUser(dto.getActivityCode());

        TicketReleaseDomain releaseSpecifiedQuantityDomain = new TicketReleaseDomain();
        releaseSpecifiedQuantityDomain.setDomainCode(dto.getDomainCode());
        releaseSpecifiedQuantityDomain.setTenantCode(dto.getTenantCode());
        releaseSpecifiedQuantityDomain.setOrgCode(dto.getOrgCode());
        releaseSpecifiedQuantityDomain.setActivityCode(helpToGetCouponActivityCode);
        releaseSpecifiedQuantityDomain.setQuality(1L);
        releaseSpecifiedQuantityDomain.setOperateUser(dto.getActivityCode());
        releaseSpecifiedQuantityDomain.setReleaseCode(ticketReleaseQueryResult.getReleaseCode());

        int releaseSpecifiedQuantity = 1;

        TicketSendDomain ticketSendDomain = new TicketSendDomain();
        ticketSendDomain.setActivityCode(helpToGetCouponActivityCode);
        ticketSendDomain.setMemberCodes(Lists.newArrayList(helpMemberCode));
        ticketSendDomain.setTenantCode(dto.getTenantCode());
        ticketSendDomain.setDomainCode(dto.getDomainCode());
        ticketSendDomain.setOrgCode(dto.getOrgCode());
        ticketSendDomain.setQuality(1);
        ticketSendDomain.setRightOfFirstRefusalSourceCode(dto.getSharingRecordCode());

        List<TicketSendResult> expected = new ArrayList<>();
        TicketSendResult ticketSendResult = new TicketSendResult();
        expected.add(ticketSendResult);

        when(ticketComponent.queryRelease(eq(ticketReleaseDomain), any(TicketReleaseQueryParam.class))).thenReturn(ticketReleaseQueryResultPageResult);
//        when(ticketComponent.release(eq(ticketReleaseDomain))).thenReturn("releaseCode");
        when(ticketComponent.releaseSpecifiedQuantity(eq(releaseSpecifiedQuantityDomain))).thenReturn(releaseSpecifiedQuantity);
        when(ticketComponent.sendTicket(eq(ticketSendDomain))).thenReturn(expected);

        // Act
        List<TicketSendResult> actual = boostSharingComponent.sendTicket(dto, helpToGetCouponActivityCode, helpMemberCode);

        // Assert
        Assertions.assertEquals(expected, actual);

    }


    @Test
    public void testSendCoupon() {
        // Create test data
        HelpRecordDto dto = new HelpRecordDto();
        dto.setTenantCode("tenantCode");
        dto.setDomainCode("domainCode");
        dto.setActivityCode("activityCode");

        // Mock releaseCouponInDTO
        ReleaseCouponInDTO releaseCouponInDTO = new ReleaseCouponInDTO();
        releaseCouponInDTO.setReleaseSource(CouponReleaseSourceEnum.SYSTEM_CREATE.code());
        releaseCouponInDTO.setTimeSameActivity("1");
        releaseCouponInDTO.setActivityCode(dto.getActivityCode());
        releaseCouponInDTO.setTenantCode(dto.getTenantCode());
        releaseCouponInDTO.setReleaseType(ReleaseTypeEnum.IMMEDIATELY.code());
        releaseCouponInDTO.setTimeSameActivity(ReleaseTimeSameActivityEnum.SAME.getCode());
        releaseCouponInDTO.setReceiveTimeSameActivity(ReleaseTimeSameActivityEnum.SAME.getCode());
        Map<String, String> outCouponMap = new HashMap<>();

        // Mock couponReleaseList
        CouponReleaseDomain couponReleaseDomain = new CouponReleaseDomain();
        couponReleaseDomain.setTenantCode(dto.getTenantCode());
        couponReleaseDomain.setActivityCode(dto.getActivityCode());
        List<CouponReleaseDomain> couponReleaseList = new ArrayList<>();
        couponReleaseList.add(couponReleaseDomain);

        ActivityModel couponActivity = new ActivityModel();
        couponActivity.setActivityStatus(ActivityStatusEnum.EFFECTIVE.code());

        when(activityService.findActivity(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(couponActivity);

        // Mock queryCouponRelease method
        when(couponReleaseComponent.queryCouponRelease(any(CouponReleaseDomain.class), any(RequestPage.class))).thenReturn(new PageInfo<>(couponReleaseList));

        // Mock releaseCoupon method
        String releaseCouponCode = "releaseCouponCode";
//        when(couponReleaseComponent.releaseCoupon(any(ReleaseCouponInDTO.class), any(Map.class), any(CouponReleaseDomain.class))).thenReturn(releaseCouponCode);

        // Mock releaseSpecifiedQuantity method
        when(couponReleaseComponent.releaseSpecifiedQuantity(any(CouponReleaseDomain.class))).thenReturn(1);

        // Mock sendCouponToUserByUserCode method
//        when(couponCodeUserComponent.sendCouponToUserByUserCode(any(SendCouponToUserParam.class))).thenReturn(new HashMap<>());
        getMemberInfo();
        JsonResult<String> result = new JsonResult<>();

        result.setData("1");
        result.setSuccess(true);

        Mockito.when(masterDataFeignClient.getValueValue(Mockito.anyString(), Mockito.anyString())).thenReturn(result);

        // Call the method to be tested
        boostSharingComponent.sendCoupon(dto, dto.getActivityCode(), "dto.getMemberCode()");
    }


    @Test
    public void testSendCoupon2() {
        // Create test data
        HelpRecordDto dto = new HelpRecordDto();
        dto.setTenantCode("tenantCode");
        dto.setDomainCode("domainCode");
        dto.setActivityCode("activityCode");

        // Mock releaseCouponInDTO
        ReleaseCouponInDTO releaseCouponInDTO = new ReleaseCouponInDTO();
        releaseCouponInDTO.setReleaseSource(CouponReleaseSourceEnum.SYSTEM_CREATE.code());
        releaseCouponInDTO.setTimeSameActivity("1");
        releaseCouponInDTO.setActivityCode(dto.getActivityCode());
        releaseCouponInDTO.setTenantCode(dto.getTenantCode());
        releaseCouponInDTO.setReleaseType(ReleaseTypeEnum.IMMEDIATELY.code());
        releaseCouponInDTO.setTimeSameActivity(ReleaseTimeSameActivityEnum.SAME.getCode());
        releaseCouponInDTO.setReceiveTimeSameActivity(ReleaseTimeSameActivityEnum.SAME.getCode());
        Map<String, String> outCouponMap = new HashMap<>();

        // Mock couponReleaseList
        CouponReleaseDomain couponReleaseDomain = new CouponReleaseDomain();
        couponReleaseDomain.setTenantCode(dto.getTenantCode());
        couponReleaseDomain.setActivityCode(dto.getActivityCode());
        List<CouponReleaseDomain> couponReleaseList = new ArrayList<>();
        couponReleaseList.add(couponReleaseDomain);
        ActivityModel couponActivity = new ActivityModel();
        couponActivity.setActivityStatus(ActivityStatusEnum.EFFECTIVE.code());

        when(activityService.findActivity(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(couponActivity);

        // Mock queryCouponRelease method
        when(couponReleaseComponent.queryCouponRelease(any(CouponReleaseDomain.class), any(RequestPage.class))).thenReturn(new PageInfo<>(couponReleaseList));

        // Mock releaseCoupon method
        String releaseCouponCode = "releaseCouponCode";
//        when(couponReleaseComponent.releaseCoupon(any(ReleaseCouponInDTO.class), any(Map.class), any(CouponReleaseDomain.class))).thenReturn(releaseCouponCode);

        // Mock releaseSpecifiedQuantity method
        when(couponReleaseComponent.releaseSpecifiedQuantity(any(CouponReleaseDomain.class))).thenReturn(1);

        // Mock sendCouponToUserByUserCode method
        when(couponCodeUserComponent.sendCouponToUserByUserCode(any(SendCouponToUserParam.class))).thenReturn(new HashMap<>());
        getMemberInfo();
        JsonResult<String> result = new JsonResult<>();

        result.setData("0");
        result.setSuccess(true);

        Mockito.when(masterDataFeignClient.getValueValue(Mockito.anyString(), Mockito.anyString())).thenReturn(result);

        // Call the method to be tested
        boostSharingComponent.sendCoupon(dto, dto.getActivityCode(), "dto.getMemberCode()");
    }

    @Test
    public void testSendCouponMessage_Success() {
        // Arrange
        BoostSharingComponent.SendCouponMessage sendCouponMessage = new BoostSharingComponent.SendCouponMessage();
        sendCouponMessage.setSendCouponToUserParam(new SendCouponToUserParam());
        sendCouponMessage.getActivityCode();
        String expectedParam = "{\"key\":\"value\"}";


        boostSharingComponent.sendCouponMessage(sendCouponMessage);


    }


    @Test
    public void bindTheUserWithTheRightOfFirstRefusal() {

        HelpRecordDto dto = new HelpRecordDto();
        dto.setSharingMemberCode("sharingMemberCode");
        dto.setTenantCode("tenantCode");
        boostSharingComponent.bindTheUserWithTheRightOfFirstRefusal(dto, "productCode");

    }


    @Test
    public void writeOffOfPreEmptiveRights() {

        WriteOffOfPreEmptiveRightsDto dto = new WriteOffOfPreEmptiveRightsDto();


        RightOfFirstRefusalEntity entity = new RightOfFirstRefusalEntity();
        Mockito.when(rightOfFirstRefusalService.findRightOfFirstRefusalByMember(Mockito.any())).thenReturn(entity);
        Mockito.when(rightOfFirstRefusalService.updateStatus(Mockito.any())).thenReturn(1);
        boostSharingComponent.writeOffOfPreEmptiveRights(dto);

    }


    public void getMemberInfo() {
        Result<GetMemberProfileResult> memberProfile = new Result<>();
        memberProfile.setData(new GetMemberProfileResult());
        Mockito.when(memberFeignClient.getMemberProfile(Mockito.any())).thenReturn(memberProfile);

    }


    @Test
    public void filterNoRightOfFirstRefusal() {
        ShoppingCartItem cartItem = new ShoppingCartItem();
        cartItem.setProductCode("productCode");
        cartItem.setQuantity(1);
        Map<String, CacheFlashSaleModel> map = new HashMap();
        CacheFlashSaleModel flashSaleModel = new CacheFlashSaleModel();
        BoostSharingModel boostSharingModel = new BoostSharingModel();
        boostSharingModel.setBoostSharingType(BoostSharingTypeEnum.RIGHT_FIRST.code());
        boostSharingModel.setRightOfFirstRefusalProductCode("productCode");
        boostSharingModel.setRightOfFirstRefusalStartTime("20000101010101");
        boostSharingModel.setRightOfFirstRefusalEndTime("20330101010101");

        flashSaleModel.setActivityType(ActivityTypeEnum.BOOST_SHARDING.code());
        flashSaleModel.setBoostSharingModel(boostSharingModel);
        map.put("activityCode", flashSaleModel);
        Result<GetMemberProfileResult> memberProfile = new Result<>();
        memberProfile.setData(new GetMemberProfileResult());

        List<CacheFlashSaleModel> list = new ArrayList<>();
        list.add(flashSaleModel);

        List<RightOfFirstRefusalEntity> listResult = new ArrayList<>();
        listResult.add(new RightOfFirstRefusalEntity());

//        Mockito.when(marketingCacheComponent.getFlashSaleCacheMap("tenantCode", "language", ActivityTypeEnum.BOOST_SHARDING.code())).thenReturn(map);
        Mockito.when(memberFeignClient.getMemberProfile(Mockito.any())).thenReturn(memberProfile);

        Mockito.when(flashSaleComponent.getBoostSharing(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(list);

        Mockito.when(rightOfFirstRefusalService.queryRightOfFirstRefusalByMember(Mockito.any())).thenReturn(listResult);

        FilterNoRightOfFirstRefusalDto filterNoRightOfFirstRefusalDto = new FilterNoRightOfFirstRefusalDto();
        filterNoRightOfFirstRefusalDto.setDomainCode("domainCode");
        filterNoRightOfFirstRefusalDto.setTenantCode("tenantCode");
        filterNoRightOfFirstRefusalDto.setUserCode("memberCode");
        filterNoRightOfFirstRefusalDto.setCartItems(Lists.newArrayList(cartItem));
        filterNoRightOfFirstRefusalDto.setOrderId("");
        filterNoRightOfFirstRefusalDto.setWriteOff(false);
        boostSharingComponent.filterNoRightOfFirstRefusal(filterNoRightOfFirstRefusalDto);

    }


    @Test
    public void testFilterNoRightOfFirstRefusal() {
        // mock data
        String domainCode = "domainCode";
        String tenantCode = "tenantCode";
        String userCode = "userCode";
        String language = "language";
        String orgCode = "orgCode";
        String productCode = "productCode";
        String startTime = "20000101010101";
        String endTime = "20400101010101";

        CacheFlashSaleModel cacheFlashSaleModel = new CacheFlashSaleModel();
        cacheFlashSaleModel.setActivityType(ActivityTypeEnum.BOOST_SHARDING.code());
        cacheFlashSaleModel.setBoostSharingModel(new BoostSharingModel());
        cacheFlashSaleModel.getBoostSharingModel().setRightOfFirstRefusalStartTime(startTime);
        cacheFlashSaleModel.getBoostSharingModel().setRightOfFirstRefusalEndTime(endTime);
        cacheFlashSaleModel.getBoostSharingModel().setRightOfFirstRefusalProductCode(productCode);
        cacheFlashSaleModel.getBoostSharingModel().setBoostSharingType(BoostSharingTypeEnum.RIGHT_FIRST.code());
        cacheFlashSaleModel.setActivityCode("activityCode");
        cacheFlashSaleModel.setActivityBegin("20000101010101");
        cacheFlashSaleModel.setActivityEnd("20400101010101");

        Map<String, CacheFlashSaleModel> marketingCacheMap = new HashMap<>();
        marketingCacheMap.put("activityCode", cacheFlashSaleModel);

        ShoppingCartItem shoppingCartItem = new ShoppingCartItem();
        shoppingCartItem.setProductCode(productCode);
        shoppingCartItem.setQuantity(1);
        List<RightOfFirstRefusalEntity> rightOfFirstRefusalEntities = new ArrayList<>();
        rightOfFirstRefusalEntities.add(new RightOfFirstRefusalEntity());
        Result<GetMemberProfileResult> memberProfile = new Result<>();
        GetMemberProfileResult getMemberProfileResult = new GetMemberProfileResult();
        getMemberProfileResult.setTags(new ArrayList<>());
        getMemberProfileResult.setMemberCode(userCode);
        memberProfile.setData(getMemberProfileResult);

        List<CacheFlashSaleModel> spuMarketing = new ArrayList<>();
        spuMarketing.add(cacheFlashSaleModel);

        // mock dependencies
        when(marketingCacheComponent.getFlashSaleCacheMap(tenantCode, language, ActivityTypeEnum.BOOST_SHARDING.code())).thenReturn(marketingCacheMap);
        when(memberFeignClient.getMemberProfile(Mockito.any())).thenReturn(memberProfile);
        when(flashSaleComponent.getBoostSharing(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(spuMarketing);
        when(rightOfFirstRefusalService.queryRightOfFirstRefusalByMember(any())).thenReturn(new ArrayList<>());

        when(rightOfFirstRefusalService.queryRightOfFirstRefusalByMember(any())).thenReturn(rightOfFirstRefusalEntities);
        // call the method to be tested
        FilterNoRightOfFirstRefusalDto filterNoRightOfFirstRefusalDto = new FilterNoRightOfFirstRefusalDto();
        filterNoRightOfFirstRefusalDto.setDomainCode(domainCode);
        filterNoRightOfFirstRefusalDto.setTenantCode(tenantCode);
        filterNoRightOfFirstRefusalDto.setUserCode(userCode);
        filterNoRightOfFirstRefusalDto.setLanguage(language);
        filterNoRightOfFirstRefusalDto.setCartItems(Lists.newArrayList(shoppingCartItem));
        filterNoRightOfFirstRefusalDto.setOrderId("");
        filterNoRightOfFirstRefusalDto.setWriteOff(false);
        boostSharingComponent.filterNoRightOfFirstRefusal(filterNoRightOfFirstRefusalDto);

    }


    @Test
    public void testGetMemberQualification() {

        String domainCode = "domainCode";
        String tenantCode = "tenantCode";
        String memberCode = "memberCode";
        String orgCode = "orgCode";
        Result<GetMemberProfileResult> memberProfile = new Result<>();
        GetMemberProfileResult getMemberProfileResult = new GetMemberProfileResult();
        getMemberProfileResult.setTags(new ArrayList<>());
        getMemberProfileResult.setMemberCode("userCode");
        memberProfile.setData(getMemberProfileResult);

        when(memberFeignClient.getMemberProfile(Mockito.any())).thenReturn(memberProfile);
        boostSharingComponent.getMemberQualification(domainCode, tenantCode, memberCode, orgCode);

    }

    @Test
    public void queryRightOfFirstRefusal() {

        WriteOffOfPreEmptiveRightsDto dto = new WriteOffOfPreEmptiveRightsDto();

        boostSharingComponent.queryRightOfFirstRefusal(dto);

    }

    @Test
    public void queryHelpRecord() {

        QuerySharingInformationDto dto = new QuerySharingInformationDto();

        boostSharingComponent.queryHelpRecord(dto);

    }

    @Test
    public void exportBoostSharingTotal(){
        boostSharingComponent.exportBoostSharingTotal(new ExportBoostSharingTotalDto());
    }
    @Test
    public void exportBoostSharingDetail(){
        boostSharingComponent.exportBoostSharingDetail(new ExportBoostSharingDetailDto());
    }

    @Test
    public void testFilterNoRightOfFirstRefusal1() {
        // Mock data
        String domainCode = "domainCode";
        String tenantCode = "tenantCode";
        String userCode = "userCode";
        String language = "language";
        List<ShoppingCartItem> cartItems = new ArrayList<>();
        String orderId = "orderId";
        ShoppingCartItem shoppingCartItem = new ShoppingCartItem();
        shoppingCartItem.setProductCode("productCode");
        shoppingCartItem.setSkuCode("skuCode");
        shoppingCartItem.setQuantity(1);
        cartItems.add(shoppingCartItem);
        boolean writeOff = true;
        Map<String, List<ShoppingCartItem>> byProduct = new HashMap<>();
        List<ShoppingCartItem> cardItem = new ArrayList<>();
        cardItem.add(new ShoppingCartItem());
        byProduct.put("productCode", cardItem);
        // Mock marketingCacheMap
        Map<String, CacheFlashSaleModel> marketingCacheMap = new HashMap<>();
        CacheFlashSaleModel cacheFlashSaleModel = new CacheFlashSaleModel();
        BoostSharingModel boostSharingModel = new BoostSharingModel();

        boostSharingModel.setBoostSharingType(BoostSharingTypeEnum.RIGHT_FIRST.code());
        boostSharingModel.setRightOfFirstRefusalProductCode("productCode");
        cacheFlashSaleModel.setBoostSharingModel(boostSharingModel);
        marketingCacheMap.put("activityCode", cacheFlashSaleModel);

        // Mock flashSaleComponent.getBoostSharing
        List<CacheFlashSaleModel> spuMarketing = new ArrayList<>();
        CacheFlashSaleModel spuMarketingModel = new CacheFlashSaleModel();
        spuMarketingModel.setActivityType(ActivityTypeEnum.BOOST_SHARDING.code());
        BoostSharingModel boostSharingModel2 = new BoostSharingModel();
        boostSharingModel2.setRightOfFirstRefusalStartTime("20000101010101");
        boostSharingModel2.setRightOfFirstRefusalEndTime("21000101010101");
        boostSharingModel2.setRightOfFirstRefusalProductCode("productCode");
        boostSharingModel2.setBoostSharingType(BoostSharingTypeEnum.RIGHT_FIRST.code());
        spuMarketingModel.setBoostSharingModel(boostSharingModel2);
        spuMarketing.add(spuMarketingModel);
        Result<GetMemberProfileResult> memberProfile = new Result<>();
        GetMemberProfileResult getMemberProfileResult = new GetMemberProfileResult();
        getMemberProfileResult.setTags(new ArrayList<>());
        getMemberProfileResult.setMemberCode("userCode");
        memberProfile.setData(getMemberProfileResult);

        when(memberFeignClient.getMemberProfile(Mockito.any())).thenReturn(memberProfile);
        when(flashSaleComponent.getBoostSharing(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(spuMarketing);

        // Mock rightOfFirstRefusalService.queryRightOfFirstRefusalByMember
        List<RightOfFirstRefusalEntity> rightOfFirstRefusalEntities = new ArrayList<>();
        RightOfFirstRefusalEntity rightOfFirstRefusalEntity = new RightOfFirstRefusalEntity();
        rightOfFirstRefusalEntity.setRightOfFirstRefusalCode("rightOfFirstRefusalCode");
        rightOfFirstRefusalEntity.setActivityCode("activityCode");
        rightOfFirstRefusalEntities.add(rightOfFirstRefusalEntity);

        when(rightOfFirstRefusalService.queryRightOfFirstRefusalByMember(any(WriteOffOfPreEmptiveRightsDto.class))).thenReturn(rightOfFirstRefusalEntities);

        // Mock rightOfFirstRefusalMap
        Map<String, List<RightOfFirstRefusalEntity>> rightOfFirstRefusalMap = new HashMap<>();
        rightOfFirstRefusalMap.put("productCode", rightOfFirstRefusalEntities);

        // Execute the method to test
        FilterNoRightOfFirstRefusalDto filterNoRightOfFirstRefusalDto = new FilterNoRightOfFirstRefusalDto();
        filterNoRightOfFirstRefusalDto.setDomainCode(domainCode);
        filterNoRightOfFirstRefusalDto.setTenantCode(tenantCode);
        filterNoRightOfFirstRefusalDto.setUserCode(userCode);
        filterNoRightOfFirstRefusalDto.setLanguage(language);
        filterNoRightOfFirstRefusalDto.setCartItems(cartItems);
        filterNoRightOfFirstRefusalDto.setOrderId(orderId);
        filterNoRightOfFirstRefusalDto.setWriteOff(writeOff);
        boostSharingComponent.filterNoRightOfFirstRefusal(filterNoRightOfFirstRefusalDto);

    }


    // Sends a coupon to a member with valid parameters and qualifications
    @Test
    public void test_send_coupon_valid_parameters_and_qualifications() {
        // Mock dependencies
        HelpRecordDto dto = new HelpRecordDto();
        String activityCode = "ACT001";
        String memberCode = "MEM001";

        ReleaseCouponInDTO releaseCouponInDTO = new ReleaseCouponInDTO();
        releaseCouponInDTO.setReleaseSource(CouponReleaseSourceEnum.SYSTEM_CREATE.code());
        releaseCouponInDTO.setTimeSameActivity("1");
        releaseCouponInDTO.setActivityCode(activityCode);
        releaseCouponInDTO.setTenantCode(dto.getTenantCode());
        releaseCouponInDTO.setReleaseType(ReleaseTypeEnum.IMMEDIATELY.code());
        releaseCouponInDTO.setTimeSameActivity(ReleaseTimeSameActivityEnum.SAME.getCode());
        releaseCouponInDTO.setReceiveTimeSameActivity(ReleaseTimeSameActivityEnum.SAME.getCode());

        Map<String, String> outCouponMap = new HashMap<>();

        CouponReleaseDomain releaseDomain = new CouponReleaseDomain();
        releaseDomain.setTenantCode(dto.getTenantCode());
        releaseDomain.setActivityCode(activityCode);
        releaseDomain.setReleaseStatus(null);

        RequestPage page = new RequestPage();
        page.setPageCount(100);

        ActivityModel couponActivity = new ActivityModel();
        couponActivity.setActivityStatus(ActivityStatusEnum.EFFECTIVE.code());

        PageInfo<CouponReleaseDomain> couponReleaseList = new PageInfo<>();
        CouponReleaseDomain couponReleaseDomain = new CouponReleaseDomain();

        SendCouponToUserParam param = new SendCouponToUserParam();
        param.setDomainCode(dto.getDomainCode());
        param.setTenantCode(dto.getTenantCode());
        param.setActivityCode(activityCode);
        param.setUserCode(memberCode);
        param.setReceiveCount(1);

        Map<String, List<String>> memberQualification = new HashMap<>();
        List<QualificationModel> qualificationModels = new ArrayList<>();

        JsonResult<String> couponAutoSend = new JsonResult<>();
        couponAutoSend.setSuccess(true);
        couponAutoSend.setData("1");

        // Set up mock behavior
//        when(couponReleaseComponent.queryCouponRelease(any(CouponReleaseDomain.class), any(RequestPage.class))).thenReturn(couponReleaseList);
//        when(activityService.findActivity(anyString(), anyString(), any())).thenReturn(couponActivity);
//        when(couponReleaseComponent.releaseCoupon(any(ReleaseCouponInDTO.class), any(Map.class), any())).thenReturn("COUPON001");
//        when(couponReleaseComponent.releaseSpecifiedQuantity(any(CouponReleaseDomain.class))).thenReturn(1);
//        when(masterDataFeignClient.getValueValue(anyString(), anyString())).thenReturn(couponAutoSend);

        // Invoke the method

        boostSharingComponent.sendCoupon(dto, activityCode, memberCode);

         }


}

