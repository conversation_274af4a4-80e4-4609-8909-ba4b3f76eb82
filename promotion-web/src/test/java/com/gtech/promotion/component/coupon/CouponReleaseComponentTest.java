package com.gtech.promotion.component.coupon;

import com.google.common.collect.Lists;
import com.gtech.promotion.dao.model.activity.ActivityLanguageModel;
import com.gtech.promotion.dao.model.activity.ActivityModel;
import com.gtech.promotion.dao.model.coupon.CountCouponCodeModel;
import com.gtech.promotion.dao.model.coupon.CouponReleaseModel;
import com.gtech.promotion.domain.coupon.CouponReleaseDomain;
import com.gtech.promotion.dto.in.coupon.ReleaseCouponInDTO;
import com.gtech.promotion.exception.PromotionException;
import com.gtech.promotion.helper.ActivityRedisHelpler;
import com.gtech.promotion.service.activity.ActivityFuncParamService;
import com.gtech.promotion.service.activity.ActivityFuncRankService;
import com.gtech.promotion.service.coupon.PromoCouponCodeUserService;
import com.gtech.promotion.service.coupon.PromoCouponInnerCodeService;
import com.gtech.promotion.service.coupon.PromoCouponReleaseService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

/**
 * <AUTHOR>
 * @Date 2021/9/22 10:19
 */
@RunWith(MockitoJUnitRunner.class)
public class CouponReleaseComponentTest {

    @InjectMocks
    private CouponReleaseComponent couponReleaseComponent;

    @Mock
    private PromoCouponReleaseService couponReleaseService;

    @Mock
    private PromoCouponCodeUserService couponCodeUserService;

    @Mock
    private PromoCouponInnerCodeService couponInnerCodeService;

    @Mock
    private ActivityFuncParamService activityFuncParamService;

    @Mock
    private ActivityFuncRankService activityFuncRankService;

    @Mock
    private CouponActivityComponent couponActivityComponent;

    @Mock
    private ActivityRedisHelpler redisService;


    @Test
    public void countCouponRelease(){
        CouponReleaseDomain releaseDomain = new CouponReleaseDomain();
        releaseDomain.setTenantCode("");
        releaseDomain.setActivityCode("");
        releaseDomain.setCouponType("02");
        releaseDomain.setReleaseCode("");
        releaseDomain.setReleaseStatus("");
        releaseDomain.setReleaseQuantity(0);
        releaseDomain.setInventory(0);
        releaseDomain.setReceivedQuantity(0);
        releaseDomain.setUsedTotal(0);
        releaseDomain.setLocked(0);
        releaseDomain.setReleaseSource("");
        releaseDomain.setReceiveStartTime("");
        releaseDomain.setReceiveEndTime("");
        releaseDomain.setValidDays(0);
        releaseDomain.setValidStartTime("");
        releaseDomain.setValidEndTime("");
        releaseDomain.setReleaseTime("");
        releaseDomain.setReleaseType("");
        releaseDomain.setCouponCodePrefix("");
        releaseDomain.setTimeSameActivity("");
        CountCouponCodeModel m = new CountCouponCodeModel();
        m.setActivityCode("");
        m.setReleaseCode("");
        m.setCouponStatus("");
        m.setCouponCount(0);

        List<CountCouponCodeModel> cccModels = new ArrayList<>();
        cccModels.add(m);
        Mockito.when(couponInnerCodeService.countCouponCode(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(cccModels);
        couponReleaseComponent.countCouponRelease(releaseDomain);
        releaseDomain.setCouponType("03");
        couponReleaseComponent.countCouponRelease(releaseDomain);
        releaseDomain.setCouponType("01");
        couponReleaseComponent.countCouponRelease(releaseDomain);

    }

    
    @Test
    public void releaseCoupon(){

        ReleaseCouponInDTO dto = new ReleaseCouponInDTO();
        dto.setTimeSameActivity("1");
        dto.setReleaseType("01");
        dto.setValidStart("20210922");
        dto.setValidEnd("20210924");
        dto.setReleaseQuantity(1001);
        dto.setReceiveStart("20210921");
        dto.setReceiveEnd("20210923");
        Map<String, String> outCouponMap = new HashMap<>();
        ActivityModel activityModel = new ActivityModel();
        activityModel.setActivityEnd("20500000000000");
        activityModel.setActivityBegin("20400000000000");
        activityModel.setActivityStatus("04");
        activityModel.setCouponType("02");
        activityModel.setTotalQuantity(1000000000);
        Mockito.when(couponActivityComponent.findEffectiveActivity(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(activityModel);
//        Mockito.when(couponReleaseService.queryReleaseCount111(Mockito.any())).thenReturn(11111111l);
        try {
            couponReleaseComponent.releaseCoupon(dto, outCouponMap);

        }catch (Exception e){

        }
    }

    @Test
    public void generateCouponCode(){

        Map<String, String> outCouponMap = new HashMap<>();
        outCouponMap.put("123","123");
        ActivityModel activityModel = new ActivityModel();
        activityModel.setTotalQuantity(2000);
        Long releaseTotal = 10L;
        CouponReleaseModel promoCouponReleaseVO = new CouponReleaseModel();
        promoCouponReleaseVO.setTenantCode("1");
        promoCouponReleaseVO.setActivityCode("1");
        promoCouponReleaseVO.setCouponType("1");
        promoCouponReleaseVO.setReleaseCode("1");
        promoCouponReleaseVO.setReleaseStatus("1");
        promoCouponReleaseVO.setReleaseQuantity(0);
        promoCouponReleaseVO.setInventory(0);
        promoCouponReleaseVO.setReleaseSource("1");
        promoCouponReleaseVO.setReleaseType("1");
        promoCouponReleaseVO.setReleaseTime("1");
        promoCouponReleaseVO.setReceiveStartTime("1");
        promoCouponReleaseVO.setReceiveEndTime("1");
        promoCouponReleaseVO.setValidStartTime("1");
        promoCouponReleaseVO.setValidEndTime("1");
        promoCouponReleaseVO.setValidDays(0);
        promoCouponReleaseVO.setCouponCodePrefix("1");
        promoCouponReleaseVO.setTimeSameActivity("1");

        promoCouponReleaseVO.setReleaseType("01");

        couponReleaseComponent.generateCouponCode(outCouponMap,activityModel,releaseTotal,promoCouponReleaseVO);

    }

    @Test
    public void checkReleaseTime(){

        ReleaseCouponInDTO dto = new ReleaseCouponInDTO();
        dto.setReleaseType("02");
        dto.setReleaseTime("20000000000000");
        dto.setReceiveEnd("28880000000000");
        ActivityModel activityModel = new ActivityModel();
        activityModel.setActivityBegin("19990000000000");
        activityModel.setCouponType("01");
        couponReleaseComponent.checkReleaseTime(dto,activityModel);
    }


    @Test
    public void cancelReleaseCoupon111(){
        CouponReleaseDomain releaseDomain = new CouponReleaseDomain();
        releaseDomain.setTenantCode("");
        releaseDomain.setActivityCode("");
        releaseDomain.setCouponType("");
        releaseDomain.setReleaseCode("");
        releaseDomain.setReleaseStatus("01");
        releaseDomain.setReleaseQuantity(0);
        releaseDomain.setInventory(0);
        releaseDomain.setReceivedQuantity(0);
        releaseDomain.setUsedTotal(0);
        releaseDomain.setLocked(0);
        releaseDomain.setReleaseSource("");
        releaseDomain.setReceiveStartTime("");
        releaseDomain.setReceiveEndTime("");
        releaseDomain.setValidDays(0);
        releaseDomain.setValidStartTime("");
        releaseDomain.setValidEndTime("");
        releaseDomain.setReleaseTime("");
        releaseDomain.setReleaseType("02");
        releaseDomain.setCouponCodePrefix("");
        releaseDomain.setTimeSameActivity("");


        Mockito.when(couponReleaseService.findCouponReleaseByReleaseCode(Mockito.any(),Mockito.any())).thenReturn(releaseDomain);

        couponReleaseComponent.cancelReleaseCoupon111("123","123");

    }


    @Test
    public void couponReleaseTimer(){
        CouponReleaseModel model = new CouponReleaseModel();
        model.setTenantCode("1");
        model.setActivityCode("1");
        model.setCouponType("1");
        model.setReleaseCode("1");
        model.setReleaseStatus("1");
        model.setReleaseQuantity(0);
        model.setInventory(0);
        model.setReleaseSource("1");
        model.setReleaseType("1");
        model.setReleaseTime("1");
        model.setReceiveStartTime("1");
        model.setReceiveEndTime("1");
        model.setValidStartTime("1");
        model.setValidEndTime("1");
        model.setValidDays(0);
        model.setCouponCodePrefix("1");
        model.setTimeSameActivity("1");


        model.setReleaseQuantity(2000);

        List<CouponReleaseModel> releaseVOs = new ArrayList<>();
        releaseVOs.add(model);


        ActivityModel activityModel = new ActivityModel();
        activityModel.setCouponInfo(new ActivityModel());
        activityModel.setLanguage(new ActivityLanguageModel());
        activityModel.setItemScopeType(0);
        activityModel.setId(0L);
        activityModel.setDomainCode("1");
        activityModel.setTenantCode("1");
        activityModel.setActivityCode("1");
        activityModel.setCustomCondition("1");
        activityModel.setActivityType("1");
        activityModel.setActivityLabel("1");
        activityModel.setActivityName("1");
        activityModel.setActivityDesc("1");
        activityModel.setShowFlag(0);
        activityModel.setActivityRemark("1");
        activityModel.setActivityExpr("1");
        activityModel.setPriority(0);
        activityModel.setTemplateCode("1");
        activityModel.setIncentiveLimitedFlag("1");
        activityModel.setWarmBegin("1");
        activityModel.setWarmEnd("1");
        activityModel.setActivityBegin("1");
        activityModel.setActivityEnd("1");
        activityModel.setPeriodType("1");
        activityModel.setProductSelectionType("1");
        activityModel.setStoreType("1");
        activityModel.setActivityUrl("1");
        activityModel.setActivityStatus("1");
        activityModel.setSponsors("1");
        activityModel.setOpsType("1");
        activityModel.setExclusionKey("1");
        activityModel.setCreateUser("1");
        activityModel.setCreateTime(new Date());
        activityModel.setUpdateUser("1");
        activityModel.setTotalQuantity(0);
        activityModel.setUserLimitMax(0);
        activityModel.setCouponType("1");
        activityModel.setCouponCode("1");
        activityModel.setReserveInventory(0);
        activityModel.setCoolDown("1");
        activityModel.setBackgroundImage("1");
        activityModel.setProductCondition("1");
        activityModel.setRibbonImage("1");
        activityModel.setRibbonPosition("1");
        activityModel.setRibbonText("1");
        activityModel.setCouponCodes(Lists.newArrayList());


        Mockito.when(couponReleaseService.queryReleasesNotReleased()).thenReturn(releaseVOs);

        Mockito.when(couponActivityComponent.findValidActivity(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(activityModel);
        couponReleaseComponent.couponReleaseTimer();

    }




    @Test
    public void couponReleaseTimer_null(){
        CouponReleaseModel model = new CouponReleaseModel();
        model.setTenantCode("1");
        model.setActivityCode("1");
        List<CouponReleaseModel> releaseVOs = new ArrayList<>();
        releaseVOs.add(model);
        Mockito.when(couponReleaseService.queryReleasesNotReleased()).thenReturn(releaseVOs);
        Mockito.when(couponActivityComponent.findValidActivity(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(null);
        couponReleaseComponent.couponReleaseTimer();
    }

    @Test
    public void couponReleaseTimer_empty(){
        Mockito.when(couponReleaseService.queryReleasesNotReleased()).thenReturn(new ArrayList<>());
        couponReleaseComponent.couponReleaseTimer();
    }

    @Test(expected = PromotionException.class)
    public void createInnerCoupons(){
        CouponReleaseModel releaseModel = new CouponReleaseModel();
        releaseModel.setReleaseQuantity(1111111111);
        ActivityModel activityModel = new ActivityModel();
        activityModel.setActivityCode("1");
        Mockito.when(activityFuncRankService.getRankListByActivityCode(Mockito.any())).thenReturn(new ArrayList<>());
        couponReleaseComponent.createInnerCoupons(releaseModel,activityModel);
    }

    @Test
    public void checkQuantity(){
        ReleaseCouponInDTO dto = new ReleaseCouponInDTO();
        dto.setReleaseQuantity(1);
        ActivityModel activityModel = new ActivityModel();
        activityModel.setActivityCode("1");
        activityModel.setTotalQuantity(4);
        couponReleaseComponent.checkQuantity(dto,activityModel,2L);
    }


    @Test(expected = PromotionException.class)
    public void checkQuantity_more(){
        ReleaseCouponInDTO dto = new ReleaseCouponInDTO();
        dto.setReleaseQuantity(1);
        ActivityModel activityModel = new ActivityModel();
        activityModel.setActivityCode("1");
        activityModel.setTotalQuantity(4);
        couponReleaseComponent.checkQuantity(dto,activityModel,5L);
    }
    @Test
    public void checkQuantity_null_1(){
        ReleaseCouponInDTO dto = new ReleaseCouponInDTO();
        dto.setReleaseQuantity(1);
        ActivityModel activityModel = new ActivityModel();
        activityModel.setActivityCode("1");
        activityModel.setTotalQuantity(4);
        couponReleaseComponent.checkQuantity(dto,activityModel,null);
    }

    @Test(expected = PromotionException.class)
    public void checkQuantity_null_2(){
        ReleaseCouponInDTO dto = new ReleaseCouponInDTO();
        dto.setReleaseQuantity(6);
        ActivityModel activityModel = new ActivityModel();
        activityModel.setActivityCode("1");
        activityModel.setTotalQuantity(4);
        couponReleaseComponent.checkQuantity(dto,activityModel,null);
    }

}
