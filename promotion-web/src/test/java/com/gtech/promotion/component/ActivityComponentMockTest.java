package com.gtech.promotion.component;

import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.gtech.basic.masterdata.client.MasterDataClient;
import com.gtech.basic.masterdata.response.JsonResult;
import com.gtech.commons.exception.GTechBaseException;
import com.gtech.promotion.code.activity.ActivityStatusEnum;
import com.gtech.promotion.code.activity.ActivityTypeEnum;
import com.gtech.promotion.code.coupon.CouponTypeEnum;
import com.gtech.promotion.component.activity.ActivityCacheDomain;
import com.gtech.promotion.component.activity.ActivityComponentDomain;
import com.gtech.promotion.component.activity.ActivityExpireComponentDomain;
import com.gtech.promotion.component.activity.ActivityPriceComponentDomain;
import com.gtech.promotion.dao.model.activity.*;
import com.gtech.promotion.dao.model.coupon.CouponReleaseModel;
import com.gtech.promotion.dao.model.marketing.MarketingModel;
import com.gtech.promotion.dto.cache.ActivityCacheDTO;
import com.gtech.promotion.dto.cache.ProductSkuDetailDTO;
import com.gtech.promotion.dto.in.activity.ActivityExtendInDTO;
import com.gtech.promotion.dto.in.activity.ActivityUpdateDTO;
import com.gtech.promotion.dto.out.activity.TPromoActivityOutDTO;
import com.gtech.promotion.dto.out.product.ProductDetailInDTO;
import com.gtech.promotion.exception.PromotionException;
import com.gtech.promotion.helper.RedisOpsHelper;
import com.gtech.promotion.service.activity.*;
import com.gtech.promotion.service.coupon.PromoCouponActivityService;
import com.gtech.promotion.service.coupon.PromoCouponCodeUserService;
import com.gtech.promotion.service.coupon.PromoCouponInnerCodeService;
import com.gtech.promotion.service.coupon.PromoCouponReleaseService;
import com.gtech.promotion.service.mongo.activity.TPromoProductService;
import com.gtech.promotion.utils.DateUtil;
import com.gtech.promotion.vo.bean.*;
import com.gtech.promotion.vo.param.activity.*;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.*;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ActivityComponentMockTest {

    @InjectMocks
    private ActivityComponentDomain activityComponentDomain;
    @Mock
    private ActivityFuncParamService activityFuncParamService;
    @Mock
    private ActivityService activityService;
    @Mock
    private TemplateService templateService;
    @Mock
    private ActivityProductDetailService activityProductDetailService;
    @Mock
    private TPromoProductService productService;
    @Mock
    private ActivityCacheDomain activityCacheDomain;
    @Mock
    private OperationLogService operationLogService;
    @Mock
    private TPromoIncentiveLimitedService incentiveLimitedService;
    @Mock
    private RedisOpsHelper incentiveLimitedHelper;
    @Mock
    private PromoCouponActivityService couponActivityService;
    @Mock
    private PromoCouponReleaseService couponReleaseService;
    @Mock
    private PromoCouponInnerCodeService couponInnerCodeService;
    @Mock
    private PromoCouponCodeUserService couponCodeUserService;
    @Mock
    private ActivityExpireComponentDomain activityExpireComponentDomain;
    @Mock
    private ActivityFuncRankService activityFuncRankService;
    @Mock
    private QualificationService qualificationService;
    @Mock
    private GiveawayService giveawayService;
    @Mock
    private ActivityPeriodService activityPeriodService;
    @Mock
    private ActivityLanguageService languageService;
    @Mock
    private ActivityStoreService activityStoreService;
    @Mock
    private MasterDataClient masterDataClient;
    @Mock
    private ActivityPriceComponentDomain activityPriceComponentDomain;
    @Mock
    private com.gtech.promotion.service.marketing.MarketingService MarketingService;
    @Mock
    private ActivityProductDetailService productDetailService;

    @Test
    public void updateActivityProduct_product() {
        ActivityUpdateDTO param = new ActivityUpdateDTO();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setActivityCode("1");
        param.setTemplateCode("0101020103010401");
        ArrayList<ProductScope> products = new ArrayList<>();
        products.add(ProductScope.builder().categoryCode("1").build());
        param.setProducts(products);
        param.setActivityType("01");

        ActivityModel activity = new ActivityModel();
        activity.setActivityStatus(ActivityStatusEnum.EFFECTIVE.code());
        activity.setActivityBegin(DateUtil.getBeforeMonth(-1, DateUtil.DATETIMESTOREFORMAT));
        activity.setActivityEnd(DateUtil.getBeforeMonth(1, DateUtil.DATETIMESTOREFORMAT));

        TemplateModel template = new TemplateModel();
        template.setTemplateCode(param.getTemplateCode());
        template.setTagCode("12");
        when(activityService.findActivity(any(), any(), any())).thenReturn(activity);
        when(templateService.getTemplateByCode(any())).thenReturn(template);
        when(activityService.updatePromoActivity(any())).thenReturn(1);
        //Mockito.when(activityProductDetailService.deleteProductDetails(Mockito.any(),Mockito.any())).thenReturn(1);
        when(productService.getProducts(any())).thenReturn(new ArrayList<>());
        when(productService.deleteProducts(any())).thenReturn(1);
        when(productService.insertProducts(any(), any(), any())).thenReturn(1);
        Mockito.doNothing().when(activityCacheDomain).putCache(any(), any());
        when(operationLogService.insertLog(any(), any())).thenReturn(1);
        int i = activityComponentDomain.updateActivityProduct(param);
        Assert.assertEquals(1, i);
    }

    @Test
    public void updateActivityProduct_product_detail() {
        ActivityUpdateDTO param = new ActivityUpdateDTO();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setActivityCode("1");
        param.setTemplateCode("0101020103010401");
        ArrayList<ProductDetailInDTO> productDetails = new ArrayList<>();
        ArrayList<ProductDetailInDTO> productDetailBlackList = new ArrayList<>();
        ProductDetailInDTO productDetailInDTO = new ProductDetailInDTO();
        productDetailInDTO.setSkuCode("1");
        productDetailInDTO.setProductCode("1");
        productDetailInDTO.setSeqNum(1);
        productDetails.add(productDetailInDTO);
        productDetailBlackList.add(productDetailInDTO);
        param.setProductDetails(productDetails);
        param.setProductDetailBlackList(productDetailBlackList);

        ActivityModel activity = new ActivityModel();
        activity.setActivityStatus(ActivityStatusEnum.EFFECTIVE.code());
        activity.setActivityBegin(DateUtil.getBeforeMonth(-1, DateUtil.DATETIMESTOREFORMAT));
        activity.setActivityEnd(DateUtil.getBeforeMonth(1, DateUtil.DATETIMESTOREFORMAT));

        TemplateModel template = new TemplateModel();
        template.setTemplateCode(param.getTemplateCode());
        template.setTagCode("12");
        when(activityService.findActivity(any(), any(), any())).thenReturn(activity);
        when(templateService.getTemplateByCode(any())).thenReturn(template);
        when(activityService.updatePromoActivity(any())).thenReturn(1);
        when(activityProductDetailService.deleteProductDetails(any(), any())).thenReturn(1);
        when(activityProductDetailService.deleteProductSkuBySeqNum(any(), any())).thenReturn(1);
        when(activityProductDetailService.insertProductDetail(any())).thenReturn(1);
        when(productService.getProducts(any())).thenReturn(new ArrayList<>());
        when(productService.deleteProducts(any())).thenReturn(1);
        Mockito.doNothing().when(activityCacheDomain).putCache(any(), any());
        when(operationLogService.insertLog(any(), any())).thenReturn(1);
        int i = activityComponentDomain.updateActivityProduct(param);
        Assert.assertEquals(1, i);
    }

    @Test
    public void updateActivityProductDetailBlackList() {
        String tenantCode = "1";
        List<ProductDetail> productDetailBlackList = new ArrayList<>();

        List<ActivityModel> list = new ArrayList<>();
        ActivityModel activityModel = new ActivityModel();
        activityModel.setActivityCode("1");
        activityModel.setActivityBegin(DateUtil.getBeforeMonth(-1, DateUtil.DATETIMESTOREFORMAT));
        activityModel.setActivityEnd(DateUtil.getBeforeMonth(1, DateUtil.DATETIMESTOREFORMAT));
        list.add(activityModel);
        when(activityService.queryEffectiveActivityByTenantCode(any())).thenReturn(list);
        //Mockito.when(activityProductDetailService.insertProductSku(Mockito.any())).thenReturn(1);

        int i = activityComponentDomain.updateActivityProductDetailBlackList(tenantCode, productDetailBlackList);

        Assert.assertEquals(1, i);
    }

    @Test(expected = GTechBaseException.class)
    public void updateActivityProductDetailBlackList_null() {
        String tenantCode = "1";
        List<ProductDetail> productDetailBlackList = new ArrayList<>();

        when(activityService.queryEffectiveActivityByTenantCode(any())).thenReturn(new ArrayList<>());

        int i = activityComponentDomain.updateActivityProductDetailBlackList(tenantCode, productDetailBlackList);
    }

    @Test
    public void updateActivityProductDetailBlackList_Not_null() {
        String tenantCode = "1";
        List<ProductDetail> productDetailBlackList = new ArrayList<>();
        ProductDetail productDetail = new ProductDetail();
        productDetail.setSeqNum(1);
        productDetail.setProductCode("1");
        productDetailBlackList.add(productDetail);
        List<ActivityModel> list = new ArrayList<>();
        ActivityModel activityModel = new ActivityModel();
        activityModel.setActivityCode("1");
        activityModel.setActivityBegin(DateUtil.getBeforeMonth(-1, DateUtil.DATETIMESTOREFORMAT));
        activityModel.setActivityEnd(DateUtil.getBeforeMonth(1, DateUtil.DATETIMESTOREFORMAT));
        list.add(activityModel);

        when(activityService.queryEffectiveActivityByTenantCode(any())).thenReturn(list);

        int i = activityComponentDomain.updateActivityProductDetailBlackList(tenantCode, productDetailBlackList);
        Assert.assertEquals(1, i);
    }

    @Test(expected = PromotionException.class)
    public void activityExtend_status_error() {
        ActivityExtendInDTO dto = new ActivityExtendInDTO();
        ActivityModel activity = new ActivityModel();
        activity.setActivityStatus(ActivityStatusEnum.END.code());
        when(activityService.findActivityByActivityCode(any(), any())).thenReturn(activity);
        activityComponentDomain.activityExtend(dto);
    }

    @Test(expected = PromotionException.class)
    public void activityExtend_end_time_error() {
        ActivityExtendInDTO dto = new ActivityExtendInDTO();
        Date dateTime = new Date();
        String dateString = DateUtil.getDateString(DateUtil.addMinutes(dateTime, 10), DateUtil.DATETIMESTOREFORMAT);
        dto.setEndTime(dateString);
        ActivityModel activity = new ActivityModel();
        activity.setActivityEnd(dateString);
        activity.setActivityStatus(ActivityStatusEnum.END.code());
        when(activityService.findActivityByActivityCode(any(), any())).thenReturn(activity);
        activityComponentDomain.activityExtend(dto);
    }

    @Test
    public void activityExtend() {
        ActivityExtendInDTO dto = new ActivityExtendInDTO();
        Date dateTime = new Date();
        String activityEnd = DateUtil.getDateString(DateUtil.addMinutes(dateTime, 10), DateUtil.DATETIMESTOREFORMAT);
        String extendEnd = DateUtil.getDateString(DateUtil.addMinutes(dateTime, 11), DateUtil.DATETIMESTOREFORMAT);
        String activityBegin = DateUtil.getDateString(DateUtil.addMinutes(dateTime, 1), DateUtil.DATETIMESTOREFORMAT);
        dto.setEndTime(extendEnd);
        ActivityModel activity = new ActivityModel();
        activity.setActivityEnd(activityEnd);
        activity.setActivityBegin(activityBegin);
        activity.setActivityStatus(ActivityStatusEnum.EFFECTIVE.code());
        activity.setActivityType(ActivityTypeEnum.COUPON.code());
        List<TPromoIncentiveLimitedVO> limitedListByActivityCode = new ArrayList<>();
        limitedListByActivityCode.add(new TPromoIncentiveLimitedVO());
        ActivityModel couponActivity = new ActivityModel();
        couponActivity.setCouponType(CouponTypeEnum.PROMOTION_CODE.code());
        List<CouponReleaseModel> list = new ArrayList<>();
        list.add(new CouponReleaseModel());

        when(activityService.findActivityByActivityCode(any(), any())).thenReturn(activity);
        when(activityService.updatePromoActivity(any())).thenReturn(1);
        when(incentiveLimitedService.getLimitedListByActivityCode(any())).thenReturn(limitedListByActivityCode);
        when(incentiveLimitedHelper.extendKey(any(), Mockito.anyLong())).thenReturn(true);
        when(couponActivityService.findCouponActivity(any(), any())).thenReturn(couponActivity);
        when(couponReleaseService.queryCouponRelease(any())).thenReturn(list);
//        when(couponReleaseService.updateCouponReleaseByReleaseCode(any())).thenReturn(1);
//        when(couponInnerCodeService.updateInnerCouponEndTime(any(), any(), any(), any())).thenReturn(1);
        when(operationLogService.insertLog(any(), any())).thenReturn(1);
        int i = activityComponentDomain.activityExtend(dto);
        Assert.assertEquals(1, i);
    }

    @Test
    public void activityExtend_不是优惠码() {
        ActivityExtendInDTO dto = new ActivityExtendInDTO();
        Date dateTime = new Date();
        String activityEnd = DateUtil.getDateString(DateUtil.addMinutes(dateTime, 10), DateUtil.DATETIMESTOREFORMAT);
        String extendEnd = DateUtil.getDateString(DateUtil.addMinutes(dateTime, 11), DateUtil.DATETIMESTOREFORMAT);
        String activityBegin = DateUtil.getDateString(DateUtil.addMinutes(dateTime, 1), DateUtil.DATETIMESTOREFORMAT);
        dto.setEndTime(extendEnd);
        ActivityModel activity = new ActivityModel();
        activity.setActivityEnd(activityEnd);
        activity.setActivityBegin(activityBegin);
        activity.setActivityStatus(ActivityStatusEnum.EFFECTIVE.code());
        activity.setActivityType(ActivityTypeEnum.COUPON.code());
        List<TPromoIncentiveLimitedVO> limitedListByActivityCode = new ArrayList<>();
        limitedListByActivityCode.add(new TPromoIncentiveLimitedVO());
        ActivityModel couponActivity = new ActivityModel();
        couponActivity.setCouponType(CouponTypeEnum.ANONYMITY_COUPON.code());

        when(activityService.findActivityByActivityCode(any(), any())).thenReturn(activity);
        when(activityService.updatePromoActivity(any())).thenReturn(1);
        when(incentiveLimitedService.getLimitedListByActivityCode(any())).thenReturn(limitedListByActivityCode);
        when(incentiveLimitedHelper.extendKey(any(), Mockito.anyLong())).thenReturn(true);
        when(couponActivityService.findCouponActivity(any(), any())).thenReturn(couponActivity);
        when(operationLogService.insertLog(any(), any())).thenReturn(1);
        int i = activityComponentDomain.activityExtend(dto);
        Assert.assertEquals(1, i);
    }

    @Test
    public void activityExtend_不是券活动() {
        ActivityExtendInDTO dto = new ActivityExtendInDTO();
        Date dateTime = new Date();
        String activityEnd = DateUtil.getDateString(DateUtil.addMinutes(dateTime, 10), DateUtil.DATETIMESTOREFORMAT);
        String extendEnd = DateUtil.getDateString(DateUtil.addMinutes(dateTime, 11), DateUtil.DATETIMESTOREFORMAT);
        String activityBegin = DateUtil.getDateString(DateUtil.addMinutes(dateTime, -1), DateUtil.DATETIMESTOREFORMAT);
        dto.setEndTime(extendEnd);
        ActivityModel activity = new ActivityModel();
        activity.setActivityEnd(activityEnd);
        activity.setActivityBegin(activityBegin);
        activity.setActivityStatus(ActivityStatusEnum.EFFECTIVE.code());
        activity.setActivityType(ActivityTypeEnum.ACTIVITY.code());
        List<TPromoIncentiveLimitedVO> limitedListByActivityCode = new ArrayList<>();
        limitedListByActivityCode.add(new TPromoIncentiveLimitedVO());

        when(activityService.findActivityByActivityCode(any(), any())).thenReturn(activity);
        when(activityService.updatePromoActivity(any())).thenReturn(1);
        when(incentiveLimitedService.getLimitedListByActivityCode(any())).thenReturn(limitedListByActivityCode);
        when(incentiveLimitedHelper.extendKey(any(), Mockito.anyLong())).thenReturn(true);
        when(operationLogService.insertLog(any(), any())).thenReturn(1);
        Mockito.doNothing().when(activityCacheDomain).extendActivityCacheTime(any());
        int i = activityComponentDomain.activityExtend(dto);
        Assert.assertEquals(1, i);
    }

    @Test
    public void expireActivity() {
        when(activityService.expireActivity()).thenReturn(1);
        int i = activityComponentDomain.expireActivity();
        Assert.assertEquals(1, i);
    }

    @Test
    public void queryValidActivities_null() {
        String tenantCode = "";
        Date date = null;
        List<String> activityCodes = new ArrayList<>();

        List<ActivityModel> activityModels = activityComponentDomain.queryValidActivities(tenantCode, activityCodes, date);
        Assert.assertEquals(0, activityModels.size());
    }

    @Test
    public void queryValidActivities_not_null() {
        String tenantCode = "1";
        Date date = new Date();
        List<String> activityCodes = new ArrayList<>();
        activityCodes.add("1");
        List<ActivityModel> list = new ArrayList<>();
        ActivityModel activityModel = new ActivityModel();
        activityModel.setActivityCode("1");
        activityModel.setActivityStatus("04");
        activityModel.setActivityBegin(DateUtil.getDateString(DateUtil.addMinutes(date, -1), DateUtil.DATETIMESTOREFORMAT));
        activityModel.setActivityEnd(DateUtil.getDateString(DateUtil.addMinutes(date, 10), DateUtil.DATETIMESTOREFORMAT));
        list.add(activityModel);

        when(activityService.queryEffectiveActivity(any(), any())).thenReturn(list);
        List<ActivityModel> activityModels = activityComponentDomain.queryValidActivities(tenantCode, activityCodes, date);
        Assert.assertEquals(1, activityModels.size());
    }

    @Test
    public void findActivityDetail_null() {
        String tenantCode = "1";
        String activityCode = "1";
        String orgCode = "1";
        ActivityModel activityModel = new ActivityModel();

        when(activityService.findActivity(any(), any(), any())).thenReturn(activityModel);
        when(activityFuncRankService.getRankListByActivityCode(any())).thenReturn(new ArrayList<>());
        when(qualificationService.queryQualifications(any(), any())).thenReturn(new ArrayList<>());
        when(giveawayService.getGiftListByActivityCode(any(), any())).thenReturn(new ArrayList<>());
        when(activityPeriodService.findPeriod(any(), any())).thenReturn(new ActivityPeriodModel());
        when(languageService.queryActivityLanguages(any(), any())).thenReturn(new ArrayList<>());
        activityComponentDomain.findActivityDetail(tenantCode, activityCode, orgCode);
    }

    @Test
    public void findActivityDetail_not_null() {
        String tenantCode = "1";
        String activityCode = "1";
        String orgCode = "1";
        ActivityModel activityModel = new ActivityModel();
        activityModel.setIncentiveLimitedFlag("01");
        activityModel.setStoreType("01");
        activityModel.setActivityType("02");
        List<ProductDetail> productDetilsList = new ArrayList<>();
        ProductDetail productDetail1 = new ProductDetail();
        productDetail1.setType(2);
        ProductDetail productDetail2 = new ProductDetail();
        productDetail2.setType(1);
        productDetilsList.add(productDetail1);
        productDetilsList.add(productDetail2);
        ActivityModel couponActivity = new ActivityModel();
        couponActivity.setCouponType("03");

        when(activityService.findActivity(any(), any(), any())).thenReturn(activityModel);
        when(activityFuncRankService.getRankListByActivityCode(any())).thenReturn(new ArrayList<>());
        activityComponentDomain.findActivityDetail(tenantCode, activityCode, orgCode);
    }


    @Test
    public void findActivityByActivityCode() {

        ActivityModel activityModel = new ActivityModel();
        when(activityService.findActivity(any(), any(), any())).thenReturn(activityModel);
        activityComponentDomain.findActivityByActivityCode("", "", "");
    }


    @Test
    public void findValidActivity_tenant_null() {
        activityComponentDomain.findValidActivity("", "1", "1", new Date());
    }


    @Test
    public void findValidActivity_activityCode_null() {
        activityComponentDomain.findValidActivity("1", "", "1", new Date());
    }

    @Test
    public void findValidActivity_data_null() {
        activityComponentDomain.findValidActivity("1", "1", "1", null);
    }

    @Test
    public void findValidActivity_activityModel_not_null() {
        ActivityModel activityModel = new ActivityModel();
        activityModel.setCouponInfo(new ActivityModel());
        activityModel.setLanguage(new ActivityLanguageModel());
        activityModel.setItemScopeType(0);
        activityModel.setId(0L);
        activityModel.setDomainCode("");
        activityModel.setTenantCode("");
        activityModel.setActivityCode("");
        activityModel.setCustomCondition("");
        activityModel.setActivityType("");
        activityModel.setActivityLabel("");
        activityModel.setActivityName("");
        activityModel.setActivityDesc("");
        activityModel.setShowFlag(0);
        activityModel.setActivityRemark("");
        activityModel.setActivityExpr("");
        activityModel.setPriority(0);
        activityModel.setTemplateCode("");
        activityModel.setIncentiveLimitedFlag("");
        activityModel.setWarmBegin("");
        activityModel.setWarmEnd("");
        activityModel.setActivityBegin("");
        activityModel.setActivityEnd("");
        activityModel.setPeriodType("");
        activityModel.setProductSelectionType("");
        activityModel.setStoreType("");
        activityModel.setActivityUrl("");
        activityModel.setActivityStatus("");
        activityModel.setSponsors("");
        activityModel.setOpsType("");
        activityModel.setExclusionKey("");
        activityModel.setCreateUser("");
        activityModel.setCreateTime(new Date());
        activityModel.setUpdateUser("");
        activityModel.setTotalQuantity(0);
        activityModel.setUserLimitMax(0);
        activityModel.setCouponType("");
        activityModel.setCouponCode("");
        activityModel.setReserveInventory(0);
        activityModel.setCoolDown("");
        activityModel.setBackgroundImage("");
        activityModel.setProductCondition("");
        activityModel.setRibbonImage("");
        activityModel.setRibbonPosition("");
        activityModel.setRibbonText("");
        activityModel.setCouponCodes(Lists.newArrayList());

        when(activityService.findEffectiveActivity(any(), any())).thenReturn(activityModel);
        activityComponentDomain.findValidActivity("1", "1", "1", new Date());
    }


    @Test
    public void findValidActivity_language_null() {
        ActivityModel activityModel = new ActivityModel();
        activityModel.setCouponInfo(new ActivityModel());
        activityModel.setLanguage(new ActivityLanguageModel());
        activityModel.setItemScopeType(0);
        activityModel.setId(0L);
        activityModel.setDomainCode("");
        activityModel.setTenantCode("");
        activityModel.setActivityCode("");
        activityModel.setCustomCondition("");
        activityModel.setActivityType("");
        activityModel.setActivityLabel("");
        activityModel.setActivityName("");
        activityModel.setActivityDesc("");
        activityModel.setShowFlag(0);
        activityModel.setActivityRemark("");
        activityModel.setActivityExpr("");
        activityModel.setPriority(0);
        activityModel.setTemplateCode("");
        activityModel.setIncentiveLimitedFlag("");
        activityModel.setWarmBegin("");
        activityModel.setWarmEnd("");
        activityModel.setActivityBegin("");
        activityModel.setActivityEnd("");
        activityModel.setPeriodType("");
        activityModel.setProductSelectionType("");
        activityModel.setStoreType("");
        activityModel.setActivityUrl("");
        activityModel.setActivityStatus("");
        activityModel.setSponsors("");
        activityModel.setOpsType("");
        activityModel.setExclusionKey("");
        activityModel.setCreateUser("");
        activityModel.setCreateTime(new Date());
        activityModel.setUpdateUser("");
        activityModel.setTotalQuantity(0);
        activityModel.setUserLimitMax(0);
        activityModel.setCouponType("");
        activityModel.setCouponCode("");
        activityModel.setReserveInventory(0);
        activityModel.setCoolDown("");
        activityModel.setBackgroundImage("");
        activityModel.setProductCondition("");
        activityModel.setRibbonImage("");
        activityModel.setRibbonPosition("");
        activityModel.setRibbonText("");
        activityModel.setCouponCodes(Lists.newArrayList());

        when(activityService.findEffectiveActivity(any(), any())).thenReturn(activityModel);
        activityComponentDomain.findValidActivity("1", "1", "", new Date());
    }


    @Test
    public void findValidActivity_language_not_null_activityLanguage_not_null() {
        ActivityModel activityModel = new ActivityModel();
        activityModel.setCouponInfo(new ActivityModel());
        activityModel.setLanguage(new ActivityLanguageModel());
        activityModel.setItemScopeType(0);
        activityModel.setId(0L);
        activityModel.setDomainCode("");
        activityModel.setTenantCode("");
        activityModel.setActivityCode("");
        activityModel.setCustomCondition("");
        activityModel.setActivityType("");
        activityModel.setActivityLabel("");
        activityModel.setActivityName("");
        activityModel.setActivityDesc("");
        activityModel.setShowFlag(0);
        activityModel.setActivityRemark("");
        activityModel.setActivityExpr("");
        activityModel.setPriority(0);
        activityModel.setTemplateCode("");
        activityModel.setIncentiveLimitedFlag("");
        activityModel.setWarmBegin("");
        activityModel.setWarmEnd("");
        activityModel.setActivityBegin("");
        activityModel.setActivityEnd("");
        activityModel.setPeriodType("");
        activityModel.setProductSelectionType("");
        activityModel.setStoreType("");
        activityModel.setActivityUrl("");
        activityModel.setActivityStatus("");
        activityModel.setSponsors("");
        activityModel.setOpsType("");
        activityModel.setExclusionKey("");
        activityModel.setCreateUser("");
        activityModel.setCreateTime(new Date());
        activityModel.setUpdateUser("");
        activityModel.setTotalQuantity(0);
        activityModel.setUserLimitMax(0);
        activityModel.setCouponType("");
        activityModel.setCouponCode("");
        activityModel.setReserveInventory(0);
        activityModel.setCoolDown("");
        activityModel.setBackgroundImage("");
        activityModel.setProductCondition("");
        activityModel.setRibbonImage("");
        activityModel.setRibbonPosition("");
        activityModel.setRibbonText("");
        activityModel.setCouponCodes(Lists.newArrayList());

        when(activityService.findEffectiveActivity(any(), any())).thenReturn(activityModel);
        ActivityLanguageModel activityLanguage = new ActivityLanguageModel();
        activityLanguage.setTenantCode("");
        activityLanguage.setActivityCode("");
        activityLanguage.setActivityType("");
        activityLanguage.setActivityLabel("");
        activityLanguage.setActivityName("");
        activityLanguage.setActivityDesc("");
        activityLanguage.setActivityRemark("");
        activityLanguage.setLanguage("");

        when(languageService.findActivityLanguage(any(), any(), any())).thenReturn(activityLanguage);
        activityComponentDomain.findValidActivity("1", "1", "1", new Date());
    }


    @Test
    public void findValidActivity_language_not_null_activityLanguage_null() {
        ActivityModel activityModel = new ActivityModel();
        activityModel.setCouponInfo(new ActivityModel());
        activityModel.setLanguage(new ActivityLanguageModel());
        activityModel.setItemScopeType(0);
        activityModel.setId(0L);
        activityModel.setDomainCode("");
        activityModel.setTenantCode("");
        activityModel.setActivityCode("");
        activityModel.setCustomCondition("");
        activityModel.setActivityType("");
        activityModel.setActivityLabel("");
        activityModel.setActivityName("");
        activityModel.setActivityDesc("");
        activityModel.setShowFlag(0);
        activityModel.setActivityRemark("");
        activityModel.setActivityExpr("");
        activityModel.setPriority(0);
        activityModel.setTemplateCode("");
        activityModel.setIncentiveLimitedFlag("");
        activityModel.setWarmBegin("");
        activityModel.setWarmEnd("");
        activityModel.setActivityBegin("");
        activityModel.setActivityEnd("");
        activityModel.setPeriodType("");
        activityModel.setProductSelectionType("");
        activityModel.setStoreType("");
        activityModel.setActivityUrl("");
        activityModel.setActivityStatus("");
        activityModel.setSponsors("");
        activityModel.setOpsType("");
        activityModel.setExclusionKey("");
        activityModel.setCreateUser("");
        activityModel.setCreateTime(new Date());
        activityModel.setUpdateUser("");
        activityModel.setTotalQuantity(0);
        activityModel.setUserLimitMax(0);
        activityModel.setCouponType("");
        activityModel.setCouponCode("");
        activityModel.setReserveInventory(0);
        activityModel.setCoolDown("");
        activityModel.setBackgroundImage("");
        activityModel.setProductCondition("");
        activityModel.setRibbonImage("");
        activityModel.setRibbonPosition("");
        activityModel.setRibbonText("");
        activityModel.setCouponCodes(Lists.newArrayList());
        when(activityService.findEffectiveActivity(any(), any())).thenReturn(activityModel);
        when(languageService.findActivityLanguage(any(), any(), any())).thenReturn(new ActivityLanguageModel());
        activityComponentDomain.findValidActivity("1", "1", "1", new Date());
    }


    @Test
    public void isEffectiveActivity() {

        ActivityModel activityModel = new ActivityModel();
        activityModel.setCouponInfo(new ActivityModel());
        activityModel.setLanguage(new ActivityLanguageModel());
        activityModel.setItemScopeType(0);
        activityModel.setId(0L);
        activityModel.setDomainCode("");
        activityModel.setTenantCode("");
        activityModel.setActivityCode("");
        activityModel.setCustomCondition("");
        activityModel.setActivityType("");
        activityModel.setActivityLabel("");
        activityModel.setActivityName("");
        activityModel.setActivityDesc("");
        activityModel.setShowFlag(0);
        activityModel.setActivityRemark("");
        activityModel.setActivityExpr("");
        activityModel.setPriority(0);
        activityModel.setTemplateCode("");
        activityModel.setIncentiveLimitedFlag("");
        activityModel.setWarmBegin("");
        activityModel.setWarmEnd("");
        activityModel.setActivityBegin("");
        activityModel.setActivityEnd("");
        activityModel.setPeriodType("");
        activityModel.setProductSelectionType("");
        activityModel.setStoreType("");
        activityModel.setActivityUrl("");
        activityModel.setActivityStatus("04");
        activityModel.setSponsors("");
        activityModel.setOpsType("");
        activityModel.setExclusionKey("");
        activityModel.setCreateUser("");
        activityModel.setCreateTime(new Date());
        activityModel.setUpdateUser("");
        activityModel.setTotalQuantity(0);
        activityModel.setUserLimitMax(0);
        activityModel.setCouponType("");
        activityModel.setCouponCode("");
        activityModel.setReserveInventory(0);
        activityModel.setCoolDown("");
        activityModel.setBackgroundImage("");
        activityModel.setProductCondition("");
        activityModel.setRibbonImage("");
        activityModel.setRibbonPosition("");
        activityModel.setRibbonText("");
        activityModel.setCouponCodes(Lists.newArrayList());
        activityModel.setActivityEnd("20100101010101");
        activityModel.setActivityBegin("20090101010101");


        activityComponentDomain.isEffectiveActivity(activityModel, new Date());


    }


    @Test
    public void loadActivityLanguage_language_null() {
        ActivityModel activityModel = new ActivityModel();
        activityModel.setCouponInfo(new ActivityModel());
        activityModel.setLanguage(new ActivityLanguageModel());
        activityModel.setItemScopeType(0);
        activityModel.setId(0L);
        activityModel.setDomainCode("");
        activityModel.setTenantCode("");
        activityModel.setActivityCode("");
        activityModel.setCustomCondition("");
        activityModel.setActivityType("");
        activityModel.setActivityLabel("");
        activityModel.setActivityName("");
        activityModel.setActivityDesc("");
        activityModel.setShowFlag(0);
        activityModel.setActivityRemark("");
        activityModel.setActivityExpr("");
        activityModel.setPriority(0);
        activityModel.setTemplateCode("");
        activityModel.setIncentiveLimitedFlag("");
        activityModel.setWarmBegin("");
        activityModel.setWarmEnd("");
        activityModel.setActivityBegin("");
        activityModel.setActivityEnd("");
        activityModel.setPeriodType("");
        activityModel.setProductSelectionType("");
        activityModel.setStoreType("");
        activityModel.setActivityUrl("");
        activityModel.setActivityStatus("04");
        activityModel.setSponsors("");
        activityModel.setOpsType("");
        activityModel.setExclusionKey("");
        activityModel.setCreateUser("");
        activityModel.setCreateTime(new Date());
        activityModel.setUpdateUser("");
        activityModel.setTotalQuantity(0);
        activityModel.setUserLimitMax(0);
        activityModel.setCouponType("");
        activityModel.setCouponCode("");
        activityModel.setReserveInventory(0);
        activityModel.setCoolDown("");
        activityModel.setBackgroundImage("");
        activityModel.setProductCondition("");
        activityModel.setRibbonImage("");
        activityModel.setRibbonPosition("");
        activityModel.setRibbonText("");
        activityModel.setCouponCodes(Lists.newArrayList());
        activityModel.setActivityEnd("20100101010101");
        activityModel.setActivityBegin("20090101010101");
        activityComponentDomain.loadActivityLanguage(activityModel, "");

    }

    @Test
    public void loadActivityLanguage_activityModel_null() {

        activityComponentDomain.loadActivityLanguage(new ActivityModel(), "zh_cn");

    }

    @Test
    public void loadActivityLanguage_activityLanguage_null() {
        ActivityModel activityModel = new ActivityModel();
        activityModel.setCouponInfo(new ActivityModel());
        activityModel.setLanguage(new ActivityLanguageModel());
        activityModel.setItemScopeType(0);
        activityModel.setId(0L);
        activityModel.setDomainCode("");
        activityModel.setTenantCode("");
        activityModel.setActivityCode("");
        activityModel.setCustomCondition("");
        activityModel.setActivityType("");
        activityModel.setActivityLabel("");
        activityModel.setActivityName("");
        activityModel.setActivityDesc("");
        activityModel.setShowFlag(0);
        activityModel.setActivityRemark("");
        activityModel.setActivityExpr("");
        activityModel.setPriority(0);
        activityModel.setTemplateCode("");
        activityModel.setIncentiveLimitedFlag("");
        activityModel.setWarmBegin("");
        activityModel.setWarmEnd("");
        activityModel.setActivityBegin("");
        activityModel.setActivityEnd("");
        activityModel.setPeriodType("");
        activityModel.setProductSelectionType("");
        activityModel.setStoreType("");
        activityModel.setActivityUrl("");
        activityModel.setActivityStatus("04");
        activityModel.setSponsors("");
        activityModel.setOpsType("");
        activityModel.setExclusionKey("");
        activityModel.setCreateUser("");
        activityModel.setCreateTime(new Date());
        activityModel.setUpdateUser("");
        activityModel.setTotalQuantity(0);
        activityModel.setUserLimitMax(0);
        activityModel.setCouponType("");
        activityModel.setCouponCode("");
        activityModel.setReserveInventory(0);
        activityModel.setCoolDown("");
        activityModel.setBackgroundImage("");
        activityModel.setProductCondition("");
        activityModel.setRibbonImage("");
        activityModel.setRibbonPosition("");
        activityModel.setRibbonText("");
        activityModel.setCouponCodes(Lists.newArrayList());
        activityModel.setActivityEnd("20100101010101");
        activityModel.setActivityBegin("20090101010101");
        activityComponentDomain.loadActivityLanguage(activityModel, "zh_cn");

    }


    @Test
    public void loadActivityLanguage_activityLanguage_not_null() {
        ActivityModel activityModel = new ActivityModel();
        activityModel.setCouponInfo(new ActivityModel());
        activityModel.setLanguage(new ActivityLanguageModel());
        activityModel.setItemScopeType(0);
        activityModel.setId(0L);
        activityModel.setDomainCode("");
        activityModel.setTenantCode("");
        activityModel.setActivityCode("");
        activityModel.setCustomCondition("");
        activityModel.setActivityType("");
        activityModel.setActivityLabel("");
        activityModel.setActivityName("");
        activityModel.setActivityDesc("");
        activityModel.setShowFlag(0);
        activityModel.setActivityRemark("");
        activityModel.setActivityExpr("");
        activityModel.setPriority(0);
        activityModel.setTemplateCode("");
        activityModel.setIncentiveLimitedFlag("");
        activityModel.setWarmBegin("");
        activityModel.setWarmEnd("");
        activityModel.setActivityBegin("");
        activityModel.setActivityEnd("");
        activityModel.setPeriodType("");
        activityModel.setProductSelectionType("");
        activityModel.setStoreType("");
        activityModel.setActivityUrl("");
        activityModel.setActivityStatus("04");
        activityModel.setSponsors("");
        activityModel.setOpsType("");
        activityModel.setExclusionKey("");
        activityModel.setCreateUser("");
        activityModel.setCreateTime(new Date());
        activityModel.setUpdateUser("");
        activityModel.setTotalQuantity(0);
        activityModel.setUserLimitMax(0);
        activityModel.setCouponType("");
        activityModel.setCouponCode("");
        activityModel.setReserveInventory(0);
        activityModel.setCoolDown("");
        activityModel.setBackgroundImage("");
        activityModel.setProductCondition("");
        activityModel.setRibbonImage("");
        activityModel.setRibbonPosition("");
        activityModel.setRibbonText("");
        activityModel.setCouponCodes(Lists.newArrayList());
        activityModel.setActivityEnd("20100101010101");
        activityModel.setActivityBegin("20090101010101");
        ActivityLanguageModel activityLanguageModel = new ActivityLanguageModel();
        activityLanguageModel.setTenantCode("");
        activityLanguageModel.setActivityCode("");
        activityLanguageModel.setActivityType("");
        activityLanguageModel.setActivityLabel("");
        activityLanguageModel.setActivityName("");
        activityLanguageModel.setActivityDesc("");
        activityLanguageModel.setActivityRemark("");
        activityLanguageModel.setLanguage("");

        when(languageService.findActivityLanguage(any(), any(), any())).thenReturn(activityLanguageModel);
        activityComponentDomain.loadActivityLanguage(activityModel, "zh_cn");

    }


    @Test(expected = PromotionException.class)
    public void updatePromoActivity_activity_null() {

        ActivityUpdateDTO updateActivity = new ActivityUpdateDTO();
        updateActivity.setActivityCode("");
        updateActivity.setDomainCode("");
        updateActivity.setTenantCode("");
        updateActivity.setActivityName("");
        updateActivity.setCustomConditions(Lists.newArrayList());
        updateActivity.setSponsors("");
        updateActivity.setOpsType("");
        updateActivity.setActivityType("");
        updateActivity.setActivityLanguages(Lists.newArrayList());
        updateActivity.setShowFlag(0);
        updateActivity.setWarmBegin("");
        updateActivity.setWarmEnd("");
        updateActivity.setActivityBegin("");
        updateActivity.setActivityEnd("");
        updateActivity.setActivityUrl("");
        updateActivity.setPriority(0);
        updateActivity.setSkuToken("");
        updateActivity.setProductSelectionType("");
        updateActivity.setItemScopeType(0);
        updateActivity.setTemplateCode("");
        updateActivity.setIncentiveLimitedFlag("");
        updateActivity.setIncentiveLimiteds(Lists.newArrayList());
        updateActivity.setQualifications(Lists.newArrayList());
        updateActivity.setStoreType("");
        updateActivity.setChannelStores(Lists.newArrayList());
        updateActivity.setGiveaways(Lists.newArrayList());
        updateActivity.setFuncParams(Lists.newArrayList());
        updateActivity.setProducts(Lists.newArrayList());
        updateActivity.setProductDetails(Lists.newArrayList());
        updateActivity.setProductDetailBlackList(Lists.newArrayList());
        updateActivity.setCreateUser("");
        updateActivity.setOperateLastName("");
        updateActivity.setOperateFirstName("");
        updateActivity.setUpdateUser("");
        updateActivity.setCoolDown("");
        updateActivity.setBackgroundImage("");
        updateActivity.setProductCondition("");
        updateActivity.setActivityPeriod(new ActivityPeriod());
        updateActivity.setRibbonImage("");
        updateActivity.setRibbonPosition("");
        updateActivity.setRibbonText("");


        activityComponentDomain.updatePromoActivity(updateActivity);

    }


    @Test
    public void updatePromoActivity_activity_not_null() {

        ActivityUpdateDTO updateActivity = new ActivityUpdateDTO();
        updateActivity.setActivityCode("");
        updateActivity.setDomainCode("");
        updateActivity.setTenantCode("");
        updateActivity.setActivityName("");
        updateActivity.setCustomConditions(Lists.newArrayList());
        updateActivity.setSponsors("");
        updateActivity.setOpsType("");
        updateActivity.setActivityType("");
        updateActivity.setActivityLanguages(Lists.newArrayList());
        updateActivity.setShowFlag(0);
        updateActivity.setWarmBegin("");
        updateActivity.setWarmEnd("");
        updateActivity.setActivityBegin("");
        updateActivity.setActivityEnd("");
        updateActivity.setActivityUrl("");
        updateActivity.setPriority(0);
        updateActivity.setSkuToken("");
        updateActivity.setProductSelectionType("");
        updateActivity.setItemScopeType(0);
        updateActivity.setTemplateCode("");
        updateActivity.setIncentiveLimitedFlag("");
        updateActivity.setIncentiveLimiteds(Lists.newArrayList());
        updateActivity.setQualifications(Lists.newArrayList());
        updateActivity.setStoreType("");
        updateActivity.setChannelStores(Lists.newArrayList());
        updateActivity.setGiveaways(Lists.newArrayList());
        updateActivity.setFuncParams(Lists.newArrayList());
        updateActivity.setProducts(Lists.newArrayList());
        updateActivity.setProductDetails(Lists.newArrayList());
        updateActivity.setProductDetailBlackList(Lists.newArrayList());
        updateActivity.setCreateUser("");
        updateActivity.setOperateLastName("");
        updateActivity.setOperateFirstName("");
        updateActivity.setUpdateUser("");
        updateActivity.setCoolDown("");
        updateActivity.setBackgroundImage("");
        updateActivity.setProductCondition("");
        updateActivity.setActivityPeriod(new ActivityPeriod());
        updateActivity.setRibbonImage("");
        updateActivity.setRibbonPosition("");
        updateActivity.setRibbonText("");

        ActivityModel activity = new ActivityModel();
        activity.setCouponInfo(new ActivityModel());
        activity.setLanguage(new ActivityLanguageModel());
        activity.setItemScopeType(0);
        activity.setId(0L);
        activity.setDomainCode("");
        activity.setTenantCode("");
        activity.setActivityCode("");
        activity.setCustomCondition("");
        activity.setActivityType("");
        activity.setActivityLabel("");
        activity.setActivityName("");
        activity.setActivityDesc("");
        activity.setShowFlag(0);
        activity.setActivityRemark("");
        activity.setActivityExpr("");
        activity.setPriority(0);
        activity.setTemplateCode("");
        activity.setIncentiveLimitedFlag("");
        activity.setWarmBegin("");
        activity.setWarmEnd("");
        activity.setActivityBegin("");
        activity.setActivityEnd("");
        activity.setPeriodType("");
        activity.setProductSelectionType("");
        activity.setStoreType("");
        activity.setActivityUrl("");
        activity.setActivityStatus("");
        activity.setSponsors("");
        activity.setOpsType("");
        activity.setExclusionKey("");
        activity.setCreateUser("");
        activity.setCreateTime(new Date());
        activity.setUpdateUser("");
        activity.setTotalQuantity(0);
        activity.setUserLimitMax(0);
        activity.setCouponType("");
        activity.setCouponCode("");
        activity.setReserveInventory(0);
        activity.setCoolDown("");
        activity.setBackgroundImage("");
        activity.setProductCondition("");
        activity.setRibbonImage("");
        activity.setRibbonPosition("");
        activity.setRibbonText("");
        activity.setCouponCodes(Lists.newArrayList());
        activity.setActivityEnd("20100101010101");
        activity.setActivityBegin("20090101010101");
        activity.setActivityStatus("04");

        when(activityService.findActivity(any(), any(), any())).thenReturn(activity);
        try {
            activityComponentDomain.updatePromoActivity(updateActivity);
        } catch (Exception e) {

        }


    }

    @Test
    public void updatePromoActivity_activity_isNeedToDoExpire() {

        ActivityUpdateDTO updateActivity = new ActivityUpdateDTO();
        updateActivity.setActivityCode("");
        updateActivity.setDomainCode("");
        updateActivity.setTenantCode("");
        updateActivity.setActivityName("");
        updateActivity.setCustomConditions(Lists.newArrayList());
        updateActivity.setSponsors("");
        updateActivity.setOpsType("");
        updateActivity.setActivityType("");
        updateActivity.setActivityLanguages(Lists.newArrayList());
        updateActivity.setShowFlag(0);
        updateActivity.setWarmBegin("");
        updateActivity.setWarmEnd("");
        updateActivity.setActivityBegin("");
        updateActivity.setActivityEnd("");
        updateActivity.setActivityUrl("");
        updateActivity.setPriority(0);
        updateActivity.setSkuToken("");
        updateActivity.setProductSelectionType("");
        updateActivity.setItemScopeType(0);
        updateActivity.setTemplateCode("");
        updateActivity.setIncentiveLimitedFlag("");
        updateActivity.setIncentiveLimiteds(Lists.newArrayList());
        updateActivity.setQualifications(Lists.newArrayList());
        updateActivity.setStoreType("");
        updateActivity.setChannelStores(Lists.newArrayList());
        updateActivity.setGiveaways(Lists.newArrayList());
        updateActivity.setFuncParams(Lists.newArrayList());
        updateActivity.setProducts(Lists.newArrayList());
        updateActivity.setProductDetails(Lists.newArrayList());
        updateActivity.setProductDetailBlackList(Lists.newArrayList());
        updateActivity.setCreateUser("");
        updateActivity.setOperateLastName("");
        updateActivity.setOperateFirstName("");
        updateActivity.setUpdateUser("");
        updateActivity.setCoolDown("");
        updateActivity.setBackgroundImage("");
        updateActivity.setProductCondition("");
        updateActivity.setActivityPeriod(new ActivityPeriod());
        updateActivity.setRibbonImage("");
        updateActivity.setRibbonPosition("");
        updateActivity.setRibbonText("");

        ActivityLanguage lang = new ActivityLanguage();
        lang.setActivityShortDesc("12");
        lang.setActivityRemark("1");
        lang.setActivityName("1");
        lang.setActivityLabel("1");
        lang.setActivityDesc("1");
        lang.setLanguage("zh_cn");


        List<ActivityLanguage> activityLanguages = new ArrayList<>();
        activityLanguages.add(lang);

        updateActivity.setActivityLanguages(activityLanguages);

        ActivityModel activity = new ActivityModel();
        activity.setCouponInfo(new ActivityModel());
        activity.setLanguage(new ActivityLanguageModel());
        activity.setItemScopeType(0);
        activity.setId(0L);
        activity.setDomainCode("");
        activity.setTenantCode("");
        activity.setActivityCode("");
        activity.setCustomCondition("");
        activity.setActivityType("");
        activity.setActivityLabel("");
        activity.setActivityName("");
        activity.setActivityDesc("");
        activity.setShowFlag(0);
        activity.setActivityRemark("");
        activity.setActivityExpr("");
        activity.setPriority(0);
        activity.setTemplateCode("");
        activity.setIncentiveLimitedFlag("");
        activity.setWarmBegin("");
        activity.setWarmEnd("");
        activity.setActivityBegin("");
        activity.setActivityEnd("");
        activity.setPeriodType("");
        activity.setProductSelectionType("");
        activity.setStoreType("");
        activity.setActivityUrl("");
        activity.setActivityStatus("");
        activity.setSponsors("");
        activity.setOpsType("");
        activity.setExclusionKey("");
        activity.setCreateUser("");
        activity.setCreateTime(new Date());
        activity.setUpdateUser("");
        activity.setTotalQuantity(0);
        activity.setUserLimitMax(0);
        activity.setCouponType("");
        activity.setCouponCode("");
        activity.setReserveInventory(0);
        activity.setCoolDown("");
        activity.setBackgroundImage("");
        activity.setProductCondition("");
        activity.setRibbonImage("");
        activity.setRibbonPosition("");
        activity.setRibbonText("");
        activity.setCouponCodes(Lists.newArrayList());
        activity.setActivityEnd("20400101010101");
        activity.setActivityBegin("20300101010101");
        activity.setActivityStatus("04");

        when(activityService.findActivity(any(), any(), any())).thenReturn(activity);

        TemplateModel template = new TemplateModel();
        template.setTemplateCode("");
        template.setTemplateName("");
        template.setTagCode("1");
        template.setTemplateDesc("");


        when(templateService.getTemplateByCode(any())).thenReturn(template);
        activityComponentDomain.updatePromoActivity(updateActivity);

    }


    @Test
    public void updatePromoActivity_update_true() {

        ActivityUpdateDTO updateActivity = new ActivityUpdateDTO();
        updateActivity.setActivityCode("");
        updateActivity.setDomainCode("");
        updateActivity.setTenantCode("");
        updateActivity.setActivityName("");
        updateActivity.setCustomConditions(Lists.newArrayList());
        updateActivity.setSponsors("");
        updateActivity.setOpsType("");
        updateActivity.setActivityType("");
        updateActivity.setActivityLanguages(Lists.newArrayList());
        updateActivity.setShowFlag(0);
        updateActivity.setWarmBegin("");
        updateActivity.setWarmEnd("");
        updateActivity.setActivityBegin("");
        updateActivity.setActivityEnd("");
        updateActivity.setActivityUrl("");
        updateActivity.setPriority(0);
        updateActivity.setSkuToken("");
        updateActivity.setProductSelectionType("");
        updateActivity.setItemScopeType(0);
        updateActivity.setTemplateCode("");
        updateActivity.setIncentiveLimitedFlag("");
        updateActivity.setIncentiveLimiteds(Lists.newArrayList());
        updateActivity.setQualifications(Lists.newArrayList());
        updateActivity.setStoreType("");
        updateActivity.setChannelStores(Lists.newArrayList());
        updateActivity.setGiveaways(Lists.newArrayList());
        updateActivity.setFuncParams(Lists.newArrayList());
        updateActivity.setProducts(Lists.newArrayList());
        updateActivity.setProductDetails(Lists.newArrayList());
        updateActivity.setProductDetailBlackList(Lists.newArrayList());
        updateActivity.setCreateUser("");
        updateActivity.setOperateLastName("");
        updateActivity.setOperateFirstName("");
        updateActivity.setUpdateUser("");
        updateActivity.setCoolDown("");
        updateActivity.setBackgroundImage("");
        updateActivity.setProductCondition("");
        updateActivity.setActivityPeriod(new ActivityPeriod());
        updateActivity.setRibbonImage("");
        updateActivity.setRibbonPosition("");
        updateActivity.setRibbonText("");
        updateActivity.setTenantCode("123");
        updateActivity.setActivityName("123");
        updateActivity.setActivityType("01");


        ProductDetailInDTO productDetailInDTO = new ProductDetailInDTO();
        productDetailInDTO.setSeqNum(0);
        productDetailInDTO.setProductCode("");
        productDetailInDTO.setSpuName("");
        productDetailInDTO.setSkuCode("");
        productDetailInDTO.setSkuName("");
        productDetailInDTO.setOrgCode("");
        productDetailInDTO.setOrgName("");
        productDetailInDTO.setPromoPrice("");
        List<ProductDetailInDTO> productDetailBlackList = new ArrayList<>();
        productDetailBlackList.add(productDetailInDTO);
        updateActivity.setProductDetailBlackList(productDetailBlackList);
        List<ProductDetailInDTO> productDetails = new ArrayList<>();
        productDetails.add(productDetailInDTO);
        updateActivity.setProductDetails(productDetails);

        CustomCondition customCondition = new CustomCondition();
        customCondition.setCustomKey("");
        customCondition.setCustomValue("");


        List<CustomCondition> customConditions = new ArrayList<>();
        customConditions.add(customCondition);
        updateActivity.setCustomConditions(customConditions);


        Giveaway giveaway = new Giveaway();
        giveaway.setGiveawayCode("123");
        giveaway.setGiveawayName("123");
        giveaway.setGiveawayNum(0);
        giveaway.setGiveawayType(1);
        giveaway.setOpsType("");
        giveaway.setRankParam(4);

        List<Giveaway> giveaways = new ArrayList<>();
        giveaways.add(giveaway);
        updateActivity.setGiveaways(giveaways);

        FunctionParam param = new FunctionParam();
        param.setFunctionType("01");
        param.setFunctionCode("0101");
        param.setParamType("01");
        param.setParamValue("");
        param.setParamUnit("");
        param.setRankParam(4);


        List<FunctionParam> funcParams = new ArrayList<>();
        funcParams.add(param);
        funcParams.add(param);
        funcParams.add(param);
        funcParams.add(param);
        updateActivity.setFuncParams(funcParams);

        ActivityLanguage lang = new ActivityLanguage();
        lang.setActivityShortDesc("12");
        lang.setActivityRemark("1");
        lang.setActivityName("1");
        lang.setActivityLabel("1");
        lang.setActivityDesc("1");
        lang.setLanguage("zh_cn");


        List<ActivityLanguage> activityLanguages = new ArrayList<>();
        activityLanguages.add(lang);

        updateActivity.setActivityLanguages(activityLanguages);

        ActivityModel activity = new ActivityModel();
        activity.setCouponInfo(new ActivityModel());
        activity.setLanguage(new ActivityLanguageModel());
        activity.setItemScopeType(0);
        activity.setId(0L);
        activity.setDomainCode("");
        activity.setTenantCode("");
        activity.setActivityCode("");
        activity.setCustomCondition("");
        activity.setActivityType("");
        activity.setActivityLabel("");
        activity.setActivityName("");
        activity.setActivityDesc("");
        activity.setShowFlag(0);
        activity.setActivityRemark("");
        activity.setActivityExpr("");
        activity.setPriority(0);
        activity.setTemplateCode("");
        activity.setIncentiveLimitedFlag("");
        activity.setWarmBegin("");
        activity.setWarmEnd("");
        activity.setActivityBegin("");
        activity.setActivityEnd("");
        activity.setPeriodType("");
        activity.setProductSelectionType("");
        activity.setStoreType("");
        activity.setActivityUrl("");
        activity.setActivityStatus("");
        activity.setSponsors("");
        activity.setOpsType("");
        activity.setExclusionKey("");
        activity.setCreateUser("");
        activity.setCreateTime(new Date());
        activity.setUpdateUser("");
        activity.setTotalQuantity(0);
        activity.setUserLimitMax(0);
        activity.setCouponType("");
        activity.setCouponCode("");
        activity.setReserveInventory(0);
        activity.setCoolDown("");
        activity.setBackgroundImage("");
        activity.setProductCondition("");
        activity.setRibbonImage("");
        activity.setRibbonPosition("");
        activity.setRibbonText("");
        activity.setCouponCodes(Lists.newArrayList());
        activity.setActivityEnd("20100101010101");
        activity.setActivityBegin("20300101010101");
        activity.setActivityStatus("04");

        when(activityService.findActivity(any(), any(), any())).thenReturn(activity);

        TemplateModel template = new TemplateModel();
        template.setTemplateCode("1111111111110101");
        template.setTemplateName("1");
        template.setTagCode("06");
        template.setTemplateDesc("1");


        when(templateService.getTemplateByCode(any())).thenReturn(template);
        activityComponentDomain.updatePromoActivity(updateActivity);

    }


    @Test
    public void productDetailList_productDetilsList_null() {


        List<ProductDetail> productDetilsList = new ArrayList<>();
        List<ProductDetail> productDetils = new ArrayList<>();
        List<ProductDetail> productDetilBlackList = new ArrayList<>();
        activityComponentDomain.productDetailList(productDetilsList, productDetils, productDetilBlackList);

    }


    @Test
    public void productDetailList_type_not_null() {

        ProductDetail productDetail = new ProductDetail();
        productDetail.setSeqNum(0);
        productDetail.setProductCode("");
        productDetail.setSpuName("");
        productDetail.setType(2);
        productDetail.setSkuCode("");
        productDetail.setSkuName("");
        productDetail.setPromoPrice("");
        productDetail.setOrgCode("");
        productDetail.setOrgName("");


        List<ProductDetail> productDetilsList = new ArrayList<>();
        productDetilsList.add(productDetail);
        List<ProductDetail> productDetils = new ArrayList<>();
        List<ProductDetail> productDetilBlackList = new ArrayList<>();
        activityComponentDomain.productDetailList(productDetilsList, productDetils, productDetilBlackList);

    }

    @Test
    public void productDetailList_type_1() {

        ProductDetail productDetail = new ProductDetail();
        productDetail.setSeqNum(0);
        productDetail.setProductCode("");
        productDetail.setSpuName("");
        productDetail.setType(1);
        productDetail.setSkuCode("");
        productDetail.setSkuName("");
        productDetail.setPromoPrice("");
        productDetail.setOrgCode("");
        productDetail.setOrgName("");


        List<ProductDetail> productDetilsList = new ArrayList<>();
        productDetilsList.add(productDetail);
        List<ProductDetail> productDetils = new ArrayList<>();
        List<ProductDetail> productDetilBlackList = new ArrayList<>();
        activityComponentDomain.productDetailList(productDetilsList, productDetils, productDetilBlackList);

    }


    @Test
    public void getRoundingModeDiscount_1() {

        activityComponentDomain.getRoundingModeDiscount(1);

    }

    @Test
    public void getRoundingModeDiscount_2() {

        activityComponentDomain.getRoundingModeDiscount(2);

    }


    @Test
    public void deleteActivityCorrelation() {
        ActivityModel promoActivityVO = new ActivityModel();
        promoActivityVO.setCouponInfo(new ActivityModel());
        promoActivityVO.setLanguage(new ActivityLanguageModel());
        promoActivityVO.setItemScopeType(0);
        promoActivityVO.setId(0L);
        promoActivityVO.setDomainCode("");
        promoActivityVO.setTenantCode("");
        promoActivityVO.setActivityCode("");
        promoActivityVO.setCustomCondition("");
        promoActivityVO.setActivityType("");
        promoActivityVO.setActivityLabel("");
        promoActivityVO.setActivityName("");
        promoActivityVO.setActivityDesc("");
        promoActivityVO.setShowFlag(0);
        promoActivityVO.setActivityRemark("");
        promoActivityVO.setActivityExpr("");
        promoActivityVO.setPriority(0);
        promoActivityVO.setTemplateCode("");
        promoActivityVO.setIncentiveLimitedFlag("");
        promoActivityVO.setWarmBegin("");
        promoActivityVO.setWarmEnd("");
        promoActivityVO.setActivityBegin("");
        promoActivityVO.setActivityEnd("");
        promoActivityVO.setPeriodType("");
        promoActivityVO.setProductSelectionType("");
        promoActivityVO.setStoreType("");
        promoActivityVO.setActivityUrl("");
        promoActivityVO.setActivityStatus("");
        promoActivityVO.setSponsors("");
        promoActivityVO.setOpsType("");
        promoActivityVO.setExclusionKey("");
        promoActivityVO.setCreateUser("");
        promoActivityVO.setCreateTime(new Date());
        promoActivityVO.setUpdateUser("");
        promoActivityVO.setTotalQuantity(0);
        promoActivityVO.setUserLimitMax(0);
        promoActivityVO.setCouponType("");
        promoActivityVO.setCouponCode("");
        promoActivityVO.setReserveInventory(0);
        promoActivityVO.setCoolDown("");
        promoActivityVO.setBackgroundImage("");
        promoActivityVO.setProductCondition("");
        promoActivityVO.setRibbonImage("");
        promoActivityVO.setRibbonPosition("");
        promoActivityVO.setRibbonText("");
        promoActivityVO.setCouponCodes(Lists.newArrayList());
        promoActivityVO.setActivityStatus("01");

        when(activityService.findActivity(any(), any(), any())).thenReturn(promoActivityVO);

        when(activityService.deleteActivity111(any(), any())).thenReturn(1);
        activityComponentDomain.deleteActivityCorrelation("1", "2");

    }


    @Test
    public void queryPromoListByStore() {
        QueryPromoListByStoreParam param = new QueryPromoListByStoreParam();
        param.setDomainCode("");
        param.setTenantCode("");
        param.setOrgCode("");
        param.setLanguage("");
        param.setActivityStatus("");
        param.setActivityType("");
        param.setPageSize(0);
        param.setPageNum(0);
        param.setOrderItems(Lists.newArrayList());

        TPromoActivityOutDTO tPromoActivityOutDTO = new TPromoActivityOutDTO();
        tPromoActivityOutDTO.setActivityCode("");
        tPromoActivityOutDTO.setActivityName("");
        tPromoActivityOutDTO.setSponsors("");
        tPromoActivityOutDTO.setOpsType("");
        tPromoActivityOutDTO.setTagCode("");
        tPromoActivityOutDTO.setActivityLabel("");
        tPromoActivityOutDTO.setActivityDesc("");
        tPromoActivityOutDTO.setActivityRemark("");
        tPromoActivityOutDTO.setActivityLanguages(Lists.newArrayList());
        tPromoActivityOutDTO.setActivityBegin("");
        tPromoActivityOutDTO.setActivityEnd("");
        tPromoActivityOutDTO.setWarmBegin("");
        tPromoActivityOutDTO.setWarmEnd("");
        tPromoActivityOutDTO.setPeriodType("");
        tPromoActivityOutDTO.setActivityStatus("");
        tPromoActivityOutDTO.setActivityUrl("");
        tPromoActivityOutDTO.setIncentiveLimitedFlag("");
        tPromoActivityOutDTO.setIncentiveLimiteds(Lists.newArrayList());
        tPromoActivityOutDTO.setItemScopeType("");
        tPromoActivityOutDTO.setStoreType("");
        tPromoActivityOutDTO.setPriority(0);
        tPromoActivityOutDTO.setShowFlag(0);
        tPromoActivityOutDTO.setChannelStores(Lists.newArrayList());
        tPromoActivityOutDTO.setGiveaways(Lists.newArrayList());
        tPromoActivityOutDTO.setProducts(Lists.newArrayList());
        tPromoActivityOutDTO.setProductDetils(Lists.newArrayList());
        tPromoActivityOutDTO.setProductDetilBlackList(Lists.newArrayList());
        tPromoActivityOutDTO.setQualifications(Lists.newArrayList());
        tPromoActivityOutDTO.setTemplateCode("");
        tPromoActivityOutDTO.setActivityPeriod(new ActivityPeriod());
        tPromoActivityOutDTO.setCustomCondition("");
        tPromoActivityOutDTO.setProductSelectionType("");
        tPromoActivityOutDTO.setFuncParams(Lists.newArrayList());
        tPromoActivityOutDTO.setCreateTime(new Date());
        tPromoActivityOutDTO.setPromotionCode("");
        tPromoActivityOutDTO.setCouponType("");
        tPromoActivityOutDTO.setCoolDown("");
        tPromoActivityOutDTO.setBackgroundImage("");
        tPromoActivityOutDTO.setProductCondition("");
        tPromoActivityOutDTO.setRibbonImage("");
        tPromoActivityOutDTO.setRibbonPosition("");
        tPromoActivityOutDTO.setRibbonText("");

        ArrayList<TPromoActivityOutDTO> promoActivityOutDTOS = new ArrayList<>();
        promoActivityOutDTOS.add(tPromoActivityOutDTO);

        PageInfo<TPromoActivityOutDTO> pageInfo = new PageInfo<>();

        pageInfo.setList(promoActivityOutDTOS);

        when(activityService.queryPromoListByStore(any())).thenReturn(pageInfo);

        activityComponentDomain.queryPromoListByStore(param);


    }


    @Test
    public void activityLanguage() {
        String language = "1";
        ActivityLanguageModel languageModel = new ActivityLanguageModel();

        languageModel.setLanguage("1");

        List<ActivityLanguageModel> activityLanguageModels = new ArrayList<>();

        activityLanguageModels.add(languageModel);

        Map<String, List<ActivityLanguageModel>> languagesMap = new HashMap<>();
        languagesMap.put("123", activityLanguageModels);


        TPromoActivityOutDTO activityOutDTO = new TPromoActivityOutDTO();
        activityOutDTO.setActivityCode("123");
        activityOutDTO.setActivityName("");
        activityOutDTO.setSponsors("");
        activityOutDTO.setOpsType("");
        activityOutDTO.setTagCode("");
        activityOutDTO.setActivityLabel("");
        activityOutDTO.setActivityDesc("");
        activityOutDTO.setActivityRemark("");
        activityOutDTO.setActivityLanguages(Lists.newArrayList());
        activityOutDTO.setActivityBegin("");
        activityOutDTO.setActivityEnd("");
        activityOutDTO.setWarmBegin("");
        activityOutDTO.setWarmEnd("");
        activityOutDTO.setPeriodType("");
        activityOutDTO.setActivityStatus("");
        activityOutDTO.setActivityUrl("");
        activityOutDTO.setIncentiveLimitedFlag("");
        activityOutDTO.setIncentiveLimiteds(Lists.newArrayList());
        activityOutDTO.setItemScopeType("");
        activityOutDTO.setStoreType("");
        activityOutDTO.setPriority(0);
        activityOutDTO.setShowFlag(0);
        activityOutDTO.setChannelStores(Lists.newArrayList());
        activityOutDTO.setGiveaways(Lists.newArrayList());
        activityOutDTO.setProducts(Lists.newArrayList());
        activityOutDTO.setProductDetils(Lists.newArrayList());
        activityOutDTO.setProductDetilBlackList(Lists.newArrayList());
        activityOutDTO.setQualifications(Lists.newArrayList());
        activityOutDTO.setTemplateCode("");
        activityOutDTO.setActivityPeriod(new ActivityPeriod());
        activityOutDTO.setCustomCondition("");
        activityOutDTO.setProductSelectionType("");
        activityOutDTO.setFuncParams(Lists.newArrayList());
        activityOutDTO.setCreateTime(new Date());
        activityOutDTO.setPromotionCode("");
        activityOutDTO.setCouponType("");
        activityOutDTO.setCoolDown("");
        activityOutDTO.setBackgroundImage("");
        activityOutDTO.setProductCondition("");
        activityOutDTO.setRibbonImage("");
        activityOutDTO.setRibbonPosition("");
        activityOutDTO.setRibbonText("");


        activityComponentDomain.activityLanguage(language, languagesMap, activityOutDTO);


    }


    @Test
    public void activityLanguage_null() {
        String language = "1";
        ActivityLanguageModel languageModel = new ActivityLanguageModel();
        languageModel.setTenantCode("1");
        languageModel.setActivityCode("1");
        languageModel.setActivityType("1");
        languageModel.setActivityLabel("1");
        languageModel.setActivityName("1");
        languageModel.setActivityDesc("1");
        languageModel.setActivityRemark("1");
        languageModel.setLanguage("1");


        List<ActivityLanguageModel> activityLanguageModels = new ArrayList<>();

        activityLanguageModels.add(languageModel);

        Map<String, List<ActivityLanguageModel>> languagesMap = new HashMap<>();
        languagesMap.put("123", activityLanguageModels);


        TPromoActivityOutDTO activityOutDTO = new TPromoActivityOutDTO();
        activityOutDTO.setActivityCode("123");
        activityOutDTO.setActivityName("");
        activityOutDTO.setSponsors("");
        activityOutDTO.setOpsType("");
        activityOutDTO.setTagCode("");
        activityOutDTO.setActivityLabel("");
        activityOutDTO.setActivityDesc("");
        activityOutDTO.setActivityRemark("");
        activityOutDTO.setActivityLanguages(Lists.newArrayList());
        activityOutDTO.setActivityBegin("");
        activityOutDTO.setActivityEnd("");
        activityOutDTO.setWarmBegin("");
        activityOutDTO.setWarmEnd("");
        activityOutDTO.setPeriodType("");
        activityOutDTO.setActivityStatus("");
        activityOutDTO.setActivityUrl("");
        activityOutDTO.setIncentiveLimitedFlag("");
        activityOutDTO.setIncentiveLimiteds(Lists.newArrayList());
        activityOutDTO.setItemScopeType("");
        activityOutDTO.setStoreType("");
        activityOutDTO.setPriority(0);
        activityOutDTO.setShowFlag(0);
        activityOutDTO.setChannelStores(Lists.newArrayList());
        activityOutDTO.setGiveaways(Lists.newArrayList());
        activityOutDTO.setProducts(Lists.newArrayList());
        activityOutDTO.setProductDetils(Lists.newArrayList());
        activityOutDTO.setProductDetilBlackList(Lists.newArrayList());
        activityOutDTO.setQualifications(Lists.newArrayList());
        activityOutDTO.setTemplateCode("");
        activityOutDTO.setActivityPeriod(new ActivityPeriod());
        activityOutDTO.setCustomCondition("");
        activityOutDTO.setProductSelectionType("");
        activityOutDTO.setFuncParams(Lists.newArrayList());
        activityOutDTO.setCreateTime(new Date());
        activityOutDTO.setPromotionCode("");
        activityOutDTO.setCouponType("");
        activityOutDTO.setCoolDown("");
        activityOutDTO.setBackgroundImage("");
        activityOutDTO.setProductCondition("");
        activityOutDTO.setRibbonImage("");
        activityOutDTO.setRibbonPosition("");
        activityOutDTO.setRibbonText("");
        activityComponentDomain.activityLanguage(language, languagesMap, activityOutDTO);
    }


    @Test
    public void queryList() {
        QueryListParam param = new QueryListParam();
        param.setDomainCode("");
        param.setTenantCode("");
        param.setActivityTypes(Lists.newArrayList());
        param.setActivityName("");
        param.setActivityCode("");
        param.setPageSize(0);
        param.setPageNum(0);
        param.setOrderItems(Lists.newArrayList());

        activityComponentDomain.queryList(param);
    }

    @Test
    public void getTenantPrecision() {
        activityPriceComponentDomain.getTenantPrecision("100001");
        JsonResult<String> jsonResult = new JsonResult<>();
        jsonResult.setData("2");
//        when(masterDataClient.getValueValue(any(), any())).thenReturn(jsonResult);
        activityPriceComponentDomain.getTenantPrecision("100001");
//        when(masterDataClient.getValueValue(any(), any())).thenThrow(new RuntimeException());
        activityPriceComponentDomain.getTenantPrecision("100001");
    }

    @Test
    public void addMissingGiveaway() {
        ActivityCacheDomain activityCacheDomain = new ActivityCacheDomain();
        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();
        List<Giveaway> giveawayVOS = new ArrayList<>();
        activityCacheDTO.setGiveaways(giveawayVOS);
        List<ActivityFunctionParamRankModel> rankListByActivityCodes = new ArrayList<>();
        activityCacheDomain.addMissingGiveaway(activityCacheDTO, rankListByActivityCodes);

        Giveaway giveawayVO = new Giveaway();
        giveawayVO.setGiveawayCode("test");
        giveawayVOS.add(giveawayVO);
        activityCacheDomain.addMissingGiveaway(activityCacheDTO, rankListByActivityCodes);

        ActivityFunctionParamRankModel activityFunctionParamRankModel1 = new ActivityFunctionParamRankModel();
        activityFunctionParamRankModel1.setRankParam(1);
        ActivityFunctionParamRankModel activityFunctionParamRankModel2 = new ActivityFunctionParamRankModel();
        activityFunctionParamRankModel2.setRankParam(2);
        rankListByActivityCodes.add(activityFunctionParamRankModel1);
        rankListByActivityCodes.add(activityFunctionParamRankModel2);
        List<Giveaway> giveawayVOS1 = new ArrayList<>();
        Giveaway giveawayVO1 = new Giveaway();
        giveawayVO1.setGiveawayCode("test");
        giveawayVOS1.add(giveawayVO);
        activityCacheDTO.setGiveaways(giveawayVOS1);
        activityCacheDomain.addMissingGiveaway(activityCacheDTO, rankListByActivityCodes);
    }

    @Test
    public void addMissingGiveaway1() {

        List<Giveaway> giveawayVOS = new ArrayList<>();
        List<ActivityFunctionParamRankModel> rankListByActivityCodes = new ArrayList<>();
        activityComponentDomain.addMissingGiveaway(giveawayVOS, rankListByActivityCodes);

        Giveaway giveawayVO = new Giveaway();
        giveawayVO.setGiveawayCode("test");
        giveawayVOS.add(giveawayVO);
        activityComponentDomain.addMissingGiveaway(giveawayVOS, rankListByActivityCodes);

        ActivityFunctionParamRankModel activityFunctionParamRankModel1 = new ActivityFunctionParamRankModel();
        activityFunctionParamRankModel1.setRankParam(1);
        ActivityFunctionParamRankModel activityFunctionParamRankModel2 = new ActivityFunctionParamRankModel();
        activityFunctionParamRankModel2.setRankParam(2);
        rankListByActivityCodes.add(activityFunctionParamRankModel1);
        rankListByActivityCodes.add(activityFunctionParamRankModel2);
        List<Giveaway> giveawayVOS1 = new ArrayList<>();
        Giveaway giveawayVO1 = new Giveaway();
        giveawayVO1.setGiveawayCode("test");
        giveawayVOS1.add(giveawayVO);
        activityComponentDomain.addMissingGiveaway(giveawayVOS1, rankListByActivityCodes);
    }

    @Test
    public void checkGiveaway() {
        List<Giveaway> giveawayVOS = new ArrayList<>();
        List<FunctionParamModel> rankListByActivityCodes = new ArrayList<>();
        List<ActivityModel> activityModels = new ArrayList<>();
        when(activityService.queryActivityByActivityCodes(any(), any())).thenReturn(activityModels);
        try {
            activityComponentDomain.checkGiveaway(giveawayVOS, rankListByActivityCodes, "100001");
        } catch (Exception e) {

        }
        Giveaway giveaway1 = new Giveaway();
        giveaway1.setGiveawayCode("111");
        giveaway1.setGiveawayType(2);
        giveawayVOS.add(giveaway1);

        FunctionParamModel functionParamModel1 = new FunctionParamModel();
        functionParamModel1.setRankParam(1);
        FunctionParamModel functionParamModel2 = new FunctionParamModel();
        functionParamModel2.setRankParam(3);
        rankListByActivityCodes.add(functionParamModel1);
        rankListByActivityCodes.add(functionParamModel2);
        try {
            activityComponentDomain.checkGiveaway(giveawayVOS, rankListByActivityCodes, "100001");
        } catch (Exception e) {

        }
        giveaway1.setRankParam(1);
        Giveaway giveaway2 = new Giveaway();
        giveaway2.setGiveawayCode("222");
        giveaway2.setGiveawayType(2);
        giveaway2.setRankParam(2);
        giveawayVOS.add(giveaway2);
        functionParamModel2.setRankParam(2);
        try {
            activityComponentDomain.checkGiveaway(giveawayVOS, rankListByActivityCodes, "100001");
        } catch (Exception e) {

        }

        ActivityModel activityModel = new ActivityModel();
        activityModel.setActivityStatus("05");
        activityModels.add(activityModel);
        when(activityService.queryActivityByActivityCodes(any(), any())).thenReturn(activityModels);
        try {
            activityComponentDomain.checkGiveaway(giveawayVOS, rankListByActivityCodes, "100001");
        } catch (Exception e) {

        }

        Giveaway giveaway3 = new Giveaway();
        giveaway3.setGiveawayCode("333");
        giveaway3.setGiveawayType(4);
        giveaway3.setRankParam(1);
        giveawayVOS.add(giveaway3);

        Giveaway giveaway4 = new Giveaway();
        giveaway4.setGiveawayCode("444");
        giveaway4.setGiveawayType(4);
        giveaway4.setRankParam(2);
        giveawayVOS.add(giveaway4);
        try {
            activityComponentDomain.checkGiveaway(giveawayVOS, rankListByActivityCodes, "100001");
        } catch (Exception e) {

        }
        List<MarketingModel> marketingModelList = new ArrayList<>();
        MarketingModel marketingModel = new MarketingModel();
        marketingModel.setActivityStatus("07");
        marketingModelList.add(marketingModel);
//        when(MarketingService.getMarketingByActivityCodeList(any(), any())).thenReturn(marketingModelList);
    }

    @Test
    public void setAuditConfig() {
        TPromoActivityOutDTO promoActivityOutDTO = new TPromoActivityOutDTO();
        activityComponentDomain.setAuditConfig(promoActivityOutDTO, null);

        activityComponentDomain.setAuditConfig(promoActivityOutDTO, "0");

        activityComponentDomain.setAuditConfig(promoActivityOutDTO, "0,1");
    }

    @Test
    public void getActivityAuditConfig() {
        when(masterDataClient.getValueValue(any(),any())).thenReturn(null);
        activityComponentDomain.getActivityAuditConfig("10001");

        JsonResult<String> jsonResult = new JsonResult<>();
        jsonResult.setData("0,1");
        when(masterDataClient.getValueValue(any(),any())).thenReturn(jsonResult);
        activityComponentDomain.getActivityAuditConfig("10001");
    }

    @Test
    public void setCommitUser(){
        TPromoActivityOutDTO promoActivityOutDTO = new TPromoActivityOutDTO();
        promoActivityOutDTO.setActivityStatus(ActivityStatusEnum.CLOSURE.code());
        ActivityModel activityModel = new ActivityModel();
        activityModel.setUpdateUser("test");
        activityComponentDomain.setCommitUser(promoActivityOutDTO,activityModel);
    }

    @Test
    public void auditConfig(){
        TPromoActivityOutDTO promoActivityOutDTO = new TPromoActivityOutDTO();
        activityComponentDomain.setAuditConfig(promoActivityOutDTO,null);

        activityComponentDomain.setAuditConfig(promoActivityOutDTO,"1");

        activityComponentDomain.setAuditConfig(promoActivityOutDTO,"0,0");

    }

    @Test
    public void queryAfterDiscountPrice(){
        QueryAfterDiscountPriceParam param = new QueryAfterDiscountPriceParam();
        Map<String, ActivityCacheDTO> activityCacheDTOMap = new HashMap<>();
        when(activityCacheDomain.getActivityCacheMap(any(),any(),any(),any())).thenReturn(activityCacheDTOMap);
        List<QueryAfterDiscountItemParam> itemList = new ArrayList<>();
        QueryAfterDiscountItemParam afterDiscountItemParam = new QueryAfterDiscountItemParam();
        afterDiscountItemParam.setSkuCode("testCode");
        afterDiscountItemParam.setSalePrice(new BigDecimal(10));
        itemList.add(afterDiscountItemParam);
        param.setItemList(itemList);
        List<ProductSkuDetailDTO> productSkuDetailDTOS = new ArrayList<>();
        ProductSkuDetailDTO productSkuDetailDTO = new ProductSkuDetailDTO();

        productSkuDetailDTOS.add(productSkuDetailDTO);

        when(productDetailService.queryListByActivityCodesAndProductCodes(any(),any())).thenReturn(productSkuDetailDTOS);
        when(activityPriceComponentDomain.getTenantPrecision(any())).thenReturn(2);
        Map<String, ActivityCacheDTO> stringActivityCacheDTOMap = new HashMap<>();
        ActivityCacheDTO jj = new ActivityCacheDTO();
        TemplateModel promoTemplate = new TemplateModel();
        promoTemplate.setTemplateCode("0101020103010401");
        jj.setPromoTemplate(promoTemplate);
        List<FunctionParamModel> promoFuncParams = new ArrayList<>();
        FunctionParamModel f1 = new FunctionParamModel();
        FunctionParamModel f2 = new FunctionParamModel();
        FunctionParamModel f3 = new FunctionParamModel();
        FunctionParamModel f4 = new FunctionParamModel();
        f4.setParamValue("10");
        promoFuncParams.add(f1);
        promoFuncParams.add(f2);
        promoFuncParams.add(f3);
        promoFuncParams.add(f4);
        jj.setPromoFuncParams(promoFuncParams);
        stringActivityCacheDTOMap.put("减价",jj);

        ActivityCacheDTO jz = new ActivityCacheDTO();
        TemplateModel promoTemplate1 = new TemplateModel();
        promoTemplate1.setTemplateCode("0101020103010402");
        jz.setPromoTemplate(promoTemplate1);
        List<FunctionParamModel> promoFuncParams1 = new ArrayList<>();
        FunctionParamModel ff1 = new FunctionParamModel();
        FunctionParamModel ff2 = new FunctionParamModel();
        FunctionParamModel ff3 = new FunctionParamModel();
        FunctionParamModel ff4 = new FunctionParamModel();
        ff4.setParamValue("0.7");
        promoFuncParams1.add(ff1);
        promoFuncParams1.add(ff2);
        promoFuncParams1.add(ff3);
        promoFuncParams1.add(ff4);
        jz.setPromoFuncParams(promoFuncParams1);
        stringActivityCacheDTOMap.put("减折",jz);
        when(activityCacheDomain.filterActivityByProduct(any(),any(),any())).thenReturn(stringActivityCacheDTOMap);
        activityComponentDomain.queryAfterDiscountPrice(param);
    }
}