package com.gtech.promotion.component.purchaseconstraint;

import com.alibaba.fastjson.JSON;
import com.gtech.promotion.RedisClient;
import com.gtech.promotion.code.activity.ActivityStatusEnum;
import com.gtech.promotion.code.purchaseconstraint.PurchaseConstraintStatusEnum;
import com.gtech.promotion.dao.model.purchaseconstraint.PurchaseConstraintModel;
import com.gtech.promotion.dto.cache.PurchaseConstraintCacheDTO;
import com.gtech.promotion.dto.out.purchaseconstraint.FindPurchaseConstraintOutDto;
import com.gtech.promotion.service.purchaseconstraint.PurchaseConstraintService;
import org.apache.commons.lang.time.DateUtils;
import org.junit.Assert;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;


@RunWith(MockitoJUnitRunner.class)
public class PurchaseConstraintCacheComponentTest {

    @InjectMocks
    private PurchaseConstraintCacheComponent purchaseConstraintCacheComponent;

    @Mock
    private RedisClient redisClient;


    @Mock
    private PurchaseConstraintService purchaseConstraintService;

    @Mock
    private PurchaseConstraintComponent purchaseConstraintComponent;



    @Rule
    public ExpectedException thrown= ExpectedException.none();

    @Test
    public void putPurchaseConstraintCache(){

        PurchaseConstraintCacheDTO purchaseConstraintCacheDTO = new PurchaseConstraintCacheDTO();

        purchaseConstraintCacheDTO.setPurchaseConstraintStatus("error code");
        PurchaseConstraintCacheDTO pcResult = purchaseConstraintCacheComponent.putPurchaseConstraintCache(purchaseConstraintCacheDTO);
        Assert.assertNull(pcResult);

        purchaseConstraintCacheDTO.setTenantCode("10000");
        purchaseConstraintCacheDTO.setPurchaseConstraintCode("PCC11");
        purchaseConstraintCacheDTO.setPurchaseConstraintStatus(ActivityStatusEnum.EFFECTIVE.code());

        purchaseConstraintCacheComponent.putPurchaseConstraintCache(purchaseConstraintCacheDTO);

        Date now = new Date();
        purchaseConstraintCacheDTO.setPurchaseConstraintStartTime(DateUtils.addDays(now, -1));
        purchaseConstraintCacheDTO.setPurchaseConstraintEndTime(DateUtils.addDays(now, 1));
        purchaseConstraintCacheComponent.putPurchaseConstraintCache(purchaseConstraintCacheDTO);

        purchaseConstraintCacheDTO.setPurchaseConstraintEndTime(DateUtils.addDays(now, -1));
        purchaseConstraintCacheDTO.setPurchaseConstraintStartTime(DateUtils.addDays(now, 1));
        purchaseConstraintCacheComponent.putPurchaseConstraintCache(purchaseConstraintCacheDTO);
        Assert.assertTrue(Boolean.TRUE);
    }


    @Test
    public void putPurchaseConstraintCacheByCode(){
        String tenantCode = "11";
        String purchaseConstraintCode = "112";

        PurchaseConstraintModel purchaseConstraintModel = new PurchaseConstraintModel();
        purchaseConstraintModel.setPurchaseConstraintStatus(PurchaseConstraintStatusEnum.EFFECTIVE.getCode());

        Date now = new Date();
        Date beginTime = DateUtils.addDays(now, -1);
        Date endTime = DateUtils.addDays(now, 1);
        purchaseConstraintModel.setPurchaseConstraintStartTime(beginTime);
        purchaseConstraintModel.setPurchaseConstraintEndTime(endTime);

        Mockito.when(purchaseConstraintService.getPurchaseConstraint(Mockito.any(), Mockito.any()))
                .thenReturn(purchaseConstraintModel);

        Mockito.when(purchaseConstraintComponent.isEffectivePurchase(Mockito.any(), Mockito.any())).thenReturn(Boolean.TRUE);
        purchaseConstraintCacheComponent.putPurchaseConstraintCache(tenantCode, purchaseConstraintCode);
        Assert.assertTrue(Boolean.TRUE);
    }


    @Test
    public void getPurchaseConstraintCacheDTOFromCache(){
        String tenantCode = "100000";
        String purchaseConstraintCode="11";

        purchaseConstraintCacheComponent.getPcCacheDTOFromCache(tenantCode, purchaseConstraintCode);


        PurchaseConstraintCacheDTO purchaseConstraintCacheDTO = new PurchaseConstraintCacheDTO();
        purchaseConstraintCacheDTO.setTenantCode(tenantCode);
        Mockito.when(redisClient.getString(Mockito.any())).thenReturn(JSON.toJSONString(purchaseConstraintCacheDTO));
        PurchaseConstraintCacheDTO purchaseConstraintCacheDTO1 =
                purchaseConstraintCacheComponent.getPcCacheDTOFromCache(tenantCode, purchaseConstraintCode);
        Assert.assertEquals(tenantCode, purchaseConstraintCacheDTO1.getTenantCode());
    }

    @Test
    public void delPurchaseConstraintCache(){
        String tenantCode = "100000";
        String purchaseConstraintCode="11";
        purchaseConstraintCacheComponent.delPurchaseConstraintCache(tenantCode, purchaseConstraintCode);
        Assert.assertTrue(true);
    }

    @Test
    public void queryValidPurchaseConstraintFromCache(){
        String tenantCode = "100000";

        List<PurchaseConstraintModel> purchaseConstraintModels = new ArrayList<>();

        List<PurchaseConstraintCacheDTO> purchaseConstraintCacheDTOList =
                purchaseConstraintCacheComponent.queryValidPcFromCache(purchaseConstraintModels);

        Assert.assertEquals(0, purchaseConstraintCacheDTOList.size());


        PurchaseConstraintModel purchaseConstraintModel = new PurchaseConstraintModel();
        purchaseConstraintModel.setTenantCode(tenantCode);
        purchaseConstraintModel.setPurchaseConstraintName("222");
        purchaseConstraintModels.add(purchaseConstraintModel);

        PurchaseConstraintCacheDTO purchaseConstraintCacheDTO = new PurchaseConstraintCacheDTO();
        purchaseConstraintCacheDTO.setTenantCode(tenantCode);
        Mockito.when(redisClient.getString(Mockito.any())).thenReturn(JSON.toJSONString(purchaseConstraintCacheDTO));
        purchaseConstraintCacheDTOList =
                purchaseConstraintCacheComponent.queryValidPcFromCache(purchaseConstraintModels);
        Assert.assertEquals(1, purchaseConstraintCacheDTOList.size());



        Mockito.when(redisClient.getString(Mockito.any())).thenReturn(null);
        FindPurchaseConstraintOutDto findPurchaseConstraintOutDto = new FindPurchaseConstraintOutDto();
        Date now = new Date();
        Date beginTime = DateUtils.addDays(now, -1);
        Date endTime = DateUtils.addDays(now, 1);
        findPurchaseConstraintOutDto.setPurchaseConstraintStartTime(beginTime);
        findPurchaseConstraintOutDto.setPurchaseConstraintEndTime(endTime);
        findPurchaseConstraintOutDto.setPurchaseConstraintStatus(PurchaseConstraintStatusEnum.EFFECTIVE.getCode());

        findPurchaseConstraintOutDto.setTenantCode(tenantCode);
        Mockito.when(purchaseConstraintComponent.findPurchaseConstraint(Mockito.any()))
                .thenReturn(findPurchaseConstraintOutDto);

        purchaseConstraintCacheDTOList =
                purchaseConstraintCacheComponent.queryValidPcFromCache(purchaseConstraintModels);

        Assert.assertEquals(1, purchaseConstraintCacheDTOList.size());
    }




}
