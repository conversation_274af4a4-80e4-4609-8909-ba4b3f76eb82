/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.component;

import com.gtech.promotion.PromotionWebApplication;
import com.gtech.promotion.code.coupon.ReleaseTypeEnum;
import com.gtech.promotion.component.activity.ActivityExpireComponentDomain;
import com.gtech.promotion.component.coupon.CouponActivityComponent;
import com.gtech.promotion.controller.OrderTests;
import com.gtech.promotion.vo.param.coupon.CreateCouponReleaseParam;
import com.gtech.promotion.vo.param.coupon.UpdateCouponActivityParam;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * 
 *
 * <AUTHOR>
 * @Date 2020-03-11
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = { PromotionWebApplication.class })
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
@ActiveProfiles("dev")
//@Transactional
public class ActivityComponentTest extends OrderTests {

    @Autowired
    private ActivityExpireComponentDomain activityExpireComponentDomain;

    @Autowired
    private CouponActivityComponent couponActivityComponent;

    @Test
    public void test0000setup() {

        memberCode = super.randomCode(8);

        super.setup();
    }

//    @Test
//    public void test0010ActivityExpireComponent() {
//
//        CreatePromoActivityParam createPromoActivity = ActivityHelper.buildActivity0101_0201_0301_0401_单品无条件减金额(null, null);
//        CreateCouponActivityParam createCouponActivity = BeanCopyUtils.jsonCopyBean(createPromoActivity, CreateCouponActivityParam.class);
//
//        createCouponActivity.setCouponType("01");
//        createCouponActivity.setTotalQuantity("10000");
//        createCouponActivity.setActivityType(ActivityTypeEnum.COUPON.code());
//
//        Long now = System.currentTimeMillis();
//        createCouponActivity.setActivityBegin(DateUtil.format(new Date(now - 1000), DateUtil.FORMAT_YYYYMMDDHHMISS_14));
//        createCouponActivity.setActivityEnd(DateUtil.format(new Date(now + 30000), DateUtil.FORMAT_YYYYMMDDHHMISS_14));
//
//        List<ActivityModel> activityModelList = new ArrayList<>();
//        UpdateCouponActivityParam updateCouponActivity = null;
//
//        for(int i = 0; i < 3; i++) {
//            updateCouponActivity = super.createCouponActivity(createCouponActivity);
//            this.createCouponRelease(updateCouponActivity);
//            activityModelList.add(couponActivityComponent.findEffectiveActivity(updateCouponActivity.getTenantCode(), updateCouponActivity.getActivityCode(), null));
//        }
//
//        super.sleep(10000L);
//        activityExpireComponent.expireActivity(activityModelList);
//    }

    private String createCouponRelease(UpdateCouponActivityParam promoActivity) {
        
        CreateCouponReleaseParam param = new CreateCouponReleaseParam();

        param.setDomainCode(promoActivity.getDomainCode());
        param.setTenantCode(promoActivity.getTenantCode());
        param.setActivityCode(promoActivity.getActivityCode());

        param.setReceiveStart(promoActivity.getActivityBegin());
        param.setReceiveEnd(promoActivity.getActivityEnd());

        param.setValidStart(promoActivity.getActivityBegin());
        param.setValidEnd(promoActivity.getActivityEnd());

        param.setReleaseType(ReleaseTypeEnum.IMMEDIATELY.code());
        param.setReleaseQuantity(100);

        TestResult testResult = super.mockMvcPost("/coupon/createCouponRelease", param);
        return testResult.getData();
    }
}
