package com.gtech.promotion.exception;

import com.gtech.commons.exception.ErrorCode;
import org.junit.Test;

public class PromotionOrderExceptionTest {
    
    @Test
    public void test(){
        PromotionOrderException exception = new PromotionOrderException();
        PromotionOrderException exception1 = new PromotionOrderException(new Exception());
        PromotionOrderException exception2 = new PromotionOrderException("error",new Exception(),"123");
        PromotionOrderException exception3 = new PromotionOrderException("error","error",new Exception(),"123");
        PromotionOrderException exception4 = new PromotionOrderException(ErrorCode.DEF_ERROR_CODE,new Exception(),"123");
        PromotionOrderException exception6 = new PromotionOrderException("error","123",new Exception());
    }
}
