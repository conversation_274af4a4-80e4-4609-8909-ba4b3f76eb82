/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.orderTest;

import com.gtech.commons.result.Result;
import com.gtech.promotion.component.activity.OrderDomain;
import com.gtech.promotion.controller.activity.OrderController;
import com.gtech.promotion.helper.ActivityHelper;
import com.gtech.promotion.vo.param.activity.SalesReturnOrderParam;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.io.IOException;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class OrderControllerTest {

    @InjectMocks
    private OrderController orderController;

    @Mock
    private OrderDomain orderDomain;

    private final String ORDER_NO = "111111";
    @Test
    public void testSalesReturnPromotionOrder() throws IOException{
        // given
        SalesReturnOrderParam salesReturnOrderParam = new SalesReturnOrderParam();
        salesReturnOrderParam.setDomainCode(ActivityHelper.domainCode);
        salesReturnOrderParam.setTenantCode(ActivityHelper.tenantCode);
        salesReturnOrderParam.setOrderNo(ORDER_NO);
        // when
        when(orderDomain.salesReturnOrder(anyString(),anyString())).thenReturn(1);
        Result<Object> ok = orderController.salesReturnOrder(salesReturnOrderParam);
        Assert.assertTrue(ok.isSuccess());
        Assert.assertEquals(null, ok.getData());
    }




}
