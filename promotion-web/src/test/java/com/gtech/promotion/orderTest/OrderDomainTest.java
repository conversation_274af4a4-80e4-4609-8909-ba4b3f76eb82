/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.orderTest;

import com.gtech.promotion.code.activity.ActivityTypeEnum;
import com.gtech.promotion.component.activity.OrderDomain;
import com.gtech.promotion.component.coupon.LockCouponComponent;
import com.gtech.promotion.dao.entity.activity.TPromoActivityIncentiveEntity;
import com.gtech.promotion.dao.model.activity.ActivityModel;
import com.gtech.promotion.dao.model.activity.TPromoIncentiveLimitedVO;
import com.gtech.promotion.dao.model.activity.TPromoOrderVO;
import com.gtech.promotion.dao.model.coupon.TPromoCouponCodeUserVO;
import com.gtech.promotion.helper.ActivityHelper;
import com.gtech.promotion.service.activity.ActivityService;
import com.gtech.promotion.service.activity.TPromoActivityIncentiveService;
import com.gtech.promotion.service.activity.TPromoIncentiveLimitedService;
import com.gtech.promotion.service.activity.TPromoOrderService;
import com.gtech.promotion.service.coupon.PromoCouponCodeUserService;
import com.gtech.promotion.service.mongo.activity.TPromoOrderDetailService;
import com.gtech.promotion.vo.mongo.TPromoOrderDetailActivityVO;
import com.gtech.promotion.vo.mongo.TPromoOrderDetailVO;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class OrderDomainTest {

    @InjectMocks
    private OrderDomain OrderDomain;
    @Mock
    private TPromoOrderService orderService;
    @Mock
    private TPromoActivityIncentiveService activityIncentiveService;
    @Mock
    private TPromoOrderDetailService orderDetailService;
    @Mock
    private TPromoIncentiveLimitedService limitedService;
    @Mock
    private ActivityService activityService;
    @Mock
    private LockCouponComponent couponDomain;
    @Mock
    private PromoCouponCodeUserService couponCodeUserService;

    private final String ORDER_NO = "111111";
    private final String ACTIVITY_CODE = "1234123414334";


    @Test
    public void testSalesReturnPromotionOrder() throws IOException{
        // given
        //查询订单
        TPromoOrderVO orderVO = new TPromoOrderVO();
        orderVO.setTenantCode(ActivityHelper.tenantCode);
        orderVO.setOrderId(ORDER_NO);
        //奖励表
        List<TPromoActivityIncentiveEntity> incentiveEntityList = Lists.newArrayList();
        TPromoActivityIncentiveEntity entity = new TPromoActivityIncentiveEntity();
        entity.setActivityCode(ACTIVITY_CODE);
        incentiveEntityList.add(entity);
        //订单明细
        List<TPromoOrderDetailVO> tPromoOrderDetailVOS = Lists.newArrayList();
        TPromoOrderDetailVO detailVO = new TPromoOrderDetailVO();
        detailVO.setSkuCode("sku_111");
        detailVO.setQuantity(1);
        //订单详情关联活动集合
        ArrayList<TPromoOrderDetailActivityVO> activityVOS = new ArrayList<>();
        //单个活动
        TPromoOrderDetailActivityVO activityVO = new TPromoOrderDetailActivityVO();
        activityVO.setActivityCode(ACTIVITY_CODE);
        activityVO.setActivityType(ActivityTypeEnum.COUPON.code());
        activityVOS.add(activityVO);
        detailVO.setActivities(activityVOS);
        //活动
        ActivityModel activityModel = new ActivityModel();
        activityModel.setActivityType(ActivityTypeEnum.COUPON.code());
        activityModel.setActivityCode(ACTIVITY_CODE);
        //限制对象集合
        List<TPromoIncentiveLimitedVO> limitedList = Lists.newArrayList();

        //用户对应券集合
        List<TPromoCouponCodeUserVO> codeUserVOS = Lists.newArrayList();
        TPromoCouponCodeUserVO couponCodeUserVO = new TPromoCouponCodeUserVO();
        couponCodeUserVO.setActivityCode(ACTIVITY_CODE);
        couponCodeUserVO.setUsedRefId(ORDER_NO);
        couponCodeUserVO.setCouponCode("134134134123");
//        couponCodeUserVO.setValidEndTime();
//        couponCodeUserVO.setValidStartTime();
        couponCodeUserVO.setTenantCode(ActivityHelper.tenantCode);
        codeUserVOS.add(couponCodeUserVO);
        // when
        when(orderService.queryOrderBySalesOrderNo(anyString(),anyString())).thenReturn(orderVO);
        when(orderService.updateOrderLogicDelete(anyString(),anyString(),anyString())).thenReturn(1);
//        when(activityIncentiveService.getListByOrderId(anyString(),anyString())).thenReturn(incentiveEntityList);
//        when(orderDetailService.queryOrderDetail(anyString())).thenReturn(tPromoOrderDetailVOS);
//        when(activityService.findActivityByActivityCode(anyString(),anyString())).thenReturn(activityModel);
//        when(limitedService.getLimitedListByActivityCode(anyString())).thenReturn(limitedList);
//        when(couponCodeUserService.getCodeUserByUsedRefId(anyString(),anyString())).thenReturn(codeUserVOS);
        when(couponDomain.unLockCoupon(any())).thenReturn(1);
        //结果
        Integer ok = OrderDomain.salesReturnOrder(ActivityHelper.tenantCode,ORDER_NO);
        Assert.assertEquals(Integer.valueOf(1), ok);
    }




}
