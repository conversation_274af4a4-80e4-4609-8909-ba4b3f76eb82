package com.gtech.promotion.dao;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.gtech.promotion.dao.handler.ExtImageTypeHandler;
import com.gtech.promotion.dao.handler.JsonListTypeHandler;
import com.gtech.promotion.vo.bean.ExtImage;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.List;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

class JsonListTypeHandlerTest {

    @Mock
    private PreparedStatement preparedStatement;
    @Mock
    private ResultSet resultSet;
    @Mock
    private CallableStatement callableStatement;
    @Mock
    private ObjectMapper objectMapper;

    private JsonListTypeHandler jsonListTypeHandler;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        jsonListTypeHandler = new ExtImageTypeHandler();
    }

    @Test
    void setNonNullParameter() throws SQLException, JsonProcessingException {
        ExtImage extImage = new ExtImage();
        extImage.setImageName("123");
        extImage.setImageUrl("123");

        List<ExtImage> extImages = Arrays.asList(extImage,extImage);
        String json = new ObjectMapper().writeValueAsString(extImages);

        jsonListTypeHandler.setNonNullParameter(preparedStatement, 1, extImages, null);

        verify(preparedStatement).setString(1, json);
    }

    @Test
    void getNullableResultFromResultSetByColumnName() throws SQLException, JsonProcessingException {
        ExtImage extImage = new ExtImage();
        extImage.setImageName("123");
        extImage.setImageUrl("123");

        List<ExtImage> extImages = Arrays.asList(extImage,extImage);
        String json = new ObjectMapper().writeValueAsString(extImages);
        when(resultSet.getString("columnName")).thenReturn(json);

        List<ExtImage> result = jsonListTypeHandler.getNullableResult(resultSet, "columnName");

        verify(resultSet).getString("columnName");
        assert result != null && result.equals(extImages);
    }

    @Test
    void getNullableResultFromResultSetByColumnIndex() throws SQLException, JsonProcessingException {
        ExtImage extImage = new ExtImage();
        extImage.setImageName("123");
        extImage.setImageUrl("123");

        List<ExtImage> extImages = Arrays.asList(extImage,extImage);
        String json = new ObjectMapper().writeValueAsString(extImages);
        when(resultSet.getString(1)).thenReturn(json);

        List<ExtImage> result = jsonListTypeHandler.getNullableResult(resultSet, 1);

        verify(resultSet).getString(1);
        assert result != null && result.equals(extImages);
    }

    @Test
    void getNullableResultFromCallableStatementByColumnIndex() throws SQLException, JsonProcessingException {

        ExtImage extImage = new ExtImage();
        extImage.setImageName("123");
        extImage.setImageUrl("123");

        List<ExtImage> extImages = Arrays.asList(extImage,extImage);
        String json = new ObjectMapper().writeValueAsString(extImages);
        when(callableStatement.getString(1)).thenReturn(json);

        List<ExtImage> result = jsonListTypeHandler.getNullableResult(callableStatement, 1);

    }
}