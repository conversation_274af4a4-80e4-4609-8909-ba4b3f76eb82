/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.callable;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.mongodb.core.MongoTemplate;

import com.gtech.promotion.code.activity.ActivityTypeEnum;
import com.gtech.promotion.dao.entity.activity.ActivityEntity;
import com.gtech.promotion.dao.entity.activity.TPromoActivityIncentiveEntity;
import com.gtech.promotion.dao.entity.activity.TPromoOrderEntity;
import com.gtech.promotion.dao.mongo.activity.OrderDetailEntity;
import com.gtech.promotion.service.activity.TPromoActivityIncentiveService;
import com.gtech.promotion.service.activity.ActivityService;
import com.gtech.promotion.service.activity.TPromoActivityStatisticService;
import com.gtech.promotion.service.activity.TPromoOrderService;
import com.gtech.promotion.service.coupon.PromoCouponCodeUserService;

@RunWith(MockitoJUnitRunner.class)
public class ActivityStatisticCreateCallableTest {

    private ActivityStatisticCreateCallable createCallable;

    @Mock
    private TPromoOrderService orderService;
    @Mock
    private TPromoActivityIncentiveService incentiveService;
    @Mock
    private ActivityService activityService;
    @Mock
    private MongoTemplate mongoTemplate;
    @Mock
    private TPromoActivityStatisticService statisticService;
    @Mock
    private PromoCouponCodeUserService couponCodeUserService;

    @Before
    public void before(){
        createCallable = new ActivityStatisticCreateCallable(orderService,
                incentiveService, activityService, mongoTemplate, statisticService, couponCodeUserService);
    }

    @Test
    public void testCall_null(){
        when(orderService.queryPromoOrderYesterday()).thenReturn(null);
        String call = createCallable.call();
        Assert.assertEquals("昨天无有效订单", call);
    }

    // TODO: DZJ
//    @Test
//    public void testCall(){
//        List<TPromoOrderEntity> list = new ArrayList<>();
//        TPromoOrderEntity orderEntity = new TPromoOrderEntity();
//        orderEntity.setId(1L);
//        list.add(orderEntity);
//
//        List<TPromoActivityIncentiveEntity> incentiveList = new ArrayList<>();
//        TPromoActivityIncentiveEntity incentiveEntity = new TPromoActivityIncentiveEntity();
//        incentiveEntity.setTenantCode("1");
//        incentiveEntity.setActivityCode("1");
//        incentiveEntity.setPromoOrderId("1");
//        incentiveEntity.setIncentiveAmount(new BigDecimal(1));
//        incentiveList.add(incentiveEntity);
//        TPromoActivityIncentiveEntity incentiveEntity2 = new TPromoActivityIncentiveEntity();
//        incentiveEntity2.setTenantCode("1");
//        incentiveEntity2.setActivityCode("2");
//        incentiveEntity2.setPromoOrderId("2");
//        incentiveEntity2.setIncentiveAmount(new BigDecimal(2));
//        incentiveList.add(incentiveEntity2);
//
//        List<TPromoActivityEntity> activityEntities = new ArrayList<>();
//        TPromoActivityEntity activityEntity = new TPromoActivityEntity();
//        activityEntity.setId(1L);
//        activityEntity.setActivityType(ActivityTypeEnum.COUPON.code());
//        activityEntities.add(activityEntity);
//        TPromoActivityEntity activityEntity2 = new TPromoActivityEntity();
//        activityEntity2.setId(2L);
//        activityEntity2.setActivityType(ActivityTypeEnum.ACTIVITY.code());
//        activityEntities.add(activityEntity2);
//
//        List<Object> entityList = new ArrayList<>();
//        TPromoOrderDetailEntity detailEntity = new TPromoOrderDetailEntity();
//        detailEntity.setProductAmount(new BigDecimal(10));
//        entityList.add(detailEntity);
//        // when
//        when(orderService.queryPromoOrderYesterday()).thenReturn(list);
//        when(incentiveService.getListByOrderIds(any())).thenReturn(incentiveList);
//        when(activityService.queryActivityByCodes(any(), any())).thenReturn(activityEntities);
//        when(mongoTemplate.find(any(), any())).thenReturn(entityList);
//        when(couponCodeUserService.getAllocateCouponCountYesterday111(any(), any())).thenReturn(1);
//        when(couponCodeUserService.getUseCouponCountYesterday111(any(), any())).thenReturn(1);
//
//        // then
//        String call = createCallable.call();
//        verify(statisticService, times(1)).createActivityStatisticBatch(any());
//        Assert.assertEquals("各租铺活动统计数据成功", call);
//    }


    @Test
    public void testCall_exception(){
        List<TPromoOrderEntity> list = new ArrayList<>();
        TPromoOrderEntity orderEntity = new TPromoOrderEntity();
        orderEntity.setId(1L);
        list.add(orderEntity);

        List<TPromoActivityIncentiveEntity> incentiveList = new ArrayList<>();
        TPromoActivityIncentiveEntity incentiveEntity = new TPromoActivityIncentiveEntity();
        incentiveEntity.setTenantCode("1");
        incentiveEntity.setActivityCode("1");
        incentiveEntity.setPromoOrderId("1");
        incentiveEntity.setIncentiveAmount(new BigDecimal(1));
        incentiveList.add(incentiveEntity);
        TPromoActivityIncentiveEntity incentiveEntity2 = new TPromoActivityIncentiveEntity();
        incentiveEntity2.setTenantCode("1");
        incentiveEntity2.setActivityCode("2");
        incentiveEntity2.setPromoOrderId("2");
        incentiveEntity2.setIncentiveAmount(new BigDecimal(2));
        incentiveList.add(incentiveEntity2);

        List<ActivityEntity> activityEntities = new ArrayList<>();
        ActivityEntity activityEntity = new ActivityEntity();
        activityEntity.setId(1L);
        activityEntity.setActivityType(ActivityTypeEnum.COUPON.code());
        activityEntities.add(activityEntity);
        ActivityEntity activityEntity2 = new ActivityEntity();
        activityEntity2.setId(2L);
        activityEntity2.setActivityType(ActivityTypeEnum.ACTIVITY.code());
        activityEntities.add(activityEntity2);

        List<Object> entityList = new ArrayList<>();
        OrderDetailEntity detailEntity = new OrderDetailEntity();
        detailEntity.setProductAmount(new BigDecimal(10));
        entityList.add(detailEntity);
        // when
        when(orderService.queryPromoOrderYesterday()).thenReturn(list);
        when(incentiveService.getListByOrderIds(any())).thenReturn(incentiveList);
        when(activityService.queryActivityByCodes(any(), any())).thenReturn(activityEntities);
        when(mongoTemplate.find(any(), any())).thenReturn(entityList);
//        when(couponCodeUserService.getAllocateCouponCountYesterday111(any(), any())).thenReturn(1);
//        when(couponCodeUserService.getUseCouponCountYesterday111(any(), any())).thenThrow(new RuntimeException("1"));

        // then
        String call = createCallable.call();
        verify(statisticService, times(0)).createActivityStatisticBatch(any());
        Assert.assertEquals("", call);
    }

}
