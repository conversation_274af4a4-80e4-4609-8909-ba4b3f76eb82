package com.gtech.promotion.calc;

import com.gtech.promotion.calc.function.CalcFunction0401;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class CalcFunction0411Test {
    @InjectMocks
    CalcFunction0401 calcFunction0401;

    @Test
    public void calc(){
//        List<CalcShoppingCartItem> calcShoppingCartItemList = new ArrayList<>();
//        CalcShoppingCartItem shoppingCartItem = new CalcShoppingCartItem();
//        shoppingCartItem.setPromoAmount(new BigDecimal(918));
//        calcShoppingCartItemList.add(shoppingCartItem);
//        CalcShoppingCart promoObject = new CalcShoppingCart();
//        promoObject.setCalcShoppingCartItemList(calcShoppingCartItemList);
//
//        RedisOpsHelper redisOpsHelper = new RedisOpsHelper();
//        CalcActivity calcActivity = new CalcActivity(redisOpsHelper,"test");
//        CalcTemplate calcTemplate = new CalcTemplate();
//        calcActivity.setCalcTemplate();
//        CalcActivityIncentive incentive = new CalcActivityIncentive(calcActivity,"test","361178");
//
//        calcFunction0401.handler0101T(promoObject,incentive,null);
    }
}
