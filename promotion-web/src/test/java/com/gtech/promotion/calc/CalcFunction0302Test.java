package com.gtech.promotion.calc;

import com.gtech.promotion.calc.function.*;
import com.gtech.promotion.calc.model.CalcActivityFuncParam;
import com.gtech.promotion.calc.model.CalcActivityIncentive;
import com.gtech.promotion.calc.model.CalcShoppingCart;
import com.gtech.promotion.code.activity.SeqNumEnum;
import com.gtech.promotion.dao.model.activity.ActivityModel;
import com.gtech.promotion.dto.in.activity.ShoppingCartActivity;
import com.gtech.promotion.helper.RedisOpsHelper;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.*;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2021/9/22 11:21
 */

@RunWith(MockitoJUnitRunner.class)
public class CalcFunction0302Test {

    @InjectMocks
    private CalcFunction0302 calcFunction0302;

    @Mock
    private RedisOpsHelper redisOpsHelper;

    @InjectMocks
    private CalcFunction0203 calcFunction0203;

    @InjectMocks
    private CalcFunction0202 calcFunction0202;


    @InjectMocks
    private CalcFunction0204 calcFunction0204;

    @InjectMocks
    private CalcFunction0205 calcFunction0205;

    @InjectMocks
    private CalcFunction0206 calcFunction0206;

    @InjectMocks
    private CalcFunction0207 calcFunction0207;

    @Test
    public void calc(){

        CalcShoppingCart calcShoppingCart = new CalcShoppingCart();
        calcShoppingCart.setPostage(BigDecimal.ZERO);
        calcShoppingCart.setCalcShoppingCartItemList(new ArrayList<>());
        CalcActivity calcActivity = new CalcActivity(redisOpsHelper, "");
        Set<String> strings = new HashSet<>();
        strings.add(SeqNumEnum.A.code());
        strings.add(SeqNumEnum.B.code());
        calcActivity.setSeqNums(strings);
        calcShoppingCart.setCalcActivity(calcActivity);
        CalcActivityIncentive calcActivityIncentive = new CalcActivityIncentive(calcActivity, "", "");
        calcActivityIncentive.setIncentiveTimes(1);
        Deque<FunctionEnum> functionEnums = new ArrayDeque<>();
        ArrayList<CalcActivityFuncParam> funcParamList = new ArrayList<>();
        CalcActivityFuncParam e = new CalcActivityFuncParam();
        e.setFuncCode(calcFunction0302.getFunction().code());
        e.setParamValue("1");
        funcParamList.add(e);
        FunctionParamMap functionParamMap = new FunctionParamMap(funcParamList);
        CalcFunction calcFunction = calcFunction0302;
        CalcResult calc = calcFunction0302.calc(calcShoppingCart, calcActivityIncentive, functionEnums, functionParamMap, calcFunction);
        Assert.assertEquals(CalcResult.ERROR_DEFAULT.getResultCode(), calc.getResultCode());
    }

    @Test
    public void calcFunction0302(){

        CalcShoppingCart calcShoppingCart = new CalcShoppingCart();
        calcShoppingCart.setPostage(BigDecimal.ZERO);
        calcShoppingCart.setCalcShoppingCartItemList(new ArrayList<>());
        CalcActivity calcActivity = new CalcActivity(redisOpsHelper, "");
        Set<String> strings = new HashSet<>();
        strings.add(SeqNumEnum.A.code());
        strings.add(SeqNumEnum.B.code());
        calcActivity.setSeqNums(strings);
        ShoppingCartActivity shoppingCartActivity = new ShoppingCartActivity();
        ActivityModel activityModel = new ActivityModel();
        activityModel.setTemplateCode("0102020203020412");
        shoppingCartActivity.setActivityModel(activityModel);
        CalcTemplate calcTemplate = new CalcTemplate();
        calcTemplate.setCalcActivity(calcActivity);
        calcActivity.setCartActivity(shoppingCartActivity);
        calcShoppingCart.setCalcActivity(calcActivity);
        CalcActivityIncentive calcActivityIncentive = new CalcActivityIncentive(calcActivity, "", "");
        calcActivityIncentive.setIncentiveTimes(1);
        Deque<FunctionEnum> functionEnums = new ArrayDeque<>();
        ArrayList<CalcActivityFuncParam> funcParamList = new ArrayList<>();
        CalcActivityFuncParam e = new CalcActivityFuncParam();
        e.setFuncCode(calcFunction0302.getFunction().code());
        e.setParamValue("1");
        funcParamList.add(e);
        FunctionParamMap functionParamMap = new FunctionParamMap(funcParamList);
        CalcFunction calcFunction = calcFunction0302;
        CalcResult calc = calcFunction0302.calc(calcShoppingCart, calcActivityIncentive, functionEnums, functionParamMap, calcFunction);
        Assert.assertEquals(CalcResult.ERROR_DEFAULT.getResultCode(), calc.getResultCode());
    }


    @Test
    public void calcFunction0203(){

        CalcShoppingCart calcShoppingCart = new CalcShoppingCart();
        calcShoppingCart.setPostage(BigDecimal.ZERO);
        calcShoppingCart.setCalcShoppingCartItemList(new ArrayList<>());
        CalcActivity calcActivity = new CalcActivity(redisOpsHelper, "");
        Set<String> strings = new HashSet<>();
        strings.add(SeqNumEnum.A.code());
        strings.add(SeqNumEnum.B.code());
        calcActivity.setSeqNums(strings);
        ShoppingCartActivity shoppingCartActivity = new ShoppingCartActivity();
        ActivityModel activityModel = new ActivityModel();
        activityModel.setTemplateCode("0102020203020412");
        shoppingCartActivity.setActivityModel(activityModel);
        CalcTemplate calcTemplate = new CalcTemplate();
        calcTemplate.setCalcActivity(calcActivity);
        calcActivity.setCartActivity(shoppingCartActivity);
        calcShoppingCart.setCalcActivity(calcActivity);
        CalcActivityIncentive calcActivityIncentive = new CalcActivityIncentive(calcActivity, "", "");
        calcActivityIncentive.setIncentiveTimes(1);
        Deque<FunctionEnum> functionEnums = new ArrayDeque<>();
        ArrayList<CalcActivityFuncParam> funcParamList = new ArrayList<>();
        CalcActivityFuncParam e = new CalcActivityFuncParam();
        e.setFuncCode(calcFunction0203.getFunction().code());
        e.setParamValue("1");
        funcParamList.add(e);
        FunctionParamMap functionParamMap = new FunctionParamMap(funcParamList);
        CalcFunction calcFunction = calcFunction0203;
        CalcResult calc = calcFunction0302.calc(calcShoppingCart, calcActivityIncentive, functionEnums, functionParamMap, calcFunction);
        Assert.assertEquals(CalcResult.ERROR_DEFAULT.getResultCode(), calc.getResultCode());
    }

    @Test
    public void calcFunction0202(){

        CalcShoppingCart calcShoppingCart = new CalcShoppingCart();
        calcShoppingCart.setPostage(BigDecimal.ZERO);
        calcShoppingCart.setCalcShoppingCartItemList(new ArrayList<>());
        CalcActivity calcActivity = new CalcActivity(redisOpsHelper, "");
        Set<String> strings = new HashSet<>();
        strings.add(SeqNumEnum.A.code());
        strings.add(SeqNumEnum.B.code());
        calcActivity.setSeqNums(strings);
        ShoppingCartActivity shoppingCartActivity = new ShoppingCartActivity();
        ActivityModel activityModel = new ActivityModel();
        activityModel.setTemplateCode("0102020203020412");
        shoppingCartActivity.setActivityModel(activityModel);
        CalcTemplate calcTemplate = new CalcTemplate();
        calcTemplate.setCalcActivity(calcActivity);
        calcActivity.setCartActivity(shoppingCartActivity);
        calcShoppingCart.setCalcActivity(calcActivity);
        CalcActivityIncentive calcActivityIncentive = new CalcActivityIncentive(calcActivity, "", "");
        calcActivityIncentive.setIncentiveTimes(1);
        Deque<FunctionEnum> functionEnums = new ArrayDeque<>();
        ArrayList<CalcActivityFuncParam> funcParamList = new ArrayList<>();
        CalcActivityFuncParam e = new CalcActivityFuncParam();
        e.setFuncCode(calcFunction0302.getFunction().code());
        e.setParamValue("1");
        funcParamList.add(e);
        FunctionParamMap functionParamMap = new FunctionParamMap(funcParamList);
        CalcFunction calcFunction = calcFunction0202;
        CalcResult calc = calcFunction0302.calc(calcShoppingCart, calcActivityIncentive, functionEnums, functionParamMap, calcFunction);
        Assert.assertEquals(CalcResult.SUCCESS_FALSE.getResultCode(), calc.getResultCode());
    }

    @Test
    public void calcFunction0204(){

        CalcShoppingCart calcShoppingCart = new CalcShoppingCart();
        calcShoppingCart.setPostage(BigDecimal.ZERO);
        calcShoppingCart.setCalcShoppingCartItemList(new ArrayList<>());
        CalcActivity calcActivity = new CalcActivity(redisOpsHelper, "");
        Set<String> strings = new HashSet<>();
        strings.add(SeqNumEnum.A.code());
        strings.add(SeqNumEnum.B.code());
        calcActivity.setSeqNums(strings);
        ShoppingCartActivity shoppingCartActivity = new ShoppingCartActivity();
        ActivityModel activityModel = new ActivityModel();
        activityModel.setTemplateCode("0102020203020412");
        shoppingCartActivity.setActivityModel(activityModel);
        CalcTemplate calcTemplate = new CalcTemplate();
        calcTemplate.setCalcActivity(calcActivity);
        calcActivity.setCartActivity(shoppingCartActivity);
        calcShoppingCart.setCalcActivity(calcActivity);
        CalcActivityIncentive calcActivityIncentive = new CalcActivityIncentive(calcActivity, "", "");
        calcActivityIncentive.setIncentiveTimes(1);
        Deque<FunctionEnum> functionEnums = new ArrayDeque<>();
        ArrayList<CalcActivityFuncParam> funcParamList = new ArrayList<>();
        CalcActivityFuncParam e = new CalcActivityFuncParam();
        e.setFuncCode(calcFunction0302.getFunction().code());
        e.setParamValue("1");
        funcParamList.add(e);
        FunctionParamMap functionParamMap = new FunctionParamMap(funcParamList);
        CalcFunction calcFunction = calcFunction0204;
        CalcResult calc = calcFunction0302.calc(calcShoppingCart, calcActivityIncentive, functionEnums, functionParamMap, calcFunction);
        Assert.assertEquals(CalcResult.SUCCESS_FALSE.getResultCode(), calc.getResultCode());
    }

    @Test
    public void calcFunction0205(){

        CalcShoppingCart calcShoppingCart = new CalcShoppingCart();
        calcShoppingCart.setPostage(BigDecimal.ZERO);
        calcShoppingCart.setCalcShoppingCartItemList(new ArrayList<>());
        CalcActivity calcActivity = new CalcActivity(redisOpsHelper, "");
        Set<String> strings = new HashSet<>();
        strings.add(SeqNumEnum.A.code());
        strings.add(SeqNumEnum.B.code());
        calcActivity.setSeqNums(strings);
        ShoppingCartActivity shoppingCartActivity = new ShoppingCartActivity();
        ActivityModel activityModel = new ActivityModel();
        activityModel.setTemplateCode("0102020203020412");
        shoppingCartActivity.setActivityModel(activityModel);
        CalcTemplate calcTemplate = new CalcTemplate();
        calcTemplate.setCalcActivity(calcActivity);
        calcActivity.setCartActivity(shoppingCartActivity);
        calcShoppingCart.setCalcActivity(calcActivity);
        CalcActivityIncentive calcActivityIncentive = new CalcActivityIncentive(calcActivity, "", "");
        calcActivityIncentive.setIncentiveTimes(1);
        Deque<FunctionEnum> functionEnums = new ArrayDeque<>();
        ArrayList<CalcActivityFuncParam> funcParamList = new ArrayList<>();
        CalcActivityFuncParam e = new CalcActivityFuncParam();
        e.setFuncCode(calcFunction0302.getFunction().code());
        e.setParamValue("1");
        funcParamList.add(e);
        FunctionParamMap functionParamMap = new FunctionParamMap(funcParamList);
        CalcFunction calcFunction = calcFunction0205;
        CalcResult calc = calcFunction0302.calc(calcShoppingCart, calcActivityIncentive, functionEnums, functionParamMap, calcFunction);
        Assert.assertEquals(CalcResult.SUCCESS_FALSE.getResultCode(), calc.getResultCode());
    }


    @Test
    public void calcFunction0205_sq(){

        CalcShoppingCart calcShoppingCart = new CalcShoppingCart();
        calcShoppingCart.setPostage(BigDecimal.ZERO);
        calcShoppingCart.setCalcShoppingCartItemList(new ArrayList<>());
        CalcActivity calcActivity = new CalcActivity(redisOpsHelper, "");
        Set<String> strings = new HashSet<>();
        strings.add(SeqNumEnum.A.code());
        calcActivity.setSeqNums(strings);
        ShoppingCartActivity shoppingCartActivity = new ShoppingCartActivity();
        ActivityModel activityModel = new ActivityModel();
        activityModel.setTemplateCode("0102020203020412");
        shoppingCartActivity.setActivityModel(activityModel);
        CalcTemplate calcTemplate = new CalcTemplate();
        calcTemplate.setCalcActivity(calcActivity);
        calcActivity.setCartActivity(shoppingCartActivity);
        calcShoppingCart.setCalcActivity(calcActivity);
        CalcActivityIncentive calcActivityIncentive = new CalcActivityIncentive(calcActivity, "", "");
        calcActivityIncentive.setIncentiveTimes(1);
        Deque<FunctionEnum> functionEnums = new ArrayDeque<>();
        ArrayList<CalcActivityFuncParam> funcParamList = new ArrayList<>();
        CalcActivityFuncParam e = new CalcActivityFuncParam();
        e.setFuncCode(calcFunction0302.getFunction().code());
        e.setParamValue("1");
        funcParamList.add(e);
        FunctionParamMap functionParamMap = new FunctionParamMap(funcParamList);
        CalcFunction calcFunction = calcFunction0205;
        CalcResult calc = calcFunction0302.calc(calcShoppingCart, calcActivityIncentive, functionEnums, functionParamMap, calcFunction);
        Assert.assertEquals(CalcResult.ERROR_DEFAULT.getResultCode(), calc.getResultCode());
    }

    @Test
    public void calcFunction0206(){

        CalcShoppingCart calcShoppingCart = new CalcShoppingCart();
        calcShoppingCart.setPostage(BigDecimal.ZERO);
        calcShoppingCart.setCalcShoppingCartItemList(new ArrayList<>());
        CalcActivity calcActivity = new CalcActivity(redisOpsHelper, "");
        Set<String> strings = new HashSet<>();
        strings.add(SeqNumEnum.A.code());
        strings.add(SeqNumEnum.B.code());
        calcActivity.setSeqNums(strings);
        ShoppingCartActivity shoppingCartActivity = new ShoppingCartActivity();
        ActivityModel activityModel = new ActivityModel();
        activityModel.setTemplateCode("0102020203020412");
        shoppingCartActivity.setActivityModel(activityModel);
        CalcTemplate calcTemplate = new CalcTemplate();
        calcTemplate.setCalcActivity(calcActivity);
        calcActivity.setCartActivity(shoppingCartActivity);
        calcShoppingCart.setCalcActivity(calcActivity);
        CalcActivityIncentive calcActivityIncentive = new CalcActivityIncentive(calcActivity, "", "");
        calcActivityIncentive.setIncentiveTimes(1);
        Deque<FunctionEnum> functionEnums = new ArrayDeque<>();
        ArrayList<CalcActivityFuncParam> funcParamList = new ArrayList<>();
        CalcActivityFuncParam e = new CalcActivityFuncParam();
        e.setFuncCode(calcFunction0302.getFunction().code());
        e.setParamValue("1");
        funcParamList.add(e);
        FunctionParamMap functionParamMap = new FunctionParamMap(funcParamList);
        CalcFunction calcFunction = calcFunction0206;
        CalcResult calc = calcFunction0302.calc(calcShoppingCart, calcActivityIncentive, functionEnums, functionParamMap, calcFunction);
        Assert.assertEquals(CalcResult.SUCCESS_FALSE.getResultCode(), calc.getResultCode());
    }

    @Test
    public void calcFunction0207(){

        CalcShoppingCart calcShoppingCart = new CalcShoppingCart();
        calcShoppingCart.setPostage(BigDecimal.ZERO);
        calcShoppingCart.setCalcShoppingCartItemList(new ArrayList<>());
        CalcActivity calcActivity = new CalcActivity(redisOpsHelper, "");
        Set<String> strings = new HashSet<>();
        strings.add(SeqNumEnum.A.code());
        strings.add(SeqNumEnum.B.code());
        calcActivity.setSeqNums(strings);
        ShoppingCartActivity shoppingCartActivity = new ShoppingCartActivity();
        ActivityModel activityModel = new ActivityModel();
        activityModel.setTemplateCode("0102020203020412");
        shoppingCartActivity.setActivityModel(activityModel);
        CalcTemplate calcTemplate = new CalcTemplate();
        calcTemplate.setCalcActivity(calcActivity);
        calcActivity.setCartActivity(shoppingCartActivity);
        calcShoppingCart.setCalcActivity(calcActivity);
        CalcActivityIncentive calcActivityIncentive = new CalcActivityIncentive(calcActivity, "", "");
        calcActivityIncentive.setIncentiveTimes(1);
        Deque<FunctionEnum> functionEnums = new ArrayDeque<>();
        ArrayList<CalcActivityFuncParam> funcParamList = new ArrayList<>();
        CalcActivityFuncParam e = new CalcActivityFuncParam();
        e.setFuncCode(calcFunction0302.getFunction().code());
        e.setParamValue("1");
        funcParamList.add(e);
        FunctionParamMap functionParamMap = new FunctionParamMap(funcParamList);
        CalcFunction calcFunction = calcFunction0207;
        CalcResult calc = calcFunction0302.calc(calcShoppingCart, calcActivityIncentive, functionEnums, functionParamMap, calcFunction);
        Assert.assertEquals(CalcResult.SUCCESS_FALSE.getResultCode(), calc.getResultCode());
    }
}
