package com.gtech.promotion.calc;

import com.gtech.promotion.calc.function.CalcFunction0406;
import com.gtech.promotion.calc.model.CalcActivityFuncParam;
import com.gtech.promotion.calc.model.CalcActivityFuncRank;
import com.gtech.promotion.helper.RedisOpsHelper;
import com.gtech.promotion.vo.bean.Giveaway;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

@RunWith(MockitoJUnitRunner.class)
public class CalcFunction0406Test {

    @InjectMocks
    private CalcFunction0406 calcFunction0406;

    @Mock
    private RedisOpsHelper redisOpsHelper;

    @Test
    public void getGiveawayByRank(){
        CalcActivity calcActivity = new CalcActivity(redisOpsHelper, "");
        calcFunction0406.getGiveawayByRank(calcActivity,"1");

        CalcTemplate calcTemplate = new CalcTemplate();
        List<CalcActivityFuncRank> calcFuncRankList = new ArrayList<>();
        calcTemplate.setCalcFuncRankList(calcFuncRankList);
        calcActivity.setCalcTemplate(calcTemplate);
        calcFunction0406.getGiveawayByRank(calcActivity,"1");

        CalcActivityFuncRank calcActivityFuncRank = new CalcActivityFuncRank();
        calcActivityFuncRank.setFuncRankId("123");
        calcFuncRankList.add(calcActivityFuncRank);
        calcFunction0406.getGiveawayByRank(calcActivity,"1");

        List<CalcActivityFuncParam> funcParamList = new ArrayList<>();
        CalcActivityFuncParam calcActivityFuncParam = new CalcActivityFuncParam();
        calcActivityFuncParam.setFuncType("03");
        calcActivityFuncParam.setFuncCode("0302");
        calcActivityFuncParam.setParamValue("1");

        CalcActivityFuncParam calcActivityFuncParam1 = new CalcActivityFuncParam();
        calcActivityFuncParam1.setFuncType("04");
        calcActivityFuncParam1.setFuncCode("0406");
        calcActivityFuncParam1.setParamValue("2");

        funcParamList.add(calcActivityFuncParam);
        funcParamList.add(calcActivityFuncParam1);
        calcActivityFuncRank.setFuncParamList(funcParamList);

        List<Giveaway> giveaways = new ArrayList<>();
        calcActivity.setGiveaways(giveaways);
        calcFunction0406.getGiveawayByRank(calcActivity,"1");
    }
}
