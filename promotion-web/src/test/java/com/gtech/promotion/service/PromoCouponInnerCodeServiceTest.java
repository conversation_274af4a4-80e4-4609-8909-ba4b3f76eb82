/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.service;

import com.github.pagehelper.PageInfo;
import com.gtech.commons.page.PageData;
import com.gtech.commons.utils.DateUtil;
import com.gtech.promotion.code.coupon.CouponStatusEnum;
import com.gtech.promotion.dao.entity.coupon.TPromoCouponInnerCodeEntity;
import com.gtech.promotion.dao.mapper.coupon.TPromoCouponInnerCodeMapper;
import com.gtech.promotion.dao.model.coupon.PromoPassVO;
import com.gtech.promotion.dao.model.coupon.TPromoCouponInnerCodeVO;
import com.gtech.promotion.dto.in.activity.CouponQuantityDTO;
import com.gtech.promotion.dto.in.activity.ManagementDataInDTO;
import com.gtech.promotion.dto.in.coupon.ExportCouponInDTO;
import com.gtech.promotion.dto.in.coupon.TCouponListQueryDTO;
import com.gtech.promotion.dto.out.coupon.ExportCouponOutDTO;
import com.gtech.promotion.page.RequestPage;
import com.gtech.promotion.service.coupon.SqlPublicMethodsService;
import com.gtech.promotion.service.impl.coupon.PromoCouponInnerCodeServiceImpl;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

import java.util.*;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PromoCouponInnerCodeServiceTest {

    @InjectMocks
    private PromoCouponInnerCodeServiceImpl promoCouponInnerCodeService;

    @Mock
    private TPromoCouponInnerCodeMapper couponInnerCodeMapper;

    @Mock
    private SqlPublicMethodsService sql;

    @Before
    public void before(){
        EntityHelper.initEntityNameMap(TPromoCouponInnerCodeEntity.class, new MapperHelper().getConfig());
    }

    @Test
    public void testUpdateCouponToGrantedState(){
        String tenantCode = "1";
        String couponCode = "1";
        String takeLabel = "1";
        List<String> arrayList = new ArrayList();
        arrayList.add(couponCode);
        // when
        when(couponInnerCodeMapper.updateByConditionSelective(any(), any())).thenReturn(1);
        // then
        int i = promoCouponInnerCodeService.updateCouponToGrantedState(tenantCode, arrayList, takeLabel,1);
        Assert.assertEquals(1, i);
    }

    @Test
    public void testUpdateCouponToGrantedState1(){
        String tenantCode = "1";
        String couponCode = "1";
        String takeLabel = "1";
        List<String> arrayList = new ArrayList();
        arrayList.add(couponCode);
        arrayList.add(couponCode);
        // when
        when(couponInnerCodeMapper.updateByConditionSelective(any(), any())).thenReturn(1);
        // then
        int i = promoCouponInnerCodeService.updateCouponToGrantedState(tenantCode, arrayList, takeLabel,2);
        Assert.assertEquals(1, i);
    }

    @Test
    public void testUpdateStatusBatch(){
        String tenantCode = "1";
        List<String> couponCodes = new ArrayList<>();
        couponCodes.add("1");
        // when
        when(couponInnerCodeMapper.updateByConditionSelective(any(), any())).thenReturn(1);
        // then
        int i = promoCouponInnerCodeService.updateStatusBatch(tenantCode, couponCodes, CouponStatusEnum.UN_GRANT);
        Assert.assertEquals(1, i);
    }

    @Test
    public void testSelectCouponList(){
        String tenantCode = "1";
        String activityCode = "1";
        String couponStatus = "1";
        Date createTimeStart = new Date();
        Date createTimeEnd = new Date();
        RequestPage page = new RequestPage();
        String couponCode = "1";

        List<TPromoCouponInnerCodeEntity> list = new ArrayList<>();
        TPromoCouponInnerCodeEntity entity = new TPromoCouponInnerCodeEntity();
        entity.setId(1L);
        list.add(entity);
        // when
        when(couponInnerCodeMapper.selectByCondition(any())).thenReturn(list);
        // then
        PageInfo<TPromoCouponInnerCodeVO> result = promoCouponInnerCodeService.selectCouponList(tenantCode, activityCode, couponStatus, createTimeStart, createTimeEnd, page, couponCode);
        Assert.assertEquals(1, result.getTotal());
        Assert.assertEquals("1", result.getList().get(0).getId());
    }

    @Test
    public void testSelectCouponList1(){
        String tenantCode = "1";
        String activityCode = "1";
        String couponStatus = "";
        Date createTimeStart = null;
        Date createTimeEnd = null;
        RequestPage page = null;
        String couponCode = "";

        List<TPromoCouponInnerCodeEntity> list = new ArrayList<>();
        TPromoCouponInnerCodeEntity entity = new TPromoCouponInnerCodeEntity();
        entity.setId(1L);
        list.add(entity);
        // when
        when(couponInnerCodeMapper.selectByCondition(any())).thenReturn(list);
        // then
        PageInfo<TPromoCouponInnerCodeVO> result = promoCouponInnerCodeService.selectCouponList(tenantCode, activityCode, couponStatus, createTimeStart, createTimeEnd, page, couponCode);
        Assert.assertEquals(1, result.getTotal());
        Assert.assertEquals("1", result.getList().get(0).getId());
    }

    @Test
    public void testSelectCouponList_empty(){
        String tenantCode = "1";
        String activityCode = "";
        String couponStatus = "1";
        Date createTimeStart = new Date();
        Date createTimeEnd = new Date();
        RequestPage page = new RequestPage();
        String couponCode = "1";
        PageInfo<TPromoCouponInnerCodeVO> result = promoCouponInnerCodeService.selectCouponList(tenantCode, activityCode, couponStatus, createTimeStart, createTimeEnd, page, couponCode);
        Assert.assertEquals(0, result.getTotal());
    }

    @Test
    public void testSelectCouponList_empty1(){
        String tenantCode = "";
        String activityCode = "1";
        String couponStatus = "1";
        Date createTimeStart = new Date();
        Date createTimeEnd = new Date();
        RequestPage page = new RequestPage();
        String couponCode = "1";
        PageInfo<TPromoCouponInnerCodeVO> result = promoCouponInnerCodeService.selectCouponList(tenantCode, activityCode, couponStatus, createTimeStart, createTimeEnd, page, couponCode);
        Assert.assertEquals(0, result.getTotal());
    }

    @Test
    public void testSelectCouponList_empty2(){
        String tenantCode = "";
        String activityCode = "";
        String couponStatus = "1";
        Date createTimeStart = new Date();
        Date createTimeEnd = new Date();
        RequestPage page = new RequestPage();
        String couponCode = "1";
        PageInfo<TPromoCouponInnerCodeVO> result = promoCouponInnerCodeService.selectCouponList(tenantCode, activityCode, couponStatus, createTimeStart, createTimeEnd, page, couponCode);
        Assert.assertEquals(0, result.getTotal());
    }

    @Test
    public void testSelectCouponCode(){
        String tenantCode = "1";
        String activityId = "1";
        String releaseId = "1";
        String couponStatus = "1";
        Date createTimeStart = new Date();
        Date createTimeEnd = new Date();
        RequestPage page = new RequestPage();
        String couponCode = "1";

        List<TPromoCouponInnerCodeEntity> list = new ArrayList<>();
        TPromoCouponInnerCodeEntity entity = new TPromoCouponInnerCodeEntity();
        entity.setId(1L);
        list.add(entity);
        // when
        when(couponInnerCodeMapper.selectByCondition(any())).thenReturn(list);
        // then
        TPromoCouponInnerCodeVO innerCodeVO=new TPromoCouponInnerCodeVO();
        innerCodeVO.setTenantCode(tenantCode);
        innerCodeVO.setActivityCode(activityId);
        innerCodeVO.setCouponCode(couponCode);
        innerCodeVO.setReleaseCode(releaseId);
        PageInfo<TPromoCouponInnerCodeVO> result = promoCouponInnerCodeService.selectCouponCode(innerCodeVO, couponStatus, createTimeStart, createTimeEnd, page);
        Assert.assertEquals(1, result.getTotal());
        Assert.assertEquals("1", result.getList().get(0).getId());
    }

    @Test
    public void testSelectCouponCode1(){
        String tenantCode = "1";
        String activityId = "1";
        String releaseId = "";
        String couponStatus = "";
        Date createTimeStart = null;
        Date createTimeEnd = null;
        RequestPage page = null;
        String couponCode = "";

        List<TPromoCouponInnerCodeEntity> list = new ArrayList<>();
        TPromoCouponInnerCodeEntity entity = new TPromoCouponInnerCodeEntity();
        entity.setId(1L);
        list.add(entity);
        // when
        when(couponInnerCodeMapper.selectByCondition(any())).thenReturn(list);
        // then
        TPromoCouponInnerCodeVO innerCodeVO=new TPromoCouponInnerCodeVO();
        innerCodeVO.setTenantCode(tenantCode);
        innerCodeVO.setActivityCode(activityId);
        innerCodeVO.setCouponCode(couponCode);
        innerCodeVO.setReleaseCode(releaseId);
        PageInfo<TPromoCouponInnerCodeVO> result = promoCouponInnerCodeService.selectCouponCode(innerCodeVO, couponStatus, createTimeStart, createTimeEnd, page);
        Assert.assertEquals(1, result.getTotal());
        Assert.assertEquals("1", result.getList().get(0).getId());
    }

    @Test
    public void testGetCouponByActivityCode(){
        String tenantCode = "1";
        String activityCode = "1";
        TPromoCouponInnerCodeEntity entity = new TPromoCouponInnerCodeEntity();
        entity.setId(1L);
        // when
        when(couponInnerCodeMapper.selectOne(any())).thenReturn(entity);
        // then
        TPromoCouponInnerCodeVO result = promoCouponInnerCodeService.getCouponByActivityCode(tenantCode, activityCode);
        Assert.assertEquals("1", result.getId());
    }

    @Test
    public void testUpdateCouponToLockedState(){
        String tenantCode = "1";
        String couponCode = "1";
        String takeLabel = "1";
        // when
        when(couponInnerCodeMapper.updateByConditionSelective(any(), any())).thenReturn(1);
        // then
        int result = promoCouponInnerCodeService.updateCouponToLockedState(tenantCode, couponCode, takeLabel);
        Assert.assertEquals(1, result);
    }

    @Test
    public void testFrozenInnerCode(){
        String tenantCode = "1";
        String couponCode = "1";
        // when
        when(couponInnerCodeMapper.updateByConditionSelective(any(), any())).thenReturn(1);
        // then
        int result = promoCouponInnerCodeService.frozenInnerCode(tenantCode, couponCode,1,0);
        Assert.assertEquals(1, result);
    }


    @Test
    public void frozenFindInnerCode(){
        String tenantCode = "1";
        String couponCode = "1";

        int result = promoCouponInnerCodeService.frozenFindInnerCode(tenantCode, couponCode);
        Assert.assertEquals(0, result);
    }

    @Test
    public void testFrozenInnerCode1(){
        String tenantCode = "1";
        String couponCode = "1";
        // when
        when(couponInnerCodeMapper.updateByConditionSelective(any(), any())).thenReturn(1);
        // then
        int result = promoCouponInnerCodeService.frozenInnerCode(tenantCode, couponCode,2,1);
        Assert.assertEquals(1, result);
    }

    @Test
    public void queryHeaderCouponCodes(){
        promoCouponInnerCodeService.queryHeaderCouponCodes("1","1","1",1);
    }

    @Test
    public void testGetFrozenAndUnGrantCodeCount111(){
        String releaseId = "1";
        // when
        when(couponInnerCodeMapper.selectCount(any())).thenReturn(1);
        // then
        int result = promoCouponInnerCodeService.getFrozenAndUnGrantCodeCount111(releaseId);
        Assert.assertEquals(1, result);
    }

    @Test
    public void updateInnerCouponEndTime(){
        String tenantCode = "1";
        String activityCode = "1";
        String releaseCode = "1";
        String endTime = "1";
        when(couponInnerCodeMapper.updateByConditionSelective(any(), any())).thenReturn(1);
        int i = promoCouponInnerCodeService.updateInnerCouponEndTime(tenantCode, activityCode, releaseCode, endTime);
        Assert.assertEquals(1, i);
    }

    @Test
    public void insertPromoCouponInnerCode(){
        List<TPromoCouponInnerCodeVO> innerCodeVOs = new ArrayList<>();
        for (int i = 0; i < 101; i++) {
            TPromoCouponInnerCodeVO vo = new TPromoCouponInnerCodeVO();
            vo.setCouponCode("100"+i);
            innerCodeVOs.add(vo);
        }
        Mockito.when(couponInnerCodeMapper.insertList(Mockito.any())).thenReturn(1);
        int i = promoCouponInnerCodeService.insertPromoCouponInnerCode(innerCodeVOs);
        Assert.assertEquals(2,i);
    }

    @Test
    public void insertPromoCouponInnerCode1(){
        List<TPromoCouponInnerCodeVO> innerCodeVOs = new ArrayList<>();
        for (int i = 0; i < 100; i++) {
            TPromoCouponInnerCodeVO vo = new TPromoCouponInnerCodeVO();
            vo.setCouponCode("100"+i);
            innerCodeVOs.add(vo);
        }
        int i = promoCouponInnerCodeService.insertPromoCouponInnerCode(innerCodeVOs);
        Assert.assertEquals(0,i);
    }

    @Test
    public void insertPromoCouponInnerCode_is_null(){
        List<TPromoCouponInnerCodeVO> innerCodeVOs = new ArrayList<>();
        for (int i = 0; i < 100; i++) {
            TPromoCouponInnerCodeVO vo = new TPromoCouponInnerCodeVO();
            vo.setCouponCode("100"+i);
            innerCodeVOs.add(vo);
        }
        int i = promoCouponInnerCodeService.insertPromoCouponInnerCode(null);
        Assert.assertEquals(0,i);
    }

    @Test
    public void findCouponByCouponCode(){
        TPromoCouponInnerCodeEntity vo = new TPromoCouponInnerCodeEntity();
        Mockito.when(couponInnerCodeMapper.selectOne(Mockito.any())).thenReturn(vo);
        TPromoCouponInnerCodeVO code = promoCouponInnerCodeService.findCouponByCouponCode("", "");
        Assert.assertNotNull(code);
    }

    @Test
    public void updateCouponInnerFrozenStatus(){
        Mockito.when(couponInnerCodeMapper.updateByConditionSelective(Mockito.any(),Mockito.any())).thenReturn(1);
        int i = promoCouponInnerCodeService.updateCouponInnerFrozenStatus("1", "1");
        Assert.assertEquals(1,i);
    }

    @Test
    public void findCouponByCouponCodeOrPassword(){

        List<TPromoCouponInnerCodeEntity> tPromoCouponInnerCodeEntities = new ArrayList<>();

        Mockito.when(couponInnerCodeMapper.selectByCondition(Mockito.any())).thenReturn(tPromoCouponInnerCodeEntities);

        TPromoCouponInnerCodeVO couponByCouponCodeOrPassword = promoCouponInnerCodeService.findCouponByCouponCodeOrPassword("1", "1");

        Assert.assertNull(couponByCouponCodeOrPassword);
    }

    @Test
    public void findCouponByCouponCodeOrPassword1(){

        List<TPromoCouponInnerCodeEntity> tPromoCouponInnerCodeEntities = new ArrayList<>();
        TPromoCouponInnerCodeEntity entity = new TPromoCouponInnerCodeEntity();
        tPromoCouponInnerCodeEntities.add(entity);
        Mockito.when(couponInnerCodeMapper.selectByCondition(Mockito.any())).thenReturn(tPromoCouponInnerCodeEntities);

        TPromoCouponInnerCodeVO couponByCouponCodeOrPassword = promoCouponInnerCodeService.findCouponByCouponCodeOrPassword("1", "1");

        Assert.assertNotNull(couponByCouponCodeOrPassword);
    }

    @Test
    public void logicDelete(){
        Mockito.when(couponInnerCodeMapper.updateByConditionSelective(Mockito.any(),Mockito.any())).thenReturn(1);
        int i = promoCouponInnerCodeService.logicDelete("1", "1");
        Assert.assertEquals(1,i);
    }

    @Test
    public void updateInnerCoudeById(){
        TPromoCouponInnerCodeVO innerCodeVO = new TPromoCouponInnerCodeVO();
        Mockito.when(couponInnerCodeMapper.updateByPrimaryKey(Mockito.any())).thenReturn(1);
        int i = promoCouponInnerCodeService.updateInnerCoudeById(innerCodeVO);
        Assert.assertEquals(1,i);
    }

    @Test
    public void updateInnerCodeByCode(){
        Mockito.when(couponInnerCodeMapper.updateByConditionSelective(Mockito.any(),Mockito.any())).thenReturn(1);
        int i = promoCouponInnerCodeService.updateInnerCodeByCode("1", "1","1");
        Assert.assertEquals(1,i);
    }

    @Test
    public void getCouponInnerCodeByCodes(){
        List<TPromoCouponInnerCodeEntity> innerCodeEntities = new ArrayList<>();
        List<String> couponCodes = new ArrayList<>();
        Mockito.when(couponInnerCodeMapper.selectByCondition(Mockito.any())).thenReturn(innerCodeEntities);
        List<TPromoCouponInnerCodeVO> codes = promoCouponInnerCodeService.getCouponInnerCodeByCodes("1", couponCodes);
        Assert.assertEquals(0,codes.size());
    }

    @Test
    public void expireByActivityCode_tenant_empty(){
        int i = promoCouponInnerCodeService.expireByActivityCode("", "", "");
        Assert.assertEquals(0,i);
    }

    @Test
    public void expireByActivityCode_tenant_empty1(){
        int i = promoCouponInnerCodeService.expireByActivityCode("1", "", "");
        Assert.assertEquals(0,i);
    }
    @Test
    public void expireByActivityCode_tenant_empty2(){
        int i = promoCouponInnerCodeService.expireByActivityCode("", "1", "");
        Assert.assertEquals(0,i);
    }

    @Test
    public void expireByActivityCode(){

        Mockito.when(couponInnerCodeMapper.updateByConditionSelective(Mockito.any(),Mockito.any())).thenReturn(1);
        int i = promoCouponInnerCodeService.expireByActivityCode("1", "1", "1");
        Assert.assertEquals(1,i);
    }

    @Test
    public void expireByActivityCode1(){

        Mockito.when(couponInnerCodeMapper.updateByConditionSelective(Mockito.any(),Mockito.any())).thenReturn(1);
        int i = promoCouponInnerCodeService.expireByActivityCode("1", "1", "");
        Assert.assertEquals(1,i);
    }

    @Test
    public void updateCouponStatus(){

        Mockito.when(couponInnerCodeMapper.updateByConditionSelective(Mockito.any(),Mockito.any())).thenReturn(1);
        int i = promoCouponInnerCodeService.updateCouponStatus("", "", "", CouponStatusEnum.EXPIRE);
        Assert.assertEquals(1,i);
    }

    @Test
    public void updateCouponStatus1(){

        Mockito.when(couponInnerCodeMapper.updateByConditionSelective(Mockito.any(),Mockito.any())).thenReturn(1);
        int i = promoCouponInnerCodeService.updateCouponStatus("", "", "", CouponStatusEnum.UN_GRANT);
        Assert.assertEquals(1,i);
    }

    @Test
    public void queryInnerCouponByReleaseCode(){
        List<TPromoCouponInnerCodeEntity> innerCodeEntities = new ArrayList<>();
        Mockito.when(couponInnerCodeMapper.select(Mockito.any())).thenReturn(innerCodeEntities);
        List<TPromoCouponInnerCodeVO> innerCodeVOS = promoCouponInnerCodeService.queryInnerCouponByReleaseCode("", "", "");
        Assert.assertEquals(0,innerCodeVOS.size());
    }

    @Test
    public void queryManagementData(){
        ManagementDataInDTO dataInDTO = new ManagementDataInDTO();
        dataInDTO.setTenantCode("1");
        dataInDTO.setReleaseCodes(new ArrayList<>());
        List<TPromoCouponInnerCodeEntity> innerCodeEntities = new ArrayList<>();
        Mockito.when(couponInnerCodeMapper.selectByCondition(Mockito.any())).thenReturn(innerCodeEntities);
        Mockito.doNothing().when(sql).sqlSelectCondition(Mockito.any(), Mockito.any());

        PageData<TPromoCouponInnerCodeVO> pageData = promoCouponInnerCodeService.queryManagementData(dataInDTO);
        Assert.assertEquals(0,pageData.getList().size());
    }

    @Test
    public void deleteCouponInnerCode111(){
        Mockito.when(couponInnerCodeMapper.delete(Mockito.any())).thenReturn(1);
        int i = promoCouponInnerCodeService.deleteCouponInnerCode111("", "");
        Assert.assertEquals(1,i);
    }


    @Test
    public void exportCoupon(){
        List<TPromoCouponInnerCodeEntity> innerCodeEntities = new ArrayList<>();
        ExportCouponInDTO exportCouponInDTO = new ExportCouponInDTO();
        Mockito.when(couponInnerCodeMapper.selectByCondition(Mockito.any())).thenReturn(innerCodeEntities);
        List<ExportCouponOutDTO> i = promoCouponInnerCodeService.exportCoupon(exportCouponInDTO);
        Assert.assertEquals(0,i.size());
    }

    @Test
    public void expireCouponCode(){
        Mockito.when(couponInnerCodeMapper.updateByConditionSelective(Mockito.any(),Mockito.any())).thenReturn(1);
        int i = promoCouponInnerCodeService.expireCouponCode();
        Assert.assertEquals(1,i);
    }
    @Test
    public void findCouponCodeData(){
        ManagementDataInDTO dataInDTO= new ManagementDataInDTO();
        List<TPromoCouponInnerCodeEntity> innerCodeEntities = new ArrayList<>();
        Mockito.when(couponInnerCodeMapper.selectByCondition(Mockito.any())).thenReturn(innerCodeEntities);
        PageData<TPromoCouponInnerCodeVO> i = promoCouponInnerCodeService.findCouponCodeData(dataInDTO);
        Assert.assertEquals(0,i.getList().size());
    }

    @Test
    public void findActivityCodeByCouponCode(){
        Mockito.when(couponInnerCodeMapper.selectOne(Mockito.any())).thenReturn(new TPromoCouponInnerCodeEntity());
        promoCouponInnerCodeService.findActivityCodeByCouponCode("1","1");
    }


    @Test
    public void queryActivityByCouponCodes(){
        Mockito.when(couponInnerCodeMapper.selectByCondition(Mockito.any())).thenReturn(new ArrayList<>());
        promoCouponInnerCodeService.queryActivityByCouponCodes("1",Arrays.asList("!"));
    }

    @Test
    public void countCouponCode(){
        promoCouponInnerCodeService.countCouponCode("1","1","1");
    }


    @Test
    public void countAnonymityCouponCode(){
        promoCouponInnerCodeService.countAnonymityCouponCode("1","1","1");
    }

    @Test
    public void selectCouponCodeList(){
        promoCouponInnerCodeService.selectCouponCodeList(new TCouponListQueryDTO(),new Date(),new Date());
    }

    @Test
    public void getCouponQuantity(){
        when(couponInnerCodeMapper.getCouponQuantity(any())).thenReturn(1);
        CouponQuantityDTO couponQuantityDTO = new CouponQuantityDTO();
        promoCouponInnerCodeService.getCouponQuantity(couponQuantityDTO);
    }

    @Test
    public void updateBatchInnerCodeValidTime(){
        List<TPromoCouponInnerCodeVO> vos = new ArrayList<>();
        TPromoCouponInnerCodeVO entity = new TPromoCouponInnerCodeVO();

        entity.setTenantCode("1");
        entity.setCouponCode("1");
        Calendar instance = Calendar.getInstance();
        Date startDate = instance.getTime();
        String startTime = DateUtil.format(startDate, DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        entity.setValidStartTime(startTime);
        Date endDate = instance.getTime();
        String endTime = DateUtil.format(endDate, DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        entity.setValidEndTime(endTime);
        vos.add(entity);
        promoCouponInnerCodeService.updateBatchInnerCodeValidTime(vos);
    }

    @Test
    public void findCouponCodeByPassword(){
        TPromoCouponInnerCodeEntity entity = new TPromoCouponInnerCodeEntity();

        entity.setTenantCode("1");
        entity.setCouponCode("1");
        Calendar instance = Calendar.getInstance();
        Date startDate = instance.getTime();
        String startTime = DateUtil.format(startDate, DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        entity.setValidStartTime(startTime);
        Date endDate = instance.getTime();
        String endTime = DateUtil.format(endDate, DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        entity.setValidEndTime(endTime);
        List<TPromoCouponInnerCodeEntity> byExample = new ArrayList<>();
        byExample.add(entity);

        when(couponInnerCodeMapper.selectByCondition(any())).thenReturn(byExample);

        List<PromoPassVO> couponCodeByPassword = promoCouponInnerCodeService.findCouponCodeByPassword("1", "1");
        Assert.assertEquals(1,couponCodeByPassword.size());

    }


    @Test
    public void findCouponCodeByPassword_null(){

        TPromoCouponInnerCodeEntity entity = new TPromoCouponInnerCodeEntity();

        entity.setTenantCode("1");
        entity.setCouponCode("1");
        Calendar instance = Calendar.getInstance();
        Date startDate = instance.getTime();
        String startTime = DateUtil.format(startDate, DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        entity.setValidStartTime(startTime);
        Date endDate = instance.getTime();
        String endTime = DateUtil.format(endDate, DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        entity.setValidEndTime(endTime);

        List<TPromoCouponInnerCodeEntity> byExample = new ArrayList<>();

        when(couponInnerCodeMapper.selectByCondition(any())).thenReturn(byExample);

        List<PromoPassVO> couponCodeByPassword = promoCouponInnerCodeService.findCouponCodeByPassword("1", "1");
        Assert.assertEquals(0,couponCodeByPassword.size());

    }

    @Test
    public void updateBatchInnerCodeByCouponCode() {

        TPromoCouponInnerCodeVO vo = new TPromoCouponInnerCodeVO();

        vo.setTenantCode("1");
        vo.setCouponCode("1");
        Calendar instance = Calendar.getInstance();
        Date startDate = instance.getTime();
        String startTime = DateUtil.format(startDate, DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        vo.setValidStartTime(startTime);
        Date endDate = instance.getTime();
        String endTime = DateUtil.format(endDate, DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        vo.setValidEndTime(endTime);

        //when(couponInnerCodeMapper.updateByConditionSelective(any(), any())).thenReturn(1);

        promoCouponInnerCodeService.updateBatchInnerCodeByCouponCodes(Arrays.asList(vo));

    }
}
