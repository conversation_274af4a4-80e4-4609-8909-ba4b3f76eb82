
package com.gtech.promotion.service.marketing.impl;

import com.gtech.promotion.code.marketing.LuckeyDrawPrizeProductStatusEnum;
import com.gtech.promotion.dao.entity.marketing.MarketingLuckDrawPrizeProductEntity;
import com.gtech.promotion.dao.mapper.marketing.MarketingLuckDrawPrizeProductMapper;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

public class MarketingLuckDrawPrizeProductServiceImplTest {
    private final String tenantCode = "T001";
    private final String activityCode = "A001";
    private final String memberCode = "M001";
    private final String orderId = "O001";
    private final String prizeProductCode = "PP001";
    @Mock
    private MarketingLuckDrawPrizeProductMapper marketingLuckDrawPrizeProductMapper;

    @InjectMocks
    private MarketingLuckDrawPrizeProductServiceImpl service;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
            EntityHelper.initEntityNameMap(MarketingLuckDrawPrizeProductEntity.class, new MapperHelper().getConfig());
    }

    @Test
    public void testCheckLuckDrawPrizeProduct() {
        // Arrange
        String domainCode = "domain";
        String tenantCode = "tenant";
        String userCode = "user";
        String activityCode = "activity";
        String productCode = "product";
        Example example = new Example(MarketingLuckDrawPrizeProductEntity.class);
        example.createCriteria()
                .andEqualTo("domainCode", domainCode)
                .andEqualTo("tenantCode", tenantCode)
                .andEqualTo("memberCode", userCode)
                .andEqualTo("activityCode", activityCode)
                .andEqualTo("productCode", productCode)
                .andEqualTo("status", "UN_USED");

        List<MarketingLuckDrawPrizeProductEntity> entities = Collections.emptyList();
        when(marketingLuckDrawPrizeProductMapper.selectByCondition(any(Example.class))).thenReturn(entities);

        // Act
        boolean result = service.checkLuckDrawPrizeProduct(domainCode, tenantCode, userCode, activityCode, productCode);

        // Assert
        assertFalse(result);
    }

    @Test
    public void testLockPrizeProduct() {
        // Arrange
        String tenantCode = "tenant";
        String activityCode = "activity";
        String memberCode = "user";
        String productCode = "product";
        String orderId = "order";
        Example example = new Example(MarketingLuckDrawPrizeProductEntity.class);
        example.createCriteria()
                .andEqualTo("tenantCode", tenantCode)
                .andEqualTo("activityCode", activityCode)
                .andEqualTo("productCode", productCode)
                .andEqualTo("memberCode", memberCode)
                .andEqualTo("status", "UN_USED");
        List<MarketingLuckDrawPrizeProductEntity> entities = Collections.singletonList(new MarketingLuckDrawPrizeProductEntity());
        when(marketingLuckDrawPrizeProductMapper.selectByExample(any(Example.class))).thenReturn(entities);

        // Act
        boolean result = service.lockPrizeProduct(tenantCode, activityCode, memberCode, productCode, orderId);

    }

    @Test
    public void testCancelPrizeProduct_Success() {
        // Arrange
        String tenantCode = "T001";
        String activityCode = "A001";
        String memberCode = "M001";
        String orderId = "O001";

        MarketingLuckDrawPrizeProductEntity prizeProduct = new MarketingLuckDrawPrizeProductEntity();
        prizeProduct.setLuckyDrawPrizeProductCode("LP001");
        prizeProduct.setStatus(LuckeyDrawPrizeProductStatusEnum.USED.code());

        when(marketingLuckDrawPrizeProductMapper.selectOneByExample(any(Example.class))).thenReturn(prizeProduct);
        when(marketingLuckDrawPrizeProductMapper.updateByExampleSelective(any(MarketingLuckDrawPrizeProductEntity.class), any(Example.class))).thenReturn(1);

        // Act
        Boolean result = service.cancelPrizeProduct(tenantCode, activityCode, memberCode, orderId);


    }

    @Test
    public void testCancelPrizeProduct_Failure_NotFound() {
        // Arrange
        String tenantCode = "T001";
        String activityCode = "A001";
        String memberCode = "M001";
        String orderId = "O001";

        when(marketingLuckDrawPrizeProductMapper.selectOneByExample(any(Example.class))).thenReturn(null);

        // Act
        Boolean result = service.cancelPrizeProduct(tenantCode, activityCode, memberCode, orderId);

    }



    @Test
    public void testUsedPrizeProduct_WhenPrizeProductExistsAndIsLock_ShouldReturnTrue() {
        // Arrange (Setup your test, no need to arrange since we have @BeforeEach)
        MarketingLuckDrawPrizeProductEntity prizeProduct = new MarketingLuckDrawPrizeProductEntity();
        prizeProduct.setLuckyDrawPrizeProductCode("123");
        Mockito.when(marketingLuckDrawPrizeProductMapper.selectOneByExample(Mockito.any())).thenReturn(prizeProduct);

        // Act
        Boolean result = service.usedPrizeProduct(tenantCode, activityCode, memberCode, orderId);


    }

    @Test
    public void testUsedPrizeProduct_WhenPrizeProductDoesNotExist_ShouldReturnFalse() {
        // Arrange
        when(marketingLuckDrawPrizeProductMapper.selectOneByExample(any(Example.class)))
                .thenReturn(null);

        // Act
        Boolean result = service.usedPrizeProduct(tenantCode, activityCode, memberCode, orderId);


    }

}