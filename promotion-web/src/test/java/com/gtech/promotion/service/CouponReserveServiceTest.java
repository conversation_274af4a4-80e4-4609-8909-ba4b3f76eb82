package com.gtech.promotion.service;

import com.gtech.promotion.dao.entity.coupon.CouponReserveEntity;
import com.gtech.promotion.dao.mapper.coupon.CouponReserveMapper;
import com.gtech.promotion.dto.in.coupon.ReserveCouponDto;
import com.gtech.promotion.service.impl.coupon.CouponReserveServiceImpl;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

@RunWith(MockitoJUnitRunner.class)
public class CouponReserveServiceTest {

    @InjectMocks
    private CouponReserveServiceImpl couponReserveService;

    @Mock
    private CouponReserveMapper couponReserveMapper;

    @Before
    public void before(){
        EntityHelper.initEntityNameMap(CouponReserveEntity.class, new MapperHelper().getConfig());
    }

    @Test
    public void testAddReserve() {
        ReserveCouponDto reserveCouponDto = new ReserveCouponDto();
        Mockito.when(couponReserveMapper.insertSelective(Mockito.any())).thenReturn(1);
        int i = couponReserveService.addReserve(reserveCouponDto);
        Assert.assertEquals(1, i);
    }

    @Test
    public void testGetReserve() {
        ReserveCouponDto reserveCouponDto = new ReserveCouponDto();
        Mockito.when(couponReserveMapper.selectOne(Mockito.any())).thenReturn(null);
        ReserveCouponDto reserve = couponReserveService.getReserve(reserveCouponDto);
        Assert.assertNull(reserve);
    }

    @Test
    public void testUpdateReserve() {
        ReserveCouponDto reserveCouponDto = new ReserveCouponDto();
        Mockito.when(couponReserveMapper.updateByConditionSelective(Mockito.any(), Mockito.any())).thenReturn(1);
        int i = couponReserveService.updateReserve(reserveCouponDto);
        Assert.assertEquals(1, i);
    }
}