package com.gtech.promotion.service.flash;

import com.gtech.promotion.dao.entity.marketing.RightOfFirstRefusalEntity;
import com.gtech.promotion.dao.mapper.marketing.RightOfFirstRefusalMapper;
import com.gtech.promotion.dto.in.flashsale.WriteOffOfPreEmptiveRightsDto;
import com.gtech.promotion.service.marketing.impl.RightOfFirstRefusalServiceImpl;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-09-18 10:45
 */
@RunWith(MockitoJUnitRunner.class)
public class RightOfFirstRefusalServiceTest {
    @InjectMocks
    private RightOfFirstRefusalServiceImpl rightOfFirstRefusalService;
    @Mock
    private RightOfFirstRefusalMapper rightOfFirstRefusalMapper;

    @Before
    public void before(){
        EntityHelper.initEntityNameMap(RightOfFirstRefusalEntity.class, new MapperHelper().getConfig());
    }

    @Test
    public void findRightOfFirstRefusalByMember(){

        WriteOffOfPreEmptiveRightsDto dto = new WriteOffOfPreEmptiveRightsDto();

        RightOfFirstRefusalEntity rightOfFirstRefusalByMember = rightOfFirstRefusalService.findRightOfFirstRefusalByMember(dto);
        Assert.assertNull(rightOfFirstRefusalByMember);
    }

    @Test
    public void updateStatus(){

        WriteOffOfPreEmptiveRightsDto dto = new WriteOffOfPreEmptiveRightsDto();
        dto.setDomainCode("123");
        dto.setMemberCode("123");
        dto.setActivityCode("123");
        dto.setOrgCode("123");
        int i = rightOfFirstRefusalService.updateStatus(dto);
        Assert.assertEquals(0,0);
    }

    @Test
    public void queryRightOfFirstRefusalByMember(){

        WriteOffOfPreEmptiveRightsDto dto = new WriteOffOfPreEmptiveRightsDto();
        dto.setRightOfFirstRefusalCode("!");
        dto.setRightOfFirstRefusalProductCode("1");
        List<RightOfFirstRefusalEntity> rightOfFirstRefusalEntities = rightOfFirstRefusalService.queryRightOfFirstRefusalByMember(dto);
        Assert.assertEquals(0,rightOfFirstRefusalEntities.size());
    }

    @Test
    public void queryRightOfFirstRefusalByMember_empty(){

        WriteOffOfPreEmptiveRightsDto dto = new WriteOffOfPreEmptiveRightsDto();

        List<RightOfFirstRefusalEntity> rightOfFirstRefusalEntities = rightOfFirstRefusalService.queryRightOfFirstRefusalByMember(dto);
        Assert.assertEquals(0,rightOfFirstRefusalEntities.size());
    }
}
