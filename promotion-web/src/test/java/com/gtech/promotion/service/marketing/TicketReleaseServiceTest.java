package com.gtech.promotion.service.marketing;

import com.gtech.promotion.dao.entity.marketing.TicketReleaseEntity;
import com.gtech.promotion.dao.mapper.marketing.TicketReleaseMapper;
import com.gtech.promotion.dao.model.marketing.BaseModel;
import com.gtech.promotion.dto.in.marketing.TicketSendOutDto;
import com.gtech.promotion.exception.PromotionException;
import com.gtech.promotion.helper.RedisLock;
import com.gtech.promotion.service.marketing.impl.TicketReleaseServiceImpl;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

import java.util.ArrayList;
import java.util.List;

@RunWith(MockitoJUnitRunner.class)
public class TicketReleaseServiceTest {

    @InjectMocks
    private TicketReleaseServiceImpl ticketReleaseService;

    @Mock
    private TicketReleaseMapper releaseMapper;

    @Mock
    private RedisLock redisLock;

    @Before
    public void before(){
        EntityHelper.initEntityNameMap(TicketReleaseEntity.class, new MapperHelper().getConfig());
    }

    @Test(expected = PromotionException.class)
    public void deductInventory(){
        BaseModel baseModel = new BaseModel();
//        Mockito.when(redisLock.tryLockAndRetry(Mockito.any(), Mockito.any(), Mockito.anyInt())).thenReturn("");
        ticketReleaseService.deductInventory(baseModel, 1);
    }

    @Test(expected = PromotionException.class)
    public void deductInventory1(){
        BaseModel baseModel = new BaseModel();
        List<TicketReleaseEntity> ticketReleaseEntities = new ArrayList<>();
//        Mockito.when(redisLock.tryLockAndRetry(Mockito.any(), Mockito.any(), Mockito.anyInt())).thenReturn("key");
        Mockito.when(releaseMapper.selectByCondition(Mockito.any())).thenReturn(ticketReleaseEntities);
//        Mockito.doNothing().when(redisLock).unlock(Mockito.anyString(), Mockito.anyString());
        ticketReleaseService.deductInventory(baseModel, 1);
    }

    @Test(expected = PromotionException.class)
    public void deductInventory2(){
        BaseModel baseModel = new BaseModel();
        List<TicketReleaseEntity> ticketReleaseEntities = new ArrayList<>();
        TicketReleaseEntity e = new TicketReleaseEntity();
        e.setInventory(10L);
        ticketReleaseEntities.add(e);
//        Mockito.when(redisLock.tryLockAndRetry(Mockito.any(), Mockito.any(), Mockito.anyInt())).thenReturn("key");
        Mockito.when(releaseMapper.selectByCondition(Mockito.any())).thenReturn(ticketReleaseEntities);
//        Mockito.doNothing().when(redisLock).unlock(Mockito.anyString(), Mockito.anyString());
        ticketReleaseService.deductInventory(baseModel, 20);
    }

    @Test(expected = PromotionException.class)
    public void deductInventory3(){
        BaseModel baseModel = new BaseModel();
        List<TicketReleaseEntity> ticketReleaseEntities = new ArrayList<>();
        TicketReleaseEntity e = new TicketReleaseEntity();
        e.setInventory(10L);
        ticketReleaseEntities.add(e);
        TicketReleaseEntity e1 = new TicketReleaseEntity();
        e1.setInventory(10L);
        ticketReleaseEntities.add(e1);
//        Mockito.when(redisLock.tryLockAndRetry(Mockito.any(), Mockito.any(), Mockito.anyInt())).thenReturn("key");
        Mockito.when(releaseMapper.selectByCondition(Mockito.any())).thenReturn(ticketReleaseEntities);
        Mockito.when(releaseMapper.updateInventory(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyLong())).thenReturn(0);
//        Mockito.doNothing().when(redisLock).unlock(Mockito.anyString(), Mockito.anyString());
        ticketReleaseService.deductInventory(baseModel, 2);
    }

    @Test
    public void deductInventory4(){
        BaseModel baseModel = new BaseModel();
        List<TicketReleaseEntity> ticketReleaseEntities = new ArrayList<>();
        TicketReleaseEntity e = new TicketReleaseEntity();
        e.setInventory(10L);
        ticketReleaseEntities.add(e);
        TicketReleaseEntity e1 = new TicketReleaseEntity();
        e1.setInventory(10L);
        ticketReleaseEntities.add(e1);
//        Mockito.when(redisLock.tryLockAndRetry(Mockito.any(), Mockito.any(), Mockito.anyInt())).thenReturn("key");
        Mockito.when(releaseMapper.selectByCondition(Mockito.any())).thenReturn(ticketReleaseEntities);
        Mockito.when(releaseMapper.updateInventory(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyLong())).thenReturn(1);
//        Mockito.doNothing().when(redisLock).unlock(Mockito.anyString(), Mockito.anyString());
        List<TicketSendOutDto> ticketSendOutDtos = ticketReleaseService.deductInventory(baseModel, 2);
        Assert.assertEquals(2, ticketSendOutDtos.size());
    }


    @Test
    public void addUsed(){
        BaseModel baseModel = new BaseModel();
        Mockito.when(releaseMapper.addUsed(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(0);
        int i = ticketReleaseService.addUsed(baseModel, "");
        Assert.assertEquals(0, i);
    }


    @Test
    public void deductInventory5(){
        BaseModel baseModel = new BaseModel();
        List<TicketReleaseEntity> ticketReleaseEntities = new ArrayList<>();
        TicketReleaseEntity e = new TicketReleaseEntity();
        e.setInventory(10L);
        ticketReleaseEntities.add(e);
        TicketReleaseEntity e1 = new TicketReleaseEntity();
        e1.setInventory(10L);
        ticketReleaseEntities.add(e1);
//        Mockito.when(redisLock.tryLockAndRetry(Mockito.any(), Mockito.any(), Mockito.anyInt())).thenReturn("key");
//        Mockito.when(releaseMapper.selectByCondition(Mockito.any())).thenReturn(ticketReleaseEntities);
//        Mockito.doNothing().when(redisLock).unlock(Mockito.anyString(), Mockito.anyString());
        List<TicketSendOutDto> ticketSendOutDtos = ticketReleaseService.deductInventory(baseModel, 0);
        Assert.assertEquals(0, ticketSendOutDtos.size());
    }

    @Test
    public void deductInventory6(){
        BaseModel baseModel = new BaseModel();
        List<TicketReleaseEntity> ticketReleaseEntities = new ArrayList<>();
        TicketReleaseEntity e = new TicketReleaseEntity();
        e.setInventory(10L);
        ticketReleaseEntities.add(e);
        TicketReleaseEntity e1 = new TicketReleaseEntity();
        e1.setInventory(8L);
        ticketReleaseEntities.add(e1);
//        Mockito.when(redisLock.tryLockAndRetry(Mockito.any(), Mockito.any(), Mockito.anyInt())).thenReturn("key");
        Mockito.when(releaseMapper.selectByCondition(Mockito.any())).thenReturn(ticketReleaseEntities);
        Mockito.when(releaseMapper.updateInventory(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyLong())).thenReturn(1);
//        Mockito.doNothing().when(redisLock).unlock(Mockito.anyString(), Mockito.anyString());
        List<TicketSendOutDto> ticketSendOutDtos = ticketReleaseService.deductInventory(baseModel, 11);
        Assert.assertEquals(2, ticketSendOutDtos.size());
    }
}
