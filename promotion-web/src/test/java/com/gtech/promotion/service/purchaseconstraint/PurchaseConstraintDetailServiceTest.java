package com.gtech.promotion.service.purchaseconstraint;

import com.google.common.collect.Lists;
import com.gtech.promotion.dao.entity.purchaseconstraint.PurchaseConstraintDetail;
import com.gtech.promotion.dao.mapper.purchaseconstraint.PurchaseConstraintDetailMapper;
import com.gtech.promotion.dao.model.purchaseconstraint.PurchaseConstraintDetailListModel;
import com.gtech.promotion.service.purchaseconstraint.impl.PurchaseConstraintDetailServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@RunWith(MockitoJUnitRunner.class)
public class PurchaseConstraintDetailServiceTest {
    @InjectMocks
    private PurchaseConstraintDetailServiceImpl purchaseConstraintDetailService;
    @Mock
    private PurchaseConstraintDetailMapper purchaseConstraintDetailMapper;

    @Before
    public void before() {
        EntityHelper.initEntityNameMap(PurchaseConstraintDetail.class, new MapperHelper().getConfig());
    }
    @Test
    public void insert() {
        purchaseConstraintDetailService.insert(new PurchaseConstraintDetail());
    }

    @Test
    public void testInsert() {
        purchaseConstraintDetailService.insert(Lists.newArrayList(new PurchaseConstraintDetail()));
    }

    @Test
    public void list() {
        purchaseConstraintDetailService.list(new PurchaseConstraintDetailListModel());

    }

    @Test
    public void decrement() {
        purchaseConstraintDetailService.decrement(new PurchaseConstraintDetail());
    }


    @Test
    public void testDelete_NonEmptyList() {
        List<PurchaseConstraintDetail> detailInserts = new ArrayList<>();
        PurchaseConstraintDetail detail = new PurchaseConstraintDetail();
        detail.setDetailQty(1);
        detail.setDetailQtyBack(1);
        detailInserts.add(detail);
        // Act
        int result = purchaseConstraintDetailService.delete(detailInserts);


    }



}
