package com.gtech.promotion.service.point;

import com.github.pagehelper.Page;
import com.gtech.promotion.code.point.PointTransactionTypeEnum;
import com.gtech.promotion.dao.entity.point.PointAccountEntity;
import com.gtech.promotion.dao.entity.point.PointCampaignEntity;
import com.gtech.promotion.dao.entity.point.PointTransactionEntity;
import com.gtech.promotion.dao.mapper.point.PointCampaignMapper;
import com.gtech.promotion.dao.mapper.point.PointTransactionMapper;
import com.gtech.promotion.dto.in.point.PointTransactionDto;
import com.gtech.promotion.service.point.impl.PointTransactionServiceImpl;
import com.gtech.promotion.vo.param.point.PointTransactionParam;
import com.gtech.promotion.vo.param.point.query.GetPointTransactionParam;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2021/8/6 14:15
 */
@RunWith(MockitoJUnitRunner.class)
public class PointTransactionServiceTest {

    @InjectMocks
    private PointTransactionServiceImpl pointTransactionService;

    @Mock
    PointTransactionMapper pointTransactionMapper;

    @Mock
    private PointCampaignMapper pointCampaignMapper;

    @Before
    public void before() {
        EntityHelper.initEntityNameMap(PointAccountEntity.class, new MapperHelper().getConfig());
        EntityHelper.initEntityNameMap(PointTransactionEntity.class, new MapperHelper().getConfig());
        EntityHelper.initEntityNameMap(PointCampaignEntity.class, new MapperHelper().getConfig());
    }

    @Test
    public void savePointTransaction() {
        PointTransactionParam uniqueVo = new PointTransactionParam();
        uniqueVo.setCampaignCode("100001");
        uniqueVo.setTransactionSn("100001");
        uniqueVo.setTransactionType(PointTransactionTypeEnum.INCREASE.number());
        uniqueVo.setTenantCode("100001");
        List<PointTransactionEntity> list = new ArrayList<>();
        PointCampaignEntity pointCampaignEntity = new PointCampaignEntity();
        pointCampaignEntity.setEndTime("**************");
        PointTransactionEntity pointTransactionEntity = new PointTransactionEntity();
        list.add(pointTransactionEntity);
        Mockito.when(pointTransactionMapper.insert(Mockito.any())).thenReturn(1);
        Mockito.when(pointCampaignMapper.selectOne(Mockito.any())).thenReturn(pointCampaignEntity);
        Assert.assertNotNull(pointTransactionService.savePointTransaction(uniqueVo));
    }

    @Test
    public void getPointTransaction() {
        GetPointTransactionParam uniqueVo = new GetPointTransactionParam();
        uniqueVo.setTransactionSn("100001");
        uniqueVo.setTenantCode("100001");
        List<PointTransactionEntity> list = new ArrayList<>();
        PointCampaignEntity pointCampaignEntity = new PointCampaignEntity();
        pointCampaignEntity.setEndTime("**************");
        PointTransactionEntity pointTransactionEntity = new PointTransactionEntity();
        list.add(pointTransactionEntity);
        Mockito.when(pointTransactionMapper.selectOne(Mockito.any())).thenReturn(new PointTransactionEntity());
        Assert.assertNotNull(pointTransactionService.getPointTransaction(uniqueVo));
    }

    @Test
    public void queryPointTransactionEndTime() {
        GetPointTransactionParam uniqueVo = new GetPointTransactionParam();
        uniqueVo.setTransactionSn("100001");
        uniqueVo.setTenantCode("100001");
        List<PointTransactionEntity> list = new ArrayList<>();
        PointCampaignEntity pointCampaignEntity = new PointCampaignEntity();
        pointCampaignEntity.setEndTime("**************");
        PointTransactionEntity pointTransactionEntity = new PointTransactionEntity();
        list.add(pointTransactionEntity);
        Mockito.when(pointTransactionMapper.selectEndTime(Mockito.anyLong(), Mockito.anyInt(), Mockito.anyString())).thenReturn(list);
        Assert.assertNotNull(pointTransactionService.queryPointTransactionEndTime(1l, 1, "20200101000000"));
    }

    @Test
    public void updatePointTransaction() {
        PointTransactionParam uniqueVo = new PointTransactionParam();
        uniqueVo.setTransactionSn("100001");
        uniqueVo.setTenantCode("100001");
        PointCampaignEntity pointCampaignEntity = new PointCampaignEntity();
        pointCampaignEntity.setEndTime("**************");
        uniqueVo.setTransactionType(PointTransactionTypeEnum.INCREASE.number());
        Mockito.when(pointTransactionMapper.selectOne(Mockito.any())).thenReturn(new PointTransactionEntity());
        Mockito.when(pointCampaignMapper.selectOne(Mockito.any())).thenReturn(pointCampaignEntity);
        pointTransactionService.updatePointTransaction(uniqueVo);
    }

    @Test
    public void queryPointTransactionPage() {
        PointTransactionDto uniqueVo = new PointTransactionDto();
        uniqueVo.setTransactionSn("100001");
        uniqueVo.setTenantCode("100001");
        uniqueVo.setReferTransactionSn("100001");
        uniqueVo.setAccountCode("100001");
        uniqueVo.setAccountType(1);
        uniqueVo.setCampaignCode("1");
        uniqueVo.setTransactionType(1);
        uniqueVo.setTransactionDate(1L);
        uniqueVo.setReferOrderNumber("1");
        uniqueVo.setBeginDateFrom("1");
        uniqueVo.setEndDateTo("1");
        Mockito.when(pointTransactionMapper.selectByCondition(Mockito.any())).thenReturn(new Page<>());
        Assert.assertNotNull(pointTransactionService.queryPointTransactionPage(uniqueVo));
    }

}
