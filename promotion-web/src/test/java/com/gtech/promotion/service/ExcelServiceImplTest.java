package com.gtech.promotion.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.gtech.basic.filecloud.api.model.ImportFileMessage;
import com.gtech.promotion.RedisClient;
import com.gtech.promotion.dao.model.marketing.flashsale.FlashSaleProductModel;
import com.gtech.promotion.feign.PimFeignClient;
import com.gtech.promotion.service.common.impl.ExcelServiceImpl;
import com.gtech.promotion.service.flashsale.FlashSaleProductService;
import com.gtech.promotion.utils.FlashSaleConstants;
import org.apache.commons.collections.map.HashedMap;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.dao.DuplicateKeyException;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@RunWith(MockitoJUnitRunner.class)
public class ExcelServiceImplTest {

    @InjectMocks
    private ExcelServiceImpl excelService;

    @Mock
    private PimFeignClient pimFeignClient;

    @Mock
    private FlashSaleProductService flashSaleProductService;

    @Mock
    private RedisClient redisClient;

    @Test
    public void importSku_null(){
        ImportFileMessage<String> importFileMessage = new ImportFileMessage<>();
        importFileMessage.setCategory("1");
        ImportFileMessage<ImportFileMessage.Result> resultImportFileMessage = excelService.importSku(importFileMessage);
        Assert.assertEquals("1", resultImportFileMessage.getCategory());
    }

    @Test
    public void importSku_no_head(){
        ImportFileMessage<String> importFileMessage = new ImportFileMessage<>();
        importFileMessage.setCategory("1");
        List<ImportFileMessage.Payload<String>> payloads = new ArrayList<>();
        ImportFileMessage.Payload<String> e = new ImportFileMessage.Payload<>();
        e.setHead("-1");
        e.setBody("1");
        payloads.add(e);
        importFileMessage.setPayload(payloads);
        ImportFileMessage<ImportFileMessage.Result> resultImportFileMessage = excelService.importSku(importFileMessage);
        Assert.assertEquals("1", resultImportFileMessage.getCategory());
    }

    @Test
    public void importSku_equals_redis(){
        ImportFileMessage<String> importFileMessage = new ImportFileMessage<>();
        importFileMessage.setCategory("1");
        List<ImportFileMessage.Payload<String>> payloads = new ArrayList<>();
        ImportFileMessage.Payload<String> e = new ImportFileMessage.Payload<>();
        e.setHead("1");
        e.setBody("1");
        payloads.add(e);
        importFileMessage.setPayload(payloads);

        Mockito.when(redisClient.getString(Mockito.anyString())).thenReturn("1");
        ImportFileMessage<ImportFileMessage.Result> resultImportFileMessage = excelService.importSku(importFileMessage);
        Assert.assertEquals("1", resultImportFileMessage.getCategory());
    }

    @Test
    public void importSku_no_content(){
        ImportFileMessage<String> importFileMessage = new ImportFileMessage<>();
        importFileMessage.setCategory("1");
        List<ImportFileMessage.Payload<String>> payloads = new ArrayList<>();
        ImportFileMessage.Payload<String> e = new ImportFileMessage.Payload<>();
        e.setHead("1");
        e.setBody("1");
        payloads.add(e);
        importFileMessage.setPayload(payloads);


        Mockito.when(redisClient.getString(Mockito.anyString())).thenReturn("--1");
        Mockito.doNothing().when(redisClient).setStringValue(Mockito.anyString(), Mockito.anyString(), Mockito.anyLong(), Mockito.any());
        Mockito.when(flashSaleProductService.deleteByActivityCode(Mockito.anyString())).thenReturn(1);
        ImportFileMessage<ImportFileMessage.Result> resultImportFileMessage = excelService.importSku(importFileMessage);
        Assert.assertEquals("1", resultImportFileMessage.getCategory());
    }

    @Test
    public void importSku(){
        ImportFileMessage<String> importFileMessage = new ImportFileMessage<>();
        importFileMessage.setCategory("1");
        List<ImportFileMessage.Payload<String>> payloads = new ArrayList<>();
        ImportFileMessage.Payload<String> e = new ImportFileMessage.Payload<>();
        e.setHead("1");
        JSONObject jsonObject = new JSONObject();
        jsonObject.put(FlashSaleConstants.FILE_SKU_CODE, "1");
        jsonObject.put(FlashSaleConstants.FILE_SKU_FLASH_PRICE, "1");
        jsonObject.put(FlashSaleConstants.FILE_SKU_QUOTA, "1");
        jsonObject.put(FlashSaleConstants.FILE_SKU_MAX_USER, "1");
        e.setBody(jsonObject.toJSONString());
        payloads.add(e);
        importFileMessage.setPayload(payloads);

        JSONObject resultJson = new JSONObject();
        JSONObject listJson = new JSONObject();
        JSONArray jsonArray = new JSONArray();
        JSONObject tempJson = new JSONObject();
        tempJson.put("skuCode", "1");
        tempJson.put("salePrice", "2");
        jsonArray.add(tempJson);
        listJson.put("list", jsonArray);
        resultJson.put("data", listJson);

        Mockito.when(redisClient.getString(Mockito.anyString())).thenReturn("--1");
        Mockito.when(pimFeignClient.querySkuPage(Mockito.any())).thenReturn(resultJson);
        Mockito.when(flashSaleProductService.insert(Mockito.any())).thenReturn(1);
        ImportFileMessage<ImportFileMessage.Result> resultImportFileMessage = excelService.importSku(importFileMessage);
        Assert.assertEquals("1", resultImportFileMessage.getCategory());
    }

    @Test
    public void isErrorMessageByPayloadList(){
        List<ImportFileMessage.Payload<String>> payloadList = new ArrayList<>();

        List<ImportFileMessage.Payload<ImportFileMessage.Result>> resultPayloadList = new ArrayList<>();

        List<String> skuCodeList = new ArrayList<>();

        Mockito.when(flashSaleProductService.deleteByActivityCode(Mockito.any())).thenReturn(1);
        excelService.isErrorMessageByPayloadList(payloadList,resultPayloadList,"test",skuCodeList);

        String json = "{}";
        ImportFileMessage.Payload<String> stringPayload = new ImportFileMessage.Payload<>();
        stringPayload.setBody(json);
        payloadList.add(stringPayload);
        excelService.isErrorMessageByPayloadList(payloadList,resultPayloadList,"test",skuCodeList);
        Map<String,String> map = new HashedMap();
        map.put("SKU Code", "testSku");
        stringPayload.setBody(JSON.toJSONString(JSONObject.parseObject(JSON.toJSONString(map))));
        excelService.isErrorMessageByPayloadList(payloadList,resultPayloadList,"test",skuCodeList);

        map.put("Flash Sale Price", "1");
        stringPayload.setBody(JSON.toJSONString(JSONObject.parseObject(JSON.toJSONString(map))));
        excelService.isErrorMessageByPayloadList(payloadList,resultPayloadList,"test",skuCodeList);

        map.put("Campaign Stock", "-1");
        stringPayload.setBody(JSON.toJSONString(JSONObject.parseObject(JSON.toJSONString(map))));
        excelService.isErrorMessageByPayloadList(payloadList,resultPayloadList,"test",skuCodeList);

        map.put("Campaign Stock", "1");
        stringPayload.setBody(JSON.toJSONString(JSONObject.parseObject(JSON.toJSONString(map))));
        excelService.isErrorMessageByPayloadList(payloadList,resultPayloadList,"test",skuCodeList);

        map.put("Max. Per User", "-1");
        stringPayload.setBody(JSON.toJSONString(JSONObject.parseObject(JSON.toJSONString(map))));
        excelService.isErrorMessageByPayloadList(payloadList,resultPayloadList,"test",skuCodeList);

        map.put("Max. Per User", "1");
        stringPayload.setBody(JSON.toJSONString(JSONObject.parseObject(JSON.toJSONString(map))));
        excelService.isErrorMessageByPayloadList(payloadList,resultPayloadList,"test",skuCodeList);
    }

    @Test
    public void insertData(){
        ImportFileMessage.Payload<String> stringPayload = new ImportFileMessage.Payload<>();
        Mockito.when(flashSaleProductService.insert(Mockito.any())).thenReturn(1);
        excelService.insertData(new ArrayList<>(),false,stringPayload,new FlashSaleProductModel());

        Mockito.when(flashSaleProductService.insert(Mockito.any())).thenThrow(new DuplicateKeyException("test\'test"));
        try {
            excelService.insertData(new ArrayList<>(),false,stringPayload,new FlashSaleProductModel());
        }catch (Exception e){

        }
    }
}
