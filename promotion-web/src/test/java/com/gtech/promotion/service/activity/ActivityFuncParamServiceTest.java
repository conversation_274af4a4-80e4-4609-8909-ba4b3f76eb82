package com.gtech.promotion.service.activity;

import com.gtech.promotion.dao.entity.activity.ActivityFuncParamEntity;
import com.gtech.promotion.dao.entity.activity.ActivityFuncRankEntity;
import com.gtech.promotion.dao.mapper.activity.ActivityFuncParamMapper;
import com.gtech.promotion.dao.mapper.activity.ActivityFuncRankMapper;
import com.gtech.promotion.dao.model.activity.ActivityFunctionParamRankModel;
import com.gtech.promotion.dao.model.activity.FunctionParamModel;
import com.gtech.promotion.dao.model.activity.TemplateModel;
import com.gtech.promotion.exception.PromotionException;
import com.gtech.promotion.service.impl.activity.ActivityFuncParamServiceImpl;
import com.gtech.promotion.service.impl.activity.ActivityFuncRankServiceImpl;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.dao.DuplicateKeyException;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2021/9/18 10:16
 */
@RunWith(MockitoJUnitRunner.class)
public class ActivityFuncParamServiceTest {

    @InjectMocks
    private ActivityFuncParamServiceImpl activityFuncParamService;
    @InjectMocks
    private ActivityFuncRankServiceImpl activityFuncRankService;

    @Mock
    private ActivityFuncParamMapper activityFuncParamMapper;

    @Mock
    private ActivityFuncRankMapper activityFuncRankMapper;

    @Mock
    private TemplateService templateService;

    @Before
    public void before(){
        EntityHelper.initEntityNameMap(ActivityFuncParamEntity.class, new MapperHelper().getConfig());
        EntityHelper.initEntityNameMap(ActivityFuncRankEntity.class, new MapperHelper().getConfig());


    }

    @Test(expected = Exception.class)
    public void saveTPromoRuleFuncParam_null_1(){
        List<FunctionParamModel> paramVOs =null;
        activityFuncParamService.saveTPromoRuleFuncParam("1", "1", paramVOs);
    }

    @Test(expected = Exception.class)
    public void saveTPromoRuleFuncParam_null_2(){
        List<FunctionParamModel> paramVOs =new ArrayList<>();
        Mockito.when(templateService.getTemplateByCode(Mockito.any())).thenReturn(null);
        activityFuncParamService.saveTPromoRuleFuncParam("1", "1", paramVOs);
    }

    @Test(expected = PromotionException.class)
    public void saveTPromoRuleFuncParam_null_3(){
        List<FunctionParamModel> paramVOs =new ArrayList<>();
        FunctionParamModel functionParamModel = new FunctionParamModel();
        functionParamModel.setRankParam(0);
        paramVOs.add(functionParamModel);
        TemplateModel templateModel = new TemplateModel();
        Mockito.when(templateService.getTemplateByCode(Mockito.any())).thenReturn(templateModel);
        Mockito.doThrow(new DuplicateKeyException("duplicate")).when(activityFuncParamMapper).insertSelective(Mockito.any());
        activityFuncParamService.saveTPromoRuleFuncParam("1", "1", paramVOs);
    }


    @Test
    public void saveTPromoRuleFuncParam_0(){
        List<FunctionParamModel> paramVOs =new ArrayList<>();
        FunctionParamModel functionParamModel = new FunctionParamModel();
        functionParamModel.setRankParam(0);
        paramVOs.add(functionParamModel);
        TemplateModel templateModel = new TemplateModel();
        Mockito.when(templateService.getTemplateByCode(Mockito.any())).thenReturn(templateModel);
        Mockito.when(activityFuncParamMapper.insertSelective(Mockito.any())).thenReturn(0);
        int i = activityFuncParamService.saveTPromoRuleFuncParam("1", "1", paramVOs);
        Assert.assertEquals(0,i);
    }

    @Test
    public void saveTPromoRuleFuncParam_1(){
        List<FunctionParamModel> paramVOs =new ArrayList<>();
        FunctionParamModel functionParamModel = new FunctionParamModel();
        functionParamModel.setRankParam(0);
        paramVOs.add(functionParamModel);
        TemplateModel templateModel = new TemplateModel();
        Mockito.when(templateService.getTemplateByCode(Mockito.any())).thenReturn(templateModel);
        Mockito.when(activityFuncParamMapper.insertSelective(Mockito.any())).thenReturn(1);
        int i = activityFuncParamService.saveTPromoRuleFuncParam("1", "1", paramVOs);
        Assert.assertEquals(1,i);
    }


    @Test
    public void getRuleFuncParamListByRankIds(){
        List<String> rankIds =new ArrayList<>();
        rankIds.add("1");
        Mockito.when(activityFuncParamMapper.selectByCondition(Mockito.any())).thenReturn(new ArrayList<>());
        List<FunctionParamModel> i = activityFuncParamService.getRuleFuncParamListByRankIds("1", rankIds);
        Assert.assertEquals(0,i.size());
    }

    @Test
    public void getRuleFuncParamListByRankIdAndFunctionCode(){
        Mockito.when(activityFuncParamMapper.selectOne(Mockito.any())).thenReturn(new ActivityFuncParamEntity());
        FunctionParamModel code = activityFuncParamService.getRuleFuncParamListByRankIdAndFunctionCode("1", "1");
        Assert.assertEquals(null,code.getFunctionCode());
    }

    @Test
    public void getRankListByActivityCodes(){
        List<String> activityCodes = new ArrayList<>();
        activityCodes.add("1");
        Mockito.when(activityFuncRankMapper.selectByCondition(Mockito.any())).thenReturn(new ArrayList<>());
        List<ActivityFunctionParamRankModel> rankListByActivityCodes = activityFuncRankService.getRankListByActivityCodes("1", activityCodes);
        Assert.assertEquals(0,rankListByActivityCodes.size());
    }


}
