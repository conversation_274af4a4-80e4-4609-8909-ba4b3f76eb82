package com.gtech.promotion.service.purchaseconstraint;

import com.google.common.collect.Lists;
import com.gtech.promotion.dao.entity.purchaseconstraint.PurchaseConstraintCustomer;
import com.gtech.promotion.dao.entity.purchaseconstraint.PurchaseConstraintDetail;
import com.gtech.promotion.dao.mapper.purchaseconstraint.PurchaseConstraintCustomerMapper;
import com.gtech.promotion.dao.model.purchaseconstraint.PcRuleCalculateModel;
import com.gtech.promotion.dao.model.purchaseconstraint.PurchaseConstraintCustomerListMode;
import com.gtech.promotion.service.purchaseconstraint.impl.PurchaseConstraintCustomerServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@Slf4j
@RunWith(MockitoJUnitRunner.class)
public class PurchaseConstraintCustomerServiceTest {
    @InjectMocks
    private PurchaseConstraintCustomerServiceImpl purchaseConstraintCustomerService;
    @Mock
    private PurchaseConstraintCustomerMapper purchaseConstraintCustomerMapper;
    @Before
    public void setUp() throws Exception {
    }

    @Before
    public void before() {
        EntityHelper.initEntityNameMap(PurchaseConstraintCustomer.class, new MapperHelper().getConfig());
    }

    @After
    public void tearDown() throws Exception {
    }

    @Test
    public void insert() {
        purchaseConstraintCustomerService.insert(new PurchaseConstraintCustomer());

    }

    @Test
    public void testInsert() {
        purchaseConstraintCustomerService.insert(Lists.newArrayList(new PurchaseConstraintCustomer()));
    }

    @Test
    public void increment() {
        purchaseConstraintCustomerService.increment(Lists.newArrayList(new PurchaseConstraintCustomer()));
    }

    @Test
    public void decrement() {
        purchaseConstraintCustomerService.decrement(new PurchaseConstraintCustomer());
    }

    @Test
    public void list() {
        purchaseConstraintCustomerService.list(new PurchaseConstraintCustomerListMode());
    }

    @Mock
    private PurchaseConstraintDetailService pcDetailService;
    @Test
    public void testDecrement() {
        Mockito.when(pcDetailService.decrement(Mockito.any())).thenReturn(1);
        PurchaseConstraintCustomer customer = new PurchaseConstraintCustomer();
        customer.setPurchaseConstraintRuleType(0);
        Mockito.when(purchaseConstraintCustomerMapper.list(Mockito.any())).thenReturn(Lists.newArrayList(customer));
        PurchaseConstraintDetail purchaseConstraintDetail = new PurchaseConstraintDetail();
        purchaseConstraintDetail.setSkuCode("skuCode:001");
        purchaseConstraintDetail.setCustomerCode("1,2,3");
        purchaseConstraintDetail.setDetailAmount(BigDecimal.ONE);
        purchaseConstraintDetail.setDetailQty(1);
        ArrayList<PurchaseConstraintDetail> list = Lists.newArrayList(purchaseConstraintDetail);
        PcRuleCalculateModel model = new PcRuleCalculateModel();
        HashMap<String, PcRuleCalculateModel.IncrementProduct> skuMap = new HashMap<>();
        PcRuleCalculateModel.IncrementProduct value = new PcRuleCalculateModel.IncrementProduct();
        value.setSellAmount(BigDecimal.ONE);
        value.setSkuCount(1);
        skuMap.put("skuCode:001", value);
        purchaseConstraintCustomerService.decrement(model, list, skuMap);
    }

    @Test
    public void testDelete() {
        // create test data
        List<PurchaseConstraintCustomer> customers = new ArrayList<>();
        customers.add(new PurchaseConstraintCustomer());
        customers.add(new PurchaseConstraintCustomer());
        customers.add(new PurchaseConstraintCustomer());


        purchaseConstraintCustomerService.delete(customers);

    }



}