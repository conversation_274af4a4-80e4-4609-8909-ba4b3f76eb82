package com.gtech.promotion.service.activity;

import com.gtech.promotion.dao.entity.activity.LuckyDrawRulesEntity;
import com.gtech.promotion.dao.mapper.activity.LuckyDrawRulesMapper;
import com.gtech.promotion.dao.model.activity.LuckyDrawRuleModel;
import com.gtech.promotion.dao.model.marketing.flashsale.FlashSaleProductModel;
import com.gtech.promotion.service.impl.activity.LuckyDrawRuleServiceImpl;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/3/30 15:11
 */
@RunWith(MockitoJUnitRunner.class)
public class LuckyDrawRuleServiceTest {

    @InjectMocks
    private LuckyDrawRuleServiceImpl luckyDrawRuleService;

    @Mock
    private LuckyDrawRulesMapper luckyDrawRulesMapper;

    @Before
    public void before(){
        EntityHelper.initEntityNameMap(LuckyDrawRulesEntity.class, new MapperHelper().getConfig());
    }

    @Test
    public void createLuckyDrawRule(){
        List<LuckyDrawRuleModel> ruleModels = new ArrayList<>();
        LuckyDrawRuleModel model = new LuckyDrawRuleModel();
        ruleModels.add(model);
        Mockito.when(luckyDrawRulesMapper.insertSelective(Mockito.any())).thenReturn(1);
        int luckyDrawRule = luckyDrawRuleService.createLuckyDrawRule("1", ruleModels, "1");
        Assert.assertEquals(1,luckyDrawRule);
    }

    @Test
    public void deleteLuckyDrawRuleByActivityCode(){
        Mockito.when(luckyDrawRulesMapper.deleteByCondition(Mockito.any())).thenReturn(1);
        int i = luckyDrawRuleService.deleteLuckyDrawRuleByActivityCode("1");
        Assert.assertEquals(1,i);
    }

    @Test
    public void queryLuckyDrawRulesByActivityCode(){
        Mockito.when(luckyDrawRulesMapper.select(Mockito.any())).thenReturn(new ArrayList<>());
        List<LuckyDrawRuleModel> models = luckyDrawRuleService.queryLuckyDrawRulesByActivityCode("1", "1");
        Assert.assertEquals(0,models.size());
    }

    @Test
    public void queryLuckyDrawRulesByProductCode(){
        List<String> productCodes = new ArrayList<>();
        productCodes.add("1");
        Mockito.when(luckyDrawRulesMapper.selectByCondition(Mockito.any())).thenReturn(new ArrayList<>());
        List<LuckyDrawRuleModel> models = luckyDrawRuleService.queryLuckyDrawRulesByProductCode("1", productCodes);
        Assert.assertEquals(0,models.size());
    }


    @Test
    public void testGetLuckDrawProductsByActivityCodesAndProducts_EmptyLists() {
        String tenantCode = "tenant1";
        ArrayList<String> activityCodes = new ArrayList<>();
        List<String> skuCodeList = Collections.emptyList();

        List<FlashSaleProductModel> result = luckyDrawRuleService.getLuckDrawProductsByActivityCodesAndProducts(tenantCode, activityCodes, skuCodeList);


    }

    @Test
    public void testGetLuckDrawProductsByActivityCodesAndProducts_NonEmptyLists() {
        String tenantCode = "tenant1";
        ArrayList<String> activityCodes = new ArrayList<>();
        activityCodes.add("activity1");
        List<String> skuCodeList = new ArrayList<>();
        skuCodeList.add("sku1");

        // Mocking the database response
        LuckyDrawRulesEntity mockEntity = new LuckyDrawRulesEntity();
        mockEntity.setActivityCode(activityCodes.get(0));
        mockEntity.setProductCode(skuCodeList.get(0));

        Mockito.when(luckyDrawRulesMapper.selectByCondition(Mockito.any())).thenReturn(Collections.singletonList(mockEntity));

        List<FlashSaleProductModel> result = luckyDrawRuleService.getLuckDrawProductsByActivityCodesAndProducts(tenantCode, activityCodes, skuCodeList);


    }

}
