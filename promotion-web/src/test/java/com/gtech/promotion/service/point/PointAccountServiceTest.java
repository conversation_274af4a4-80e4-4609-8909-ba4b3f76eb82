package com.gtech.promotion.service.point;

import com.gtech.commons.page.PageData;
import com.gtech.commons.result.PageResult;
import com.gtech.promotion.code.point.PointTransactionTypeEnum;
import com.gtech.promotion.dao.entity.point.PointAccountEntity;
import com.gtech.promotion.dao.entity.point.PointCampaignEntity;
import com.gtech.promotion.dao.entity.point.PointTransactionEntity;
import com.gtech.promotion.dao.mapper.point.PointAccountMapper;
import com.gtech.promotion.dto.in.point.PointTransactionDto;
import com.gtech.promotion.exception.PromotionException;
import com.gtech.promotion.service.point.impl.PointAccountServiceImpl;
import com.gtech.promotion.vo.param.point.PointTransactionParam;
import com.gtech.promotion.vo.param.point.UpdatePointAccountParam;
import com.gtech.promotion.vo.param.point.UpdatePointParam;
import com.gtech.promotion.vo.param.point.query.GetPointAccountCampaignParam;
import com.gtech.promotion.vo.param.point.query.GetPointAccountParam;
import com.gtech.promotion.vo.param.point.query.GetPointTransactionParam;
import com.gtech.promotion.vo.param.point.query.PointAccountUniqueParam;
import com.gtech.promotion.vo.result.point.PointAccountCampaignResult;
import com.gtech.promotion.vo.result.point.PointAccountResult;
import com.gtech.promotion.vo.result.point.PointTransactionResult;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2021/8/6 13:57
 */
@RunWith(MockitoJUnitRunner.class)
public class PointAccountServiceTest {
    @InjectMocks
    private PointAccountServiceImpl pointAccountService;
    @Mock
    private PointAccountMapper pointAccountMapper;
    @Mock
    private PointTransactionService pointTransactionService;

    @Before
    public void before() {
        EntityHelper.initEntityNameMap(PointAccountEntity.class, new MapperHelper().getConfig());
        EntityHelper.initEntityNameMap(PointTransactionEntity.class, new MapperHelper().getConfig());
        EntityHelper.initEntityNameMap(PointAccountEntity.class, new MapperHelper().getConfig());
    }

    @Test
    public void updatePointAccountStatus() {
        PointAccountUniqueParam.PointAccountStatusUniqueVo uniqueVo = new PointAccountUniqueParam.PointAccountStatusUniqueVo();
        uniqueVo.setTenantCode("100001");
        uniqueVo.setPointAccountCode("100001");
        Mockito.when(pointAccountMapper.updateByExampleSelective(Mockito.any(), Mockito.any())).thenReturn(1);
        Assert.assertEquals(1, pointAccountService.updatePointAccountStatus(uniqueVo));
    }
        @Test
        public void updatePointAccountStatus_0(){
            PointAccountUniqueParam.PointAccountStatusUniqueVo vo= new PointAccountUniqueParam.PointAccountStatusUniqueVo();
            int i = pointAccountService.updatePointAccountStatus(vo);
            Assert.assertEquals(0,i);
        }
        @Test
        public void updatePointAccountStatus_1(){
            PointAccountUniqueParam.PointAccountStatusUniqueVo vo= new PointAccountUniqueParam.PointAccountStatusUniqueVo();
            vo.setTenantCode("1");
            vo.setPointAccountCode("1");
            Mockito.when(pointAccountMapper.updateByExampleSelective(Mockito.any(), Mockito.any())).thenReturn(1);
            int i = pointAccountService.updatePointAccountStatus(vo);
            Assert.assertEquals(1,i);
        }

        @Test
        public void updatePointAccount(){
            UpdatePointAccountParam pointAccountParam = new UpdatePointAccountParam();
            Mockito.when(pointAccountMapper.updateByExampleSelective(Mockito.any(), Mockito.any())).thenReturn(1);
            int i = pointAccountService.updatePointAccount(pointAccountParam);
            Assert.assertEquals(1,i);
        }

        @Test
        public void getPointAccount(){
            PointAccountEntity entity = new PointAccountEntity();
            GetPointAccountParam pointAccountParam = new GetPointAccountParam();
            Mockito.when(pointAccountMapper.selectOne(Mockito.any())).thenReturn(entity);
            PointAccountResult i = pointAccountService.getPointAccount(pointAccountParam);
            Assert.assertEquals(null,i.getTenantCode());
        }

        @Test
        public void getPointAccount_null(){
            GetPointAccountParam pointAccountParam = new GetPointAccountParam();
            PointAccountResult i = pointAccountService.getPointAccount(pointAccountParam);
            Assert.assertEquals(null,i);
        }

        @Test
        public void queryPointAccountPage(){
            Map<String, Object> map = new HashMap<>();
            Mockito.when(pointAccountMapper.query(Mockito.any())).thenReturn(new ArrayList<>());
            PageResult<PointAccountResult> i = pointAccountService.queryPointAccountPage(map);
            Assert.assertEquals(0,i.getData().getList().size());
        }


        @Test(expected = PromotionException.class)
        public void increaseOrDecreasePoint_exception(){
            UpdatePointParam param = new UpdatePointParam();
            Mockito.when(pointAccountMapper.updatePoint(Mockito.any())).thenReturn(0);
            pointAccountService.increaseOrDecreasePoint(param);
        }

        @Test
        public void increaseOrDecreasePoint(){
            UpdatePointParam param = new UpdatePointParam();
            Mockito.when(pointAccountMapper.updatePoint(Mockito.any())).thenReturn(1);
            Mockito.when(pointTransactionService.savePointTransaction(Mockito.any())).thenReturn("1");
            int i = pointAccountService.increaseOrDecreasePoint(param);
            Assert.assertEquals(1,i);
        }
        @Test
        public void getPointAccountCampaign_null(){
            GetPointAccountCampaignParam param = new GetPointAccountCampaignParam();
            Mockito.when(pointAccountMapper.selectOne(Mockito.any())).thenReturn(null);
            PointAccountCampaignResult i = pointAccountService.getPointAccountCampaign(param);
            Assert.assertEquals(null,i);
        }

        @Test
        public void getPointAccountCampaign(){
            PointAccountEntity entity = new PointAccountEntity();
            List<PointAccountCampaignResult.CampaignBalance> campaignBalances = new ArrayList<>();
            PointAccountCampaignResult.CampaignBalance campaignBalance = new PointAccountCampaignResult.CampaignBalance();
            campaignBalance.setAccountBalance(1);
            campaignBalances.add(campaignBalance);
            GetPointAccountCampaignParam param = new GetPointAccountCampaignParam();
            Mockito.when(pointAccountMapper.selectOne(Mockito.any())).thenReturn(entity);
            Mockito.when(pointTransactionService.getCountPointTransactionCampaign(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(campaignBalances);
            PointAccountCampaignResult i = pointAccountService.getPointAccountCampaign(param);
            Assert.assertEquals(null,i.getTenantCode());
        }



        @Test
        public void savePointTransaction() {
            PointTransactionParam uniqueVo = new PointTransactionParam();
            uniqueVo.setCampaignCode("100001");
            uniqueVo.setTransactionSn("100001");
            uniqueVo.setTransactionType(PointTransactionTypeEnum.INCREASE.number());
            uniqueVo.setTenantCode("100001");
            List<PointTransactionEntity> list = new ArrayList<>();
            PointCampaignEntity pointCampaignEntity = new PointCampaignEntity();
            pointCampaignEntity.setEndTime("**************");
            PointTransactionEntity pointTransactionEntity = new PointTransactionEntity();
            list.add(pointTransactionEntity);
            Assert.assertEquals(null,pointTransactionService.savePointTransaction(uniqueVo));
        }

        @Test
        public void getPointTransaction() {
            GetPointTransactionParam uniqueVo = new GetPointTransactionParam();
            uniqueVo.setTransactionSn("100001");
            uniqueVo.setTenantCode("100001");
            List<PointTransactionEntity> list = new ArrayList<>();
            PointCampaignEntity pointCampaignEntity = new PointCampaignEntity();
            pointCampaignEntity.setEndTime("**************");
            PointTransactionEntity pointTransactionEntity = new PointTransactionEntity();
            list.add(pointTransactionEntity);
            PointTransactionResult pointTransaction = pointTransactionService.getPointTransaction(uniqueVo);
            Assert.assertEquals(null,pointTransaction);
        }

        @Test
        public void queryPointTransactionEndTime() {
            GetPointTransactionParam uniqueVo = new GetPointTransactionParam();
            uniqueVo.setTransactionSn("100001");
            uniqueVo.setTenantCode("100001");
            List<PointTransactionEntity> list = new ArrayList<>();
            PointCampaignEntity pointCampaignEntity = new PointCampaignEntity();
            pointCampaignEntity.setEndTime("**************");
            PointTransactionEntity pointTransactionEntity = new PointTransactionEntity();
            list.add(pointTransactionEntity);
            Assert.assertNotNull(pointTransactionService.queryPointTransactionEndTime(1l, 1, "20200101000000"));
        }

        @Test
        public void updatePointTransaction() {
            PointTransactionParam uniqueVo = new PointTransactionParam();
            uniqueVo.setTransactionSn("100001");
            uniqueVo.setTenantCode("100001");
            PointCampaignEntity pointCampaignEntity = new PointCampaignEntity();
            pointCampaignEntity.setEndTime("**************");
            uniqueVo.setTransactionType(PointTransactionTypeEnum.INCREASE.number());
            pointTransactionService.updatePointTransaction(uniqueVo);
        }

        @Test
        public void queryPointTransactionPage() {
            PointTransactionDto uniqueVo = new PointTransactionDto();
            uniqueVo.setTransactionSn("100001");
            uniqueVo.setTenantCode("100001");
            uniqueVo.setReferTransactionSn("100001");
            uniqueVo.setAccountCode("100001");
            uniqueVo.setAccountType(1);
            uniqueVo.setCampaignCode("1");
            uniqueVo.setTransactionType(1);
            uniqueVo.setTransactionDate(1L);
            uniqueVo.setReferOrderNumber("1");
            uniqueVo.setBeginDateFrom("1");
            uniqueVo.setEndDateTo("1");
            PageData<PointTransactionResult> data = pointTransactionService.queryPointTransactionPage(uniqueVo);
            Assert.assertEquals(null,data);
        }
    }

