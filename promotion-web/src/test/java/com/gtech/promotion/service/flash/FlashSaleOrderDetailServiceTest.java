package com.gtech.promotion.service.flash;

import com.gtech.promotion.dao.entity.marketing.flashsale.FlashSaleOrderDetailEntity;
import com.gtech.promotion.dao.mapper.marketing.flashsale.FlashSaleOrderDetailMapper;
import com.gtech.promotion.service.flashsale.impl.FlashSaleOrderDetailServiceImpl;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2021-09-18 10:42
 */
@RunWith(MockitoJUnitRunner.class)
public class FlashSaleOrderDetailServiceTest {
    @InjectMocks
    private FlashSaleOrderDetailServiceImpl flashSaleOrderDetailService;
    @Mock
    private FlashSaleOrderDetailMapper orderDetailMapper;

    @Before
    public void before(){
        EntityHelper.initEntityNameMap(FlashSaleOrderDetailEntity.class, new MapperHelper().getConfig());
    }

    @Test
    public void findByOrderNo(){
        flashSaleOrderDetailService.findByOrderNo("1","1");
    }

    @Test
    public void findByOrderNoList(){

        flashSaleOrderDetailService.findByOrderNoList("1",null);
    }

    @Test
    public void findByOrderNoList1(){

        flashSaleOrderDetailService.findByOrderNoList("1", Arrays.asList("1"));
    }
}
