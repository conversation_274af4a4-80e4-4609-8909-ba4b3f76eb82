package com.gtech.promotion.service.point;

import com.gtech.commons.utils.DateUtil;
import com.gtech.promotion.dao.entity.point.PointCampaignEntity;
import com.gtech.promotion.dao.mapper.point.PointCampaignMapper;
import com.gtech.promotion.service.point.impl.PointCampaignServiceImpl;
import com.gtech.promotion.vo.param.point.PointCampaignParam;
import com.gtech.promotion.vo.param.point.UpdatePointParam;
import com.gtech.promotion.vo.param.point.query.PointCampaignUniqueParam;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2021/8/6 14:11
 */
@RunWith(MockitoJUnitRunner.class)
public class PointCampaignServiceTest {

    @InjectMocks
    private PointCampaignServiceImpl pointCampaignService;

    @Mock
    PointCampaignMapper pointCampaignMapper;

    @Before
    public void before() {
        EntityHelper.initEntityNameMap(PointCampaignEntity.class, new MapperHelper().getConfig());
    }

    @Test
    public void savePointCampaign() {
        PointCampaignParam uniqueVo = new PointCampaignParam();
        uniqueVo.setCampaignCode("100001");
        Mockito.when(pointCampaignMapper.insert(Mockito.any())).thenReturn(1);
        Assert.assertNotNull(pointCampaignService.savePointCampaign(uniqueVo));
    }

    @Test
    public void updatePointCampaignStatus() {
        PointCampaignUniqueParam.PointCampaignStatusUniqueVo uniqueVo = new PointCampaignUniqueParam.PointCampaignStatusUniqueVo();
        uniqueVo.setCampaignCode("100001");
        uniqueVo.setTenantCode("100001");
        Mockito.when(pointCampaignMapper.updateByExampleSelective(Mockito.any(), Mockito.any())).thenReturn(1);
        pointCampaignService.updatePointCampaignStatus(uniqueVo);
    }

    @Test
    public void updatePoint() {
        UpdatePointParam uniqueVo = new UpdatePointParam();
        uniqueVo.setCampaignCode("100001");
        uniqueVo.setTenantCode("100001");
        uniqueVo.setTransactionType(1);
        PointCampaignEntity pointCampaignEntity = new PointCampaignEntity();
        pointCampaignEntity.setStatus(1);
        pointCampaignEntity.setTotalPoints(1);
        pointCampaignEntity.setEndTime(DateUtil.format(DateUtil.addDay(1), DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        Mockito.when(pointCampaignMapper.selectOne(Mockito.any())).thenReturn(pointCampaignEntity);
        Mockito.when(pointCampaignMapper.updatePoint(Mockito.any())).thenReturn(1);
        int i = pointCampaignService.updatePoint(uniqueVo);
        Assert.assertEquals(1, i);
    }

    @Test
    public void queryPointCampaignEndTime() {
        UpdatePointParam uniqueVo = new UpdatePointParam();
        uniqueVo.setCampaignCode("100001");
        uniqueVo.setTenantCode("100001");
        uniqueVo.setTransactionType(1);
        PointCampaignEntity pointCampaignEntity = new PointCampaignEntity();
        pointCampaignEntity.setStatus(1);
        pointCampaignEntity.setTotalPoints(1);
        pointCampaignEntity.setEndTime(DateUtil.format(DateUtil.addDay(1), DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        Assert.assertNotNull(pointCampaignService.queryPointCampaignEndTime());
    }

    @Test
    public void updatePointCampaign() {
        PointCampaignParam uniqueVo = new PointCampaignParam();
        uniqueVo.setCampaignCode("100001");
        uniqueVo.setTenantCode("100001");
        PointCampaignEntity pointCampaignEntity = new PointCampaignEntity();
        pointCampaignEntity.setStatus(1);
        pointCampaignEntity.setTotalPoints(1);
        pointCampaignEntity.setEndTime(DateUtil.format(DateUtil.addDay(1), DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        Mockito.when(pointCampaignMapper.selectOne(Mockito.any())).thenReturn(new PointCampaignEntity());
        Mockito.when(pointCampaignMapper.updateByPrimaryKeySelective(Mockito.any())).thenReturn(1);
        pointCampaignService.updatePointCampaign(uniqueVo);
    }

    @Test
    public void getPointCampaign() {
        PointCampaignUniqueParam uniqueVo = new PointCampaignUniqueParam();
        uniqueVo.setCampaignCode("100001");
        uniqueVo.setTenantCode("100001");
        PointCampaignEntity pointCampaignEntity = new PointCampaignEntity();
        pointCampaignEntity.setStatus(1);
        pointCampaignEntity.setTotalPoints(1);
        pointCampaignEntity.setEndTime(DateUtil.format(DateUtil.addDay(1), DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        Mockito.when(pointCampaignMapper.selectOne(Mockito.any())).thenReturn(new PointCampaignEntity());
        Assert.assertNotNull(pointCampaignService.getPointCampaign(uniqueVo));
    }

    @Test
    public void queryPointCampaignPage() {
        Map<String, Object> map = new HashMap<>();
        Mockito.when(pointCampaignMapper.query(Mockito.any())).thenReturn(new ArrayList<>());
        Assert.assertNotNull(pointCampaignService.queryPointCampaignPage(map));
    }


}
