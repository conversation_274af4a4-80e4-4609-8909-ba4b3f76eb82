package com.gtech.promotion.service.marketing;

import com.github.pagehelper.PageHelper;
import com.gtech.commons.page.PageData;
import com.gtech.commons.utils.DateUtil;
import com.gtech.promotion.code.activity.ActivityStatusEnum;
import com.gtech.promotion.code.marketing.ActivityTypeEnum;
import com.gtech.promotion.component.flashsale.DataSyncComponent;
import com.gtech.promotion.dao.entity.marketing.MarketingEntity;
import com.gtech.promotion.dao.mapper.marketing.MarketingMapper;
import com.gtech.promotion.dao.model.marketing.BaseModel;
import com.gtech.promotion.dao.model.marketing.MarketingModel;
import com.gtech.promotion.dto.in.marketing.MarketingQueryInDto;
import com.gtech.promotion.service.activity.OperationLogService;
import com.gtech.promotion.service.marketing.impl.MarketingServiceImpl;
import com.gtech.promotion.vo.param.marketing.flashsale.FlashSaleQueryListParam;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

import java.util.ArrayList;
import java.util.List;

@RunWith(MockitoJUnitRunner.class)
public class MarketingServiceTest {

    @InjectMocks
    private MarketingServiceImpl marketingService;

    @Mock
    private MarketingMapper marketingMapper;

    @Mock
    private OperationLogService operationLogService;
    @Mock
    private DataSyncComponent dataSyncComponent;

    @Before
    public void before(){
        EntityHelper.initEntityNameMap(MarketingEntity.class, new MapperHelper().getConfig());
    }

    @Test
    public void queryMarketingList1(){
        MarketingQueryInDto inDto = new MarketingQueryInDto();
        inDto.setActivityType(ActivityTypeEnum.LUCKY_DRAW.code());
        inDto.setActivityStatus(ActivityStatusEnum.EFFECTIVE.code());
        inDto.setActivityName("1");
        inDto.setSponsors("1");
        inDto.setDefaultFlag(true);
        List<MarketingEntity> list = new ArrayList<>();
        Mockito.when(marketingMapper.selectByCondition(Mockito.any())).thenReturn(list);
        PageData<MarketingModel> marketingModelPageData = marketingService.queryMarketingList(inDto);
        PageHelper.clearPage();
        Assert.assertEquals(list.size(), marketingModelPageData.getList().size());
    }

    @Test
    public void queryMarketingList2(){
        MarketingQueryInDto inDto = new MarketingQueryInDto();
        inDto.setActivityBeginFrom("1");
        inDto.setActivityBeginTo("1");
        inDto.setActivityEndFrom("1");
        inDto.setActivityEndTo("1");
        inDto.setActivityCode("1");
        inDto.setOpsType("1");
        inDto.setDefaultFlag(true);
        List<MarketingEntity> list = new ArrayList<>();
        Mockito.when(marketingMapper.selectByCondition(Mockito.any())).thenReturn(list);
        PageData<MarketingModel> marketingModelPageData = marketingService.queryMarketingList(inDto);
        PageHelper.clearPage();
        Assert.assertEquals(list.size(), marketingModelPageData.getList().size());
    }


    @Test
    public void selectOneByActivityCode(){
        BaseModel baseModel = new BaseModel();
        baseModel.setDomainCode("1");
        baseModel.setActivityCode("1");
        baseModel.setOrgCode("1");
        baseModel.setTenantCode("1");

        List<MarketingEntity> list = new ArrayList<>();
        Mockito.when(marketingMapper.selectByCondition(Mockito.any())).thenReturn(list);
        MarketingModel marketingModel = marketingService.findByActivityCode(baseModel.getActivityCode());
        Assert.assertNull(marketingModel);
    }

    @Test
    public void selectOneByActivityCode1(){
        BaseModel baseModel = new BaseModel();
        baseModel.setDomainCode("1");
        baseModel.setActivityCode("1");
        baseModel.setOrgCode("1");
        baseModel.setTenantCode("1");

        List<MarketingEntity> list = new ArrayList<>();
        MarketingEntity entity = new MarketingEntity();
        entity.setActivityStatus(ActivityStatusEnum.EFFECTIVE.code());
        entity.setActivityEnd(DateUtil.format(DateUtil.addDay(1), DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        list.add(entity);
        Mockito.when(marketingMapper.selectByCondition(Mockito.any())).thenReturn(list);
        MarketingModel marketingModel = marketingService.findByActivityCode(baseModel.getActivityCode());
        Assert.assertNotNull(marketingModel);
        Assert.assertEquals(ActivityStatusEnum.EFFECTIVE.code(), marketingModel.getActivityStatus());
    }

    @Test
    public void selectOneByActivityCode3(){
        BaseModel baseModel = new BaseModel();
        baseModel.setDomainCode("1");
        baseModel.setActivityCode("1");
        baseModel.setOrgCode("1");
        baseModel.setTenantCode("1");

        List<MarketingEntity> list = new ArrayList<>();
        MarketingEntity entity = new MarketingEntity();
        entity.setActivityStatus(ActivityStatusEnum.EFFECTIVE.code());
        entity.setActivityEnd(DateUtil.format(DateUtil.addDay(-1), DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        list.add(entity);
        Mockito.when(marketingMapper.selectByCondition(Mockito.any())).thenReturn(list);
        Mockito.when(operationLogService.insertLog(Mockito.any(), Mockito.any())).thenReturn(0);
        MarketingModel marketingModel = marketingService.findByActivityCode(baseModel.getActivityCode());
        Assert.assertNotNull(marketingModel);
        Assert.assertEquals(ActivityStatusEnum.CLOSURE.code(), marketingModel.getActivityStatus());
    }

    @Test
    public void expireMarketing(){
        Mockito.when(marketingMapper.updateByConditionSelective(Mockito.any(), Mockito.any())).thenReturn(1);
        int i = marketingService.expireMarketing();
        Assert.assertEquals(1, i);
    }

    @Test
    public void updateByActivityCode(){
        MarketingModel marketingModel = new MarketingModel();
        marketingModel.setActivityCode("1");
        Mockito.when(marketingMapper.updateByConditionSelective(Mockito.any(), Mockito.any())).thenReturn(1);
        int i = marketingService.updateByActivityCode(marketingModel);
        Assert.assertEquals(1, i);
    }

    @Test
    public void queryMarketingList(){
        Mockito.when(marketingMapper.selectByCondition(Mockito.any())).thenReturn(new ArrayList<>());
        List<MarketingModel> marketingModels = marketingService.queryMarketingList("1", "1", "1");
        Assert.assertEquals(0,marketingModels.size());
    }

    @Test
    public void queryCurrentEffectiveMarketingList(){
        Mockito.when(marketingMapper.selectByCondition(Mockito.any())).thenReturn(new ArrayList<>());
        List<MarketingModel> marketingModels = marketingService.queryCurrentEffectiveMarketingList("1", "1");
        Assert.assertEquals(0,marketingModels.size());
    }

    @Test
    public void queryMarketingFlashSaleList(){
        FlashSaleQueryListParam param = new FlashSaleQueryListParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setActivityStatus("04");
        param.setSponsors("1");
        param.setEndTime("1");
        param.setStartTime("1");
        param.setOrgCode("1");
        param.setActivityStatus("04");
        Mockito.when(marketingMapper.selectFlashSaleByStore(Mockito.any())).thenReturn(new ArrayList<>());
        PageData<MarketingModel> pageData = marketingService.queryMarketingFlashSaleList(param);
        Assert.assertEquals(0,pageData.getList().size());
    }

    @Test
    public void queryMarketingFlashSaleList1(){
        FlashSaleQueryListParam param = new FlashSaleQueryListParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        Mockito.when(marketingMapper.selectByCondition(Mockito.any())).thenReturn(new ArrayList<>());
        PageData<MarketingModel> pageData = marketingService.queryMarketingFlashSaleList(param);
        Assert.assertEquals(0,pageData.getList().size());
    }

    @Test
    public void queryShouldExpireMarketingList(){
        Mockito.when(marketingMapper.selectByCondition(Mockito.any())).thenReturn(new ArrayList<>());
        List<MarketingModel> marketingModels = marketingService.queryShouldExpireMarketingList();
        Assert.assertEquals(0,marketingModels.size());
    }

    @Test
    public void queryAllTenantEffectiveFlashSaleList(){
        Mockito.when(marketingMapper.selectByCondition(Mockito.any())).thenReturn(new ArrayList<>());
        List<MarketingModel> marketingModels = marketingService.queryAllTenantEffectiveFlashSaleList("1", 10);
        Assert.assertEquals(0,marketingModels.size());
    }

    @Test
    public void getMarketingByActivityCode(){
        List<String> activityCode = new ArrayList<>();
        activityCode.add("1");
        Mockito.when(marketingMapper.selectByCondition(Mockito.any())).thenReturn(new ArrayList<>());
        List<MarketingModel> marketingModels = marketingService.getMarketingByActivityCode("1", activityCode, "1");
        Assert.assertEquals(0,marketingModels.size());
    }

    @Test
    public void getMarketingByActivityCodeList(){
        List<String> activityCode = new ArrayList<>();
        activityCode.add("1");
        Mockito.when(marketingMapper.selectByCondition(Mockito.any())).thenReturn(new ArrayList<>());

        List<MarketingModel> marketingModels = marketingService.getMarketingByActivityCodeList("1", activityCode);
        Assert.assertEquals(0,marketingModels.size());
    }
}
