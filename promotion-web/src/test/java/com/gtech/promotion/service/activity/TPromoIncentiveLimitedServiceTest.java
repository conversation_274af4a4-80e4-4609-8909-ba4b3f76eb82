package com.gtech.promotion.service.activity;

import com.gtech.promotion.dao.entity.activity.TPromoIncentiveLimitedEntity;
import com.gtech.promotion.dao.mapper.activity.LuckyDrawIncentiveLimitedMapper;
import com.gtech.promotion.dao.mapper.activity.TPromoIncentiveLimitedMapper;
import com.gtech.promotion.dao.model.activity.TPromoIncentiveLimitedVO;
import com.gtech.promotion.service.impl.activity.TPromoIncentiveLimitedServiceImpl;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/3/31 10:54
 */
@RunWith(MockitoJUnitRunner.class)
public class TPromoIncentiveLimitedServiceTest {

    @InjectMocks
    private TPromoIncentiveLimitedServiceImpl incentiveLimitedService;

    @Mock
    private TPromoIncentiveLimitedMapper limitedMapper;

    @Mock
    private LuckyDrawIncentiveLimitedMapper luckyDrawIncentiveLimitedMapper;

    @Before
    public void before(){
        EntityHelper.initEntityNameMap(TPromoIncentiveLimitedEntity.class, new MapperHelper().getConfig());
    }

    @Test
    public void insertLuckyDrawLimitedList(){
        List<TPromoIncentiveLimitedVO> limitedVOList = new ArrayList<>();
        TPromoIncentiveLimitedVO limitedVO = new TPromoIncentiveLimitedVO();
        limitedVO.setLimitationCode("01");
        limitedVO.setLimitationValue(new BigDecimal("10"));
        limitedVOList.add(limitedVO);
        Mockito.when(luckyDrawIncentiveLimitedMapper.insertSelective(Mockito.any())).thenReturn(1);
        Integer integer = incentiveLimitedService.insertLuckyDrawLimitedList("1", limitedVOList, "1");
        Assert.assertEquals(1,(long)integer);
    }

    @Test
    public void getLuckyDrawLimitedListByActivityCode(){
        Mockito.when(luckyDrawIncentiveLimitedMapper.select(Mockito.any())).thenReturn(new ArrayList<>());
        List<TPromoIncentiveLimitedVO> list = incentiveLimitedService.getLuckyDrawLimitedListByActivityCode("1");
        Assert.assertEquals(0,list.size());
    }

    @Test
    public void deleteLuckyDrawLimitedByActivityCode(){
        Mockito.when(luckyDrawIncentiveLimitedMapper.delete(Mockito.any())).thenReturn(1);
        Integer integer = incentiveLimitedService.deleteLuckyDrawLimitedByActivityCode("1");
        Assert.assertEquals(1,(long)integer);
    }

    @Test
    public void getLimitedListByActivityCodes(){
        List<String> activityCodes = new ArrayList<>();
        activityCodes.add("1");
        Mockito.when(limitedMapper.selectByCondition(Mockito.any())).thenReturn(new ArrayList<>());
        List<TPromoIncentiveLimitedVO> list = incentiveLimitedService.getLimitedListByActivityCodes("1", activityCodes);
        Assert.assertEquals(0,list.size());
    }
}
