package com.gtech.promotion.client;

import com.gtech.promotion.vo.param.marketing.flashsale.QueryFlashSalePriceByProductSyncParam;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

@RunWith(MockitoJUnitRunner.class)
public class QueryFlashSalePriceByProductSyncParamTest {

    @Test
    public void createQueryFlashSalePriceByProductSyncParam(){
        QueryFlashSalePriceByProductSyncParam t = new QueryFlashSalePriceByProductSyncParam();
        t.setTenantCode("100008");
        List<QueryFlashSalePriceByProductSyncParam.Product> products = new ArrayList<>();
        QueryFlashSalePriceByProductSyncParam.Product p = new QueryFlashSalePriceByProductSyncParam.Product();
        p.setProductCode("test");
        p.setSkuCode("test");
        products.add(p);
        t.setProducts(products);

    }
}
