/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.domain.coupon.activity;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * 测试券活动创建
 */
@RunWith(MockitoJUnitRunner.class)
public class CouponActivityStoreQueryTest{
    @Test
    public void test(){

    }
//    // 1、有无奖励 限制   incentiveLimitedFlag 00无限制  01有限制
//    // 2、有无渠道 限制   storeType 店铺范围：00-全店铺 01-自定义
//    // 3、有无会员 等级限制   members
//    // 商品范围： productType  00-全商品 01-指定范围 02-指定商品（上传文件）  03-指定多个商品范围
//    // 4、商品：全商品  
//    // 5、商品：指定商品范围
//    // 6、商品：指定商品sku
//    // 7、商品：指定多个商品范围
//    // 8、正反选 productSelectType商品范围正反选：01-正选；02-反选
//
//    @Test
//    public void test1(){
//        assert true;
//    }
//
//    @InjectMocks
//    private CouponActivityDomainImpl couponActivityDomain;
//
//    @Mock
//    private TPromoActivityFuncRankServiceImpl promoActivityFuncRankService;
//
//    @Mock
//    private TPromoActivityFuncParamServiceImpl tPromoActivityFuncParamService;
//
//    @Mock
//    private PromoCouponReleaseServiceImpl promoCouponReleaseService;
//
//    @Mock
//    private PromoCouponCodeUserServiceImpl promoCouponCodeUserService;
//    
//    @Mock
//    private CouponReleaseDomain couponReleaseDomain;
//    
//    @Mock
//    private RedisServiceImpl redisService;
//
//    @Mock
//    private TPromoTemplateServiceImpl tPromoTemplateService;
//
//    @Mock
//    private TPromoActivityGiftServiceImpl tPromoRuleGiftService;
//
//    @Mock
//    private TPromoMemberServiceImpl tPromoMemberService;
//
//    @Mock
//    private ActivityMemberDomain memberDomain;
//
//    @Mock
//    private ActivityMemberLabelDomain memberLabelDomain;
//
//    @Mock
//    private PromoCouponActivityServiceImpl promoCouponActivityService;
//    
//    @Mock
//    private ActivityCacheDomain activityCacheDomain;
//
//    @Mock
//    private TPromoActivityLanguageServiceImpl languageService;
//
//    @Mock
//    private TPromoActivityServiceImpl activityService;
//
//    @Mock
//    private TPromoActivityStoreServiceImpl tPromoActivityStoreService;
//
//    
//    @Test
//    public void 店铺下_生效的券活动列表_未开始_2(){
//        //given
//        StoreCouponActivityInDTO inStore = ActivityFactory.queryCouponActivityInStore();
//        Template template = Template.builder().templateEnum(TemplateEnum.T304).condition("2").reward("5").build();
//        TPromoTemplateVO templateVO = new TPromoTemplateVO();
//        templateVO.setId(TemplateEnum.T304.getId());
//        templateVO.setTemplateCode(template.getTemplateEnum().getCode());
//        TPromoActivityVO tPromoActivityVO = new TPromoActivityVO();
//        tPromoActivityVO.setId("1");
//        tPromoActivityVO.setActivityBegin("20180611170850");
//        Date date = DateUtil.addDay(new Date(), 1);
//        String string = DateUtil.format(date, DateUtil.FORMAT_YYYYMMDDHHMISS_14);
//        tPromoActivityVO.setActivityEnd(string);
//        List<TPromoActivityVO> arrayList = new ArrayList<>();
//        arrayList.add(tPromoActivityVO);
//        TPromoCouponActivityVO couponActivityVO = ActivityFactory.createCouponActivityVO("01","1","1");
//        List<TPromoActivityStoreVO> storeVOs = new ArrayList<>();
//        TPromoActivityStoreVO storeVO = new TPromoActivityStoreVO();
//        storeVO.setStoreCode(1);
//        storeVOs.add(storeVO);
//        
//        List<TPromoActivityFuncParamVO>  funcParams = new ArrayList<>();
//        TPromoActivityFuncParamVO tPromoFuncParamDTO1 = new TPromoActivityFuncParamVO();
//        tPromoFuncParamDTO1.setFunctionCode("0102");
//        tPromoFuncParamDTO1.setFunctionType("01");
//        tPromoFuncParamDTO1.setParamType("01");
//        tPromoFuncParamDTO1.setParamUnit("");
//        tPromoFuncParamDTO1.setParamValue("");
//        tPromoFuncParamDTO1.setRankParam(1);
//        TPromoActivityFuncParamVO tPromoFuncParamDTO2 = new TPromoActivityFuncParamVO();
//        tPromoFuncParamDTO2.setFunctionCode("0202");
//        tPromoFuncParamDTO2.setFunctionType("02");
//        tPromoFuncParamDTO2.setParamType("01");
//        tPromoFuncParamDTO2.setParamUnit("");
//        tPromoFuncParamDTO2.setParamValue("");
//        tPromoFuncParamDTO2.setRankParam(1);
//        TPromoActivityFuncParamVO tPromoFuncParamDTO3 = new TPromoActivityFuncParamVO();
//        tPromoFuncParamDTO3.setFunctionCode("0302");
//        tPromoFuncParamDTO3.setFunctionType("03");
//        tPromoFuncParamDTO3.setParamType("02");
//        tPromoFuncParamDTO3.setParamUnit("02");
//        tPromoFuncParamDTO3.setParamValue("2");
//        tPromoFuncParamDTO3.setRankParam(1);
//        TPromoActivityFuncParamVO tPromoFuncParamDTO4 = new TPromoActivityFuncParamVO();
//        tPromoFuncParamDTO4.setFunctionCode("0401");
//        tPromoFuncParamDTO4.setFunctionType("04");
//        tPromoFuncParamDTO4.setParamType("02");
//        tPromoFuncParamDTO4.setParamUnit("01");
//        tPromoFuncParamDTO4.setParamValue("5");
//        tPromoFuncParamDTO4.setRankParam(1);
//        funcParams.add(tPromoFuncParamDTO1);
//        funcParams.add(tPromoFuncParamDTO2);
//        funcParams.add(tPromoFuncParamDTO3);
//        funcParams.add(tPromoFuncParamDTO4);
//        Map<String, ActivityCacheDTO> map = new HashMap<>();
//        // 创建券活动缓存
//        ActivityCacheDTO activity = new ActivityCacheDTO();
//        activity.setCouponActivity(couponActivityVO);
//        activity.setPromoActivity(tPromoActivityVO);
//        activity.setPromoTemplate(templateVO);
//        activity.setPromoFuncParams(funcParams);
//        map.put(activity.getPromoActivity().getId(), activity);
//        //when
//        when(activityCacheDomain.getActivityCacheMap(anyString(), any(), any())).thenReturn(map);
////        when(tPromoTemplateService.getTemplateById(anyString())).thenReturn(templateVO);//模板对象
////        when(activityService.queryActivityByTenantCode(anyString(), any(), any(), any())).thenReturn(arrayList);
////        when(tPromoRuleGiftService.getGiftListByActivityId(anyString())).thenReturn(null);//赠品
////        when(promoActivityFuncRankService.getRankListByRuleId(anyString())).thenReturn(new ArrayList<TPromoActivityFuncRankVO>());//层级
////        when(tPromoActivityFuncParamService.getRuleFuncParamListByRankId(anyString())).thenReturn(new ArrayList<TPromoActivityFuncParamVO>());//层级参数
////        when(memberDomain.checkMemberLevel(anyString(), anyString())).thenReturn(true);
////        when(memberLabelDomain.checkMemberLabel(anyString(), anyString())).thenReturn(true);
////        when(tPromoActivityFuncParamService.getRuleFuncParamListByRankIdAndFunctionCode(anyString(), anyString())).thenReturn(null);
////        when(promoCouponActivityService.findCouponActivity(anyString())).thenReturn(couponActivityVO);//券活动
//        when(tPromoActivityStoreService.getStoresByActivityId(anyString())).thenReturn(storeVOs);//店铺集合
////        when(promoCouponReleaseService.queryReleasesByActivityIds(new ArrayList<>())).thenReturn(new ArrayList<TPromoCouponReleaseVO>());
////        when(promoCouponCodeUserService.getUserCouponCountByActivityId(anyString(), anyString())).thenReturn(1);
////        when(promoCouponCodeUserService.getCodeUserByReleaseId(anyString())).thenReturn(1);
//        PageInfo<StoreCouponActivityOutDTO> pageInfo = couponActivityDomain.queryCouponActivityInStore(inStore);
//        // then
//        Assert.assertEquals(2, pageInfo.getList().get(0).getReceiveStatus());
//    }
//
//    
//    @Test
//    public void 店铺下_生效的券活动列表_限领_5(){
//        //given
//        StoreCouponActivityInDTO inStore = ActivityFactory.queryCouponActivityInStore();
//        Template template = Template.builder().templateEnum(TemplateEnum.T304).condition("2").reward("5").build();
//        TPromoTemplateVO templateVO = new TPromoTemplateVO();
//        templateVO.setId(TemplateEnum.T304.getId());
//        templateVO.setTemplateCode(template.getTemplateEnum().getCode());
//        TPromoActivityVO tPromoActivityVO = new TPromoActivityVO();
//        tPromoActivityVO.setId("1");
//        tPromoActivityVO.setActivityBegin("20180611170850");
//        Date date = DateUtil.addDay(new Date(), 5);
//        String string = DateUtil.format(date, DateUtil.FORMAT_YYYYMMDDHHMISS_14);
//        tPromoActivityVO.setActivityEnd(string);
//        tPromoActivityVO.setStoreType("01");
//        List<TPromoActivityVO> arrayList = new ArrayList<>();
//        arrayList.add(tPromoActivityVO);
//        TPromoCouponActivityVO couponActivityVO = ActivityFactory.createCouponActivityVO("01","1","1");
//        couponActivityVO.setUserLimitMax(2);
//
//        List<TPromoCouponReleaseVO> releaseVOs = new ArrayList<TPromoCouponReleaseVO>();
//        TPromoCouponReleaseVO releaseVO = new TPromoCouponReleaseVO();
//        Date release = DateUtil.addDay(new Date(), 1);
//        String dateString = DateUtil.format(release, DateUtil.FORMAT_YYYYMMDDHHMISS_14);
//        releaseVO.setReceiveEndTime(dateString);
//        releaseVO.setReceiveStartTime("20180611170850");
//        releaseVO.setReleaseQuantity("5");
//        releaseVO.setReleaseType("01");
//        releaseVO.setActivityId("1");
//        releaseVOs.add(releaseVO);
//        List<TPromoActivityFuncParamVO>  funcParams = new ArrayList<>();
//        TPromoActivityFuncParamVO tPromoFuncParamDTO1 = new TPromoActivityFuncParamVO();
//        tPromoFuncParamDTO1.setFunctionCode("0102");
//        tPromoFuncParamDTO1.setFunctionType("01");
//        tPromoFuncParamDTO1.setParamType("01");
//        tPromoFuncParamDTO1.setParamUnit("");
//        tPromoFuncParamDTO1.setParamValue("");
//        tPromoFuncParamDTO1.setRankParam(1);
//        TPromoActivityFuncParamVO tPromoFuncParamDTO2 = new TPromoActivityFuncParamVO();
//        tPromoFuncParamDTO2.setFunctionCode("0202");
//        tPromoFuncParamDTO2.setFunctionType("02");
//        tPromoFuncParamDTO2.setParamType("01");
//        tPromoFuncParamDTO2.setParamUnit("");
//        tPromoFuncParamDTO2.setParamValue("");
//        tPromoFuncParamDTO2.setRankParam(1);
//        TPromoActivityFuncParamVO tPromoFuncParamDTO3 = new TPromoActivityFuncParamVO();
//        tPromoFuncParamDTO3.setFunctionCode("0302");
//        tPromoFuncParamDTO3.setFunctionType("03");
//        tPromoFuncParamDTO3.setParamType("02");
//        tPromoFuncParamDTO3.setParamUnit("02");
//        tPromoFuncParamDTO3.setParamValue("2");
//        tPromoFuncParamDTO3.setRankParam(1);
//        TPromoActivityFuncParamVO tPromoFuncParamDTO4 = new TPromoActivityFuncParamVO();
//        tPromoFuncParamDTO4.setFunctionCode("0401");
//        tPromoFuncParamDTO4.setFunctionType("04");
//        tPromoFuncParamDTO4.setParamType("02");
//        tPromoFuncParamDTO4.setParamUnit("01");
//        tPromoFuncParamDTO4.setParamValue("5");
//        tPromoFuncParamDTO4.setRankParam(1);
//        funcParams.add(tPromoFuncParamDTO1);
//        funcParams.add(tPromoFuncParamDTO2);
//        funcParams.add(tPromoFuncParamDTO3);
//        funcParams.add(tPromoFuncParamDTO4);
//        
//        List<TPromoActivityStoreVO> storeVOs = new ArrayList<>();
//        TPromoActivityStoreVO storeVO = new TPromoActivityStoreVO();
//        storeVO.setStoreCode(1);
//        storeVOs.add(storeVO);
//        
//        Map<String, ActivityCacheDTO> map = new HashMap<>();
//        // 创建券活动缓存
//        ActivityCacheDTO activity = new ActivityCacheDTO();
//        activity.setCouponActivity(couponActivityVO);
//        activity.setPromoActivity(tPromoActivityVO);
//        activity.setPromoTemplate(templateVO);
//        activity.setPromoFuncParams(funcParams);
//        map.put(activity.getPromoActivity().getId(), activity);
//        //when
//        when(activityCacheDomain.getActivityCacheMap(anyString(), any(), any())).thenReturn(map);
////        when(activityService.queryActivityByTenantCode(anyString(), any(), any(), any())).thenReturn(arrayList);
////        when(tPromoRuleGiftService.getGiftListByActivityId(anyString())).thenReturn(null);//赠品
////        when(promoActivityFuncRankService.getRankListByRuleId(anyString())).thenReturn(new ArrayList<TPromoActivityFuncRankVO>());//层级
////        when(tPromoActivityFuncParamService.getRuleFuncParamListByRankId(anyString())).thenReturn(new ArrayList<TPromoActivityFuncParamVO>());//层级参数
////        when(memberDomain.checkMemberLevel(anyString(), anyString())).thenReturn(true);
////        when(memberLabelDomain.checkMemberLabel(anyString(), anyString())).thenReturn(true);
////        when(tPromoActivityFuncParamService.getRuleFuncParamListByRankIdAndFunctionCode(anyString(), anyString())).thenReturn(null);
////        when(promoCouponActivityService.findCouponActivity(anyString())).thenReturn(couponActivityVO);//券活动
//        when(tPromoActivityStoreService.getStoresByActivityId(anyString())).thenReturn(storeVOs);//店铺集合
////        when(tPromoTemplateService.getTemplateById(anyString())).thenReturn(templateVO);//模板对象
//        when(promoCouponReleaseService.queryReleasesByActivityIds(any())).thenReturn(releaseVOs);//投放集合     
////        when(promoCouponCodeUserService.getUserCouponCountByActivityId(anyString(), anyString())).thenReturn(2);
////        when(promoCouponCodeUserService.getCodeUserByReleaseId(anyString())).thenReturn(2);
//        when(redisService.getCouponUserCount(anyString(),anyString(),anyString())).thenReturn(2);
//        PageInfo<StoreCouponActivityOutDTO> pageInfo = couponActivityDomain.queryCouponActivityInStore(inStore);
//        // then
//        Assert.assertEquals(5, pageInfo.getList().get(0).getReceiveStatus());
//    }
//    
//    @Test
//    public void 店铺下_生效的券活动列表_会员不符合_6(){
//        //given
//        StoreCouponActivityInDTO inStore = ActivityFactory.queryCouponActivityInStore();
//        Template template = Template.builder().templateEnum(TemplateEnum.T304).condition("2").reward("5").build();
//        TPromoTemplateVO templateVO = new TPromoTemplateVO();
//        templateVO.setId(TemplateEnum.T304.getId());
//        templateVO.setTemplateCode(template.getTemplateEnum().getCode());
//        TPromoActivityVO tPromoActivityVO = new TPromoActivityVO();
//        tPromoActivityVO.setId("1");
//        tPromoActivityVO.setActivityBegin("20180611170850");
//        Date date = DateUtil.addDay(new Date(), 5);
//        String string = DateUtil.format(date, DateUtil.FORMAT_YYYYMMDDHHMISS_14);
//        tPromoActivityVO.setActivityEnd(string);
//        tPromoActivityVO.setStoreType("01");
//        List<TPromoActivityVO> arrayList = new ArrayList<>();
//        arrayList.add(tPromoActivityVO);
//        TPromoCouponActivityVO couponActivityVO = ActivityFactory.createCouponActivityVO("01","1","1");
//        couponActivityVO.setUserLimitMax(2);
//
//        List<TPromoCouponReleaseVO> releaseVOs = new ArrayList<TPromoCouponReleaseVO>();
//        TPromoCouponReleaseVO releaseVO = new TPromoCouponReleaseVO();
//        Date release = DateUtil.addDay(new Date(), 1);
//        String dateString = DateUtil.format(release, DateUtil.FORMAT_YYYYMMDDHHMISS_14);
//        releaseVO.setReceiveEndTime(dateString);
//        releaseVO.setReceiveStartTime("20180611170850");
//        releaseVO.setReleaseQuantity("5");
//        releaseVO.setReleaseType("01");
//        releaseVO.setActivityId("1");
//        releaseVOs.add(releaseVO);
//        List<TPromoActivityFuncParamVO>  funcParams = new ArrayList<>();
//        TPromoActivityFuncParamVO tPromoFuncParamDTO1 = new TPromoActivityFuncParamVO();
//        tPromoFuncParamDTO1.setFunctionCode("0102");
//        tPromoFuncParamDTO1.setFunctionType("01");
//        tPromoFuncParamDTO1.setParamType("01");
//        tPromoFuncParamDTO1.setParamUnit("");
//        tPromoFuncParamDTO1.setParamValue("");
//        tPromoFuncParamDTO1.setRankParam(1);
//        TPromoActivityFuncParamVO tPromoFuncParamDTO2 = new TPromoActivityFuncParamVO();
//        tPromoFuncParamDTO2.setFunctionCode("0202");
//        tPromoFuncParamDTO2.setFunctionType("02");
//        tPromoFuncParamDTO2.setParamType("01");
//        tPromoFuncParamDTO2.setParamUnit("");
//        tPromoFuncParamDTO2.setParamValue("");
//        tPromoFuncParamDTO2.setRankParam(1);
//        TPromoActivityFuncParamVO tPromoFuncParamDTO3 = new TPromoActivityFuncParamVO();
//        tPromoFuncParamDTO3.setFunctionCode("0302");
//        tPromoFuncParamDTO3.setFunctionType("03");
//        tPromoFuncParamDTO3.setParamType("02");
//        tPromoFuncParamDTO3.setParamUnit("02");
//        tPromoFuncParamDTO3.setParamValue("2");
//        tPromoFuncParamDTO3.setRankParam(1);
//        TPromoActivityFuncParamVO tPromoFuncParamDTO4 = new TPromoActivityFuncParamVO();
//        tPromoFuncParamDTO4.setFunctionCode("0401");
//        tPromoFuncParamDTO4.setFunctionType("04");
//        tPromoFuncParamDTO4.setParamType("02");
//        tPromoFuncParamDTO4.setParamUnit("01");
//        tPromoFuncParamDTO4.setParamValue("5");
//        tPromoFuncParamDTO4.setRankParam(1);
//        funcParams.add(tPromoFuncParamDTO1);
//        funcParams.add(tPromoFuncParamDTO2);
//        funcParams.add(tPromoFuncParamDTO3);
//        funcParams.add(tPromoFuncParamDTO4);
//        
//        //会员等级
//        List<TPromoActivityMemberVO> members = new ArrayList<>();
//        TPromoActivityMemberVO promoMemberDTO = new TPromoActivityMemberVO();
//        promoMemberDTO.setMemberLevelCode("12");
//        promoMemberDTO.setMemberLevelName("级别1");
//        members.add(promoMemberDTO);
//       //店铺
//        List<TPromoActivityStoreVO> storeVOs = new ArrayList<>();
//        TPromoActivityStoreVO storeVO = new TPromoActivityStoreVO();
//        storeVO.setStoreCode(12);
//        storeVO.setStoreCode(12);
//        storeVO.setStoreName("店铺名称12");
//        storeVO.setChannelCode(12);
//        storeVO.setChannelName("mingchg");
//        storeVOs.add(storeVO);
//        
//        Map<String, ActivityCacheDTO> map = new HashMap<>();
//        // 创建券活动缓存
//        ActivityCacheDTO activity = new ActivityCacheDTO();
//        activity.setCouponActivity(couponActivityVO);
//        activity.setPromoActivity(tPromoActivityVO);
//        activity.setPromoTemplate(templateVO);
//        activity.setPromoFuncParams(funcParams);
//        activity.setPromoMembers(members);
//        map.put(activity.getPromoActivity().getId(), activity);
//        //when
//        when(activityCacheDomain.getActivityCacheMap(anyString(), any(), any())).thenReturn(map);
////        when(activityService.queryActivityByTenantCode(anyString(), any(), any(), any())).thenReturn(arrayList);
////        when(tPromoRuleGiftService.getGiftListByActivityId(anyString())).thenReturn(null);//赠品
////        when(promoActivityFuncRankService.getRankListByRuleId(anyString())).thenReturn(new ArrayList<TPromoActivityFuncRankVO>());//层级
////        when(tPromoActivityFuncParamService.getRuleFuncParamListByRankId(anyString())).thenReturn(new ArrayList<TPromoActivityFuncParamVO>());//层级参数
////        when(memberDomain.checkMemberLevel(anyString(), anyString())).thenReturn(true);
////        when(memberLabelDomain.checkMemberLabel(anyString(), anyString())).thenReturn(true);
////        when(tPromoActivityFuncParamService.getRuleFuncParamListByRankIdAndFunctionCode(anyString(), anyString())).thenReturn(null);
////        when(promoCouponActivityService.findCouponActivity(anyString())).thenReturn(couponActivityVO);//券活动
////        when(tPromoActivityStoreService.getStoresByActivityId(anyString())).thenReturn(storeVOs);//店铺集合
////        when(tPromoTemplateService.getTemplateById(anyString())).thenReturn(templateVO);//模板对象
//        when(promoCouponReleaseService.queryReleasesByActivityIds(any())).thenReturn(releaseVOs);//投放集合     
////        when(promoCouponCodeUserService.getUserCouponCountByActivityId(anyString(), anyString())).thenReturn(2);
////        when(promoCouponCodeUserService.getCodeUserByReleaseId(anyString())).thenReturn(2);
////        when(redisService.getCouponUserCount(anyString(),anyString(),anyString())).thenReturn(2);
//        PageInfo<StoreCouponActivityOutDTO> pageInfo = couponActivityDomain.queryCouponActivityInStore(inStore);
//        // then
//        Assert.assertEquals(6, pageInfo.getList().get(0).getReceiveStatus());
//    }
//    
//    @Test
//    public void 店铺下_生效的券活动列表_可领取_1(){
//        //given
//        StoreCouponActivityInDTO inStore = ActivityFactory.queryCouponActivityInStore();
//        Template template = Template.builder().templateEnum(TemplateEnum.T304).condition("2").reward("5").build();
//        TPromoTemplateVO templateVO = new TPromoTemplateVO();
//        templateVO.setId(TemplateEnum.T304.getId());
//        templateVO.setTemplateCode(template.getTemplateEnum().getCode());
//        TPromoActivityVO tPromoActivityVO = new TPromoActivityVO();
//        tPromoActivityVO.setId("1");
//        tPromoActivityVO.setActivityBegin("20180611170850");
//        Date date = DateUtil.addDay(new Date(), 5);
//        String string = DateUtil.format(date, DateUtil.FORMAT_YYYYMMDDHHMISS_14);
//        tPromoActivityVO.setActivityEnd(string);
//        List<TPromoActivityVO> arrayList = new ArrayList<>();
//        arrayList.add(tPromoActivityVO);
//        TPromoCouponActivityVO couponActivityVO = ActivityFactory.createCouponActivityVO("01","1","1");
//        couponActivityVO.setUserLimitMax(2);
//        List<TPromoActivityStoreVO> storeVOs = new ArrayList<>();
//        TPromoActivityStoreVO storeVO = new TPromoActivityStoreVO();
//        storeVO.setStoreCode(1);
//        storeVOs.add(storeVO);
//        ArrayList<TPromoCouponReleaseVO> releaseVOs = new ArrayList<TPromoCouponReleaseVO>();
//        TPromoCouponReleaseVO releaseVO = new TPromoCouponReleaseVO();
//        Date release = DateUtil.addDay(new Date(), 1);
//        String dateString = DateUtil.format(release, DateUtil.FORMAT_YYYYMMDDHHMISS_14);
//        releaseVO.setReceiveEndTime(dateString);
//        releaseVO.setReceiveStartTime("20180611170850");
//        releaseVO.setReleaseQuantity("5");
//        releaseVO.setReleaseType("01");
//        releaseVO.setActivityId("1");
//        releaseVO.setInventory("5");
//        releaseVOs.add(releaseVO);
//        List<TPromoActivityFuncParamVO>  funcParams = new ArrayList<>();
//        TPromoActivityFuncParamVO tPromoFuncParamDTO1 = new TPromoActivityFuncParamVO();
//        tPromoFuncParamDTO1.setFunctionCode("0102");
//        tPromoFuncParamDTO1.setFunctionType("01");
//        tPromoFuncParamDTO1.setParamType("01");
//        tPromoFuncParamDTO1.setParamUnit("");
//        tPromoFuncParamDTO1.setParamValue("");
//        tPromoFuncParamDTO1.setRankParam(1);
//        TPromoActivityFuncParamVO tPromoFuncParamDTO2 = new TPromoActivityFuncParamVO();
//        tPromoFuncParamDTO2.setFunctionCode("0202");
//        tPromoFuncParamDTO2.setFunctionType("02");
//        tPromoFuncParamDTO2.setParamType("01");
//        tPromoFuncParamDTO2.setParamUnit("");
//        tPromoFuncParamDTO2.setParamValue("");
//        tPromoFuncParamDTO2.setRankParam(1);
//        TPromoActivityFuncParamVO tPromoFuncParamDTO3 = new TPromoActivityFuncParamVO();
//        tPromoFuncParamDTO3.setFunctionCode("0302");
//        tPromoFuncParamDTO3.setFunctionType("03");
//        tPromoFuncParamDTO3.setParamType("02");
//        tPromoFuncParamDTO3.setParamUnit("02");
//        tPromoFuncParamDTO3.setParamValue("2");
//        tPromoFuncParamDTO3.setRankParam(1);
//        TPromoActivityFuncParamVO tPromoFuncParamDTO4 = new TPromoActivityFuncParamVO();
//        tPromoFuncParamDTO4.setFunctionCode("0401");
//        tPromoFuncParamDTO4.setFunctionType("04");
//        tPromoFuncParamDTO4.setParamType("02");
//        tPromoFuncParamDTO4.setParamUnit("01");
//        tPromoFuncParamDTO4.setParamValue("5");
//        tPromoFuncParamDTO4.setRankParam(1);
//        funcParams.add(tPromoFuncParamDTO1);
//        funcParams.add(tPromoFuncParamDTO2);
//        funcParams.add(tPromoFuncParamDTO3);
//        funcParams.add(tPromoFuncParamDTO4);
//        Map<String, ActivityCacheDTO> map = new HashMap<>();
//        // 创建券活动缓存
//        ActivityCacheDTO activity = new ActivityCacheDTO();
//        activity.setCouponActivity(couponActivityVO);
//        activity.setPromoActivity(tPromoActivityVO);
//        activity.setPromoTemplate(templateVO);
//        activity.setPromoFuncParams(funcParams);
//        map.put(activity.getPromoActivity().getId(), activity);
//        //when
//        when(activityCacheDomain.getActivityCacheMap(anyString(), any(), any())).thenReturn(map);
////        when(activityService.queryActivityByTenantCode(anyString(), any(), any(), any())).thenReturn(arrayList);
////        when(tPromoRuleGiftService.getGiftListByActivityId(anyString())).thenReturn(null);//赠品
////        when(promoActivityFuncRankService.getRankListByRuleId(anyString())).thenReturn(new ArrayList<TPromoActivityFuncRankVO>());//层级
////        when(tPromoActivityFuncParamService.getRuleFuncParamListByRankId(anyString())).thenReturn(new ArrayList<TPromoActivityFuncParamVO>());//层级参数
////        when(memberDomain.checkMemberLevel(anyString(), anyString())).thenReturn(true);
////        when(memberLabelDomain.checkMemberLabel(anyString(), anyString())).thenReturn(true);
////        when(tPromoActivityFuncParamService.getRuleFuncParamListByRankIdAndFunctionCode(anyString(), anyString())).thenReturn(null);
////        when(promoCouponActivityService.findCouponActivity(anyString())).thenReturn(couponActivityVO);//券活动
//        when(tPromoActivityStoreService.getStoresByActivityId(anyString())).thenReturn(storeVOs);//店铺集合
////        when(tPromoTemplateService.getTemplateById(anyString())).thenReturn(templateVO);//模板对象
//        when(promoCouponReleaseService.queryReleasesByActivityIds(any())).thenReturn(releaseVOs);//投放集合
////        when(promoCouponCodeUserService.getUserCouponCountByActivityId(anyString(), anyString())).thenReturn(1);
////        when(promoCouponCodeUserService.getCodeUserByReleaseId(anyString())).thenReturn(1);
//       PageInfo<StoreCouponActivityOutDTO> pageInfo = couponActivityDomain.queryCouponActivityInStore(inStore);
//        // then
//        Assert.assertEquals(1, pageInfo.getList().get(0).getReceiveStatus());
//    }
//
//    
//    @Test
//    public void 店铺下_生效的券活动列表_可领取_资源回滚_1(){
//        //given
//        StoreCouponActivityInDTO inStore = ActivityFactory.queryCouponActivityInStore();
//        Template template = Template.builder().templateEnum(TemplateEnum.T304).condition("2").reward("5").build();
//        TPromoTemplateVO templateVO = new TPromoTemplateVO();
//        templateVO.setId(TemplateEnum.T304.getId());
//        templateVO.setTemplateCode(template.getTemplateEnum().getCode());
//        TPromoActivityVO tPromoActivityVO = new TPromoActivityVO();
//        tPromoActivityVO.setId("1");
//        tPromoActivityVO.setActivityBegin("20180611170850");
//        Date date = DateUtil.addDay(new Date(), 5);
//        String string = DateUtil.format(date, DateUtil.FORMAT_YYYYMMDDHHMISS_14);
//        tPromoActivityVO.setActivityEnd(string);
//        List<TPromoActivityVO> arrayList = new ArrayList<>();
//        arrayList.add(tPromoActivityVO);
//        TPromoCouponActivityVO couponActivityVO = ActivityFactory.createCouponActivityVO("01","1","1");
//        couponActivityVO.setUserLimitMax(2);
//        List<TPromoActivityStoreVO> storeVOs = new ArrayList<>();
//        TPromoActivityStoreVO storeVO = new TPromoActivityStoreVO();
//        storeVO.setStoreCode(1);
//        storeVOs.add(storeVO);
//        ArrayList<TPromoCouponReleaseVO> releaseVOs = new ArrayList<TPromoCouponReleaseVO>();
//        TPromoCouponReleaseVO releaseVO = new TPromoCouponReleaseVO();
//        Date release = DateUtil.addDay(new Date(), 1);
//        String dateString = DateUtil.format(release, DateUtil.FORMAT_YYYYMMDDHHMISS_14);
//        releaseVO.setReceiveEndTime(dateString);
//        releaseVO.setReceiveStartTime("20180611170850");
//        releaseVO.setReleaseQuantity("5");
//        releaseVO.setReleaseType("01");
//        releaseVO.setActivityId("1");
//        releaseVO.setInventory("-1");
//        releaseVO.setId("1");
//        releaseVOs.add(releaseVO);
//        List<TPromoActivityFuncParamVO>  funcParams = new ArrayList<>();
//        TPromoActivityFuncParamVO tPromoFuncParamDTO1 = new TPromoActivityFuncParamVO();
//        tPromoFuncParamDTO1.setFunctionCode("0102");
//        tPromoFuncParamDTO1.setFunctionType("01");
//        tPromoFuncParamDTO1.setParamType("01");
//        tPromoFuncParamDTO1.setParamUnit("");
//        tPromoFuncParamDTO1.setParamValue("");
//        tPromoFuncParamDTO1.setRankParam(1);
//        TPromoActivityFuncParamVO tPromoFuncParamDTO2 = new TPromoActivityFuncParamVO();
//        tPromoFuncParamDTO2.setFunctionCode("0202");
//        tPromoFuncParamDTO2.setFunctionType("02");
//        tPromoFuncParamDTO2.setParamType("01");
//        tPromoFuncParamDTO2.setParamUnit("");
//        tPromoFuncParamDTO2.setParamValue("");
//        tPromoFuncParamDTO2.setRankParam(1);
//        TPromoActivityFuncParamVO tPromoFuncParamDTO3 = new TPromoActivityFuncParamVO();
//        tPromoFuncParamDTO3.setFunctionCode("0302");
//        tPromoFuncParamDTO3.setFunctionType("03");
//        tPromoFuncParamDTO3.setParamType("02");
//        tPromoFuncParamDTO3.setParamUnit("02");
//        tPromoFuncParamDTO3.setParamValue("2");
//        tPromoFuncParamDTO3.setRankParam(1);
//        TPromoActivityFuncParamVO tPromoFuncParamDTO4 = new TPromoActivityFuncParamVO();
//        tPromoFuncParamDTO4.setFunctionCode("0401");
//        tPromoFuncParamDTO4.setFunctionType("04");
//        tPromoFuncParamDTO4.setParamType("02");
//        tPromoFuncParamDTO4.setParamUnit("01");
//        tPromoFuncParamDTO4.setParamValue("5");
//        tPromoFuncParamDTO4.setRankParam(1);
//        funcParams.add(tPromoFuncParamDTO1);
//        funcParams.add(tPromoFuncParamDTO2);
//        funcParams.add(tPromoFuncParamDTO3);
//        funcParams.add(tPromoFuncParamDTO4);
//        Map<String, ActivityCacheDTO> map = new HashMap<>();
//        // 创建券活动缓存
//        ActivityCacheDTO activity = new ActivityCacheDTO();
//        activity.setCouponActivity(couponActivityVO);
//        activity.setPromoActivity(tPromoActivityVO);
//        activity.setPromoTemplate(templateVO);
//        activity.setPromoFuncParams(funcParams);
//        map.put(activity.getPromoActivity().getId(), activity);
//        //when
//        when(activityCacheDomain.getActivityCacheMap(anyString(), any(), any())).thenReturn(map);
////        when(activityService.queryActivityByTenantCode(anyString(), any(), any(), any())).thenReturn(arrayList);
////        when(tPromoRuleGiftService.getGiftListByActivityId(anyString())).thenReturn(null);//赠品
////        when(promoActivityFuncRankService.getRankListByRuleId(anyString())).thenReturn(new ArrayList<TPromoActivityFuncRankVO>());//层级
////        when(tPromoActivityFuncParamService.getRuleFuncParamListByRankId(anyString())).thenReturn(new ArrayList<TPromoActivityFuncParamVO>());//层级参数
////        when(memberDomain.checkMemberLevel(anyString(), anyString())).thenReturn(true);
////        when(memberLabelDomain.checkMemberLabel(anyString(), anyString())).thenReturn(true);
////        when(tPromoActivityFuncParamService.getRuleFuncParamListByRankIdAndFunctionCode(anyString(), anyString())).thenReturn(null);
////        when(promoCouponActivityService.findCouponActivity(anyString())).thenReturn(couponActivityVO);//券活动
////        when(tPromoActivityStoreService.getStoresByActivityId(anyString())).thenReturn(storeVOs);//店铺集合
////        when(tPromoTemplateService.getTemplateById(anyString())).thenReturn(templateVO);//模板对象
//        when(promoCouponReleaseService.queryReleasesByActivityIds(any())).thenReturn(releaseVOs);//投放集合
////        when(promoCouponCodeUserService.getUserCouponCountByActivityId(anyString(), anyString())).thenReturn(1);
////        when(promoCouponCodeUserService.getCodeUserByReleaseId(anyString())).thenReturn(1);
//        when(couponReleaseDomain.updateCouponReleaseInventory(any())).thenReturn(releaseVO);
//        PageInfo<StoreCouponActivityOutDTO> pageInfo = couponActivityDomain.queryCouponActivityInStore(inStore);
//        // then
//        Assert.assertEquals(1, pageInfo.getList().get(0).getReceiveStatus());
//    }
//    
//    @Test
//    public void 店铺下_生效的券活动列表_已结束_3(){
//        //given
//        StoreCouponActivityInDTO inStore = ActivityFactory.queryCouponActivityInStore();
//        Template template = Template.builder().templateEnum(TemplateEnum.T304).condition("2").reward("5").build();
//        TPromoTemplateVO templateVO = new TPromoTemplateVO();
//        templateVO.setId(TemplateEnum.T304.getId());
//        templateVO.setTemplateCode(template.getTemplateEnum().getCode());
//        TPromoActivityVO tPromoActivityVO = new TPromoActivityVO();
//        tPromoActivityVO.setId("1");
//        tPromoActivityVO.setActivityBegin("20180611170850");
//        Date date = DateUtil.addDay(new Date(), 5);
//        String string = DateUtil.format(date, DateUtil.FORMAT_YYYYMMDDHHMISS_14);
//        tPromoActivityVO.setActivityEnd(string);
//        List<TPromoActivityVO> arrayList = new ArrayList<>();
//        arrayList.add(tPromoActivityVO);
//        TPromoCouponActivityVO couponActivityVO = new TPromoCouponActivityVO();
//        couponActivityVO.setActivityId("1");
//        couponActivityVO.setId("1");
//        couponActivityVO.setCouponType("01");
//        couponActivityVO.setUserLimitMax(0);
//        List<TPromoActivityStoreVO> storeVOs = new ArrayList<>();
//        TPromoActivityStoreVO storeVO = new TPromoActivityStoreVO();
//        storeVO.setStoreCode(1);
//        storeVOs.add(storeVO);
//        ArrayList<TPromoCouponReleaseVO> releaseVOs = new ArrayList<TPromoCouponReleaseVO>();
//        TPromoCouponReleaseVO releaseVO = new TPromoCouponReleaseVO();
//        Calendar strDate = Calendar.getInstance();
//        Date time = strDate.getTime();
//        String dateString = DateUtil.format(time, DateUtil.FORMAT_YYYYMMDDHHMISS_14);
//        releaseVO.setReceiveEndTime(dateString);
//        releaseVO.setReceiveStartTime("20180611170850");
//        releaseVO.setReleaseQuantity("5");
//        releaseVO.setReleaseType("01");
//        releaseVO.setActivityId("1");
//        releaseVO.setInventory("5");
//        releaseVOs.add(releaseVO);
//        List<TPromoActivityFuncParamVO>  funcParams = new ArrayList<>();
//        TPromoActivityFuncParamVO tPromoFuncParamDTO1 = new TPromoActivityFuncParamVO();
//        tPromoFuncParamDTO1.setFunctionCode("0102");
//        tPromoFuncParamDTO1.setFunctionType("01");
//        tPromoFuncParamDTO1.setParamType("01");
//        tPromoFuncParamDTO1.setParamUnit("");
//        tPromoFuncParamDTO1.setParamValue("");
//        tPromoFuncParamDTO1.setRankParam(1);
//        TPromoActivityFuncParamVO tPromoFuncParamDTO2 = new TPromoActivityFuncParamVO();
//        tPromoFuncParamDTO2.setFunctionCode("0202");
//        tPromoFuncParamDTO2.setFunctionType("02");
//        tPromoFuncParamDTO2.setParamType("01");
//        tPromoFuncParamDTO2.setParamUnit("");
//        tPromoFuncParamDTO2.setParamValue("");
//        tPromoFuncParamDTO2.setRankParam(1);
//        TPromoActivityFuncParamVO tPromoFuncParamDTO3 = new TPromoActivityFuncParamVO();
//        tPromoFuncParamDTO3.setFunctionCode("0302");
//        tPromoFuncParamDTO3.setFunctionType("03");
//        tPromoFuncParamDTO3.setParamType("02");
//        tPromoFuncParamDTO3.setParamUnit("02");
//        tPromoFuncParamDTO3.setParamValue("2");
//        tPromoFuncParamDTO3.setRankParam(1);
//        TPromoActivityFuncParamVO tPromoFuncParamDTO4 = new TPromoActivityFuncParamVO();
//        tPromoFuncParamDTO4.setFunctionCode("0401");
//        tPromoFuncParamDTO4.setFunctionType("04");
//        tPromoFuncParamDTO4.setParamType("02");
//        tPromoFuncParamDTO4.setParamUnit("01");
//        tPromoFuncParamDTO4.setParamValue("5");
//        tPromoFuncParamDTO4.setRankParam(1);
//        funcParams.add(tPromoFuncParamDTO1);
//        funcParams.add(tPromoFuncParamDTO2);
//        funcParams.add(tPromoFuncParamDTO3);
//        funcParams.add(tPromoFuncParamDTO4);
//        Map<String, ActivityCacheDTO> map = new HashMap<>();
//        // 创建券活动缓存
//        ActivityCacheDTO activity = new ActivityCacheDTO();
//        activity.setCouponActivity(couponActivityVO);
//        activity.setPromoActivity(tPromoActivityVO);
//        activity.setPromoTemplate(templateVO);
//        activity.setPromoFuncParams(funcParams);
//        map.put(activity.getPromoActivity().getId(), activity);
//        //when
//        when(activityCacheDomain.getActivityCacheMap(anyString(), any(), any())).thenReturn(map);
////        when(activityService.queryActivityByTenantCode(anyString(), any(), any(), any())).thenReturn(arrayList);
////        when(tPromoRuleGiftService.getGiftListByActivityId(anyString())).thenReturn(null);//赠品
////        when(promoActivityFuncRankService.getRankListByRuleId(anyString())).thenReturn(new ArrayList<TPromoActivityFuncRankVO>());//层级
////        when(tPromoActivityFuncParamService.getRuleFuncParamListByRankId(anyString())).thenReturn(new ArrayList<TPromoActivityFuncParamVO>());//层级参数
////        when(memberDomain.checkMemberLevel(anyString(), anyString())).thenReturn(true);
////        when(memberLabelDomain.checkMemberLabel(anyString(), anyString())).thenReturn(true);
////        when(tPromoActivityFuncParamService.getRuleFuncParamListByRankIdAndFunctionCode(anyString(), anyString())).thenReturn(null);
////        when(promoCouponActivityService.findCouponActivity(anyString())).thenReturn(couponActivityVO);//券活动
////        when(tPromoActivityStoreService.getStoresByActivityId(anyString())).thenReturn(storeVOs);//店铺集合
////        when(tPromoTemplateService.getTemplateById(anyString())).thenReturn(templateVO);//模板对象
//        when(promoCouponReleaseService.queryReleasesByActivityIds(any())).thenReturn(releaseVOs);//投放集合     
////        when(promoCouponCodeUserService.getUserCouponCountByActivityId(anyString(), anyString())).thenReturn(1);
////        when(promoCouponCodeUserService.getCodeUserByReleaseId(anyString())).thenReturn(1);
//
//        PageInfo<StoreCouponActivityOutDTO> pageInfo = couponActivityDomain.queryCouponActivityInStore(inStore);
//        // then
//        Assert.assertEquals(3, pageInfo.getList().get(0).getReceiveStatus());
//    }
//    
//    
    
}
