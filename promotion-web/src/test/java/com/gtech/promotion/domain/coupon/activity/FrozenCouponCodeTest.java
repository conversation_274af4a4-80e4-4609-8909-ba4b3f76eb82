/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.domain.coupon.activity;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * 测试券活动创建
 */
@RunWith(MockitoJUnitRunner.class)
public class FrozenCouponCodeTest{

    @Test
    public void test(){

    }
    // 1、有无奖励 限制   incentiveLimitedFlag 00无限制  01有限制
    // 2、有无渠道 限制   storeType 店铺范围：00-全店铺 01-自定义
    // 3、有无会员 等级限制   members
    // 商品范围： productType  00-全商品 01-指定范围 02-指定商品（上传文件）  03-指定多个商品范围
    // 4、商品：全商品  
    // 5、商品：指定商品范围
    // 6、商品：指定商品sku
    // 7、商品：指定多个商品范围
    // 8、正反选 productSelectType商品范围正反选：01-正选；02-反选
//
//    @InjectMocks
//    private FilterCouponDomainImpl filterCouponDomainImpl;
//    
//    @Mock
//    private PromoCouponCodeUserServiceImpl promoCouponCodeUserService;
//    
//    @Mock
//    private PromoCouponInnerCodeServiceImpl promoCouponInnerCodeService;
//    
//    @Mock
//    private TPromoActivityFuncRankServiceImpl promoActivityFuncRankService;
//
//    @Mock
//    private TPromoActivityFuncParamServiceImpl tPromoActivityFuncParamService;
//
//    @Mock
//    private PromoCouponReleaseServiceImpl promoCouponReleaseService;
//
//
//
//    @Mock
//    private TPromoTemplateServiceImpl tPromoTemplateService;
//
//    @Mock
//    private TPromoActivityGiftServiceImpl tPromoRuleGiftService;
//
//    @Mock
//    private TPromoMemberServiceImpl tPromoMemberService;
//
//    @Mock
//    private ActivityMemberDomain memberDomain;
//
//    @Mock
//    private ActivityMemberLabelDomain memberLabelDomain;
//
//    @Mock
//    private PromoCouponActivityServiceImpl promoCouponActivityService;
//
//    @Mock
//    private TPromoActivityLanguageServiceImpl languageService;
//
//    @Mock
//    private TPromoActivityServiceImpl activityService;
//
//    @Mock
//    private TPromoActivityStoreServiceImpl tPromoActivityStoreService;
//
//    @Test
//    public void 冻结券码(){
//        //given
//        FrozenCouponCodeInDTO frozen = ActivityFactory.frozenCouponCode();
//        //when
//        when(promoCouponInnerCodeService.frozenInnerCode(anyString(),anyString())).thenReturn(1);
//        when(promoCouponCodeUserService.frozenCouponCodeUser(anyString(),anyString())).thenReturn(1);
//        // then
//        filterCouponDomainImpl.frozenCouponCode(frozen);
//    }

}
