/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.domain.impl.activity;

import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.promotion.calc.TemplateEnum;
import com.gtech.promotion.code.activity.ActivityTypeEnum;
import com.gtech.promotion.code.activity.ProductSelectionEnum;
import com.gtech.promotion.component.activity.*;
import com.gtech.promotion.dao.model.activity.PromoGroupVO;
import com.gtech.promotion.dto.cache.ActivityCacheDTO;
import com.gtech.promotion.dto.cache.ProductSkuDetailDTO;
import com.gtech.promotion.dto.in.activity.SingleProductDTO;
import com.gtech.promotion.dto.out.activity.SkuActivityPriceDTO;
import com.gtech.promotion.helper.ActivityRedisHelpler;
import com.gtech.promotion.helper.bean.ActivityProductDetail;
import com.gtech.promotion.helper.bean.Template;
import com.gtech.promotion.helper.factory.CacheActivityFactory;
import com.gtech.promotion.helper.factory.ProductFactory;
import com.gtech.promotion.service.activity.ActivityProductDetailService;
import com.gtech.promotion.service.activity.PromotionGroupService;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * plp页面controller测试类
 */
public class PLPPDPActivityTest{

    @InjectMocks
    private ActivityComponentDomain activityComponentDomain;

    @Mock
    private ActivityCacheDomain activityCacheDomain;

    @Mock
    private ActivityRedisHelpler redisService;

    @Mock
    private TemplateDomain templateTagDomain;
    @Mock
    private ActivityProductDetailService activityProductDetailService;
    @Mock
    private ActivityPriceComponentDomain activityPriceComponentDomain;

    @Mock
    private PromoGroupDomain promoGroupDomain;

    @Mock
    private PromotionGroupService promotionGroupService;

    @Before
    public void setup(){
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void pdp单品价格计算103特价(){
        SingleProductDTO skuCodeListDTO = ProductFactory.initSkuCodeListDTO();

        Map<String, ActivityCacheDTO> map = new HashMap<>();
        // 创建活动范围
        List<ActivityProductDetail> details = new ArrayList<>();
        details.add(ActivityProductDetail.builder().productCode("ppp").build());
        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0103).reward("5").build();
        // 创建活动
        ActivityCacheDTO activity = CacheActivityFactory.createActivity(ActivityTypeEnum.ACTIVITY, template, ProductSelectionEnum.SELECT, null, details, null);
        map.put(activity.getActivityModel().getActivityCode(), activity);

        when(activityCacheDomain.filterActivityByProduct(any(), any(), any())).thenReturn(map);
        when(templateTagDomain.findTemplateTagByTagId(anyString())).thenReturn(TemplateEnum.T0101.code()+","+TemplateEnum.T0102.code()+","+TemplateEnum.T0103.code());
        when(redisService.getActivitySetting(anyString(), anyString())).thenReturn(new HashMap<>());
        when(activityPriceComponentDomain.getTenantPrecision(any())).thenReturn(2);
        SkuActivityPriceDTO skuQuery = activityComponentDomain.activitySkuPrice(skuCodeListDTO, map);
        assertEquals(skuQuery.getActivity().get(0).getPromotionPrice(), new BigDecimal("5.00"));
    }

    @Test
    public void pdp单品价格计算101减金额(){
        SingleProductDTO skuCodeListDTO = ProductFactory.initSkuCodeListDTO();

        Map<String, ActivityCacheDTO> map = new HashMap<>();

        PromoGroupVO promoGroupVO = new PromoGroupVO();
        promoGroupVO.setGroupCode("groupCode");
        promoGroupVO.setPriority(1L);

        // 创建活动范围
        List<ActivityProductDetail> details = new ArrayList<>();
        details.add(ActivityProductDetail.builder().productCode("ppp").build());
        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0101).reward("1").build();
        // 创建活动
        ActivityCacheDTO activity = CacheActivityFactory.createActivity(ActivityTypeEnum.ACTIVITY, template, ProductSelectionEnum.SELECT, null, details, null);
        map.put(activity.getActivityModel().getActivityCode(), activity);
        when(activityCacheDomain.filterActivityByProduct(any(), any(), any())).thenReturn(map);
        when(templateTagDomain.findTemplateTagByTagId(anyString())).thenReturn(TemplateEnum.T0101.code()+","+TemplateEnum.T0102.code()+","+TemplateEnum.T0103.code());
        when(redisService.getActivitySetting(anyString(), anyString())).thenReturn(new HashMap<>());
        when(activityPriceComponentDomain.getTenantPrecision(any())).thenReturn(2);
        when(promoGroupDomain.checkGroupEnabledByTenantCode(Mockito.any(), Mockito.any())).thenReturn(true);
        when(promotionGroupService.getGroupByGroupCode(Mockito.any(), Mockito.any())).thenReturn(promoGroupVO);

        SkuActivityPriceDTO skuQuery = activityComponentDomain.activitySkuPrice(skuCodeListDTO, map);
        assertEquals(skuQuery.getActivity().get(0).getPromotionPrice(), new BigDecimal("1.00"));
    }

    @Test
    public void pdp单品价格计算104减金额(){
        SingleProductDTO skuCodeListDTO = ProductFactory.initSkuCodeListDTO();

        Map<String, ActivityCacheDTO> map = new HashMap<>();
        // 创建活动范围
        List<ActivityProductDetail> details = new ArrayList<>();
        details.add(ActivityProductDetail.builder().productCode("ppp").build());
        // 创建活动模板
        Template template = Template.builder().templateEnum(TemplateEnum.T0104).reward("1").build();
        // 创建活动
        ActivityCacheDTO activity = CacheActivityFactory.createActivity(ActivityTypeEnum.ACTIVITY, template, ProductSelectionEnum.SELECT, null, details, null);
        map.put(activity.getActivityModel().getActivityCode(), activity);

        when(activityCacheDomain.filterActivityByProduct(any(), any(), any())).thenReturn(map);
        when(templateTagDomain.findTemplateTagByTagId(anyString())).thenReturn(TemplateEnum.T0101.code()+","+TemplateEnum.T0102.code()+","+TemplateEnum.T0103.code()+","+TemplateEnum.T0104.code());
        Map<Object, Object> persion = new HashMap<>();
        persion.put("2", "1");
        when(redisService.getActivitySetting(anyString(), anyString())).thenReturn(persion);
        List<ProductSkuDetailDTO> productSkuDetailDTOS = BeanCopyUtils.jsonCopyList(details, ProductSkuDetailDTO.class);
        productSkuDetailDTOS.forEach(x->x.setPromoPrice(new BigDecimal("1.00")));
        when(activityProductDetailService.queryListByActivityCodesAndProductCode(any(), anyString())).thenReturn(productSkuDetailDTOS);
        when(activityPriceComponentDomain.getTenantPrecision(any())).thenReturn(2);
        SkuActivityPriceDTO skuQuery = activityComponentDomain.activitySkuPrice(skuCodeListDTO, map);
        assertEquals(skuQuery.getActivity().get(0).getPromotionPrice(), new BigDecimal("1.00"));
    }

}
