/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.domain.coupon.activity;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class FilterCouponDomainTest {

    @Test
    public void test(){

    }
//
//    @InjectMocks
//    private FilterCouponDomainImpl filterCouponDomain;
//
//    @Mock
//    private PromoCouponInnerCodeService couponInnerCodeService;
//
//    @Mock
//    private PromoCouponCodeUserService couponCodeUserService;
//
//    @Mock
//    private PromoCouponReleaseService promoCouponReleaseService;
//
//    @Mock
//    private StringRedisTemplate redisTemplate;
//
//    @Mock
//    private CouponReleaseDomain couponReleaseDomain;
//
//    @Mock
//    private ActivityCacheDomain activityCacheDomain;
//
//    @Mock
//    private TPromoActivityService promoActivityService;
//    @Mock
//    private PromoCouponActivityService couponActivityService;
//    @Mock
//    private FunctionParamDomain functionParamDomain;
//    @Mock
//    private TPromoTemplateService templateService;
//
//    @Test
//    public void testFrozenCouponCode(){
//        String couponCode = "1";
//        FrozenCouponCodeInDTO frozenDTO = new FrozenCouponCodeInDTO();
//        frozenDTO.setCouponCode(couponCode);
//        TPromoCouponInnerCodeVO vo2 = new TPromoCouponInnerCodeVO();
//        vo2.setReleaseId("1");
//        TPromoCouponReleaseVO releaseVO = new TPromoCouponReleaseVO();
//        releaseVO.setInventory("-1");
//        ZSetOperations<String, String> zSetOperations = Mockito.mock(ZSetOperations.class);
//        Set<String> range = new HashSet<>();
//        CouponCodeCacheDTO dto = new CouponCodeCacheDTO();
//        dto.setCouponCode(couponCode);
//        range.add(JSONObject.toJSONString(dto));
//        // when
//        when(couponInnerCodeService.frozenInnerCode(any(),any())).thenReturn(1);
//        when(couponCodeUserService.frozenCouponCodeUser(any(),any())).thenReturn(1);
//        when(couponInnerCodeService.findCouponByCouponCode(any(),any())).thenReturn(vo2);
//        when(promoCouponReleaseService.getReleaseById(any())).thenReturn(releaseVO);
//        when(redisTemplate.opsForZSet()).thenReturn(zSetOperations);
//        when(zSetOperations.range(any(), anyLong(), anyLong())).thenReturn(range);
//        when(redisTemplate.opsForZSet()).thenReturn(zSetOperations);
//        when(zSetOperations.remove(any(), any())).thenReturn(1L);
//        when(couponReleaseDomain.updateCouponReleaseInventory(any())).thenReturn(releaseVO);
//        when(promoCouponReleaseService.deductInventory(any())).thenReturn(1);
//
//        filterCouponDomain.frozenCouponCode(frozenDTO);
//    }
//
//    @Test
//    public void testFilterCoupon_fail(){
//        ShoppingCart shoppingCart = new ShoppingCart();
//        shoppingCart.setCouponCodes("1");
//        List<TPromoCouponInnerCodeVO> couponInnerCodes = new ArrayList<>();
//        TPromoCouponInnerCodeVO couponInnerCodeVO = new TPromoCouponInnerCodeVO();
//        couponInnerCodeVO.setCouponType(CouponTypeEnum.ANONYMITY_COUPON.code());
//        couponInnerCodeVO.setCouponCode("1");
//        couponInnerCodeVO.setStatus(CouponStatusEnum.UN_GRANT.code());
//        couponInnerCodeVO.setReceiveStartTime(String.valueOf(Long.parseLong(DateValidUtil.getNowFormatDate()) - 10000));
//        couponInnerCodeVO.setReceiveEndTime(String.valueOf(Long.parseLong(DateValidUtil.getNowFormatDate()) + 10000));
//        couponInnerCodes.add(couponInnerCodeVO);
//
//        TPromoActivityVO activityVO = new TPromoActivityVO();
//        activityVO.setActivityStatus(ActivityStatusEnum.EFFECTIVE.code());
//
//        TPromoCouponReleaseVO releaseVO = new TPromoCouponReleaseVO();
//        TPromoCouponActivityVO couponActivity = new TPromoCouponActivityVO();
//        ConditionAndFace conditionAndFace = new ConditionAndFace();
//        TPromoTemplateVO template = new TPromoTemplateVO();
//        template.setTemplateCode("1231");
//        // when
//        when(couponInnerCodeService.getCouponInnerCodeByCodes(any(), any())).thenReturn(couponInnerCodes);
//        when(activityCacheDomain.getActivityCacheMap(any(), any(), any())).thenReturn(null);
//        when(promoActivityService.findActivityByActivityId(any())).thenReturn(activityVO);
//        when(promoCouponReleaseService.getReleaseById(any())).thenReturn(releaseVO);
//        when(couponActivityService.findCouponActivity(any())).thenReturn(couponActivity);
//        when(functionParamDomain.findConditionAndFace(any())).thenReturn(conditionAndFace);
//        when(templateService.getTemplateById(any())).thenReturn(template);
//
//        List<CouponInfoDTO> couponInfoDTOS = filterCouponDomain.filterCoupon(shoppingCart);
//        Assert.assertEquals("活动未生效", couponInfoDTOS.get(0).getFalseReason());
//    }
}
