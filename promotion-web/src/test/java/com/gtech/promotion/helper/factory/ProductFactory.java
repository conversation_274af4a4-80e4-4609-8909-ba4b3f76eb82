/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.helper.factory;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;

import com.alibaba.fastjson.JSON;
import com.gtech.promotion.dao.model.activity.TPromoActivityProductDetailVO;
import com.gtech.promotion.dto.in.activity.SingleProductDTO;

/**
 * 商品工程类
 */
public class ProductFactory{

    /**
     * 初始化plp页面接口入参
     */
    public static SingleProductDTO initSkuCodeListDTO(){
        SingleProductDTO skuCodeListDTO = new SingleProductDTO();
        skuCodeListDTO.setTenantCode("111");
        skuCodeListDTO.setCategoryCodes(Arrays.asList("cc"));
        skuCodeListDTO.setBrandCode("bbb");
        skuCodeListDTO.setProductCode("ppp");
        skuCodeListDTO.setSkuCode("kk");
        skuCodeListDTO.setPrice(new BigDecimal("2"));
        return skuCodeListDTO;
    }

    /**   
     * 初始化商品详细列表
     */
    public static String initProductDetail(){
        ArrayList<TPromoActivityProductDetailVO> arrayList = new ArrayList<>();
        TPromoActivityProductDetailVO productDetailVO = new TPromoActivityProductDetailVO();
        productDetailVO.setActivityCode("1");
        productDetailVO.setSeqNum(1);
        productDetailVO.setProductCode("sdfkj");
        arrayList.add(productDetailVO);
        String jsonString = JSON.toJSONString(arrayList);
        return jsonString;
    }
    
}
