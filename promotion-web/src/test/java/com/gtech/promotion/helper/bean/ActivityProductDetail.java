/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WA<PERSON>RANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.helper.bean;


import lombok.Builder;
import lombok.Data;

/**
 * <功能描述>
 */
@Data
@Builder
public class ActivityProductDetail {

    private String tenantCode;

    @Builder.Default
    private Integer seqNum = 1;

    private String productCode;

    private String spuName;

    private String skuCode;

    private String skuName;

    private String orgCode;

    private String orgName;

    public static void main(String[] args) {
        System.out.println(ActivityProductDetail.builder().build());
    }

}
