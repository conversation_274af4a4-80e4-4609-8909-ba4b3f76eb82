package com.gtech.promotion.helper;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

@RunWith(MockitoJUnitRunner.class)
public class TryLockTest {
    @InjectMocks
    private RedisLock  redisLock ;
    @Mock
    private StringRedisTemplate stringRedisTemplate;
    @Mock
    private ValueOperations valueoperations;
    
    @Test
    public void tryLock(){
    	Mockito.when(stringRedisTemplate.opsForValue()).thenReturn(valueoperations);
       redisLock.tryLock( "1", 1L);
    }
    @Test
    public void tryLockAndRetry(){
    	Mockito.when(stringRedisTemplate.opsForValue()).thenReturn(valueoperations);
    	redisLock.tryLockAndRetry( "1", 1L,1);
    }
    @Test
    public void unlock(){
    	redisLock.unlock( "1",  "1");
    }
}
