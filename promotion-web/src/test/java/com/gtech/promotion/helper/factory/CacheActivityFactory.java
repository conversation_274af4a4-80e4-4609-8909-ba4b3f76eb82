/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.helper.factory;

import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.promotion.calc.TemplateEnum;
import com.gtech.promotion.code.PromotionConstants;
import com.gtech.promotion.code.activity.ActivityTypeEnum;
import com.gtech.promotion.code.activity.ProductSelectionEnum;
import com.gtech.promotion.dao.model.activity.*;
import com.gtech.promotion.dto.cache.ActivityCacheDTO;
import com.gtech.promotion.dto.cache.ProductSkuDetailDTO;
import com.gtech.promotion.helper.ActivityHelper;
import com.gtech.promotion.helper.bean.ActivityProductDetail;
import com.gtech.promotion.helper.bean.Template;
import com.gtech.promotion.vo.bean.ProductScope;
import com.gtech.promotion.vo.bean.Qualification;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.util.*;

/**
 * <功能描述>
 * 
 */
public class CacheActivityFactory{

    /**
     * 创建活动
     * 
     * @param activityType 活动类型
     * @param template 模板信息
     * @param selectType 正反选
     * @param activityProducts 商品范围
     * @param activityProductDetails 指定sku的范围
     * @param qualifications 资格
     * @return 活动
     */
    public static ActivityCacheDTO createActivity(
                    ActivityTypeEnum activityType,
                    Template template,
                    ProductSelectionEnum selectType,
                    List<ProductScope> activityProducts,
                    List<ActivityProductDetail> activityProductDetails,
                    List<Qualification> qualifications){
        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();

        // 创建活动
        ActivityModel activityModel = createActivityModel(activityType.code(), selectType.code(), template.getTemplateEnum().code());
        activityCacheDTO.setActivityModel(activityModel);

        // 创建活动模板
        TemplateModel tPromoTemplateVO = createTemplate(template.getTemplateEnum());
        activityCacheDTO.setPromoTemplate(tPromoTemplateVO);

        // 创建商品
        activityCacheDTO.setPromoProducts(createActivityProduct(activityProducts));
        activityCacheDTO.setSeqNum("1");

        // 指定商品
//        activityCacheDTO.setPromoProductDetails(createSpuAndSku(activityProductDetails));

        // 资格
        activityCacheDTO.setQualificationModels(QualificationModel.convertToModel(qualifications));

        // 创建模板参数
        activityCacheDTO.setPromoFuncParams(createFunctionParams(template));

        // 创建用于购物车计算的函数层级参数
        activityCacheDTO.setPromoTemplateFunctions(createFunctions(tPromoTemplateVO, activityCacheDTO.getPromoFuncParams()));

        // 创建模板参数级别
        List<ActivityFunctionParamRankModel> promoFuncRanks = creataFunctionRanks(activityModel, tPromoTemplateVO);
        activityCacheDTO.setPromoFuncRanks(promoFuncRanks);
        return activityCacheDTO;
    }

    public static ActivityCacheDTO createCouponActivity(ActivityTypeEnum activityType,Template template,ProductSelectionEnum selectType){
        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();
        // 创建活动
        ActivityModel activityModel = createCouponActivityVO111(activityType.code(), selectType.code(), template.getTemplateEnum().code());
        activityCacheDTO.setActivityModel(activityModel);
        // 创建活动模板
        TemplateModel tPromoTemplateVO = createTemplate(template.getTemplateEnum());
        activityCacheDTO.setPromoTemplate(tPromoTemplateVO);
        // 创建模板参数
        activityCacheDTO.setPromoFuncParams(createFunctionParams(template));

        // 创建用于购物车计算的函数层级参数
        activityCacheDTO.setPromoTemplateFunctions(createFunctions(tPromoTemplateVO, activityCacheDTO.getPromoFuncParams()));

        // 创建模板参数级别
        List<ActivityFunctionParamRankModel> promoFuncRanks = creataFunctionRanks(activityModel, tPromoTemplateVO);
        activityCacheDTO.setPromoFuncRanks(promoFuncRanks);
        return activityCacheDTO;
    }

    /**
     * 创建用于购物车计算的函数层级参数
     * 
     * @param templateModel
     * @param funcParams
     */
    public static List<TemplateFunctionModel> createFunctions(TemplateModel templateModel, List<FunctionParamModel> funcParams){
        List<TemplateFunctionModel> promoTemplateFunctions = new ArrayList<>();
        funcParams.forEach(x -> {
            TemplateFunctionModel funcModel = new TemplateFunctionModel();
            funcModel.setFunctionType(x.getFunctionType());
            funcModel.setFunctionCode(x.getFunctionCode());
            promoTemplateFunctions.add(funcModel);
        });
        return promoTemplateFunctions;
    }

    /**
     * 创建函数层级
     */
    public static List<ActivityFunctionParamRankModel> creataFunctionRanks(ActivityModel activityModel, TemplateModel templateModel){

        return Arrays.asList(
            ActivityFunctionParamRankModel.builder()
                .id("1")
                .activityCode(activityModel.getActivityCode())
                .rankParam(1)
                .templateCode(templateModel.getTemplateCode())
                .tenantCode(activityModel.getTenantCode())
                .build());
    }

    /**
     * 创建活动模板
     * 
     * @param templateEnum
     * @return
     */
    public static TemplateModel createTemplate(TemplateEnum templateEnum){
        TemplateModel templateModel = new TemplateModel();
        templateModel.setTemplateCode(templateEnum.code());
        templateModel.setTagCode(templateEnum.tagCode());
        return templateModel;
    }

    public static List<TPromoActivityProductVO> createActivityProduct(List<ProductScope> activityProducts){
        List<TPromoActivityProductVO> promoProducts = null;
        if (!CollectionUtils.isEmpty(activityProducts)){
            promoProducts = new ArrayList<>();
            for (ProductScope activityProduct : activityProducts){
                TPromoActivityProductVO promoProduct = new TPromoActivityProductVO();

                promoProduct.setSeqNum(activityProduct.getSeqNum());
                promoProduct.setCategoryCode(activityProduct.getCategoryCode());
                promoProduct.setCategoryName(activityProduct.getCategoryName());
                promoProduct.setBrandCode(activityProduct.getBrandCode());
                promoProduct.setBrandName(activityProduct.getBrandName());
                promoProduct.setAttributes(activityProduct.getAttributes());

                promoProducts.add(promoProduct);
            }
        }
        return promoProducts;
    }

    /**
     * 创建活动指定商品明细记录
     * 
     * @param activityProductDetails
     * @return
     */
    public static Map<String, List<ProductSkuDetailDTO>> createSpuAndSku(List<ActivityProductDetail> activityProductDetails){
        Map<String, List<ProductSkuDetailDTO>> promoProductDetails = null;
        if (!CollectionUtils.isEmpty(activityProductDetails)){
            promoProductDetails = new HashMap<>();
            List<ProductSkuDetailDTO> detailDTOS = new ArrayList<>();
            for (ActivityProductDetail detail : activityProductDetails){
                ProductSkuDetailDTO detailDTO = new ProductSkuDetailDTO();
                BeanUtils.copyProperties(detail, detailDTO);
                detailDTO.setPromoPrice(new BigDecimal(1));
                detailDTOS.add(detailDTO);
                promoProductDetails.put(detailDTO.getProductCode(), detailDTOS);
            }
        }
        return promoProductDetails;
    }

    /**
     * 创建活动
     */
    private static ActivityModel createActivityModel(String activityType,String selectType,String templateCode){

        ActivityModel tPromoActivityVO = new ActivityModel();
        tPromoActivityVO.setActivityType(activityType);
        tPromoActivityVO.setActivityCode(String.valueOf(System.nanoTime() % 10000000000l));
        tPromoActivityVO.setTemplateCode(templateCode);
        tPromoActivityVO.setPriority(PromotionConstants.DEF_PRIORITY);
        tPromoActivityVO.setProductSelectionType(selectType);
        tPromoActivityVO.setActivityBegin(DateUtil.format(DateUtil.addDay(new Date(), -1), DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        tPromoActivityVO.setActivityEnd(DateUtil.format(DateUtil.addDay(new Date(), 1), DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        return tPromoActivityVO;
    }

    /**
     * 创建活动
     */
    private static ActivityModel createCouponActivityVO111(String activityType,String selectType,String templateCode){
        ActivityModel tPromoActivityVO = new ActivityModel();
        tPromoActivityVO.setActivityType(activityType);
        tPromoActivityVO.setActivityCode("1");
        tPromoActivityVO.setTemplateCode(templateCode);
        tPromoActivityVO.setProductSelectionType(selectType);
        tPromoActivityVO.setTenantCode("111");
        tPromoActivityVO.setActivityBegin(DateUtil.format(DateUtil.addDay(new Date(), -1), DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        tPromoActivityVO.setActivityEnd(DateUtil.format(DateUtil.addDay(new Date(), 1), DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        return tPromoActivityVO;
    }

    /**
     * 创建函数层级参数
     */
    private static List<FunctionParamModel> createFunctionParams(Template template){

        String functionCode1 = template.getTemplateEnum().code().substring(0, 4);
        String functionCode2 = template.getTemplateEnum().code().substring(4, 8);
        String functionCode3 = template.getTemplateEnum().code().substring(8, 12);
        String functionCode4 = template.getTemplateEnum().code().substring(12);

        List<FunctionParamModel> params = BeanCopyUtils.jsonCopyList(Arrays.asList(
            ActivityHelper.buildFunctionParam(functionCode1, "", 1),
            ActivityHelper.buildFunctionParam(functionCode2, "", 1),
            ActivityHelper.buildFunctionParam(functionCode3, template.getCondition(), 1),
            ActivityHelper.buildFunctionParam(functionCode4, template.getReward(), 1)), FunctionParamModel.class);
        
        for(FunctionParamModel param : params) {
            param.setRankId("1");
        }
        
        return params;
    }
}
