/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.helper.factory;

import com.github.pagehelper.PageInfo;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.promotion.calc.function.FunctionEnum;
import com.gtech.promotion.code.activity.*;
import com.gtech.promotion.code.coupon.CouponTypeEnum;
import com.gtech.promotion.dao.model.activity.ActivityModel;
import com.gtech.promotion.dao.model.activity.TPromoActivityProductVO;
import com.gtech.promotion.dao.model.coupon.CouponReleaseModel;
import com.gtech.promotion.dao.model.coupon.TPromoCouponCodeUserVO;
import com.gtech.promotion.dao.model.coupon.TPromoCouponInnerCodeVO;
import com.gtech.promotion.domain.activity.ActivityDomain;
import com.gtech.promotion.dto.in.activity.ActivityUpdateDTO;
import com.gtech.promotion.dto.in.activity.FrozenCouponCodeInDTO;
import com.gtech.promotion.dto.in.activity.TenantCodeActivityIdDTO;
import com.gtech.promotion.dto.in.coupon.CreateCouponActivityDTO;
import com.gtech.promotion.dto.in.coupon.StoreCouponActivityInDTO;
import com.gtech.promotion.dto.in.coupon.TCouponListQueryDTO;
import com.gtech.promotion.dto.in.coupon.UpdateCouponActivityInDTO;
import com.gtech.promotion.dto.out.coupon.CouponInfoImportOutDTO;
import com.gtech.promotion.dto.out.product.ProductDetailInDTO;
import com.gtech.promotion.helper.ActivityHelper;
import com.gtech.promotion.helper.bean.Template;
import com.gtech.promotion.vo.bean.*;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;

/**
 * 活动工厂类
 */
public class ActivityFactory{

    /**
     * 更新活动入参对象
     * 
     * @param createDTO 创建活动入参对象
     * @param template 函数模板
     * @param qualifications 资格列表
     * @param incentiveLimitedFlag 是否有限制
     * @param storeType 是否指定店铺
     * @param productSelectType 正反选
     * @param conditionProductType 商品类型
     */
    public static ActivityUpdateDTO createActivityUpdateDTO(
                    ActivityDomain createDTO,
                    Template template,
                    List<Qualification> qualifications,
                    IncentiveLimitedFlagEnum incentiveLimitedFlag,
                    StoreParamTypeEnum storeType,
                    ProductSelectionEnum productSelectType,
                    ProductTypeEnum conditionProductType){
        ActivityDomain dto = createActivityCreateDTO(createDTO, template, qualifications, incentiveLimitedFlag, storeType, productSelectType, conditionProductType);
        ActivityUpdateDTO updateDTO = new ActivityUpdateDTO();
        BeanUtils.copyProperties(dto, updateDTO);
        return updateDTO;

    }

    /**
     * 创建活动入参对象
     * 
     * @param createDTO 创建活动入参对象
     * @param template 函数模板
     * @param qualifications 资格列表
     * @param incentiveLimitedFlag 是否有限制
     * @param storeType 是否指定店铺
     * @param productSelectType 正反选
     * @param conditionProductType 商品类型
     */
    public static ActivityDomain createActivityCreateDTO(
                    ActivityDomain createDTO,
                    Template template,
                    List<Qualification> qualifications,
                    IncentiveLimitedFlagEnum incentiveLimitedFlag,
                    StoreParamTypeEnum storeType,
                    ProductSelectionEnum productSelectType,
                    ProductTypeEnum conditionProductType){

        ActivityDomain activitCreateDTO = initPromoActivityDTO(incentiveLimitedFlag, storeType, productSelectType, conditionProductType);
        activitCreateDTO.setTemplateCode(template.getTemplateEnum().code());
        List<FunctionParam> createFunctionParams = BeanCopyUtils.jsonCopyList(createFunctionParams(template), FunctionParam.class);
        activitCreateDTO.setFuncParams(createFunctionParams);
        if (IncentiveLimitedFlagEnum.YES.equals(incentiveLimitedFlag)){
            activitCreateDTO.setIncentiveLimiteds(createDTO.getIncentiveLimiteds());
        }
        if (StoreParamTypeEnum.STORE_CUSTOM.equals(storeType)){
            activitCreateDTO.setChannelStores(createDTO.getChannelStores());
        }
        if (null != createDTO){
            activitCreateDTO.setSkuToken(createDTO.getSkuToken());
            activitCreateDTO.setProductDetails(createDTO.getProductDetails());
        }
        if (ProductTypeEnum.CUSTOM_RANGE.equals(conditionProductType) || ProductTypeEnum.CUSTOM_SEQ.equals(conditionProductType)){
            activitCreateDTO.setProducts(createDTO.getProducts());
        }
        if (!CollectionUtils.isEmpty(qualifications)){
            activitCreateDTO.setQualifications(qualifications);
        }
        if (template.getTemplateEnum().code().endsWith(FunctionEnum.F0406.code())){
            activitCreateDTO.setGiveaways(createDTO.getGiveaways());
        }

        return activitCreateDTO;
    }

    private static ActivityDomain initPromoActivityDTO(IncentiveLimitedFlagEnum incentiveLimitedFlag, StoreParamTypeEnum storeType, ProductSelectionEnum productSelectType,
                    ProductTypeEnum conditionProductType) {

        List<ActivityLanguage> createActivityLanguages = BeanCopyUtils.jsonCopyList(ActivityHelper.buildActivityLanguages(1, ""), ActivityLanguage.class);

        ActivityDomain activityDomain = new ActivityDomain();

        activityDomain.setTenantCode("111");
        activityDomain.setActivityLanguages(createActivityLanguages);
        activityDomain.setActivityType(ActivityTypeEnum.ACTIVITY.code());
        activityDomain.setStoreType(storeType.code());
        activityDomain.setProductSelectionType(productSelectType.code());
        activityDomain.setIncentiveLimitedFlag(incentiveLimitedFlag.code());
        activityDomain.setActivityBegin(DateUtil.format(new Date(), DateUtil.FORMAT_YYYYMMDDHHMISS_14));
        activityDomain.setActivityEnd(DateUtil.format(DateUtil.addDay(new Date(), 1), DateUtil.FORMAT_YYYYMMDDHHMISS_14));

        return activityDomain;
    }

    /**
     * 复制活动入参对象
     */
    public static TenantCodeActivityIdDTO createCopyActivityDTO(){
        TenantCodeActivityIdDTO paramDTO = new TenantCodeActivityIdDTO();
        paramDTO.setActivityCode("1");
        paramDTO.setTenantCode("222");
        return paramDTO;
    }

    /**
     * 创建函数层级参数DTO
     * 
     * @param template 函数模板
     * @return List<FunctionParam> 函数列表
     */
    public static List<FunctionParam> createFunctionParams(Template template){

        String functionCode1 = template.getTemplateEnum().code().substring(0, 4);
        String functionCode2 = template.getTemplateEnum().code().substring(4, 8);
        String functionCode3 = template.getTemplateEnum().code().substring(8, 12);
        String functionCode4 = template.getTemplateEnum().code().substring(12);

        return Arrays.asList(
            ActivityHelper.buildFunctionParam(functionCode1, "", 1), 
            ActivityHelper.buildFunctionParam(functionCode2, "", 1),
            ActivityHelper.buildFunctionParam(functionCode3, template.getCondition(), 1),
            ActivityHelper.buildFunctionParam(functionCode4, template.getReward(), 1));
    }

    /**
     * 券活动创建
     */
    public static ActivityModel createCouponActivityVO111(String couponType, String activityCode) {

        ActivityModel couponActivityVO = new ActivityModel();
        couponActivityVO.setActivityCode(activityCode);
        couponActivityVO.setCouponType(couponType);
        return couponActivityVO;
    }

    /**
     * 券投放列表创建
     */
    public static List<CouponReleaseModel> createCouponReleasesVOs(){
        ArrayList<CouponReleaseModel> list = new ArrayList<>();
        CouponReleaseModel couponReleaseVO = new CouponReleaseModel();
        list.add(couponReleaseVO);
        return list;
    }

    /**
     * 券码创建
     */
    public static TPromoCouponInnerCodeVO createCouponInnerCodeVO(){
        TPromoCouponInnerCodeVO couponInnerCodeVO = new TPromoCouponInnerCodeVO();
        return couponInnerCodeVO;
    }

    /**
     * 活动限制创建
     * 
     * @param limitationCode 限制类型
     * @param num 数量
     * @return List<IncentiveLimited> 限制列表
     */
    public static List<IncentiveLimited> createLimiteds(String limitationCode,int num){
        List<IncentiveLimited> list = new ArrayList<IncentiveLimited>();
        for (int i = 0; i < num; i++){
            IncentiveLimited limitedDTO = new IncentiveLimited();
            limitedDTO.setLimitationCode(limitationCode);
            limitedDTO.setLimitationValue(new BigDecimal(i + ""));
            list.add(limitedDTO);
        }
        return list;
    }

    /**
     * 渠道店铺创建
     * 
     * @param num 数量
     * @return List<ActivityStore> 店铺渠道列表
     */
    public static List<ActivityStore> createChannelStores(int num){
        List<ActivityStore> list = new ArrayList<ActivityStore>();
        for (int i = 0; i < num; i++){
            ActivityStore storeDTO = new ActivityStore();
            storeDTO.setChannelCode(String.valueOf(i));
            storeDTO.setChannelName("ChannelName" + i);
            storeDTO.setOrgCode(String.valueOf(i));
            storeDTO.setStoreName("StoreName" + i);
            storeDTO.setUrl("url" + i);
            list.add(storeDTO);
        }
        return list;
    }

    /**
     * 会员列表创建
     * 
     * @param num 数量
     * @return List<Qualification> 资格列表
     */
    public static List<Qualification> createQualifications(int num){
        List<Qualification> list = new ArrayList<>();
        for (int i = 0; i < num; i++){
            Qualification memberDTO = new Qualification();
            memberDTO.setQualificationCode(QualificationCodeEnum.MEMBER_TAG.code());
            memberDTO.setQualificationValue(new ArrayList<>(Collections.singleton(QualificationCodeEnum.MEMBER_TAG.code() + i)));
            list.add(memberDTO);
        }
        return list;
    }

    /**
     * 商品创建
     * 
     * @param num 数量
     * @return List<ProductDTO>
     */
    public static List<ProductScope> createProducts(int num){
        List<ProductScope> list = new ArrayList<>();
        for (int i = 0; i < num; i++){
            ProductScope productDTO = new ProductScope();
            productDTO.setCategoryCode(i + "");
            productDTO.setCategoryName("name" + i);
            productDTO.setBrandCode(i + "");
            productDTO.setAttributes(Arrays.asList(ProductAttribute.builder().attributeCode("attributeCode" + i).attributeValues("attributeValue" + i).build()));
            productDTO.setSeqNum(i);
            list.add(productDTO);
        }
        return list;
    }

    /**
     * 商品创建
     * 
     * @param num 数量
     * @return List<ProductDTO>
     */
    public static List<TPromoActivityProductVO> createTPromoProducts(int num){
        List<TPromoActivityProductVO> list = new ArrayList<>();
        for (int i = 0; i < num; i++){
            TPromoActivityProductVO productDTO = new TPromoActivityProductVO();
            productDTO.setCategoryCode(i + "");
            productDTO.setCategoryName("name" + i);
            productDTO.setBrandCode(i + "");
            productDTO.addAttribute(ProductAttribute.builder().attributeCode("attributeCode1" + i).attributeValues("attributeValue1" + i).build());
            productDTO.setSeqNum(i);
            list.add(productDTO);
        }
        return list;
    }

    /**
     * 商品详情创建
     * 
     * @param num 数量
     * @return List<ProductDetail>
     */
    public static List<ProductDetail> createProductDetailsFile(int num){
        List<ProductDetail> list = new ArrayList<>();
        for (int i = 0; i < num; i++){
            ProductDetail productDTO = new ProductDetail();
            productDTO.setProductCode("22");
            productDTO.setSeqNum(i);
            list.add(productDTO);
        }
        return list;
    }

    /**
     * 商品详情创建
     * 
     * @param num 数量
     * @return List<ProductDetail>
     */
    public static List<ProductDetailInDTO> createProductDetails(int num){
        List<ProductDetailInDTO> list = new ArrayList<>();
        for (int i = 0; i < num; i++){
            ProductDetailInDTO productDTO = new ProductDetailInDTO();
            productDTO.setProductCode("22");
            productDTO.setSeqNum(i);
            list.add(productDTO);
        }
        return list;
    }

    /**
     * 赠品创建
     * 
     * @param num 数量
     * @return List<ActivityGift> 赠品列表
     */
    public static List<Giveaway> createGiveaways(int num){
        List<Giveaway> list = new ArrayList<Giveaway>();
        for (int i = 0; i < num; i++){
            Giveaway giftDTO = new Giveaway();
            giftDTO.setGiveawayCode(i + "");
            giftDTO.setGiveawayName("name" + i);
            giftDTO.setGiveawayNum(100);
            giftDTO.setGiveawayType(1);
            giftDTO.setRankParam(i+1);
            list.add(giftDTO);
        }
        return list;
    }

    /**
     * 活动对象
     * 
     * @param template
     * @param incentiveLimitedFlag
     * @param storeType
     * @param activityType
     */
    public static ActivityModel createActivityVO(Template template,IncentiveLimitedFlagEnum incentiveLimitedFlag,StoreParamTypeEnum storeType,ActivityTypeEnum activityType){
        ActivityModel activityVO = new ActivityModel();
        activityVO.setActivityCode("1");
        activityVO.setActivityCode("121232");
        activityVO.setTenantCode("222");
        activityVO.setTemplateCode(template.getTemplateEnum().code());
        activityVO.setActivityName("activityName");
        activityVO.setActivityBegin("20180713155400");
        activityVO.setActivityEnd("20180713155402");
        activityVO.setActivityStatus(ActivityStatusEnum.PENDING.code());
        activityVO.setActivityType(activityType.code());
        activityVO.setIncentiveLimitedFlag(incentiveLimitedFlag.code());
        activityVO.setStoreType(storeType.code());

        return activityVO;
    }

    public static ActivityModel createCouponActivityVO(CreateCouponActivityDTO createCouponActivityDTO) {

        ActivityModel couponActivityVO = new ActivityModel();
        couponActivityVO.setTenantCode(createCouponActivityDTO.getTenantCode());
        couponActivityVO.setActivityCode(createCouponActivityDTO.getActivityCode());
        couponActivityVO.setTotalQuantity(createCouponActivityDTO.getTotalQuantity());
        couponActivityVO.setCouponType(createCouponActivityDTO.getCouponType());
        couponActivityVO.setUserLimitMax(createCouponActivityDTO.getUserLimitMax());
        return couponActivityVO;
    }

    public static UpdateCouponActivityInDTO updateCouponActivityDTO(ActivityUpdateDTO updateDTO,String couponType,String promotionCode,Integer userLimitMax,Integer totalQuantity){
        UpdateCouponActivityInDTO updateCouponActivityDTO = new UpdateCouponActivityInDTO();
        BeanUtils.copyProperties(updateDTO, updateCouponActivityDTO);
        updateCouponActivityDTO.setActivityType(ActivityTypeEnum.COUPON.code());
        updateCouponActivityDTO.setCouponType(couponType);
        if (CouponTypeEnum.PROMOTION_CODE.code().equals(couponType)){
            updateCouponActivityDTO.setPromotionCode(promotionCode);
        }
        updateCouponActivityDTO.setUserLimitMax(userLimitMax);
        updateCouponActivityDTO.setTotalQuantity(totalQuantity);
        return updateCouponActivityDTO;
    }

    public static ActivityModel updateCouponActivityVO(UpdateCouponActivityInDTO updateCouponActivityInDTO) {

        ActivityModel couponActivityVO = new ActivityModel();
        couponActivityVO.setTenantCode(updateCouponActivityInDTO.getTenantCode());
        couponActivityVO.setActivityCode(updateCouponActivityInDTO.getActivityCode());
        couponActivityVO.setTotalQuantity(updateCouponActivityInDTO.getTotalQuantity());
        couponActivityVO.setCouponType(updateCouponActivityInDTO.getCouponType());
        couponActivityVO.setUserLimitMax(updateCouponActivityInDTO.getUserLimitMax());
        return couponActivityVO;
    }

    public static StoreCouponActivityInDTO queryCouponActivityInStore(){
        StoreCouponActivityInDTO dto = new StoreCouponActivityInDTO();
        dto.setTenantCode("111");
        dto.setChannelCode("");
        dto.setQualifications(new HashMap<>());
        dto.setOrgCode("");
        dto.setUserCode("55555");
        return dto;
    }
    
    public static StoreCouponActivityInDTO queryCouponActivityInStoreHaveCode(){
        StoreCouponActivityInDTO dto = new StoreCouponActivityInDTO();
        dto.setTenantCode("111");
        dto.setChannelCode("");
        dto.setQualifications(new HashMap<>());
        dto.setOrgCode("12");
        dto.setUserCode("55555");
        return dto;
    }

    public static FrozenCouponCodeInDTO frozenCouponCode(){
        FrozenCouponCodeInDTO dto = new FrozenCouponCodeInDTO();
        dto.setTenantCode("7777777");
        dto.setCouponCode("1231");
        return dto;
    }

    //导出券
    public static TCouponListQueryDTO exportFile(){
        TCouponListQueryDTO dto = new TCouponListQueryDTO();
        dto.setTenantCode("7777777");
        dto.setOrgCode("1");
        dto.setCouponStatus("");
        dto.setCreateTimeStart("20180909121212");
        dto.setCreateTimeEnd("20190909121212");
        dto.setPageNo(1);
        dto.setPageCount(10);
        return dto;
    }

    public static List<CouponInfoImportOutDTO> couponInfoImportOutDTOs(){
        List<CouponInfoImportOutDTO> couponInfoImportOutDTOs = new ArrayList<>();
        CouponInfoImportOutDTO dto = new CouponInfoImportOutDTO();
        dto.setCouponCode("1232");
        dto.setCouponDesc("测试描述");
        dto.setCouponName("券名称");
        dto.setCouponStatus("01");
        dto.setCreateTime("134");
        dto.setReceiveEndTime("341");
        dto.setReceiveStartTime("234123");
        dto.setReceiveTime("2143");
        dto.setReleaseCode("1");
        dto.setTakeLabel("01");
        dto.setUsedRefId("DEFAULT");
        dto.setUsedTime("");
        dto.setUserCode("24");
        dto.setValidBegin("2341243");
        dto.setValidEnd("1241243132143");
        couponInfoImportOutDTOs.add(dto);
        return couponInfoImportOutDTOs;
    }

    public static PageInfo<TPromoCouponInnerCodeVO> couponInnerCode(){

        List<TPromoCouponInnerCodeVO> list = new ArrayList<>();
        TPromoCouponInnerCodeVO dto = new TPromoCouponInnerCodeVO();
        dto.setActivityCode("1");
        dto.setCouponCode("112323");
        dto.setCouponType("03");
        dto.setCreateTime(null);
        dto.setCreateUser("34");
        dto.setFaceUnit("01");
        dto.setFaceValue(new BigDecimal("10"));
        dto.setFrozenStatus("01");
        dto.setId("1");
        dto.setLogicDelete("0");
        dto.setReceiveEndTime("132413");
        dto.setReceiveStartTime("3241243");
        dto.setReleaseCode("1");
        dto.setStatus("02");
        dto.setTakeLabel("01");
        dto.setTenantCode("7777777");
        list.add(dto);
        PageInfo<TPromoCouponInnerCodeVO> pageInfo = new PageInfo<>();
        pageInfo.setList(list);
        return pageInfo;
    }

    public static List<TPromoCouponCodeUserVO> couponCodeUserVOs(){

        List<TPromoCouponCodeUserVO> list = new ArrayList<>();
        TPromoCouponCodeUserVO dto = new TPromoCouponCodeUserVO();
        dto.setActivityCode("1");
        dto.setCouponCode("112323");
        dto.setCouponType("03");
        dto.setCreateTime(null);
        dto.setCreateUser("34");
        dto.setFaceUnit("01");
        dto.setFaceValue(new BigDecimal("10"));
        dto.setFrozenStatus("01");
        dto.setId("1");
        dto.setLogicDelete("0");
        dto.setReleaseCode("1");
        dto.setStatus("02");
        dto.setTakeLabel("01");
        dto.setTenantCode("7777777");
        list.add(dto);
        return list;
    }

    public static List<ProductDetail> createActivitySpuSkuDTO(){
        ProductDetail spuSkuDTO = new ProductDetail();
        ArrayList<ProductDetail> arrayList = new ArrayList<ProductDetail>();
        arrayList.add(spuSkuDTO);
        return arrayList;
    }

}
