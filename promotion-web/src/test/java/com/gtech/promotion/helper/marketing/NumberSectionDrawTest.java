package com.gtech.promotion.helper.marketing;

import org.junit.Assert;
import org.junit.Test;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class NumberSectionDrawTest {

    NumberSectionDraw draw = new NumberSectionDraw();

    @Test
    public void lottery_全部有商品_概率总和100(){
        List<Prize> prizes = new ArrayList<>();
        prizes.add(createPrize("20", 2));
        prizes.add(createPrize("80", 3));
        Prize lottery = draw.lottery(prizes);
        System.out.println(lottery);
        Assert.assertNotNull(lottery);
    }

    @Test
    public void lottery_仅有1件商品_概率总和100(){
        List<Prize> prizes = new ArrayList<>();
        prizes.add(createPrize("100", 2));
        Prize lottery = draw.lottery(prizes);
        System.out.println(lottery);
        Assert.assertNotNull(lottery);
    }

    @Test
    public void lottery_2件商品_0_100_概率总和100(){
        List<Prize> prizes = new ArrayList<>();
        prizes.add(createPrize("0", 2));
        prizes.add(createPrize("100", 2));
        Prize lottery = draw.lottery(prizes);
        System.out.println(lottery);
        Assert.assertNotNull(lottery);
    }

    @Test
    public void lottery_全部有商品_概率总和10(){
        List<Prize> prizes = new ArrayList<>();
        prizes.add(createPrize("0", 2));
        prizes.add(createPrize("10", 2));
        Prize lottery = draw.lottery(prizes);
        System.out.println(lottery);
    }

    @Test
    public void lottery_全部有商品_概率都是0(){
        List<Prize> prizes = new ArrayList<>();
        prizes.add(createPrize("0", 1));
        prizes.add(createPrize("0", 1));
        Prize lottery = draw.lottery(prizes);
        System.out.println(lottery);
        Assert.assertNull(lottery);
    }

    @Test
    public void lottery_有空獎品_概率总和100(){
        List<Prize> prizes = new ArrayList<>();
        prizes.add(createPrize("0", 1));
        prizes.add(createPrize("10", 2));
        prizes.add(createPrize("90", 4));
        Prize lottery = draw.lottery(prizes);
        System.out.println(lottery);
        Assert.assertNotNull(lottery);
    }

    @Test
    public void lottery_有獎品_库存0(){
        List<Prize> prizes = new ArrayList<>();
        prizes.add(createPrize("0", 0));
        prizes.add(createPrize("10", 0));
        prizes.add(createPrize("80", 0));
        Prize lottery = draw.lottery(prizes);
        System.out.println(lottery);
        Assert.assertEquals(AbstractDraw.EMPTY_PRIZE, lottery);
    }

    private Prize createPrize(String prizeProbability, int inventory) {
        Prize prize = new Prize();
        prize.setPrizeProbability(new BigDecimal(prizeProbability));
        prize.setPrizeInventory(inventory);
        return prize;
    }

}
