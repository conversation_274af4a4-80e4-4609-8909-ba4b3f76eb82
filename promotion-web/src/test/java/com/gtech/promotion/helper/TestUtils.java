/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.helper;

import java.beans.PropertyDescriptor;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.net.URL;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Date;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.jar.JarEntry;
import java.util.jar.JarFile;

import org.springframework.util.ResourceUtils;

import com.gtech.commons.code.IEnum;
import com.gtech.commons.exception.ErrorCode;
import com.gtech.commons.exception.ErrorCodes;
import com.gtech.commons.exception.GTechBaseException;
import com.gtech.promotion.checker.SystemChecker;
import com.gtech.promotion.exception.PromotionException;

/**
 * 
 *
 */
public class TestUtils {

    private TestUtils() {

    }

    /**
     * 加载类
     */
    public static Class<?> loadClass(String className){
        try{
            return Thread.currentThread().getContextClassLoader().loadClass(className);
        }catch (ClassNotFoundException e){
            throw new PromotionException(SystemChecker.CAN_NOT_INSTANCE_ERROR);
        }
    }

    public static void testEntity(String packageName) {

        List<String> classNames = getClassName(packageName, true);
        for (String className : classNames) {

            Object obj = null;

            try {
                Class<?> clazz = loadClass(className);// 这里的类名是全名, 有包的话要加上包名
                obj = classForObject(clazz, className);

                // Test for getter/setter
                testFields(obj, clazz);

                // Test for builder/build
                testBuilder(clazz, obj);

                // Test for toString
                obj.toString();

                if (obj instanceof GTechBaseException) {

                    String code = "code";
                    String message = "message";
                    String[] args = {"args"};

                    clazz.getConstructor().newInstance();

                    clazz.getConstructor(Throwable.class).newInstance(new Exception());

                    clazz.getConstructor(String.class, Throwable.class).newInstance(message, new Exception());
                    clazz.getConstructor(String.class, Throwable.class, Object[].class).newInstance(message, new Exception(), args);

                    clazz.getConstructor(String.class, String.class).newInstance(code, message);
                    clazz.getConstructor(String.class, String.class, Object[].class).newInstance(code, message, args);

                    clazz.getConstructor(String.class, String.class, Throwable.class).newInstance(code, message, new Exception());
                    clazz.getConstructor(String.class, String.class, Throwable.class, Object[].class).newInstance(code, message, new Exception(), args);

                    clazz.getConstructor(ErrorCode.class).newInstance(ErrorCodes.APPLICATION_NOT_AVAILABLE);
                    clazz.getConstructor(ErrorCode.class, Object[].class).newInstance(ErrorCodes.APPLICATION_NOT_AVAILABLE, args);

                    clazz.getConstructor(ErrorCode.class, Throwable.class).newInstance(ErrorCodes.APPLICATION_NOT_AVAILABLE, new Exception());
                    clazz.getConstructor(ErrorCode.class, Throwable.class, Object[].class).newInstance(ErrorCodes.APPLICATION_NOT_AVAILABLE, new Exception(), args);
                }
            } catch (Exception e) { }
        }
    }
    
    private static void testFields(Object obj, Class<?> clazz) throws ClassNotFoundException {

        boolean isSerializable = false;

        Field[] fields = clazz.getDeclaredFields();

        System.err.println("====class name:" + clazz.getName() + "===="); // NOSONAR

        // 写数据
        for (Field f : fields) {
            if (IEnum.class.isAssignableFrom(f.getType())) {
                String code = IEnum.class.cast(f.getType().getEnumConstants()[0]).code();
                IEnum.class.cast(f.getType().getEnumConstants()[0]).desc();
                IEnum.class.cast(f.getType().getEnumConstants()[0]).equalsCode(code);
            }

            else if ("serialVersionUID".equals(f.getName())) {
                isSerializable = true;
            }

            else {
                testField(clazz, obj, f);
            }
        }

        if (isSerializable) {
            byte[] data = writeByteArrayStream(obj);
            readByteArrayStream(data);
        }
    }

    /**
     * testBuilder
     * 
     * <AUTHOR>
     * @Date 2019-12-16
     */
    private static void testBuilder(Class<?> clazz, Object obj) {

        try {
            Method builderMethod = clazz.getMethod("builder");
            if (builderMethod != null) {
                Object builderBean = builderMethod.invoke(obj);
                if (builderBean != null) {
                    // Test for getter/setter
                    testFields(builderBean, builderBean.getClass());
                    // Test for toString
                    builderBean.toString();

                    Method buildMethod = builderBean.getClass().getMethod("build");
                    buildMethod.invoke(builderBean);
                }
            }
        } catch (Exception e) {} // NOSONAR
    }

    private static Object classForObject(Class<?> clazz, String className) throws ClassNotFoundException {

        try {
            return clazz.newInstance();
        } catch (Exception e) {
            return Class.forName(className, true, TestUtils.class.getClassLoader());
        }
    }

    /**
     * outputStream
     */
    private static byte[] writeByteArrayStream(Object obj) {

        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
                        ObjectOutputStream objectOutputStream = new ObjectOutputStream(outputStream)) {

            objectOutputStream.writeObject(obj);
            return outputStream.toByteArray();

        } catch (Exception e) {
            return new byte[0];
        }
    }

    /**
     * inputStream
     */
    private static void readByteArrayStream(byte[] data) {

        try (ObjectInputStream inputStream = new ObjectInputStream(new ByteArrayInputStream(data));
                        ObjectInputStream objectInputStream = new SecureObjectInputStream(inputStream)) {

            objectInputStream.readObject();
        } catch (Exception e) {} // NOSONAR
    }

    /**
     * testField
     */
    private static void testField(Class<?> clazz, Object obj, Field f) {

        try {
            Class<?> propertyType = null;
            Method writeMethod = null;
            PropertyDescriptor pd = null;

            if (clazz.getName().endsWith("Builder")) {

                propertyType = f.getType();
                writeMethod = clazz.getMethod(f.getName(), f.getType());
            } else {
                pd = new PropertyDescriptor(f.getName(), clazz);
                propertyType = pd.getPropertyType();
                writeMethod = pd.getWriteMethod();
            }

            if (propertyType.isAssignableFrom(int.class) || propertyType.isAssignableFrom(Integer.class)
                            || propertyType.isAssignableFrom(long.class) || propertyType.isAssignableFrom(Long.class)
                            || propertyType.isAssignableFrom(double.class) || propertyType.isAssignableFrom(Double.class)
                            || propertyType.isAssignableFrom(BigDecimal.class)
                            || propertyType.isAssignableFrom(BigInteger.class)
                            || propertyType.isAssignableFrom(Number.class)
                            || propertyType.isAssignableFrom(char.class)) {

                writeMethod.invoke(obj, 1);

            } else if (propertyType.isAssignableFrom(byte.class) || propertyType.isAssignableFrom(Byte.class)) {

                writeMethod.invoke(obj, Byte.parseByte("c"));

            } else if (propertyType.isAssignableFrom(Date.class)) {

                writeMethod.invoke(obj, new Date());

            } else if (propertyType.isAssignableFrom(boolean.class) || propertyType.isAssignableFrom(Boolean.class)) {

                writeMethod.invoke(obj, true);

            } else if (propertyType.isAssignableFrom(String.class)) {

                writeMethod.invoke(obj, "string");

            } else if (propertyType.isAssignableFrom(List.class)) {

                writeMethod.invoke(obj, new ArrayList<>());

            } else if (propertyType.isAssignableFrom(Set.class)) {

                writeMethod.invoke(obj, new HashSet<>());

            } else if (propertyType.isAssignableFrom(Map.class) || propertyType.isAssignableFrom(HashMap.class)) {

                writeMethod.invoke(obj, new HashMap<>());

            } else {

                System.err.println("$$$testField$$$" + propertyType.getName()); // NOSONAR
                writeMethod.invoke(obj, (Object)null);

            }

            if (pd != null) {
                pd.getReadMethod().invoke(obj);
            }
        } catch (Exception e) {} // NOSONAR
    }

    private static List<String> getClassName(String packageName, boolean childPackage) {

        List<String> classNameList = new ArrayList<>();
        ClassLoader loader = TestUtils.class.getClassLoader();
        String packagePath = packageName.replace(".", "/");

        try {
            Enumeration<URL> urls = loader.getResources(packagePath);
            while (urls.hasMoreElements()) {

                URL url = urls.nextElement();
                String urlProtocol = url.getProtocol();

                if (ResourceUtils.URL_PROTOCOL_FILE.equals(urlProtocol)) {
                    pickupClassNameByFile(classNameList, urlDecode(url.getPath()), packageName, childPackage);

                } else if (ResourceUtils.URL_PROTOCOL_JAR.equals(urlProtocol)) {
                    String jarPath = urlDecode(url.getPath().substring(6, urlDecode(url.getPath()).indexOf('!')));
                    pickupClassNameByJar(classNameList, jarPath, packageName);
                }
            }
        } catch (IOException e) {} // NOSONAR

        return classNameList;
    }

    private static void pickupClassNameByFile(List<String> classNameList, String filePath, String packageName, boolean childPackage) {

        if (filePath.contains(":") && filePath.startsWith("/")) {
            // 兼容windows处理
            filePath = filePath.substring(1);
        }

        File file = new File(filePath);
        File[] childFiles = file.listFiles();
        for (File childFile : childFiles) {
            if (childFile.isDirectory()) {
                if (childPackage) {
                    pickupClassNameByFile(classNameList, childFile.getPath(), packageName, childPackage);
                }
            } else {

                pickupClassName(classNameList, childFile.getPath(), packageName);
            }
        }
    }

    private static void pickupClassNameByJar(List<String> classNameList, String jarPath, String packageName) {

        if (jarPath.contains(":") && jarPath.startsWith("/")) {
            // 兼容windows处理
            jarPath = jarPath.substring(1);
        }

        try (JarFile jarFile = new JarFile(jarPath)) {
            Enumeration<JarEntry> entrys = jarFile.entries();
            while (entrys.hasMoreElements()) {
                JarEntry jar = entrys.nextElement();

                pickupClassName(classNameList, jar.getName(), packageName);
            }
        } catch (IOException e) {} // NOSONAR
    }

    private static void pickupClassName(List<String> classNameList, String classFilePath, String packageName) {

        classFilePath = classFilePath.replaceAll("/", ".").replaceAll("\\\\", ".");
        if (classFilePath.contains(packageName) && classFilePath.endsWith(".class") && !classFilePath.endsWith("Builder.class")) {

            classNameList.add(classFilePath.substring(classFilePath.indexOf(packageName), classFilePath.length() - ".class".length()));
        }
    }

    private static String urlDecode(String s) throws UnsupportedEncodingException {

        return URLDecoder.decode(s.replaceAll("\\+", "{plus}"), "UTF-8").replaceAll("\\{plus\\}", "+");
    }
}