/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * GTECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.helper;

import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.ObjectStreamClass;
import java.io.StreamCorruptedException;
import java.lang.reflect.Field;

import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.parser.ParserConfig;
import com.alibaba.fastjson.util.TypeUtils;

/**
 * 
 *
 * <AUTHOR>
 * @Date 2020-05-21
 */
public class SecureObjectInputStream extends ObjectInputStream {

    static Field[] fields;

    static volatile boolean fields_error;

    static void ensureFields() {

        if (fields == null && !fields_error) {
            try {
                final Field[] declaredFields = ObjectInputStream.class.getDeclaredFields();
                String[] fieldnames = new String[] { "bin", "passHandle", "handles", "curContext" };
                Field[] array = new Field[fieldnames.length];
                for (int i = 0; i < fieldnames.length; i++) {
                    Field field = TypeUtils
                        .getField(ObjectInputStream.class, fieldnames[i], declaredFields);
                    field.setAccessible(true);
                    array[i] = field;
                }
                fields = array;
            } catch (Throwable error) {
                fields_error = true;
            }
        }
    }

    public SecureObjectInputStream(ObjectInputStream in) throws IOException {
        super(in);
        try {
            for (int i = 0; i < fields.length; i++) {
                final Field field = fields[i];
                final Object value = field.get(in);
                field.set(this, value);
            }
        } catch (IllegalAccessException e) {
            fields_error = true;
        }
    }

    protected Class<?> resolveClass(ObjectStreamClass desc)
                    throws IOException,ClassNotFoundException {

        String name = desc.getName();
        if (name.length() > 2) {
            int index = name.lastIndexOf('[');
            if (index != -1) {
                name = name.substring(index + 1);
            }
            if (name.length() > 2 && name.charAt(0) == 'L' && name.charAt(name.length() - 1) == ';') {
                name = name.substring(1, name.length() - 1);
            }
            ParserConfig.global.checkAutoType(name, null, Feature.SupportAutoType.mask);
        }
        return super.resolveClass(desc);
    }

    protected Class<?> resolveProxyClass(String[] interfaces)
                    throws IOException,ClassNotFoundException {

        for (String interfacename : interfaces) {
            //检查是否处于黑名单
            ParserConfig.global.checkAutoType(interfacename, null);
        }
        return super.resolveProxyClass(interfaces);
    }

    //Hack:默认构造方法会调用这个方法，重写此方法使用反射还原部分关键属性
    protected void readStreamHeader() throws IOException,StreamCorruptedException {

    }
}
