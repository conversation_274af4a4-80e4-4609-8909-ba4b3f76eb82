package com.gtech.promotion.controller.Group;

import com.alibaba.fastjson.JSON;
import com.gtech.commons.redis.GTechRedisTemplate;
import com.gtech.promotion.code.activity.ActivityTypeEnum;
import com.gtech.promotion.component.activity.PromoGroupRelationDomain;
import com.gtech.promotion.dao.model.activity.ActivityGroupRelationVO;
import com.gtech.promotion.dao.model.activity.ActivityModel;
import com.gtech.promotion.dao.model.activity.PromoGroupVO;
import com.gtech.promotion.dto.cache.ActivityCacheDTO;
import com.gtech.promotion.service.activity.PromotionGroupRelationService;
import com.gtech.promotion.service.activity.PromotionGroupService;
import com.gtech.promotion.vo.param.activity.ActivityGroupRelationQueryParam;
import com.gtech.promotion.vo.param.activity.GroupRelationParam;
import com.gtech.promotion.vo.param.activity.GroupSettingRelationParam;
import com.gtech.promotion.vo.result.activity.ActivityGroupCache;
import com.gtech.promotion.vo.result.activity.ActivityGroupExclusionResult;
import com.gtech.promotion.vo.result.activity.ActivityGroupRelationResult;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/6/16 10:01
 */
@RunWith(MockitoJUnitRunner.class)
public class PromoGroupRelationDomainTest {

    @InjectMocks
    private PromoGroupRelationDomain promoGroupRelationDomain;


    @Mock
    private PromotionGroupRelationService promotionGroupRelationService;

    @Mock
    private PromotionGroupService promotionGroupService;



	@Mock
	private GTechRedisTemplate redisTemplate;

    @Test
    public void queryAllActivityGroupRelation(){

        ActivityGroupRelationQueryParam param = new ActivityGroupRelationQueryParam();

        param.setTenantCode("1");

        List<ActivityGroupRelationResult> relationResults = new ArrayList<>();

        ActivityGroupRelationResult relationResult = new ActivityGroupRelationResult();
        relationResult.setGroupCode("!");
        relationResult.setGroupName("!");
        relationResult.setGroupCode("1");
        relationResult.setPriority(1);
        relationResult.setTenantCode("!");
        relationResult.setType("02");
        List<ActivityGroupExclusionResult> results = new ArrayList<>();

        relationResult.setRelations(results);
        relationResults.add(relationResult);

        List<PromoGroupVO> outBOList = new ArrayList<>();
        PromoGroupVO promoGroupVO = new PromoGroupVO();

		promoGroupVO.setGroupCode("1");
        promoGroupVO.setPriority(1L);
        promoGroupVO.setGroupName("1");
        promoGroupVO.setTenantCode("!");
        promoGroupVO.setType("01");
        outBOList.add(promoGroupVO);
        Mockito.when(promotionGroupService.listActivityGroupByTenantCode(Mockito.anyString())).thenReturn(outBOList);

        List<ActivityGroupRelationVO> activityGroupRelationVOS = new ArrayList<>();

        ActivityGroupRelationVO vo= new ActivityGroupRelationVO();

        vo.setGroupCodeA("1");
        vo.setGroupCodeB("2");
        activityGroupRelationVOS.add(vo);

        Mockito.when(promotionGroupRelationService.queryGroupRelationByGroupCodeA(Mockito.anyString(),Mockito.anyList())).thenReturn(activityGroupRelationVOS);

        List<ActivityGroupRelationResult> relationResults1 = promoGroupRelationDomain.queryAllActivityGroupRelation(param);
        Assert.assertEquals(1, relationResults1.size());
    }

    @Test
    public void queryAllActivityGroupRelation11(){

        ActivityGroupRelationQueryParam param = new ActivityGroupRelationQueryParam();

        param.setTenantCode("1");

        List<ActivityGroupRelationResult> relationResults = new ArrayList<>();
        ActivityGroupRelationResult relationResult = new ActivityGroupRelationResult();
        relationResult.setGroupCode("!");
        relationResult.setGroupName("!");
        relationResult.setGroupCode("1");
        relationResult.setPriority(1);
        relationResult.setTenantCode("!");
        relationResult.setType("02");
        List<ActivityGroupExclusionResult> results = new ArrayList<>();
        relationResult.setRelations(results);
        relationResults.add(relationResult);

        List<PromoGroupVO> outBOList = new ArrayList<>();
        PromoGroupVO promoGroupVO = new PromoGroupVO();
		promoGroupVO.setGroupCode("1");
        promoGroupVO.setPriority(1L);
        promoGroupVO.setGroupName("1");
        promoGroupVO.setTenantCode("!");
        promoGroupVO.setType("01");
        outBOList.add(promoGroupVO);
        Mockito.when(promotionGroupService.listActivityGroupByTenantCode(Mockito.anyString())).thenReturn(outBOList);

        List<ActivityGroupRelationVO> activityGroupRelationVOS = new ArrayList<>();

        ActivityGroupRelationVO vo= new ActivityGroupRelationVO();

        vo.setGroupCodeA("1");
        vo.setGroupCodeB("2");
        activityGroupRelationVOS.add(vo);

        Mockito.when(promotionGroupRelationService.queryGroupRelationByGroupCodeA(Mockito.anyString(),Mockito.anyList())).thenReturn(activityGroupRelationVOS);

        List<ActivityGroupRelationResult> relationResults1 = promoGroupRelationDomain.queryAllActivityGroupRelation(param);
        Assert.assertEquals(1, relationResults1.size());
    }


    @Test
    public void settingActivityGroupRelation(){
        GroupSettingRelationParam param = new GroupSettingRelationParam();

        param.setDomainCode("!");
        param.setTenantCode("!");

        List<GroupRelationParam> relations = new ArrayList<>();
        GroupRelationParam groupRelationParam = new GroupRelationParam();

        groupRelationParam.setRelation(2);
        groupRelationParam.setGroupCodeB("1");
        groupRelationParam.setGroupCodeA("2");
        relations.add(groupRelationParam);
        param.setRelations(relations);


        List<PromoGroupVO> promoGroupVOS = new ArrayList<>();

        PromoGroupVO promoGroupVO1 = new PromoGroupVO();
        promoGroupVO1.setPriority(1L);
		promoGroupVO1.setGroupCode("2");
        promoGroupVO1.setTenantCode("1");
        promoGroupVO1.setType("02");

        promoGroupVOS.add(promoGroupVO1);

        PromoGroupVO promoGroupVO = new PromoGroupVO();
        promoGroupVO.setPriority(1L);
		promoGroupVO.setGroupCode("1");
        promoGroupVO.setTenantCode("1");
        promoGroupVO.setType("02");

        promoGroupVOS.add(promoGroupVO);
        Mockito.when(promotionGroupService.listActivityGroupByTenantCode(Mockito.anyString())).thenReturn(promoGroupVOS);

        Map<String, List<String>> relationMap = new HashMap<>();
        List<String> list = new ArrayList<>();
        list.add("2");
        relationMap.put("1",list);

        Mockito.when(promotionGroupRelationService.createGroupRelation(Mockito.anyString(),Mockito.anyString(),Mockito.any())).thenReturn(1);

        List<ActivityGroupRelationVO> activityGroupRelationVOS = new ArrayList<>();

        ActivityGroupRelationVO activityGroupRelationVO = new ActivityGroupRelationVO();
        activityGroupRelationVO.setGroupCodeA("2");
        activityGroupRelationVO.setGroupCodeB("1");
        activityGroupRelationVO.setRelation(1);
        activityGroupRelationVO.setTenantCode("1");
        activityGroupRelationVOS.add(activityGroupRelationVO);

        ActivityGroupRelationVO vo = new ActivityGroupRelationVO();
        vo.setGroupCodeA("1");
        vo.setGroupCodeB("2");
        vo.setRelation(1);
        vo.setTenantCode("1");
        activityGroupRelationVOS.add(vo);


        Mockito.when(promotionGroupRelationService.queryGroupRelationByGroupCodeAB(Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenReturn(0);
        int i = promoGroupRelationDomain.settingActivityGroupRelation(param);
        Assert.assertEquals(1,i);
    }

    @Test
    public void testFilterActivityByCoupon() {
        List<String> relation = Arrays.asList("activity1", "relation2");
        ActivityGroupCache activityGroupCache = new ActivityGroupCache();
        activityGroupCache.setGroupCode("group1");
        activityGroupCache.setRelationList(relation);
        activityGroupCache.setPriority(1);
        when(redisTemplate.opsValueGet(anyString(), anyString(), any())).thenReturn(JSON.toJSONString(Arrays.asList(activityGroupCache)));
        //Mockito.when(promoGroupRelationDomain.getTenantGroupCache(Mockito.any())).thenReturn(Arrays.asList(activityGroupCache));


        // 创建模拟数据
        Map<String, ActivityCacheDTO> filtedActivityMap = new HashMap<>();
        ActivityModel activityModel = new ActivityModel();
        activityModel.setActivityCode("activity1");
        activityModel.setActivityType(ActivityTypeEnum.COUPON.code());
        activityModel.setGroupCode("group1");

        ActivityModel normal1ActivityModel = new ActivityModel();
        normal1ActivityModel.setActivityCode("coupon1");
        normal1ActivityModel.setActivityType(ActivityTypeEnum.COUPON.code());
        normal1ActivityModel.setGroupCode("group1");

        ActivityCacheDTO couponActivity = new ActivityCacheDTO();
        couponActivity.setActivityModel(activityModel);
        ActivityCacheDTO normalActivity = new ActivityCacheDTO();
        normalActivity.setActivityModel(normal1ActivityModel);

        filtedActivityMap.put("coupon1", couponActivity);
        filtedActivityMap.put("activity1", normalActivity);

        // 调用被测试方法
        Map<String, ActivityCacheDTO> result = promoGroupRelationDomain.filterActivityByCoupon(filtedActivityMap, "tenant1");


    }

    @Test
    public void settingActivityGroupRelation_empty(){
        GroupSettingRelationParam param = new GroupSettingRelationParam();

        param.setDomainCode("!");
        param.setTenantCode("!");
        List<GroupRelationParam> relations = new ArrayList<>();
        GroupRelationParam groupRelationParam = new GroupRelationParam();
        groupRelationParam.setRelation(2);
        groupRelationParam.setGroupCodeB("1");
        groupRelationParam.setGroupCodeA("2");
        relations.add(groupRelationParam);
        param.setRelations(relations);
        List<PromoGroupVO> promoGroupVOS = new ArrayList<>();
        PromoGroupVO promoGroupVO1 = new PromoGroupVO();
        promoGroupVO1.setPriority(1L);
        promoGroupVO1.setGroupCode("2");
        promoGroupVO1.setTenantCode("1");
        promoGroupVO1.setType("02");

        promoGroupVOS.add(promoGroupVO1);

        PromoGroupVO promoGroupVO = new PromoGroupVO();
        promoGroupVO.setPriority(1L);
        promoGroupVO.setGroupCode("1");
        promoGroupVO.setTenantCode("1");
        promoGroupVO.setType("02");

        promoGroupVOS.add(promoGroupVO);
        Mockito.when(promotionGroupService.listActivityGroupByTenantCode(Mockito.anyString())).thenReturn(promoGroupVOS);

        Map<String, List<String>> relationMap = new HashMap<>();
        List<String> list = new ArrayList<>();
        list.add("2");
        relationMap.put("1",list);

        Mockito.when(promotionGroupRelationService.createGroupRelation(Mockito.anyString(),Mockito.anyString(),Mockito.any())).thenReturn(1);

        List<ActivityGroupRelationVO> activityGroupRelationVOS = new ArrayList<>();
        ActivityGroupRelationVO activityGroupRelationVO = new ActivityGroupRelationVO();
        activityGroupRelationVO.setGroupCodeA("2");
        activityGroupRelationVO.setGroupCodeB("1");
        activityGroupRelationVO.setRelation(1);
        activityGroupRelationVO.setTenantCode("1");
        activityGroupRelationVOS.add(activityGroupRelationVO);
        ActivityGroupRelationVO vo = new ActivityGroupRelationVO();
        vo.setGroupCodeA("1");
        vo.setGroupCodeB("2");
        vo.setRelation(1);
        vo.setTenantCode("1");
        activityGroupRelationVOS.add(vo);


        Mockito.when(promotionGroupRelationService.queryGroupRelationByGroupCodeAB(Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenReturn(0);
        int i = promoGroupRelationDomain.settingActivityGroupRelation(param);
        Assert.assertEquals(1,i);
    }

    @Test
    public void settingActivityGroupRelation_0(){
        GroupSettingRelationParam param = new GroupSettingRelationParam();

        param.setDomainCode("!");
        param.setTenantCode("!");

        List<GroupRelationParam> relations = new ArrayList<>();
        GroupRelationParam groupRelationParam = new GroupRelationParam();

        groupRelationParam.setRelation(2);
        groupRelationParam.setGroupCodeB("1");
        groupRelationParam.setGroupCodeA("2");
        relations.add(groupRelationParam);
        param.setRelations(relations);


        List<PromoGroupVO> promoGroupVOS = new ArrayList<>();

        PromoGroupVO promoGroupVO1 = new PromoGroupVO();
        promoGroupVO1.setPriority(1L);
		promoGroupVO1.setGroupCode("2");
        promoGroupVO1.setTenantCode("1");
        promoGroupVO1.setType("02");

        promoGroupVOS.add(promoGroupVO1);

        PromoGroupVO promoGroupVO = new PromoGroupVO();
        promoGroupVO.setPriority(1L);
		promoGroupVO.setGroupCode("1");
        promoGroupVO.setTenantCode("1");
        promoGroupVO.setType("02");

        promoGroupVOS.add(promoGroupVO);
        Mockito.when(promotionGroupService.listActivityGroupByTenantCode(Mockito.anyString())).thenReturn(promoGroupVOS);

        Map<String, List<String>> relationMap = new HashMap<>();
        List<String> list = new ArrayList<>();
        list.add("2");
        relationMap.put("1",list);
        List<ActivityGroupRelationVO> activityGroupRelationVOS = new ArrayList<>();

        ActivityGroupRelationVO activityGroupRelationVO = new ActivityGroupRelationVO();
        activityGroupRelationVO.setGroupCodeA("2");
        activityGroupRelationVO.setGroupCodeB("1");
        activityGroupRelationVO.setRelation(1);
        activityGroupRelationVO.setTenantCode("1");
        activityGroupRelationVOS.add(activityGroupRelationVO);

        ActivityGroupRelationVO vo = new ActivityGroupRelationVO();
        vo.setGroupCodeA("1");
        vo.setGroupCodeB("2");
        vo.setRelation(1);
        vo.setTenantCode("1");
        activityGroupRelationVOS.add(vo);


        Mockito.when(promotionGroupRelationService.queryGroupRelationByGroupCodeA(Mockito.anyString(),Mockito.anyList())).thenReturn(activityGroupRelationVOS);
        int i = promoGroupRelationDomain.settingActivityGroupRelation(param);
        Assert.assertEquals(0,i);
    }


    @Test
    public void settingActivityGroupRelation2_1(){
        GroupSettingRelationParam param = new GroupSettingRelationParam();

        param.setDomainCode("!");
        param.setTenantCode("!");

        List<GroupRelationParam> relations = new ArrayList<>();
        GroupRelationParam groupRelationParam = new GroupRelationParam();

        groupRelationParam.setRelation(1);
        groupRelationParam.setGroupCodeB("1");
        groupRelationParam.setGroupCodeA("2");
        relations.add(groupRelationParam);
        param.setRelations(relations);

        List<PromoGroupVO> promoGroupVOS = new ArrayList<>();

        PromoGroupVO promoGroupVO1 = new PromoGroupVO();
        promoGroupVO1.setPriority(1L);
		promoGroupVO1.setGroupCode("2");
        promoGroupVO1.setTenantCode("1");
        promoGroupVO1.setType("02");

        promoGroupVOS.add(promoGroupVO1);

        PromoGroupVO promoGroupVO = new PromoGroupVO();
        promoGroupVO.setPriority(1L);
		promoGroupVO.setGroupCode("1");
        promoGroupVO.setTenantCode("1");
        promoGroupVO.setType("02");

        promoGroupVOS.add(promoGroupVO);
        Mockito.when(promotionGroupService.listActivityGroupByTenantCode(Mockito.anyString())).thenReturn(promoGroupVOS);

        Map<String, List<String>> relationMap = new HashMap<>();
        List<String> list = new ArrayList<>();
        list.add("2");
        relationMap.put("1",list);

        Mockito.when(promotionGroupRelationService.deleteGroupRelation(Mockito.anyString(),Mockito.anyString(),Mockito.any())).thenReturn(1);

        List<ActivityGroupRelationVO> activityGroupRelationVOS = new ArrayList<>();

        ActivityGroupRelationVO activityGroupRelationVO = new ActivityGroupRelationVO();
        activityGroupRelationVO.setGroupCodeA("2");
        activityGroupRelationVO.setGroupCodeB("1");
        activityGroupRelationVO.setRelation(1);
        activityGroupRelationVO.setTenantCode("1");
        activityGroupRelationVOS.add(activityGroupRelationVO);

        ActivityGroupRelationVO vo = new ActivityGroupRelationVO();
        vo.setGroupCodeA("1");
        vo.setGroupCodeB("2");
        vo.setRelation(1);
        vo.setTenantCode("1");
        activityGroupRelationVOS.add(vo);


        Mockito.when(promotionGroupRelationService.queryGroupRelationByGroupCodeA(Mockito.anyString(),Mockito.anyList())).thenReturn(activityGroupRelationVOS);
        int i = promoGroupRelationDomain.settingActivityGroupRelation(param);
        Assert.assertEquals(1,i);
    }

    @Test
    public void settingActivityGroupRelation2_0(){
        GroupSettingRelationParam param = new GroupSettingRelationParam();

        param.setDomainCode("!");
        param.setTenantCode("!");

        List<GroupRelationParam> relations = new ArrayList<>();
        GroupRelationParam groupRelationParam = new GroupRelationParam();

        groupRelationParam.setRelation(1);
        groupRelationParam.setGroupCodeB("1");
        groupRelationParam.setGroupCodeA("2");
        relations.add(groupRelationParam);
        param.setRelations(relations);

        List<PromoGroupVO> promoGroupVOS = new ArrayList<>();

        PromoGroupVO promoGroupVO1 = new PromoGroupVO();
        promoGroupVO1.setPriority(1L);
		promoGroupVO1.setGroupCode("2");
        promoGroupVO1.setTenantCode("1");
        promoGroupVO1.setType("02");

        promoGroupVOS.add(promoGroupVO1);

        PromoGroupVO promoGroupVO = new PromoGroupVO();
        promoGroupVO.setPriority(1L);
		promoGroupVO.setGroupCode("1");
        promoGroupVO.setTenantCode("1");
        promoGroupVO.setType("02");

        promoGroupVOS.add(promoGroupVO);
        Mockito.when(promotionGroupService.listActivityGroupByTenantCode(Mockito.anyString())).thenReturn(promoGroupVOS);

        Map<String, List<String>> relationMap = new HashMap<>();
        List<String> list = new ArrayList<>();
        list.add("2");
        relationMap.put("1",list);

        List<ActivityGroupRelationVO> activityGroupRelationVOS = new ArrayList<>();

        ActivityGroupRelationVO activityGroupRelationVO = new ActivityGroupRelationVO();
        activityGroupRelationVO.setGroupCodeA("2");
        activityGroupRelationVO.setGroupCodeB("1");
        activityGroupRelationVO.setRelation(2);
        activityGroupRelationVO.setTenantCode("1");
        activityGroupRelationVOS.add(activityGroupRelationVO);

        ActivityGroupRelationVO vo = new ActivityGroupRelationVO();
        vo.setGroupCodeA("1");
        vo.setGroupCodeB("2");
        vo.setRelation(2);
        vo.setTenantCode("1");
        activityGroupRelationVOS.add(vo);


        Mockito.when(promotionGroupRelationService.queryGroupRelationByGroupCodeA(Mockito.anyString(),Mockito.anyList())).thenReturn(activityGroupRelationVOS);

        int i = promoGroupRelationDomain.settingActivityGroupRelation(param);
        Assert.assertEquals(0,i);
    }



    @Test
    public void settingActivityGroupRelation2_01(){
        GroupSettingRelationParam param = new GroupSettingRelationParam();

        param.setDomainCode("!");
        param.setTenantCode("!");

        List<GroupRelationParam> relations = new ArrayList<>();
        GroupRelationParam groupRelationParam = new GroupRelationParam();

        groupRelationParam.setRelation(1);
        groupRelationParam.setGroupCodeB("1");
        groupRelationParam.setGroupCodeA("2");
        relations.add(groupRelationParam);
        param.setRelations(relations);

        List<PromoGroupVO> promoGroupVOS = new ArrayList<>();

        PromoGroupVO promoGroupVO1 = new PromoGroupVO();
        promoGroupVO1.setPriority(1L);
        promoGroupVO1.setGroupCode("2");
        promoGroupVO1.setTenantCode("1");
        promoGroupVO1.setType("02");

        promoGroupVOS.add(promoGroupVO1);

        PromoGroupVO promoGroupVO = new PromoGroupVO();
        promoGroupVO.setPriority(1L);
        promoGroupVO.setGroupCode("1");
        promoGroupVO.setTenantCode("1");
        promoGroupVO.setType("02");

        promoGroupVOS.add(promoGroupVO);
        Mockito.when(promotionGroupService.listActivityGroupByTenantCode(Mockito.anyString())).thenReturn(promoGroupVOS);

        Map<String, List<String>> relationMap = new HashMap<>();
        List<String> list = new ArrayList<>();
        list.add("2");
        relationMap.put("1",list);

        List<ActivityGroupRelationVO> activityGroupRelationVOS = new ArrayList<>();

        ActivityGroupRelationVO activityGroupRelationVO = new ActivityGroupRelationVO();
        activityGroupRelationVO.setGroupCodeA("2");
        activityGroupRelationVO.setGroupCodeB("1");
        activityGroupRelationVO.setRelation(1);
        activityGroupRelationVO.setTenantCode("1");
        activityGroupRelationVOS.add(activityGroupRelationVO);

        ActivityGroupRelationVO vo = new ActivityGroupRelationVO();
        vo.setGroupCodeA("1");
        vo.setGroupCodeB("2");
        vo.setRelation(1);
        vo.setTenantCode("1");
        activityGroupRelationVOS.add(vo);


        Mockito.when(promotionGroupRelationService.queryGroupRelationByGroupCodeA(Mockito.anyString(),Mockito.anyList())).thenReturn(activityGroupRelationVOS);

        int i = promoGroupRelationDomain.settingActivityGroupRelation(param);
        Assert.assertEquals(0,i);
    }

	@Test
	public void getTenantGroupCacheTest() {
		PromoGroupVO promoGroupVO = new PromoGroupVO();
		promoGroupVO.setGroupCode("1");
		promoGroupVO.setPriority(2l);
		when(promotionGroupService.listActivityGroupByTenantCode(anyString())).thenReturn(Arrays.asList(promoGroupVO));

        List<ActivityGroupRelationVO> activityGroupRelationVOS = new ArrayList<>();

        ActivityGroupRelationVO vo= new ActivityGroupRelationVO();

        vo.setGroupCodeA("1");
        vo.setGroupCodeB("2");
        activityGroupRelationVOS.add(vo);

        Mockito.when(promotionGroupRelationService.queryGroupRelationByGroupCodeA(Mockito.anyString(),Mockito.anyList())).thenReturn(activityGroupRelationVOS);

        promoGroupRelationDomain.getTenantGroupCache("100001");
		when(redisTemplate.opsValueGet(anyString(), anyString(), any())).thenReturn(JSON.toJSONString(Collections.emptyList()));
		promoGroupRelationDomain.getTenantGroupCache("100001");
	}
}
