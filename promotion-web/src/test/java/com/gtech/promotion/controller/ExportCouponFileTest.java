/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 *
 * This software is the confidential and proprietary information of GTech. You shall not disclose such 
 * Confidential Information and shall use it only in accordance with the terms of the license agreement 
 * you entered into with GTech.
 *
 * <PERSON>ECH MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS 
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A 
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY 
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.promotion.controller;

import com.gtech.commons.page.PageData;
import com.gtech.promotion.checker.SystemChecker;
import com.gtech.promotion.component.coupon.CouponInnerCodeComponent;
import com.gtech.promotion.component.feign.BasicClientDomain;
import com.gtech.promotion.controller.coupon.CouponInnerCodeController;
import com.gtech.promotion.dao.model.activity.ActivityModel;
import com.gtech.promotion.dto.in.activity.CouponDataInDTO;
import com.gtech.promotion.dto.in.coupon.TCouponListQueryDTO;
import com.gtech.promotion.dto.out.coupon.ManagementDataOutDTO;
import com.gtech.promotion.exception.PromotionException;
import com.gtech.promotion.service.activity.ActivityService;
import com.gtech.promotion.service.coupon.PromoCouponActivityService;
import com.gtech.promotion.utils.ExportCouponUtil;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * 测试导出券
 */
@RunWith(MockitoJUnitRunner.class)
public class ExportCouponFileTest{

    @InjectMocks
    private CouponInnerCodeController innerCodeController;

    @Mock
    private PromoCouponActivityService promoCouponActivityService;

    @Mock
    private BasicClientDomain basicClientDomain;

    @Mock
    private CouponInnerCodeComponent couponInnerCodeDomain;
    @Mock
    private ActivityService activityService;


    @Test
    public void exportFileTest_导出券码失败_简体中文() throws IOException {
        //given
        ActivityModel couponActivityVO = new ActivityModel();
        TCouponListQueryDTO listQueryDTO = new TCouponListQueryDTO();
        listQueryDTO.setActivityCode("1");
        listQueryDTO.setTenantCode("222");
        //when
        when(promoCouponActivityService.findCouponActivity("222", "1")).thenReturn(couponActivityVO);
//        when(basicClientDomain.getLanguages(any())).thenThrow(new PromotionCouponException(ErrorCodes.ERROR_UNKNOWN, null));
        try{
            innerCodeController.exportFile(listQueryDTO, new MockHttpServletResponse(), new MockHttpServletRequest());
        }catch (PromotionException e){
            Assert.assertEquals(e.getCode(), SystemChecker.ERROR_STREAM.getCode());
        }
    }

//    @Test
//    public void exportFileTest_导出券码_优惠码成功_繁体() throws IOException{
//        //given
//        TPromoCouponActivityVO couponActivityVO = new TPromoCouponActivityVO();
//        couponActivityVO.setCouponType("03");
//        //when
//        when(promoCouponActivityService.findCouponActivity(any())).thenReturn(couponActivityVO);
//        TCouponListQueryDTO listQueryDTO = new TCouponListQueryDTO();
//        listQueryDTO.setActivityId("1");
//        listQueryDTO.setTenantCode("222");
//        ExportCouponUtil.exportCouponInfoHeader(new ByteArrayOutputStream(), "zh_HK");
//        innerCodeController.exportFile(listQueryDTO, new MockHttpServletResponse(), new MockHttpServletRequest());
//    }

    @Test
    public void exportFileTest_导出券码_优惠码成功_英文_03() throws IOException{
        //given
        ActivityModel couponActivityVO = new ActivityModel();
        couponActivityVO.setCouponType("03");
        //when
        when(promoCouponActivityService.findCouponActivity(anyString(), anyString())).thenReturn(couponActivityVO);
        TCouponListQueryDTO listQueryDTO = new TCouponListQueryDTO();
        listQueryDTO.setActivityCode("1");
        listQueryDTO.setTenantCode("222");
        ExportCouponUtil.exportCouponInfoHeader(new ByteArrayOutputStream(), "en_US");
        try {
			innerCodeController.exportFile(listQueryDTO, new MockHttpServletResponse(), new MockHttpServletRequest());
		} catch (Exception e) {
		}
    }

    @Test
    public void exportFileTest_导出券码_优惠码成功_英文_02() throws IOException{
        //given
        ActivityModel couponActivityVO = new ActivityModel();
        couponActivityVO.setCouponType("02");
        //when
        when(promoCouponActivityService.findCouponActivity(anyString(), anyString())).thenReturn(couponActivityVO);
        TCouponListQueryDTO listQueryDTO = new TCouponListQueryDTO();
        listQueryDTO.setActivityCode("1");
        listQueryDTO.setTenantCode("222");
        ExportCouponUtil.exportCouponInfoHeader(new ByteArrayOutputStream(), "en_US");
        try {
            innerCodeController.exportFile(listQueryDTO, new MockHttpServletResponse(), new MockHttpServletRequest());
        } catch (Exception e) {
        }
    }


    @Test
    public void exportFileTest_导出券码_优惠码成功_英文_01() throws IOException{
        //given
        ActivityModel couponActivityVO = new ActivityModel();
        couponActivityVO.setCouponType("01");
        //when
        when(promoCouponActivityService.findCouponActivity(anyString(), anyString())).thenReturn(couponActivityVO);
        TCouponListQueryDTO listQueryDTO = new TCouponListQueryDTO();
        listQueryDTO.setActivityCode("1");
        listQueryDTO.setTenantCode("222");
        ExportCouponUtil.exportCouponInfoHeader(new ByteArrayOutputStream(), "en_US");
        Mockito.doThrow(new IOException("TEST")).when(couponInnerCodeDomain).exportCouponInfos(Mockito.any(),Mockito.any(),Mockito.anyMap(),Mockito.anyMap());
        try {
            innerCodeController.exportFile(listQueryDTO, new MockHttpServletResponse(), new MockHttpServletRequest());
        } catch (Exception e) {
        }
    }

    @Test
    public void exportCouponCodeDataTest_条件导出优惠码券码_英文() throws IOException{
        //given
        CouponDataInDTO dataInDTO = new CouponDataInDTO();
        dataInDTO.setTenantCode("222");
        ManagementDataOutDTO outDTO = new ManagementDataOutDTO();
        List<ManagementDataOutDTO> dtos = new ArrayList<>();
        dtos.add(outDTO);
        PageData<ManagementDataOutDTO> pageData = new PageData<>(dtos, 1L);
        //when
//        when(couponInnerCodeDomain.getPageInfo(any())).thenReturn(pageData);
        ExportCouponUtil.exportManagementCouponInfoHeader(new ByteArrayOutputStream());
        try {
            innerCodeController.exportCouponCodeData(dataInDTO, new MockHttpServletResponse(), new MockHttpServletRequest());
        } catch (Exception e) {
        }
    }

    @Test
    public void exportCouponCodeDataTest_条件导出优惠码券码() throws IOException {
        //given
        CouponDataInDTO dataInDTO = new CouponDataInDTO();
        dataInDTO.setTenantCode("222");
        ManagementDataOutDTO outDTO = new ManagementDataOutDTO();
        List<ManagementDataOutDTO> dtos = new ArrayList<>();
        dtos.add(outDTO);
        PageData<ManagementDataOutDTO> pageData = new PageData<>(dtos, 1L);
        //when

        Mockito.when(couponInnerCodeDomain.getPageInfo(Mockito.any())).thenReturn(pageData);
        ExportCouponUtil.exportManagementCouponInfoHeader(new ByteArrayOutputStream());
        try {
            innerCodeController.exportCouponCodeData(dataInDTO, new MockHttpServletResponse(), new MockHttpServletRequest());
        } catch (Exception e) {
        }
    }

//
//    @Test
//    public void exportCouponCodeDataTest_条件导出优惠码券码_繁体_失败() throws IOException{
//        //given
//        CouponDataInDTO dataInDTO = new CouponDataInDTO();
//        dataInDTO.setTenantCode("222");
//        //when
//        when(couponInnerCodeDomain.getPageInfo(any())).thenReturn(null);
//        ExportCouponUtil.exportManagementCouponInfoHeader(new ByteArrayOutputStream(), "zh_HK");
//        try{
//            innerCodeController.exportCouponCodeData(dataInDTO, new MockHttpServletResponse(), new MockHttpServletRequest());
//        }catch (UnexPromoException e){
//            Assert.assertEquals(e.getCode(), "10140123");
//        }
//    }

}
