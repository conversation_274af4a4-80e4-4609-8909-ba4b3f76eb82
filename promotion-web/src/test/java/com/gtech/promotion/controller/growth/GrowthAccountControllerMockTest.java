package com.gtech.promotion.controller.growth;

import com.gtech.commons.page.PageData;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.promotion.service.growth.GrowthAccountSaveService;
import com.gtech.promotion.service.growth.GrowthAccountService;
import com.gtech.promotion.vo.param.growth.CreateGrowthAccountParam;
import com.gtech.promotion.vo.param.growth.UpdateGrowthParam;
import com.gtech.promotion.vo.param.growth.query.GetGrowthAccountParam;
import com.gtech.promotion.vo.param.growth.query.GrowthAccountUniqueParam;
import com.gtech.promotion.vo.param.growth.query.QueryGrowthAccountParam;
import com.gtech.promotion.vo.result.growth.GrowthAccountResult;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2021/3/30 13:42
 */
@RunWith(MockitoJUnitRunner.class)
public class GrowthAccountControllerMockTest {

    @InjectMocks
    private GrowthAccountController growthAccountController;

    @Mock
    private GrowthAccountService growthAccountService;

    @Mock
    private GrowthAccountSaveService growthAccountSaveService;

    @Test
    public void queryGrowthAccountList() {
        QueryGrowthAccountParam param = new QueryGrowthAccountParam();
        param.setTenantCode("1");
        PageResult<GrowthAccountResult> resultPageResult = new PageResult<>();
        PageData<GrowthAccountResult> data = new PageData<>();
        List<GrowthAccountResult> list = new ArrayList<>();
        GrowthAccountResult result = new GrowthAccountResult();
        result.setCreateTime(new Date());
        result.setUpdateTime(new Date());
        result.setValidBeginTime(new Date());
        result.setValidEndTime(new Date());
        list.add(result);
        data.setList(list);
        resultPageResult.setData(data);

        Mockito.when(growthAccountService.queryGrowthAccountPage(Mockito.any())).thenReturn(resultPageResult);
        PageResult<GrowthAccountResult> pageResult = growthAccountController.queryGrowthAccountList(param);
        Assert.assertTrue(pageResult.isSuccess());
    }

    @Test
    public void getGrowthAccount(){
        GetGrowthAccountParam param = new GetGrowthAccountParam();
        param.setTenantCode("1");
        param.setDomainCode("1");
        param.setAccountCode("1");
        param.setAccountType(1);
        GrowthAccountResult result = new GrowthAccountResult();
        result.setCreateTime(new Date());
        result.setUpdateTime(new Date());
        result.setValidBeginTime(new Date());
        result.setValidEndTime(new Date());

        Mockito.when(growthAccountService.getGrowthAccount(Mockito.any())).thenReturn(result);
        Result<GrowthAccountResult> growthAccount = growthAccountController.getGrowthAccount(param);
        Assert.assertTrue(growthAccount.isSuccess());
    }


    @Test
    public void getGrowthAccount_null(){
        GetGrowthAccountParam param = new GetGrowthAccountParam();
        param.setTenantCode("1");
        param.setDomainCode("1");
        param.setAccountCode("1");
        param.setAccountType(1);
        Mockito.when(growthAccountService.getGrowthAccount(Mockito.any())).thenReturn(null);
        Result<GrowthAccountResult> growthAccount = growthAccountController.getGrowthAccount(param);
        Assert.assertTrue(growthAccount.isSuccess());
    }

    @Test
    public void updateGrowthAccountStatus(){
        GrowthAccountUniqueParam.GrowthAccountStatusUniqueVo param = new GrowthAccountUniqueParam.GrowthAccountStatusUniqueVo();
        param.setTenantCode("1");
        param.setDomainCode("1");
        param.setGrowthAccountCode("1");
        param.setStatus(1);
        param.setOldStatus(0);

        Mockito.when(growthAccountService.updateGrowthAccountStatus(Mockito.any())).thenReturn(1);
        Result<Void> result = growthAccountController.updateGrowthAccountStatus(param);
        Assert.assertTrue(result.isSuccess());
    }

    @Test
    public void createGrowthAccount(){
        CreateGrowthAccountParam param = new CreateGrowthAccountParam();
        param.setTenantCode("1");
        param.setDomainCode("1");
        param.setAccountCode("1");
        param.setAccountType(1);
        param.setAccountBalance(1);

        Mockito.when(growthAccountSaveService.saveGrowthAccount(Mockito.any())).thenReturn("1");
        Result<Map<String, String>> growthAccount = growthAccountController.createGrowthAccount(param);
        Assert.assertTrue(growthAccount.isSuccess());
    }

    @Test
    public void editGrowthAccount(){
        CreateGrowthAccountParam param = new CreateGrowthAccountParam();
        param.setTenantCode("1");
        param.setDomainCode("1");
        param.setAccountCode("1");
        param.setAccountType(1);
        param.setAccountBalance(1);

        Mockito.when(growthAccountService.updateGrowthAccount(Mockito.any())).thenReturn(1);
        Result<Void> voidResult = growthAccountController.editGrowthAccount(param);
        Assert.assertTrue(voidResult.isSuccess());
    }

    @Test
    public void changeGrowth(){
        UpdateGrowthParam param = new UpdateGrowthParam();
        param.setTenantCode("1");
        param.setDomainCode("1");
        param.setAccountCode("1");
        param.setAccountType(1);
        param.setTransactionType(1);
        param.setTransactionAmount(1);

        Mockito.when(growthAccountService.updateGrowth(Mockito.any())).thenReturn(1);
        Result<Void> voidResult = growthAccountController.changeGrowth(param);
        Assert.assertTrue(voidResult.isSuccess());
    }


}
