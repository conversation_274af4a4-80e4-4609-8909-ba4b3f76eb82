package com.gtech.promotion.controller.point2;

import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.promotion.controller.point.PointAccountController;
import com.gtech.promotion.service.point.PointAccountService;
import com.gtech.promotion.service.point.PointAccountTempService;
import com.gtech.promotion.vo.param.point.CreatePointAccountParam;
import com.gtech.promotion.vo.param.point.UpdatePointAccountParam;
import com.gtech.promotion.vo.param.point.UpdatePointParam;
import com.gtech.promotion.vo.param.point.query.GetPointAccountCampaignParam;
import com.gtech.promotion.vo.param.point.query.GetPointAccountParam;
import com.gtech.promotion.vo.param.point.query.PointAccountUniqueParam;
import com.gtech.promotion.vo.param.point.query.QueryPointAccountParam;
import com.gtech.promotion.vo.result.point.PointAccountCampaignResult;
import com.gtech.promotion.vo.result.point.PointAccountResult;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021-09-08 14:16
 */
@RunWith(MockitoJUnitRunner.class)
public class PointAccountControllerMockTest {
    @InjectMocks
    private PointAccountController pointAccountController;
    @Mock
    private PointAccountService pointAccountService;
    @Mock
    private PointAccountTempService pointAccountTempService;

    @Test
    public void queryPointAccountList(){
        QueryPointAccountParam pointAccountQueryVo = new QueryPointAccountParam();
        Mockito.when(pointAccountService.queryPointAccountPage(Mockito.any())).thenReturn(new PageResult<>());
        PageResult<PointAccountResult> result = pointAccountController.queryPointAccountList(pointAccountQueryVo);
        Assert.assertTrue(result.isSuccess());
    }

    @Test
    public void getPointAccount(){
        GetPointAccountParam pointAccountQueryVo = new GetPointAccountParam();
        Mockito.when(pointAccountService.getPointAccount(Mockito.any())).thenReturn(new PointAccountResult());
        Result<PointAccountResult> result = pointAccountController.getPointAccount(pointAccountQueryVo);
        Assert.assertTrue(result.isSuccess());
    }

    @Test
    public void getPointAccountCampaign(){
        GetPointAccountCampaignParam pointAccountQueryVo = new GetPointAccountCampaignParam();
        Mockito.when(pointAccountService.getPointAccountCampaign(Mockito.any())).thenReturn(new PointAccountCampaignResult());
        Result<PointAccountCampaignResult> result = pointAccountController.getPointAccountCampaign(pointAccountQueryVo);
        Assert.assertTrue(result.isSuccess());
    }

    @Test
    public void updatePointAccountStatus(){
        PointAccountUniqueParam.PointAccountStatusUniqueVo pointAccountQueryVo = new PointAccountUniqueParam.PointAccountStatusUniqueVo();
        Mockito.when(pointAccountService.updatePointAccountStatus(Mockito.any())).thenReturn(1);
        Result<Void> voidResult = pointAccountController.updatePointAccountStatus(pointAccountQueryVo);
        Assert.assertTrue(voidResult.isSuccess());
    }

    @Test
    public void createPointAccount(){
        CreatePointAccountParam pointAccountQueryVo = new CreatePointAccountParam();
        Mockito.when(pointAccountTempService.savePointAccount(Mockito.any())).thenReturn("1");
        Result<Map<String, String>> result = pointAccountController.createPointAccount(pointAccountQueryVo);
        Assert.assertTrue(result.isSuccess());
    }

    @Test
    public void editPointAccount(){
        UpdatePointAccountParam pointAccountQueryVo = new UpdatePointAccountParam();
        Mockito.when(pointAccountService.updatePointAccount(Mockito.any())).thenReturn(1);
        Result<Void> voidResult = pointAccountController.editPointAccount(pointAccountQueryVo);
        Assert.assertTrue(voidResult.isSuccess());
    }

    @Test
    public void changePoint(){
        UpdatePointParam param = new UpdatePointParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setAccountCode("1");
        param.setAccountType(1);
        param.setTransactionRemarks("");
        param.setTransactionType(1);
        param.setTransactionAmount(10);
        param.setOperation(1);
        Result<Void> voidResult = pointAccountController.changePoint(param);
        Assert.assertTrue(voidResult.isSuccess());
    }

    @Test
    public void increaseOrDecreasePoint(){
        UpdatePointParam param = new UpdatePointParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setAccountCode("1");
        param.setAccountType(1);
        param.setTransactionRemarks("");
        param.setTransactionType(1);
        param.setTransactionAmount(10);
        param.setOperation(1);
        Mockito.when(pointAccountService.increaseOrDecreasePoint(Mockito.any())).thenReturn(1);
        Result<Void> voidResult = pointAccountController.increaseOrDecreasePoint(param);
        Assert.assertTrue(voidResult.isSuccess());
    }
}
