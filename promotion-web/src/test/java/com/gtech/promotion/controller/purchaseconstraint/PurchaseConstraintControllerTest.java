package com.gtech.promotion.controller.purchaseconstraint;

import com.gtech.commons.result.Result;
import com.gtech.promotion.code.purchaseconstraint.PurchaseConstraintStatusEnum;
import com.gtech.promotion.component.purchaseconstraint.PurchaseConstraintCacheComponent;
import com.gtech.promotion.component.purchaseconstraint.PurchaseConstraintComponent;
import com.gtech.promotion.vo.bean.PurchaseConstraintPriority;
import com.gtech.promotion.vo.param.purchaseconstraint.CreatePurchaseConstraintParam;
import com.gtech.promotion.vo.param.purchaseconstraint.UpdatePurchaseConstraintParam;
import com.gtech.promotion.vo.param.purchaseconstraint.UpdatePurchaseConstraintPriorityParam;
import com.gtech.promotion.vo.param.purchaseconstraint.UpdatePurchaseConstraintStatusParam;
import com.gtech.promotion.vo.param.purchaseconstraint.query.FindPurchaseConstraintParam;
import com.gtech.promotion.vo.param.purchaseconstraint.query.QueryPurchaseConstraintListParam;
import com.gtech.promotion.vo.result.purchaseconstraint.FindPurchaseConstraintResult;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

@RunWith(MockitoJUnitRunner.class)
public class PurchaseConstraintControllerTest {
    @InjectMocks
    private PurchaseConstraintController purchaseConstraintController;

    @Mock
    private PurchaseConstraintComponent purchaseConstraintComponent;

    @Test
    public void queryPurchaseConstraintList(){
        QueryPurchaseConstraintListParam param = new QueryPurchaseConstraintListParam();
        param.setTenantCode("111");
        purchaseConstraintController.queryPurchaseConstraintList(param);
    }

    @Test
    public void updatePurchaseConstraintStatus(){
        UpdatePurchaseConstraintStatusParam param = new UpdatePurchaseConstraintStatusParam();
        param.setTenantCode("100000");
        param.setPurchaseConstraintCode("pcc0001");
        param.setPurchaseConstraintStatus(PurchaseConstraintStatusEnum.IN_AUDIT.getCode());
        Result<String> result = purchaseConstraintController.updatePurchaseConstraintStatus(param);
        Assert.assertTrue(result.isSuccess());
    }

    @Test
    public void createPurchaseConstraint(){
        CreatePurchaseConstraintParam param = new CreatePurchaseConstraintParam();
        param.setTenantCode("1111");
        param.setDomainCode("11111");
        param.setFirstRefusal(0);
        param.setPeriodType("00");
        param.setPurchaseConstraintName("test");
        param.setOrgCode("test");
        Result<String> result = purchaseConstraintController.createPurchaseConstraint(param);
        Assert.assertTrue(result.isSuccess());
    }

    @Test
    public void findPurchaseConstraint(){
        FindPurchaseConstraintParam param = new FindPurchaseConstraintParam();
        param.setTenantCode("1111");
        param.setPurchaseConstraintCode("2222");
        Result<FindPurchaseConstraintResult> result = purchaseConstraintController.findPurchaseConstraint(param);
        Assert.assertTrue(result.isSuccess());
    }

    @Test
    public void updatePurchaseConstraint(){
        UpdatePurchaseConstraintParam param = new UpdatePurchaseConstraintParam();
        param.setTenantCode("1111");
        param.setDomainCode("11111");
        param.setFirstRefusal(0);
        param.setPeriodType("00");
        param.setPurchaseConstraintName("test");
        param.setOrgCode("test");
        param.setPurchaseConstraintCode("testCode");

        Mockito.when(purchaseConstraintComponent.updatePurchaseConstraint(Mockito.any())).thenReturn(1);
        Result<String> result = purchaseConstraintController.updatePurchaseConstraint(param);
        Assert.assertEquals("update success", result.getData());
    }

    @Mock
    private PurchaseConstraintCacheComponent purchaseConstraintCacheComponent;
    @Test
    public void updatePurchaseConstraintPriority(){
        UpdatePurchaseConstraintPriorityParam param = new UpdatePurchaseConstraintPriorityParam();
        param.setTenantCode("111");
        List<PurchaseConstraintPriority> purchaseConstraintPriorities = new ArrayList<>();
        PurchaseConstraintPriority purchaseConstraintPriority = new PurchaseConstraintPriority();
        purchaseConstraintPriority.setPurchaseConstraintCode("1111");
        purchaseConstraintPriority.setPurchaseConstraintPriority(1);
        purchaseConstraintPriorities.add(purchaseConstraintPriority);
        param.setPurchaseConstraintPriorityList(purchaseConstraintPriorities);
        Result result = purchaseConstraintController.updatePurchaseConstraintPriority(param);
        Assert.assertTrue(result.isSuccess());
    }
}
