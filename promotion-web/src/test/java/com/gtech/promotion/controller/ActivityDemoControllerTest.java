package com.gtech.promotion.controller;

import com.gtech.promotion.dto.config.GitProperties;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ActivityDemoControllerTest {

    @InjectMocks
    private DemoController demoController;

    @Mock
    private GitProperties gitProperties;

    @Test
    public void info() {
        Mockito.when(gitProperties.getBuildVersion()).thenReturn("1");
        Mockito.when(gitProperties.getBuildTime()).thenReturn("1");
        Mockito.when(gitProperties.getBranch()).thenReturn("1");
        Mockito.when(gitProperties.getTagName()).thenReturn("1");
        Mockito.when(gitProperties.getCommitIdFull()).thenReturn("1");
        Mockito.when(gitProperties.getCommitIdAbbrev()).thenReturn("1");
        Mockito.when(gitProperties.getCommitMessage()).thenReturn("1");
        Mockito.when(gitProperties.getCommitTime()).thenReturn("1");
        Mockito.when(gitProperties.getCommitUserName()).thenReturn("1");
        Mockito.when(gitProperties.getCommitUserEmail()).thenReturn("1");
        String stringResult = demoController.info(null);
        Assert.assertNotNull(stringResult);
    }

}
