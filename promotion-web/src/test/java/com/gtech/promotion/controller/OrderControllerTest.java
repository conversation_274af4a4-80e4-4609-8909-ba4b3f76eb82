package com.gtech.promotion.controller;

import com.gtech.commons.result.Result;
import com.gtech.promotion.component.activity.ActivityCacheDomain;
import com.gtech.promotion.component.activity.OrderDomain;
import com.gtech.promotion.controller.activity.OrderController;
import com.gtech.promotion.dao.model.activity.ActivityFunctionParamRankModel;
import com.gtech.promotion.dao.model.activity.FunctionParamModel;
import com.gtech.promotion.dao.model.activity.QualificationModel;
import com.gtech.promotion.dao.model.activity.TPromoIncentiveLimitedVO;
import com.gtech.promotion.dto.cache.ActivityCacheDTO;
import com.gtech.promotion.dto.out.activity.ShoppingCartOutDTO;
import com.gtech.promotion.vo.bean.Giveaway;
import com.gtech.promotion.vo.bean.ShoppingCartItem;
import com.gtech.promotion.vo.bean.ShoppingCartStore;
import com.gtech.promotion.vo.param.activity.*;
import com.gtech.promotion.vo.result.activity.CalcShoppingCartResult;
import com.gtech.promotion.vo.result.activity.CreateOrderResult;
import org.apache.commons.collections4.map.HashedMap;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;


@RunWith(MockitoJUnitRunner.class)
public class OrderControllerTest {


    @InjectMocks
    private OrderController orderController;
    @Mock
    private OrderDomain orderDomain;
    @Mock
    private ActivityCacheDomain activityCacheDomain;

    @Test
    public void createOrder_no_reward(){
        CreateOrderParam param = new CreateOrderParam();
        param.setOrderNo("1");
        param.setPromoDeductedAmount(new BigDecimal(0));
        param.setFreePostage(1);
        param.setTenantCode("1");
        param.setDomainCode("1");
        param.setMemberCode("1");
        List<ShoppingCartStore> cartStoreList = new ArrayList<>();
        ShoppingCartStore store = new ShoppingCartStore();
        List<ShoppingCartItem> cartItemList = new ArrayList<>();
        ShoppingCartItem shoppingCartItem = new ShoppingCartItem();
        shoppingCartItem.setSkuCode("1");
        shoppingCartItem.setSelectionFlag("1");
        shoppingCartItem.setProductPrice(new BigDecimal(1));
        shoppingCartItem.setQuantity(1);
        cartItemList.add(shoppingCartItem);
        store.setCartItemList(cartItemList);
        cartStoreList.add(store);
        param.setCartStoreList(cartStoreList);
        List<CreateOrderParam.PromoGiveaway> promoGiveaways = new ArrayList<>();
        CreateOrderParam.PromoGiveaway promoGiveaway = new CreateOrderParam.PromoGiveaway();
        promoGiveaways.add(promoGiveaway);
        Result<CreateOrderResult> order = orderController.createOrder(param);
        Assert.assertEquals(null,order.getData());
    }

    @Test
    public void createOrder(){
        CreateOrderParam param = new CreateOrderParam();
        param.setOrderNo("1");
        param.setPromoDeductedAmount(new BigDecimal(1));
        param.setFreePostage(1);
        param.setTenantCode("1");
        param.setDomainCode("1");
        param.setMemberCode("1");
        List<ShoppingCartStore> cartStoreList = new ArrayList<>();
        ShoppingCartStore store = new ShoppingCartStore();
        List<ShoppingCartItem> cartItemList = new ArrayList<>();
        ShoppingCartItem shoppingCartItem = new ShoppingCartItem();
        shoppingCartItem.setSkuCode("1");
        shoppingCartItem.setSelectionFlag("1");
        shoppingCartItem.setProductPrice(new BigDecimal(1));
        shoppingCartItem.setQuantity(1);
        cartItemList.add(shoppingCartItem);
        store.setCartItemList(cartItemList);
        cartStoreList.add(store);
        param.setCartStoreList(cartStoreList);
        List<CreateOrderParam.PromoGiveaway> promoGiveaways = new ArrayList<>();
        CreateOrderParam.PromoGiveaway promoGiveaway = new CreateOrderParam.PromoGiveaway();
        promoGiveaway.setActivityCode("1");
        List<Giveaway> giveaways = new ArrayList<>();
        promoGiveaway.setGiveaways(giveaways);
        promoGiveaways.add(promoGiveaway);
        Map<String, ActivityCacheDTO> activityCacheMap = new HashedMap<>();
        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();
        List<ActivityFunctionParamRankModel> rankModelList = new ArrayList<>();
        ActivityFunctionParamRankModel model = new ActivityFunctionParamRankModel();
        model.setId("1");
        rankModelList.add(model);
        activityCacheDTO.setPromoFuncRanks(rankModelList);
        List<FunctionParamModel> paramModels = new ArrayList<>();
        FunctionParamModel model1 = new FunctionParamModel();
        model1.setRankId("1");
        paramModels.add(model1);
        activityCacheDTO.setPromoFuncParams(paramModels);
        List<TPromoIncentiveLimitedVO> list = new ArrayList<>();
        activityCacheDTO.setIncentiveLimiteds(list);
        List<QualificationModel> qualificationModels = new ArrayList<>();
        activityCacheDTO.setQualificationModels(qualificationModels);
        activityCacheDTO.setGiveaways(giveaways);
        activityCacheMap.put("1",activityCacheDTO);
        List<ShoppingCartOutDTO> shoppingCartOutDTOS = new ArrayList<>();
        Mockito.when(activityCacheDomain.getActivityCacheMap(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(activityCacheMap);

        Mockito.when(activityCacheDomain.filterActivityByCustomCondition(Mockito.any(), Mockito.any(),  Mockito.any())).thenReturn(activityCacheMap);


        Mockito.when(orderDomain.checkOrder(Mockito.any(), Mockito.any())).thenReturn(shoppingCartOutDTOS);
        Mockito.when(orderDomain.commitOrder(Mockito.any(), Mockito.any(),Mockito.any())).thenReturn(shoppingCartOutDTOS);
        Result<CreateOrderResult> order = orderController.createOrder(param);
        Assert.assertEquals(0,order.getData().getShoppingCartResults().size());

//        Mockito.when(activityCacheDomain.getActivityCacheMap(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenThrow(new DuplicateKeyException("Test"));
//        orderController.createOrder(param);
        promoGiveaway.setActivityCode(null);
        param.setPromoGiveaways(promoGiveaways);
        try {
            orderController.createOrder(param);
        }catch (Exception e){

        }

        param.setPromoDeductedAmount(new BigDecimal("0"));
        param.setPromoGiveaways(null);
        param.setPromoRewardPostage(new BigDecimal("0"));
        orderController.createOrder(param);

    }


    @Test
    public void cancelOrder(){
        CancelOrderParam param = new CancelOrderParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setOrderNo("1");
        Mockito.when(orderDomain.cancelOrder(Mockito.any(), Mockito.any())).thenReturn(1);
        Result<Object> order = orderController.cancelOrder(param);
        Assert.assertEquals(null,order.getData());
    }

    @Test
    public void confirmOrder(){
        ConfirmOrderParam param = new ConfirmOrderParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setOrderNo("1");
        Mockito.when(orderDomain.getPayOrderReduceResource(Mockito.any(), Mockito.any())).thenReturn(1);
        Result<Object> order = orderController.confirmOrder(param);
        Assert.assertEquals(null,order.getData());
    }

    @Test
    public void salesReturnOrder(){
        SalesReturnOrderParam param = new SalesReturnOrderParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setOrderNo("1");
        Mockito.when(orderDomain.salesReturnOrder(Mockito.any(), Mockito.any())).thenReturn(1);
        Result<Object> order = orderController.salesReturnOrder(param);
        Assert.assertEquals(null,order.getData());
    }

    @Test
    public void createOrderAndVerifyCoupon(){
        CreateOrderAndCouponParam param = new CreateOrderAndCouponParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setOrderNo("1");
        param.setCouponCodes("1");
        param.setMemberCode("1");
        Mockito.doNothing().when(orderDomain).commitOrderAndVerify(Mockito.any());
        Result<Object> order = orderController.createOrderAndVerifyCoupon(param);
        Assert.assertEquals(null,order.getData());
    }

    @Test
    public void buildGiveaways(){
        CreateOrderParam param = new CreateOrderParam();
        CreateOrderResult createOrderResult = new CreateOrderResult();
        orderController.buildGiveaways(param,createOrderResult);
        List<CreateOrderParam.PromoGiveaway> promoGiveaways = new ArrayList<>();
        CreateOrderParam.PromoGiveaway gv = new CreateOrderParam.PromoGiveaway();
        gv.setActivityCode("test");
        List<Giveaway> giveaways = new ArrayList<>();
        Giveaway giveaway = new Giveaway();
        giveaway.setGiveawayNum(2);
        giveaway.setGiveawayCode("gv");
        giveaways.add(giveaway);
        gv.setGiveaways(giveaways);
        promoGiveaways.add(gv);
        param.setPromoGiveaways(promoGiveaways);
        orderController.buildGiveaways(param,createOrderResult);

        List<CalcShoppingCartResult> shoppingCartResults = new ArrayList<>();
        CalcShoppingCartResult calcShoppingCartResult = new CalcShoppingCartResult();
        shoppingCartResults.add(calcShoppingCartResult);
        createOrderResult.setShoppingCartResults(shoppingCartResults);
        orderController.buildGiveaways(param,createOrderResult);

        calcShoppingCartResult.setGiveaways(giveaways);
        orderController.buildGiveaways(param,createOrderResult);
    }
}
