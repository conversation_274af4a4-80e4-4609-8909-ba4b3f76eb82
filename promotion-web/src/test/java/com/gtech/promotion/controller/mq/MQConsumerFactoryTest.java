package com.gtech.promotion.controller.mq;

import com.alibaba.rocketmq.client.consumer.DefaultMQPushConsumer;
import com.gtech.promotion.PromotionWebApplication;
import com.gtech.promotion.mq.MQConsumerFactory;
import com.gtech.promotion.pojo.MqEnums;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {PromotionWebApplication.class })
@ActiveProfiles("dev")
@Slf4j
public class MQConsumerFactoryTest{
    @Test
    public void test(){
        MQConsumerFactory factory = MQConsumerFactory.getInstance();
        DefaultMQPushConsumer consumer = new DefaultMQPushConsumer();
        factory.addConsumer("test",consumer);

        factory.getConsumer();
        factory.getConsumer(MqEnums.EXCEL_REQUEST_MQ);
    }
}
