package com.gtech.promotion.controller;

import com.gtech.commons.result.Result;
import com.gtech.promotion.component.coupon.CouponCodeUserComponent;
import com.gtech.promotion.component.coupon.CouponInnerCodeComponent;
import com.gtech.promotion.controller.coupon.CouponCodeUserController;
import com.gtech.promotion.dto.in.coupon.BindingCouponInDTO;
import com.gtech.promotion.vo.param.coupon.ExportCouponDetailParam;
import com.gtech.promotion.vo.result.coupon.ExportCouponUserDetailResult;
import com.gtech.promotion.vo.result.coupon.ExportCouponUserResult;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/3/30 9:54
 */
@RunWith(MockitoJUnitRunner.class)
public class CouponCodeUserControllerMockTest {

    @InjectMocks
    private CouponCodeUserController couponCodeUserController;

    @Mock
    private CouponInnerCodeComponent couponInnerCodeDomain;

    @Mock
    private CouponCodeUserComponent couponCodeUserComponent;

    @Test
    public void bindingCoupon(){
        BindingCouponInDTO bindingCouponInDTO = new BindingCouponInDTO();
        bindingCouponInDTO.setTenantCode("1");
        bindingCouponInDTO.setActivityCode("1");
        bindingCouponInDTO.setValidEndTime("20210330235959");
        bindingCouponInDTO.setValidStartTime("20210330100611");
        List<BindingCouponInDTO.UserAndCoupon> userCoupons = new ArrayList<>();
        BindingCouponInDTO.UserAndCoupon userAndCoupon = new BindingCouponInDTO.UserAndCoupon();
        userAndCoupon.setCouponCode("1");
        userAndCoupon.setUserCode("1");
        userCoupons.add(userAndCoupon);
        bindingCouponInDTO.setUserCoupons(userCoupons);

        Mockito.when(couponInnerCodeDomain.bindingUserAndCoupon(Mockito.any())).thenReturn(1);
        Result<Object> objectResult = couponCodeUserController.bindingCoupon(bindingCouponInDTO);
        Assert.assertTrue(objectResult.isSuccess());

        try {
            bindingCouponInDTO.setValidStartTime("test");
            couponCodeUserController.bindingCoupon(bindingCouponInDTO);
        }catch (Exception e){

        }

        try {
            bindingCouponInDTO.setValidEndTime("test");
            bindingCouponInDTO.setValidStartTime("20210330100611");
            couponCodeUserController.bindingCoupon(bindingCouponInDTO);
        }catch (Exception e){

        }

        try {
            bindingCouponInDTO.setValidEndTime("20210330235959");
            bindingCouponInDTO.setValidStartTime("20220330100611");
            couponCodeUserController.bindingCoupon(bindingCouponInDTO);
        }catch (Exception e){

        }

    }

    @Test
    public void exportCouponDetail(){
        ExportCouponDetailParam param = new ExportCouponDetailParam();
        param.setActivityCode("1");
        param.setTenantCode("1");
        param.setDomainCode("1");

        Mockito.when(couponCodeUserComponent.exportCouponDetail(Mockito.any())).thenReturn(new ArrayList<>());
        Result<List<ExportCouponUserResult>> listResult = couponCodeUserController.exportCouponDetail(param);
        Assert.assertTrue(listResult.isSuccess());
    }

    @Test
    public void exportCouponOrderDetail(){
        ExportCouponDetailParam param = new ExportCouponDetailParam();
        param.setActivityCode("1");
        param.setTenantCode("1");
        param.setDomainCode("1");

        Mockito.when(couponCodeUserComponent.exportCouponOrderDetail(Mockito.any())).thenReturn(new ArrayList<>());
        Result<List<ExportCouponUserDetailResult>> listResult = couponCodeUserController.exportCouponOrderDetail(param);
        Assert.assertTrue(listResult.isSuccess());
    }
}
