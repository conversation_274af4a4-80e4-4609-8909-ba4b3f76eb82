package com.gtech.promotion.controller.flashsale;

import com.gtech.promotion.component.marketing.MarketingCacheComponent;
import com.gtech.promotion.vo.param.activity.CancelOrderParam;
import com.gtech.promotion.vo.param.activity.ConfirmOrderParam;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class MallFlashSaleControllerExceptionTest {

    @InjectMocks
    private MallFlashSaleController mallFlashSaleController;
    @Mock
    private MarketingCacheComponent marketingCacheComponent;


    @Test(expected = Exception.class)
    public void cancelOrder_exception(){
        CancelOrderParam param = new CancelOrderParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setOrderNo("1");
        mallFlashSaleController.cancelOrder(param);

    }

    @Test(expected = Exception.class)
    public void confirmOrder(){
        ConfirmOrderParam param = new ConfirmOrderParam();
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setOrderNo("1");
        mallFlashSaleController.confirmOrder(param);
    }
}
