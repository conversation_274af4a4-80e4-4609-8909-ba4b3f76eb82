package com.gtech.promotion.controller.Group;

import com.gtech.promotion.dao.entity.activity.PromoGroupEntity;
import com.gtech.promotion.dao.entity.activity.PromoGroupRelationEntity;
import com.gtech.promotion.dao.mapper.activity.ActivityMapper;
import com.gtech.promotion.dao.mapper.activity.PromoGroupMapper;
import com.gtech.promotion.dao.mapper.activity.PromoGroupRelationMapper;
import com.gtech.promotion.dao.mapper.marketing.MarketingMapper;
import com.gtech.promotion.dao.model.activity.GroupQueryVO;
import com.gtech.promotion.dao.model.activity.PromoGroupMode;
import com.gtech.promotion.dao.model.activity.PromoGroupVO;
import com.gtech.promotion.dto.in.activity.GroupPriorityVO;
import com.gtech.promotion.service.impl.activity.PromotionGroupServiceImpl;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/6/16 14:08
 */
@RunWith(MockitoJUnitRunner.class)

public class PromotionGroupServiceTest {

    @InjectMocks
    private PromotionGroupServiceImpl promotionGroupService;

    @Mock
    private PromoGroupMapper promoGroupMapper;
    @Mock
    private ActivityMapper activityMapper;
    @Mock
    private MarketingMapper marketingMapper;
    @Mock
    private PromoGroupRelationMapper promoGroupRelationMapper;
    @Before
    public void before() {
        EntityHelper.initEntityNameMap(PromoGroupEntity.class, new MapperHelper().getConfig());
        EntityHelper.initEntityNameMap(PromoGroupRelationEntity.class, new MapperHelper().getConfig());
    }

    @Test
    public void createGroupDomain() {

        PromoGroupMode param = new PromoGroupMode();
        param.setTenantCode("1");
        param.setGroupName("1");
        param.setUpdateUser("1");
        param.setCreateUser("1");
        param.setGroupCode("!");
        param.setLogicDelete("0");
        param.setType("02");
        param.setTenantCode("!");
        param.setPriority(1L);
        Mockito.when(promoGroupMapper.insertSelective(Mockito.any())).thenReturn(1);

        int i = promotionGroupService.insertActivityGroup(param);
        Assert.assertEquals(1, i);
    }

    @Test
    public void updateActivityGroup() {

        PromoGroupMode param = new PromoGroupMode();
        param.setTenantCode("1");
        param.setGroupName("1");
        param.setUpdateUser("1");
        param.setCreateUser("1");
        param.setGroupCode("!");
        param.setLogicDelete("0");
        param.setType("02");
        param.setTenantCode("!");
        param.setPriority(1L);
        Mockito.when(promoGroupMapper.updateByConditionSelective(Mockito.any(), Mockito.any())).thenReturn(1);

        int i = promotionGroupService.updateActivityGroup(param);
        Assert.assertEquals(1, i);
    }

    @Test
    public void deleteActivityGroup() {

        PromoGroupMode param = new PromoGroupMode();
        param.setTenantCode("1");
        param.setGroupName("1");
        param.setUpdateUser("1");
        param.setCreateUser("1");
        param.setGroupCode("!");
        param.setLogicDelete("0");
        param.setType("02");
        param.setTenantCode("!");
        param.setPriority(1L);
        Mockito.when(promoGroupMapper.updateByConditionSelective(Mockito.any(), Mockito.any())).thenReturn(1);

        int i = promotionGroupService.deleteActivityGroup("1", "1", "1");
        Assert.assertEquals(1, i);
    }

    @Test
    public void getLastedGroup_null() {

        PromoGroupMode param = new PromoGroupMode();
        param.setTenantCode("1");
        param.setGroupName("1");
        param.setUpdateUser("1");
        param.setCreateUser("1");
        param.setGroupCode("!");
        param.setLogicDelete("0");
        param.setType("02");
        param.setTenantCode("!");
        param.setPriority(1L);
        Mockito.when(promoGroupMapper.selectByCondition(Mockito.any())).thenReturn(new ArrayList<>());

        PromoGroupVO i = promotionGroupService.getLastedGroup("1");
        Assert.assertEquals(null, i);
    }


    @Test
    public void getLastedGroup() {

        PromoGroupMode param = new PromoGroupMode();
        param.setTenantCode("1");
        param.setGroupName("1");
        param.setUpdateUser("1");
        param.setCreateUser("1");
        param.setGroupCode("!");
        param.setLogicDelete("0");
        param.setType("02");
        param.setTenantCode("!");
        param.setPriority(1L);
        List<PromoGroupEntity> promoGroupEntities = new ArrayList<>();
        PromoGroupEntity promoGroupEntity = new PromoGroupEntity();
        promoGroupEntity.setGroupCode("1");
        promoGroupEntities.add(promoGroupEntity);
        Mockito.when(promoGroupMapper.selectByCondition(Mockito.any())).thenReturn(promoGroupEntities);

        PromoGroupVO i = promotionGroupService.getLastedGroup("1");
		Assert.assertEquals("1", i.getGroupCode());
}


    @Test
    public void queryActivityGroupList() {

        GroupQueryVO groupQueryVO1 = new GroupQueryVO();

        groupQueryVO1.setGroupCode("1");
        groupQueryVO1.setGroupName("!");
        groupQueryVO1.setTenantCode("1");
        groupQueryVO1.setLogicDelete("0");

        List<PromoGroupEntity> promoGroupEntities = new ArrayList<>();

        PromoGroupEntity entity = new PromoGroupEntity();

        entity.setGroupCode("1");
        entity.setGroupName("!");
        entity.setTenantCode("1");
        entity.setLogicDelete(0);
        promoGroupEntities.add(entity);

        Mockito.when(promoGroupMapper.selectByCondition(Mockito.any())).thenReturn(promoGroupEntities);

        List<PromoGroupMode> promoGroupModes = promotionGroupService.queryActivityGroupList(groupQueryVO1);

        Assert.assertEquals(1, promoGroupModes.size());
    }

    @Test
    public void queryActivityGroupList_empty() {

        GroupQueryVO groupQueryVO1 = new GroupQueryVO();

        groupQueryVO1.setTenantCode("1");
        groupQueryVO1.setLogicDelete("0");

        List<PromoGroupEntity> promoGroupEntities = new ArrayList<>();

        PromoGroupEntity entity = new PromoGroupEntity();

        entity.setGroupCode("1");
        entity.setGroupName("!");
        entity.setTenantCode("1");
        entity.setLogicDelete(0);
        promoGroupEntities.add(entity);

        Mockito.when(promoGroupMapper.selectByCondition(Mockito.any())).thenReturn(promoGroupEntities);

        List<PromoGroupMode> promoGroupModes = promotionGroupService.queryActivityGroupList(groupQueryVO1);

        Assert.assertEquals(1, promoGroupModes.size());
    }

    @Test
    public void listActivityGroupByTenantCode(){

        List<PromoGroupEntity> promoGroupEntities = new ArrayList<>();

        PromoGroupEntity entity = new PromoGroupEntity();

        entity.setGroupCode("1");
        entity.setGroupName("!");
        entity.setTenantCode("1");
        entity.setLogicDelete(0);
        promoGroupEntities.add(entity);

        Mockito.when(promoGroupMapper.selectByCondition(Mockito.any())).thenReturn(promoGroupEntities);

        List<PromoGroupVO> promoGroupVOS = promotionGroupService.listActivityGroupByTenantCode("1");

        Assert.assertEquals(1, promoGroupVOS.size());
    }

    @Test
    public void getGroupByGroupCode(){

        PromoGroupEntity promoGroupEntity = new PromoGroupEntity();
        promoGroupEntity.setGroupCode("2");
        Mockito.when(promoGroupMapper.selectOne(Mockito.any())).thenReturn(promoGroupEntity);

        PromoGroupVO groupByGroupCode = promotionGroupService.getGroupByGroupCode("1", "2");

        Assert.assertEquals("2",groupByGroupCode.getGroupCode());
    }


    @Test
    public void queryActivityGroupList11() {

        GroupQueryVO vo = new GroupQueryVO();
        vo.setGroupCode("!");
        vo.setGroupName("!");


        PromoGroupMode param = new PromoGroupMode();
        param.setTenantCode("1");
        param.setGroupName("1");
        param.setUpdateUser("1");
        param.setCreateUser("1");
        param.setGroupCode("!");
        param.setLogicDelete("0");
        param.setType("02");
        param.setTenantCode("!");
        param.setPriority(1L);

        List<PromoGroupEntity> promoGroupEntities = new ArrayList<>();
        Mockito.when(promoGroupMapper.selectByCondition(Mockito.any())).thenReturn(promoGroupEntities);

        List<PromoGroupMode> promoGroupModes = promotionGroupService.queryActivityGroupList(vo);
        Assert.assertEquals(0, promoGroupModes.size());
    }

    @Test
    public void updateActivityGroupPriority(){

        GroupPriorityVO groupPriorityVO = new GroupPriorityVO();
        groupPriorityVO.setTenantCode("!");

        List<GroupPriorityVO.ActivityGroup> groups = new ArrayList<>();
        GroupPriorityVO.ActivityGroup group = new GroupPriorityVO.ActivityGroup();
        group.setGroupCode("1");
        group.setPriority(1L);
        groups.add(group);
        groupPriorityVO.setGroups(groups);
        promotionGroupService.updateActivityGroupPriority(groupPriorityVO);
    }

    @Test
    public void queryGroupByGroupCode_empty(){

        GroupPriorityVO groupPriorityVO = new GroupPriorityVO();
        groupPriorityVO.setTenantCode("!");

        List<GroupPriorityVO.ActivityGroup> groups = new ArrayList<>();
        GroupPriorityVO.ActivityGroup group = new GroupPriorityVO.ActivityGroup();
        group.setGroupCode("1");
        group.setPriority(1L);
        groups.add(group);
        groupPriorityVO.setGroups(groups);
        List<PromoGroupVO> promoGroupVOS = promotionGroupService.queryGroupByGroupCode("1", new ArrayList<>());
        Assert.assertEquals(0,promoGroupVOS.size());
    }

    @Test
    public void refreshActivityGroupData(){

        promotionGroupService.refreshActivityGroupData("domain","tenant");

    }





    @Test
    public void queryGroupByGroupCode(){

        GroupPriorityVO groupPriorityVO = new GroupPriorityVO();
        groupPriorityVO.setTenantCode("!");

        List<GroupPriorityVO.ActivityGroup> groups = new ArrayList<>();
        GroupPriorityVO.ActivityGroup group = new GroupPriorityVO.ActivityGroup();
        group.setGroupCode("1");
        group.setPriority(1L);
        groups.add(group);
        groupPriorityVO.setGroups(groups);

        List<String> stringList = new ArrayList<>();
        stringList.add("!");
        List<PromoGroupVO> promoGroupVOS = promotionGroupService.queryGroupByGroupCode("1", stringList);
        Assert.assertEquals(0,promoGroupVOS.size());
    }

}
