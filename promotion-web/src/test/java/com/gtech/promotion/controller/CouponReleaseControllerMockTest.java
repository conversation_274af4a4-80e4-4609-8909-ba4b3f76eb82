package com.gtech.promotion.controller;

import com.gtech.commons.result.Result;
import com.gtech.promotion.component.coupon.CouponReleaseComponent;
import com.gtech.promotion.controller.coupon.CouponReleaseController;
import com.gtech.promotion.exception.PromotionParamValidateException;
import com.gtech.promotion.vo.bean.MemberInfo;
import com.gtech.promotion.vo.param.coupon.CreateCouponReleaseParam;
import com.gtech.promotion.vo.param.coupon.SendCouponParam;
import com.gtech.promotion.vo.param.coupon.UaCreateSendCouponParam;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/3/30 10:12
 */
@RunWith(MockitoJUnitRunner.class)
public class CouponReleaseControllerMockTest {

    @InjectMocks
    private CouponReleaseController couponReleaseController;

    @Mock
    private CouponReleaseComponent couponReleaseComponent;

    @Test
    public void createReleaseAndSendCoupon(){

        UaCreateSendCouponParam param = new UaCreateSendCouponParam();


        param.setActivityCode("1");
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setReleaseType("01");
        param.setReceiveStart("20210330102130");
        param.setReceiveEnd("20210330235959");
        param.setReleaseQuantity(10);
        param.setValidEnd("20210330235959");
        param.setValidStart("20210330102130");
        param.setTakeLabel("1");
        param.setUserCode("!");
        List<String> couponCodes = new ArrayList<>();
        couponCodes.add("1");
        couponCodes.add(null);
        couponCodes.add(null);
        param.setCouponCodes(couponCodes);

        SendCouponParam sendCouponParam = new SendCouponParam();
        sendCouponParam.setDomainCode("1");
        sendCouponParam.setTenantCode("1");
        sendCouponParam.setActivityCode("1");
        sendCouponParam.setTakeLabel("01");
        sendCouponParam.setReceiveCount(1);
        sendCouponParam.setOperatorCode("11");
        sendCouponParam.setUserCodes("11");
        List<MemberInfo> userList = new ArrayList<>();
        MemberInfo memberInfo = new MemberInfo();
        memberInfo.setUserCode("1");
        userList.add(memberInfo);
        sendCouponParam.setUserList(userList);
        param.setCouponCodes(Arrays.asList("11"));


        Result<String> couponRelease = couponReleaseController.createUaReleaseAndSendCoupon(param);
        Assert.assertTrue(couponRelease.isSuccess());
    }

    @Test
    public void createReleaseAndSendCoupon_null(){

        UaCreateSendCouponParam param = new UaCreateSendCouponParam();

        param.setActivityCode("1");
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setReleaseType("01");
        param.setReceiveStart("20210330102130");
        param.setReceiveEnd("20210330235959");
        param.setReleaseQuantity(10);
        param.setValidEnd("20210330235959");
        param.setValidStart("20210330102130");
        List<String> couponCodes = new ArrayList<>();
        couponCodes.add("1");
        couponCodes.add(null);
        couponCodes.add(null);
        param.setCouponCodes(null);

        SendCouponParam sendCouponParam = new SendCouponParam();
        sendCouponParam.setDomainCode("1");
        sendCouponParam.setTenantCode("1");
        sendCouponParam.setActivityCode("1");
        sendCouponParam.setTakeLabel("01");
        sendCouponParam.setReceiveCount(1);
        List<MemberInfo> userList = new ArrayList<>();
        MemberInfo memberInfo = new MemberInfo();
        memberInfo.setUserCode("1");
        userList.add(memberInfo);
        sendCouponParam.setUserList(userList);
        param.setTakeLabel("1");
        param.setUserCode("!");


        param.setCouponCodes(Arrays.asList("11"));
        Result<String> couponRelease = couponReleaseController.createUaReleaseAndSendCoupon(param);
        Assert.assertTrue(couponRelease.isSuccess());
    }

    @Test
    public void createCouponRelease(){
        CreateCouponReleaseParam param = new CreateCouponReleaseParam();
        param.setActivityCode("1");
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setReleaseType("01");
        param.setReceiveStart("20210330102130");
        param.setReceiveEnd("20210330235959");
        param.setReleaseQuantity(10);
        param.setValidEnd("20210330235959");
        param.setValidStart("20210330102130");
        List<String> couponCodes = new ArrayList<>();
        couponCodes.add("1");
		couponCodes.add(null);
		couponCodes.add(null);
        param.setCouponCodes(couponCodes);

        Mockito.when(couponReleaseComponent.releaseCoupon(Mockito.any(),Mockito.anyMap(),Mockito.any())).thenReturn("1");
        Result<String> couponRelease = couponReleaseController.createCouponRelease(param);
        Assert.assertTrue(couponRelease.isSuccess());
    }

    @Test
    public void createCouponRelease_null(){
        CreateCouponReleaseParam param = new CreateCouponReleaseParam();
        param.setActivityCode("1");
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setReleaseType("01");
        param.setReceiveStart("20210330102130");
        param.setReceiveEnd("20210330235959");
        param.setReleaseQuantity(10);
        param.setValidEnd("20210330235959");
        param.setValidStart("20210330102130");
        List<String> couponCodes = new ArrayList<>();
        param.setCouponCodes(couponCodes);
        Result<String> couponRelease = couponReleaseController.createCouponRelease(param);
        Assert.assertTrue(couponRelease.isSuccess());
    }

    @Test
    public void createUaReleaseAndSendCoupon(){
        UaCreateSendCouponParam param = new UaCreateSendCouponParam();
        param.setActivityCode("1");
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setReleaseType("01");
        param.setReceiveStart("20210330102130");
        param.setReceiveEnd("20210330235959");
        param.setReleaseQuantity(10);
        param.setValidEnd("20210330235959");
        param.setValidStart("20210330102130");
        List<String> couponCodes = new ArrayList<>();
        couponCodes.add("1");
        couponCodes.add(null);
        couponCodes.add(null);
        param.setCouponCodes(couponCodes);
        param.setTakeLabel("01");
        param.setUserCode("1");
        Mockito.when(couponReleaseComponent.createUaReleaseAndSendCoupon(Mockito.any(),Mockito.anyMap())).thenReturn("1");
        Result<String> couponRelease = couponReleaseController.createUaReleaseAndSendCoupon(param);
        Assert.assertTrue(couponRelease.isSuccess());
    }


    @Test(expected = PromotionParamValidateException.class)
    public void createUaReleaseAndSendCoupon_null(){

        UaCreateSendCouponParam param = new UaCreateSendCouponParam();
        param.setActivityCode("1");
        param.setDomainCode("1");
        param.setTenantCode("1");
        param.setReleaseType("01");
        param.setReceiveStart("20210330102130");
        param.setReceiveEnd("20210330235959");
        param.setReleaseQuantity(10);
        param.setValidEnd("20210330235959");
        param.setValidStart("20210330102130");
        List<String> couponCodes = new ArrayList<>();
        couponCodes.add("1");
        couponCodes.add(null);
        couponCodes.add(null);
        param.setCouponCodes(null);
        param.setTakeLabel("01");
        param.setUserCode("1");

        //Mockito.when(couponReleaseComponent.createUaReleaseAndSendCoupon(Mockito.any(),Mockito.anyMap())).thenReturn("1");
        Result<String> couponRelease = couponReleaseController.createUaReleaseAndSendCoupon(param);
        Assert.assertTrue(couponRelease.isSuccess());
    }


    @Test
    public void couponReleaseTimer(){
        Mockito.doNothing().when(couponReleaseComponent).couponReleaseTimer();
        Result<Object> objectResult = couponReleaseController.couponReleaseTimer();
        Assert.assertTrue(objectResult.isSuccess());
    }
}
