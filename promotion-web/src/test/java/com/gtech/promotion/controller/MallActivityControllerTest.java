package com.gtech.promotion.controller;

import com.github.pagehelper.PageInfo;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.promotion.code.activity.ActivityTypeEnum;
import com.gtech.promotion.code.activity.OpsTypeEnum;
import com.gtech.promotion.component.activity.*;
import com.gtech.promotion.controller.activity.MallActivityController;
import com.gtech.promotion.dao.mapper.activity.ActivityMapper;
import com.gtech.promotion.dao.model.activity.*;
import com.gtech.promotion.dto.cache.ActivityCacheDTO;
import com.gtech.promotion.dto.in.activity.ShoppingCartActivity;
import com.gtech.promotion.dto.in.activity.ShoppingCartDTO;
import com.gtech.promotion.dto.out.activity.ActivityInfoDTO;
import com.gtech.promotion.dto.out.activity.SkuActivityPriceDTO;
import com.gtech.promotion.dto.out.activity.TPromoActivityOutDTO;
import com.gtech.promotion.exception.PromotionException;
import com.gtech.promotion.service.activity.ActivityProductDetailService;
import com.gtech.promotion.service.activity.ActivityStoreService;
import com.gtech.promotion.service.coupon.PromoCouponReleaseService;
import com.gtech.promotion.vo.bean.Giveaway;
import com.gtech.promotion.vo.bean.ShoppingCartItem;
import com.gtech.promotion.vo.bean.ShoppingCartStore;
import com.gtech.promotion.vo.param.activity.*;
import com.gtech.promotion.vo.result.activity.*;
import org.apache.commons.collections4.map.HashedMap;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.*;

import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class MallActivityControllerTest {

    @InjectMocks
    private MallActivityController mallActivityController;

    @Mock
    private ActivityCacheDomain activityCacheDomain;


    @Mock
    private ActivityComponentDomain tPromoActivityDomain;

    @Mock
    private ActivityQueryDomain queryDomain;

    @Mock
    private ShoppingCartDomain shoppingCartDomain;

    @Mock
    private ProductDomain productDomain;

    @Mock
    private ActivityProductDetailService productDetailService;

    @Mock
    private ActivityStoreService activityStoreService;

    @Mock
    private ActivityMapper activityMapper;

    @Mock
    private PromoCouponReleaseService promoCouponReleaseService;


//    @Mock
//    private ExecutorService promotionThreadPoolConfig;

    @Test
    public void queryActivityListByProductList(){
        QueryActivityListByProductListParam param = new QueryActivityListByProductListParam();
        param.setDomainCode("!");
        param.setTenantCode("!");
        ArrayList<QueryActivityListByProductListParam.Product> products = new ArrayList<>();
        QueryActivityListByProductListParam.Product product = new QueryActivityListByProductListParam.Product();
        product.setSkuCode("!");
        product.setProductCode("!");
        products.add(product);
        QueryActivityListByProductListParam.Product product1 = new QueryActivityListByProductListParam.Product();
        product1.setSkuCode("!");
        product1.setProductCode("!");
        product1.setOrgCodes("!");
        products.add(product1);
        param.setProducts(products);
        Map<String, ActivityCacheDTO> activityCacheMap = new HashedMap<>();
        when(activityCacheDomain.getActivityCacheMap(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(activityCacheMap);
//        ExecutorService mock = mock(ExecutorService.class);
//        mock.execute(Mockito.any());
//        when(queryDomain.getActivityByProduct(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new ArrayList<>());
        when(productDetailService.queryListByActivityCodesAndProductCodes(Mockito.any(), Mockito.any())).thenReturn(new ArrayList<>());
        Result<List<QueryActivityListByProductListResult>> listResult = mallActivityController.queryActivityListByProductList(param);
        Assert.assertEquals(2, listResult.getData().size());

    }


    @Test
    public void testFuncParam() {
        List<QueryActivityListByProductListResult> list = new ArrayList<>();
        QueryActivityListByProductListResult productListResult = new QueryActivityListByProductListResult();
        List<QueryActivityListByProductResult> activityList = new ArrayList<>();
        QueryActivityListByProductResult activity = new QueryActivityListByProductResult();
        activity.setOpsType(OpsTypeEnum.OPS_101.code());
        activity.setActivityCode("testCode");
        activityList.add(activity);
        productListResult.setActivityList(activityList);
        list.add(productListResult);


        HashMap<String, ActivityCacheDTO> activityCacheMap = new HashMap<>();
        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();
        activityCacheDTO.setPromoFuncRanks(new ArrayList<>());
        activityCacheDTO.setPromoFuncParams(new ArrayList<>());
        activityCacheMap.put("testCode", activityCacheDTO);

        mallActivityController.funcParam(activityCacheMap, list);

        QueryActivityListByProductResult result = productListResult.getActivityList().get(0);

    }


    @Test
    public void calcSkuPromotionPrice(){
        CalcSkuPromotionPriceParam param = new CalcSkuPromotionPriceParam();
        param.setTenantCode("1");
        param.setDomainCode("1");
        param.setPrice(new BigDecimal(11));
        when(activityCacheDomain.getActivityCacheMap(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new HashMap<>());
        when(tPromoActivityDomain.activitySkuPrice(Mockito.any(), Mockito.any())).thenReturn(new SkuActivityPriceDTO());
        Result<CalcSkuPromotionPriceResult> result = mallActivityController.calcSkuPromotionPrice(param);
        Assert.assertNull(result.getData());
    }

    @Test
    public void queryActivityListByProduct_org_not_empty(){
        QueryActivityListByProductParam param = new QueryActivityListByProductParam();
        param.setTenantCode("1");
        param.setDomainCode("1");
        param.setProductCode("1341");
        param.setOrgCodes("431");
        Result<List<QueryActivityListByProductResult>> result = mallActivityController.queryActivityListByProduct(param);
        Assert.assertEquals(0,result.getData().size());
    }
    @Test
    public void queryActivityListByProduct_org_empty(){
        QueryActivityListByProductParam param = new QueryActivityListByProductParam();
        param.setTenantCode("1");
        param.setDomainCode("1");
        param.setProductCode("1341");
        Result<List<QueryActivityListByProductResult>> result = mallActivityController.queryActivityListByProduct(param);
        Assert.assertEquals(0,result.getData().size());
    }

    @Test
    public void queryProductListByActivity(){
        QueryProductListByActivityParam param = new QueryProductListByActivityParam();
        param.setTenantCode("1");
        param.setDomainCode("1");
        param.setActivityCode("1");
        param.setSeqNum(null);
        when(productDomain.queryActivityProductByCode(Mockito.any(), Mockito.any(), Mockito.any(),Mockito.anyInt(), Mockito.any())).thenReturn(null);
        Result<QueryProductListByActivityResult> result = mallActivityController.queryProductListByActivity(param);
        Assert.assertEquals(null,result.getData());
    }

    @Test
    public void calcShoppingCart(){

        CalcShoppingCartParam param = new CalcShoppingCartParam();
        param.setTenantCode("1");
        param.setDomainCode("1");
        param.setMemberCode("1");
        List<ShoppingCartStore> cartStoreList = new ArrayList<>();
        ShoppingCartStore store = new ShoppingCartStore();
         List<ShoppingCartItem> cartItemList = new ArrayList<>();
        ShoppingCartItem shoppingCartItem = new ShoppingCartItem();
        shoppingCartItem.setSkuCode("1");
        shoppingCartItem.setSelectionFlag("1");
        shoppingCartItem.setProductPrice(new BigDecimal(1));
        shoppingCartItem.setQuantity(1);
        cartItemList.add(shoppingCartItem);
        store.setCartItemList(cartItemList);
        cartStoreList.add(store);
        param.setCartStoreList(cartStoreList);
        when(activityCacheDomain.getActivityCacheMap(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new HashMap<>());
        Result<List<CalcShoppingCartResult>> result = mallActivityController.calcShoppingCart(param);
        Assert.assertEquals(0,result.getData().size());
    }



    @Test(expected = PromotionException.class)
    public void calcShoppingCart_coupons(){

        CalcShoppingCartParam param = new CalcShoppingCartParam();
        param.setTenantCode("1");
        param.setDomainCode("1");
        param.setMemberCode("1");
        List<ShoppingCartStore> cartStoreList = new ArrayList<>();
        ShoppingCartStore store = new ShoppingCartStore();
        List<ShoppingCartItem> cartItemList = new ArrayList<>();
        ShoppingCartItem shoppingCartItem = new ShoppingCartItem();
        shoppingCartItem.setSkuCode("1");
        shoppingCartItem.setSelectionFlag("1");
        shoppingCartItem.setProductPrice(new BigDecimal(1));
        shoppingCartItem.setQuantity(1);
        cartItemList.add(shoppingCartItem);
        store.setCartItemList(cartItemList);
        cartStoreList.add(store);
        param.setCartStoreList(cartStoreList);
        param.setCouponCodes("1");
        when(activityCacheDomain.getActivityCacheMap(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new HashMap<>());
        Result<List<CalcShoppingCartResult>> result = mallActivityController.calcShoppingCart(param);
        Assert.assertEquals(0,result.getData().size());
    }


    @Test
    public void queryActivityByShoppingCartProduct_cache_empty(){
        CalcShoppingCartParam param = new CalcShoppingCartParam();
        param.setTenantCode("1");
        param.setDomainCode("1");
        param.setMemberCode("1");
        List<ShoppingCartStore> cartStoreList = new ArrayList<>();
        ShoppingCartStore store = new ShoppingCartStore();
        store.setOrgCode("1");
        store.setStoreName("1");
        List<ShoppingCartItem> items = new ArrayList<>();
       ShoppingCartItem item = new com.gtech.promotion.vo.bean.ShoppingCartItem();
        item.setSelectionFlag("01");
        item.setProductPrice(new BigDecimal(10));
        item.setQuantity(1);
        item.setSkuCode("1");
        items.add(item);
        store.setCartItemList(items);
        cartStoreList.add(store);
        param.setCartStoreList(cartStoreList);
        ShoppingCartDTO shoppingCart = new ShoppingCartDTO();
        List<com.gtech.promotion.dto.in.activity.ShoppingCartItem>  products = new ArrayList<>();
        com.gtech.promotion.dto.in.activity.ShoppingCartItem shoppingCartItem = new com.gtech.promotion.dto.in.activity.ShoppingCartItem();
        List<ShoppingCartActivity> activityList =new ArrayList<>();
        ShoppingCartActivity activity = new ShoppingCartActivity();
        activityList.add(activity);
        shoppingCartItem.setUsedActivitys(activityList);
        products.add(shoppingCartItem);
        shoppingCart.setPromoProducts(products);
        Map<String, ActivityCacheDTO> activityCacheMap = new HashedMap<>();
        when(activityCacheDomain.getActivityCacheMap(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(activityCacheMap);
        Result<List<QueryActivityByProductListResult>> listResult = mallActivityController.queryActivityByShoppingCartProduct(param);
        Assert.assertNotNull(listResult);
    }

    @Test
    public void queryPromoListByStore(){
        QueryPromoListByStoreParam param = new QueryPromoListByStoreParam();
        param.setTenantCode("1");
        param.setDomainCode("1");
        param.setOrgCode("1");
        param.setLanguage("12");
        PageInfo<TPromoActivityOutDTO> pageInfo = new PageInfo<>();
        when(tPromoActivityDomain.queryPromoListByStore(Mockito.any())).thenReturn(pageInfo);
        PageResult<QueryPromoListByStoreResult> result = mallActivityController.queryPromoListByStore(param);
        Assert.assertEquals(0,result.getData().getList().size());
    }

    @Test
    public void queryActivityByShoppingCartProduct(){
        CalcShoppingCartParam param = new CalcShoppingCartParam();
        param.setTenantCode("1");
        param.setDomainCode("1");
        param.setMemberCode("1");
        List<ShoppingCartStore> cartStoreList = new ArrayList<>();
        ShoppingCartStore store = new ShoppingCartStore();
        store.setOrgCode("1");
        store.setStoreName("1");
        List<ShoppingCartItem> items = new ArrayList<>();
        ShoppingCartItem item = new com.gtech.promotion.vo.bean.ShoppingCartItem();
        item.setSelectionFlag("01");
        item.setProductPrice(new BigDecimal(10));
        item.setQuantity(1);
        item.setSkuCode("1");
        items.add(item);
        store.setCartItemList(items);
        cartStoreList.add(store);
        param.setCartStoreList(cartStoreList);
        ShoppingCartDTO shoppingCart = new ShoppingCartDTO();
        List<com.gtech.promotion.dto.in.activity.ShoppingCartItem>  products = new ArrayList<>();
        com.gtech.promotion.dto.in.activity.ShoppingCartItem shoppingCartItem = new com.gtech.promotion.dto.in.activity.ShoppingCartItem();
        ActivityItemResult result = new ActivityItemResult();
        List<ActivityItemResult> results = new ArrayList<>();
        results.add(result);
        Map<String,List<ActivityItemResult>> skuMap = new HashMap<>();
        skuMap.put("1",results);
        //活动编码-活动实体
        Map<String,ActivityModel> activityMap = new HashMap<>();
        ActivityModel activityModel1 = new ActivityModel();
        activityModel1.setActivityCode("1");
        activityMap.put("1",activityModel1);

        List<ShoppingCartActivity> usedActivitys =new ArrayList<>();
        ShoppingCartActivity activity = new ShoppingCartActivity();
        activity.setActivityModel(activityModel1);
        usedActivitys.add(activity);
        activityModel1.setActivityCode("1");
        activity.setActivityModel(activityModel1);
        usedActivitys.add(activity);
        shoppingCartItem.setUsedActivitys(usedActivitys);
        products.add(shoppingCartItem);
        shoppingCart.setPromoProducts(products);
        Map<String, ActivityCacheDTO> activityCacheMap = new HashedMap<>();
        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();
        List<ActivityFunctionParamRankModel> rankModelList = new ArrayList<>();
        ActivityFunctionParamRankModel model = new ActivityFunctionParamRankModel();
        model.setId("1");
        rankModelList.add(model);
        activityCacheDTO.setPromoFuncRanks(rankModelList);
        List<FunctionParamModel> paramModels = new ArrayList<>();
        FunctionParamModel model1 = new FunctionParamModel();
        model1.setRankId("1");
        paramModels.add(model1);
        activityCacheDTO.setPromoFuncParams(paramModels);
        List<TPromoIncentiveLimitedVO> list = new ArrayList<>();
        activityCacheDTO.setIncentiveLimiteds(list);
        List<QualificationModel> qualificationModels = new ArrayList<>();
        activityCacheDTO.setQualificationModels(qualificationModels);
        List<Giveaway> giveaways = new ArrayList<>();
        activityCacheDTO.setGiveaways(giveaways);
        activityCacheMap.put("1",activityCacheDTO);
        when(activityCacheDomain.getActivityCacheMap(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(activityCacheMap);
        when(shoppingCartDomain.queryActivityByShoppingCartProduct(Mockito.any(), Mockito.any())).thenReturn(shoppingCart);
        Result<List<QueryActivityByProductListResult>> listResult = mallActivityController.queryActivityByShoppingCartProduct(param);
        Assert.assertNotNull(listResult);
    }

    @Test
    public void queryAfterDiscountPrice(){
        QueryAfterDiscountPriceParam param = new QueryAfterDiscountPriceParam();
        try {
            mallActivityController.queryAfterDiscountPrice(param);
        }catch (Exception e){}
        param.setTenantCode("test");
        try {
            mallActivityController.queryAfterDiscountPrice(param);
        }catch (Exception e){}
        List<QueryAfterDiscountItemParam> itemList = new ArrayList<>();
        QueryAfterDiscountItemParam afterDiscountItemParam = new QueryAfterDiscountItemParam();
        itemList.add(afterDiscountItemParam);
        param.setItemList(itemList);
        try {
            mallActivityController.queryAfterDiscountPrice(param);
        }catch (Exception e){}

        afterDiscountItemParam.setSalePrice(new BigDecimal("10"));
        try {
            mallActivityController.queryAfterDiscountPrice(param);
        }catch (Exception e){}

        afterDiscountItemParam.setSkuCode("testSku");
        Result<DiscountPriceResult> resultResult = new Result<>();
//        when(tPromoActivityDomain.queryAfterDiscountPrice(any())).thenReturn(resultResult);
        try {
            mallActivityController.queryAfterDiscountPrice(param);
        }catch (Exception e){}
    }


    @Test
    public void queryMallActivityListByCondition(){
        QueryActivityMallParam param = new QueryActivityMallParam();
        param.setDomainCode("!");
        param.setTenantCode("!");


        Map<String, ActivityCacheDTO> activityCacheMap = new HashedMap<>();
        when(activityCacheDomain.getActivityCacheMap(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(activityCacheMap);

        Result<List<ActivityInfoDTO>> listResult = mallActivityController.queryMallActivityListByCondition(param);
        Assert.assertEquals(0, listResult.getData().size());

    }

    @Test
    public void queryMallActivityListByCondition_org_not_empty(){
        QueryActivityMallParam param = new QueryActivityMallParam();
        param.setDomainCode("!");
        param.setTenantCode("!");
        param.setActivityType("01");
        Map<String, ActivityCacheDTO> activityCacheMap = new HashedMap<>();
        when(activityCacheDomain.getActivityCacheMap(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(activityCacheMap);

        Result<List<ActivityInfoDTO>> listResult = mallActivityController.queryMallActivityListByCondition(param);
        Assert.assertEquals(0, listResult.getData().size());

    }
    @Test
    public void queryMallActivityListByCondition_02(){
        QueryActivityMallParam param = new QueryActivityMallParam();
        param.setActivityType("01");
        param.setDomainCode("!");
        param.setTenantCode("!");
        param.setActivityType("02");
        param.setActivityStartTime("12345");
        param.setActivityEndTime("12345");

        Map<String, ActivityCacheDTO> activityCacheMap = new HashedMap<>();
        when(activityCacheDomain.getActivityCacheMap(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(activityCacheMap);

        Result<List<ActivityInfoDTO>> listResult = mallActivityController.queryMallActivityListByCondition(param);
        Assert.assertEquals(0, listResult.getData().size());

    }

    @Test
    public void queryMallActivityListByCondition_011(){
        QueryActivityMallParam param = new QueryActivityMallParam();
        param.setActivityType("01");
        param.setDomainCode("!");
        param.setTenantCode("!");
        param.setActivityType("02");
        param.setReceiveStartTime("12345");
        param.setReceiveEndTime("1223344");
        MallProductConditionParam productCondition = new MallProductConditionParam();
        param.setProductCondition(productCondition);
        Map<String, ActivityCacheDTO> activityCacheMap = new HashedMap<>();
        when(activityCacheDomain.getActivityCacheMap(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(activityCacheMap);

        Result<List<ActivityInfoDTO>> listResult = mallActivityController.queryMallActivityListByCondition(param);
        Assert.assertEquals(0, listResult.getData().size());

    }

    @Test
    public void filterActivityByCondition_empty(){

        Map<String, ActivityCacheDTO> activityMap = new HashMap<>();
        QueryActivityMallParam param = new QueryActivityMallParam();
        param.setActivityType("02");
        param.setDomainCode("!");
        param.setTenantCode("!");
        Map<String, ActivityCacheDTO> map = mallActivityController.filterActivityByCondition(activityMap, ActivityTypeEnum.COUPON, param);

        Assert.assertEquals(0,map.size());
    }

    @Test
    public void filterActivityByCondition_03(){

        Map<String, ActivityCacheDTO> activityMap = new HashMap<>();
        ActivityCacheDTO activityCacheDTO  = new ActivityCacheDTO();

        ActivityModel  activityModel = new ActivityModel();
        activityModel.setActivityType("03");
        activityCacheDTO.setActivityModel(activityModel);

        activityMap.put("123456",activityCacheDTO);

        QueryActivityMallParam param = new QueryActivityMallParam();
        param.setActivityType("02");
        param.setDomainCode("!");
        param.setTenantCode("!");
        Map<String, ActivityCacheDTO> map = mallActivityController.filterActivityByCondition(activityMap, null, param);

        Assert.assertEquals(0,map.size());
    }
    @Test
    public void filterActivityByCondition_04(){

        Map<String, ActivityCacheDTO> activityMap = new HashMap<>();
        ActivityCacheDTO activityCacheDTO  = new ActivityCacheDTO();

        ActivityModel  activityModel = new ActivityModel();
        activityModel.setActivityType("04");
        activityCacheDTO.setActivityModel(activityModel);
        activityMap.put("123456",activityCacheDTO);

        QueryActivityMallParam param = new QueryActivityMallParam();
        param.setActivityType("02");
        param.setDomainCode("!");
        param.setTenantCode("!");
        Map<String, ActivityCacheDTO> map = mallActivityController.filterActivityByCondition(activityMap, null, param);

        Assert.assertEquals(0,map.size());
    }

    @Test
    public void filterActivityByCondition_05(){

        Map<String, ActivityCacheDTO> activityMap = new HashMap<>();
        ActivityCacheDTO activityCacheDTO  = new ActivityCacheDTO();

        ActivityModel  activityModel = new ActivityModel();
        activityModel.setActivityType("05");
        activityCacheDTO.setActivityModel(activityModel);

        activityMap.put("123456",activityCacheDTO);

        QueryActivityMallParam param = new QueryActivityMallParam();
        param.setActivityType("02");
        param.setDomainCode("!");
        param.setTenantCode("!");
        Map<String, ActivityCacheDTO> map = mallActivityController.filterActivityByCondition(activityMap, null, param);

        Assert.assertEquals(0,map.size());
    }

    @Test
    public void filterActivityByCondition_011(){

        Map<String, ActivityCacheDTO> activityMap = new HashMap<>();
        ActivityCacheDTO activityCacheDTO  = new ActivityCacheDTO();

        ActivityModel  activityModel = new ActivityModel();
        activityModel.setActivityType("01");
        activityModel.setActivityName("3456");
        activityModel.setStoreType("00");

        activityCacheDTO.setActivityModel(activityModel);

        activityMap.put("123456",activityCacheDTO);

        QueryActivityMallParam param = new QueryActivityMallParam();
        param.setActivityType("01");
        param.setDomainCode("!");
        param.setTenantCode("!");
        param.setActivityName("活动1");
        Map<String, ActivityCacheDTO> map = mallActivityController.filterActivityByCondition(activityMap, ActivityTypeEnum.ACTIVITY, param);

        Assert.assertEquals(0,map.size());
    }

    @Test
    public void filterActivityByCondition_01_code(){

        Map<String, ActivityCacheDTO> activityMap = new HashMap<>();
        ActivityCacheDTO activityCacheDTO  = new ActivityCacheDTO();

        ActivityModel  activityModel = new ActivityModel();
        activityModel.setActivityType("01");
        activityModel.setStoreType("00");

        activityModel.setActivityName("活动123456");
        activityModel.setActivityCode("1111111");
        activityCacheDTO.setActivityModel(activityModel);

        activityMap.put("123456",activityCacheDTO);

        QueryActivityMallParam param = new QueryActivityMallParam();
        param.setActivityType("01");
        param.setDomainCode("!");
        param.setTenantCode("!");
        param.setActivityName("活动1");
        param.setActivityCode("111111");
        Map<String, ActivityCacheDTO> map = mallActivityController.filterActivityByCondition(activityMap, ActivityTypeEnum.ACTIVITY, param);

        Assert.assertEquals(0,map.size());
    }

    @Test
    public void filterActivityByCondition_01(){

        Map<String, ActivityCacheDTO> activityMap = new HashMap<>();
        ActivityCacheDTO activityCacheDTO  = new ActivityCacheDTO();

        ActivityModel  activityModel = new ActivityModel();
        activityModel.setActivityType("01");
        activityModel.setActivityName("活动123456");
        activityModel.setActivityCode("111111");
        activityModel.setStoreType("00");

        activityModel.setOrgCode("011");

        activityCacheDTO.setActivityModel(activityModel);

        activityMap.put("123456",activityCacheDTO);

        QueryActivityMallParam param = new QueryActivityMallParam();
        param.setActivityType("01");
        param.setDomainCode("!");
        param.setTenantCode("!");
        param.setActivityName("活动1");
        param.setActivityCode("111111");
        param.setActivityOrgCode("011");
        Map<String, ActivityCacheDTO> map = mallActivityController.filterActivityByCondition(activityMap, ActivityTypeEnum.ACTIVITY, param);

        Assert.assertEquals(1,map.size());
    }

    @Test
    public void filterActivityByCondition_activity_name(){

        Map<String, ActivityCacheDTO> activityMap = new HashMap<>();
        ActivityCacheDTO activityCacheDTO  = new ActivityCacheDTO();

        ActivityModel  activityModel = new ActivityModel();
        activityModel.setActivityType("01");
        activityModel.setActivityName("活动123456");
        activityModel.setActivityCode("111111");
        activityModel.setStoreType("00");

        activityModel.setOrgCode("011");

        activityCacheDTO.setActivityModel(activityModel);

        activityMap.put("123456",activityCacheDTO);

        QueryActivityMallParam param = new QueryActivityMallParam();
        param.setActivityType("01");
        param.setDomainCode("!");
        param.setTenantCode("!");
        param.setActivityName("2");
        param.setActivityCode("111111");
        param.setActivityOrgCode("011");
        Map<String, ActivityCacheDTO> map = mallActivityController.filterActivityByCondition(activityMap, ActivityTypeEnum.ACTIVITY, param);

        Assert.assertEquals(1,map.size());
    }

    @Test
    public void filterActivityByCondition_activity_code(){

        Map<String, ActivityCacheDTO> activityMap = new HashMap<>();
        ActivityCacheDTO activityCacheDTO  = new ActivityCacheDTO();

        ActivityModel  activityModel = new ActivityModel();
        activityModel.setActivityType("01");
        activityModel.setActivityName("活动123456");
        activityModel.setActivityCode("123456");
        activityModel.setStoreType("00");

        activityModel.setOrgCode("011");

        activityCacheDTO.setActivityModel(activityModel);

        activityMap.put("123456",activityCacheDTO);

        QueryActivityMallParam param = new QueryActivityMallParam();
        param.setActivityType("01");
        param.setDomainCode("!");
        param.setTenantCode("!");
        param.setActivityName("活动12345");
        param.setActivityCode("1234567");
        param.setActivityOrgCode("011");
        Map<String, ActivityCacheDTO> map = mallActivityController.filterActivityByCondition(activityMap, ActivityTypeEnum.ACTIVITY, param);

        Assert.assertEquals(0,map.size());
    }

    @Test
    public void filterActivityByCondition_activity_org_code(){

        Map<String, ActivityCacheDTO> activityMap = new HashMap<>();
        ActivityCacheDTO activityCacheDTO  = new ActivityCacheDTO();

        ActivityModel  activityModel = new ActivityModel();
        activityModel.setActivityType("01");
        activityModel.setActivityName("活动123456");
        activityModel.setActivityCode("1234567");
        activityModel.setStoreType("00");

        activityModel.setOrgCode("0112");

        activityCacheDTO.setActivityModel(activityModel);

        activityMap.put("123456",activityCacheDTO);

        QueryActivityMallParam param = new QueryActivityMallParam();
        param.setActivityType("01");
        param.setDomainCode("!");
        param.setTenantCode("!");
        param.setActivityName("活动12345");
        param.setActivityCode("1234567");
        param.setActivityOrgCode("011");
        Map<String, ActivityCacheDTO> map = mallActivityController.filterActivityByCondition(activityMap, ActivityTypeEnum.ACTIVITY, param);

        Assert.assertEquals(0,map.size());
    }

    @Test
    public void filterActivityByCondition_storeType_00(){

        Map<String, ActivityCacheDTO> activityMap = new HashMap<>();
        ActivityCacheDTO activityCacheDTO  = new ActivityCacheDTO();

        ActivityModel  activityModel = new ActivityModel();
        activityModel.setActivityType("01");
        activityModel.setActivityName("活动123456");
        activityModel.setActivityCode("1234567");
        activityModel.setStoreType("00");

        activityModel.setOrgCode("011");

        activityCacheDTO.setActivityModel(activityModel);

        activityMap.put("123456",activityCacheDTO);

        QueryActivityMallParam param = new QueryActivityMallParam();
        param.setActivityType("01");
        param.setDomainCode("!");
        param.setTenantCode("!");
        param.setActivityName("活动12345");
        param.setActivityCode("1234567");
        param.setActivityOrgCode("011");
        Map<String, ActivityCacheDTO> map = mallActivityController.filterActivityByCondition(activityMap, ActivityTypeEnum.ACTIVITY, param);

        Assert.assertEquals(1,map.size());
    }

    @Test
    public void filterActivityByCondition_storeType_01(){

        Map<String, ActivityCacheDTO> activityMap = new HashMap<>();
        ActivityCacheDTO activityCacheDTO  = new ActivityCacheDTO();

        ActivityModel  activityModel = new ActivityModel();
        activityModel.setActivityType("01");
        activityModel.setActivityName("活动123456");
        activityModel.setActivityCode("1234567");
        activityModel.setStoreType("01");

        activityModel.setOrgCode("011");

        activityCacheDTO.setActivityModel(activityModel);

        activityMap.put("123456",activityCacheDTO);

        QueryActivityMallParam param = new QueryActivityMallParam();
        param.setActivityType("01");
        param.setDomainCode("!");
        param.setTenantCode("!");
        param.setActivityName("活动12345");
        param.setActivityCode("1234567");
        param.setActivityOrgCode("011");
        param.setOrgCode("123456");
        param.setStoreOrgCode("123456");

        List<TPromoActivityStoreVO> storeVOS = new ArrayList<>();

        Mockito.when(activityStoreService.getStoresByActivityCode(Mockito.anyString())).thenReturn(storeVOS);
        Map<String, ActivityCacheDTO> map = mallActivityController.filterActivityByCondition(activityMap, ActivityTypeEnum.ACTIVITY, param);

        Assert.assertEquals(0,map.size());
    }

    @Test
    public void filterActivityByCondition_storeType_01_not_empty(){

        Map<String, ActivityCacheDTO> activityMap = new HashMap<>();
        ActivityCacheDTO activityCacheDTO  = new ActivityCacheDTO();

        ActivityModel  activityModel = new ActivityModel();
        activityModel.setActivityType("01");
        activityModel.setActivityName("活动123456");
        activityModel.setActivityCode("1234567");
        activityModel.setStoreType("01");

        activityModel.setOrgCode("011");

        activityCacheDTO.setActivityModel(activityModel);

        activityMap.put("123456",activityCacheDTO);

        QueryActivityMallParam param = new QueryActivityMallParam();
        param.setActivityType("01");
        param.setDomainCode("!");
        param.setTenantCode("!");
        param.setActivityName("活动12345");
        param.setActivityCode("1234567");
        param.setActivityOrgCode("011");
        param.setOrgCode("123456");
        param.setStoreOrgCode("123456");

        List<TPromoActivityStoreVO> storeVOS = new ArrayList<>();
        TPromoActivityStoreVO storeVO = new TPromoActivityStoreVO();
        storeVO.setOrgCode("123456");
        storeVOS.add(storeVO);
        Mockito.when(activityStoreService.getStoresByActivityCode(Mockito.anyString())).thenReturn(storeVOS);
        Map<String, ActivityCacheDTO> map = mallActivityController.filterActivityByCondition(activityMap, ActivityTypeEnum.ACTIVITY, param);

        Assert.assertEquals(1,map.size());
    }

    @Test
    public void filterByCustomCondition(){

        QueryActivityMallParam param = new QueryActivityMallParam();
        param.setActivityType("01");
        param.setDomainCode("!");
        param.setTenantCode("!");
        param.setActivityName("活动12345");
        param.setActivityCode("1234567");
        param.setActivityOrgCode("011");
        param.setOrgCode("123456");
        param.setStoreOrgCode("123456");

        Map<String, ActivityCacheDTO> map = new HashMap<>();
        map.put("1234567",new ActivityCacheDTO());

        Iterator<Map.Entry<String, ActivityCacheDTO>> iterator = map.entrySet().iterator();
        Map.Entry<String, ActivityCacheDTO> next = iterator.next();



        Map<String, ActivityCacheDTO> newCaches =new HashMap<>();

        ActivityModel activity = new ActivityModel();
        List<CustomCondition> customConditions = new ArrayList<>();

        CustomCondition customCondition1 = new CustomCondition();
        customCondition1.setCustomKey("key1");
        customCondition1.setCustomValue("value1");
        customConditions.add(customCondition1);

        CustomCondition customCondition = new CustomCondition();
        customCondition.setCustomKey("key");
        customCondition.setCustomValue("value");
        customConditions.add(customCondition);

        List<String> list = new ArrayList<>();

        list.add("1234567");


    }


    @Test
    public void filterByCustomCondition1(){

        QueryActivityMallParam param = new QueryActivityMallParam();
        param.setActivityType("01");
        param.setDomainCode("!");
        param.setTenantCode("!");
        param.setActivityName("活动12345");
        param.setActivityCode("1234567");
        param.setActivityOrgCode("011");
        param.setOrgCode("123456");
        param.setStoreOrgCode("123456");

        Map<String, ActivityCacheDTO> map = new HashMap<>();
        map.put("1234567",new ActivityCacheDTO());

        Iterator<Map.Entry<String, ActivityCacheDTO>> iterator = map.entrySet().iterator();
        Map.Entry<String, ActivityCacheDTO> next = iterator.next();



        Map<String, ActivityCacheDTO> newCaches =new HashMap<>();

        ActivityModel activity = new ActivityModel();
        List<CustomCondition> customConditions = new ArrayList<>();

        CustomCondition customCondition1 = new CustomCondition();
        customCondition1.setCustomKey("key1");
        customCondition1.setCustomValue("value1");
        customConditions.add(customCondition1);

        CustomCondition customCondition = new CustomCondition();
        customCondition.setCustomKey("key");
        customCondition.setCustomValue("value");
        customConditions.add(customCondition);

        List<String> list = new ArrayList<>();



    }



    @Test
    public void filterByCustomCondition2(){

        QueryActivityMallParam param = new QueryActivityMallParam();
        param.setActivityType("01");
        param.setDomainCode("!");
        param.setTenantCode("!");
        param.setActivityName("活动12345");
        param.setActivityCode("1234567");
        param.setActivityOrgCode("011");
        param.setOrgCode("123456");
        param.setStoreOrgCode("123456");

        Map<String, ActivityCacheDTO> map = new HashMap<>();
        map.put("1234567",new ActivityCacheDTO());

        Iterator<Map.Entry<String, ActivityCacheDTO>> iterator = map.entrySet().iterator();
        Map.Entry<String, ActivityCacheDTO> next = iterator.next();



        Map<String, ActivityCacheDTO> newCaches =new HashMap<>();

        ActivityModel activity = new ActivityModel();
        List<CustomCondition> customConditions = new ArrayList<>();

        CustomCondition customCondition1 = new CustomCondition();
        customCondition1.setCustomKey("key1");
        customCondition1.setCustomValue("value1");

        CustomCondition customCondition = new CustomCondition();
        customCondition.setCustomKey("key");
        customCondition.setCustomValue("value");

        List<String> list = new ArrayList<>();

        list.add("1234567");


    }


    @Test
    public void filterByCustomCondition3(){

        QueryActivityMallParam param = new QueryActivityMallParam();
        param.setActivityType("01");
        param.setDomainCode("!");
        param.setTenantCode("!");
        param.setActivityName("活动12345");
        param.setActivityCode("1234567");
        param.setActivityOrgCode("011");
        param.setOrgCode("123456");
        param.setStoreOrgCode("123456");

        Map<String, ActivityCacheDTO> map = new HashMap<>();
        map.put("1234567",new ActivityCacheDTO());

        Iterator<Map.Entry<String, ActivityCacheDTO>> iterator = map.entrySet().iterator();
        Map.Entry<String, ActivityCacheDTO> next = iterator.next();



        Map<String, ActivityCacheDTO> newCaches =new HashMap<>();

        ActivityModel activity = new ActivityModel();
        List<CustomCondition> customConditions = new ArrayList<>();

        CustomCondition customCondition1 = new CustomCondition();
        customCondition1.setCustomKey("key1");
        customCondition1.setCustomValue("value1");
        customConditions.add(customCondition1);

        CustomCondition customCondition2 = new CustomCondition();
        customCondition2.setCustomKey("key");
        customCondition2.setCustomValue("value");
        customConditions.add(customCondition2);

        CustomCondition customCondition = new CustomCondition();
        customCondition.setCustomKey("key");
        customCondition.setCustomValue("value");
        customConditions.add(customCondition);

        List<String> list = new ArrayList<>();

        list.add("1234567");


    }


    @Test
    public void filterActivityCouponByReceiveTimeEmpty(){

        QueryActivityMallParam param = new QueryActivityMallParam();
        param.setActivityType("01");
        param.setDomainCode("!");
        param.setTenantCode("!");
        param.setActivityName("活动12345");
        param.setActivityCode("1234567");
        param.setActivityOrgCode("011");
        param.setOrgCode("123456");
        param.setStoreOrgCode("123456");
        Map<String, ActivityCacheDTO> map = new HashMap<>();
        map.put("1234567",new ActivityCacheDTO());
        Iterator<Map.Entry<String, ActivityCacheDTO>> iterator = map.entrySet().iterator();
        Map<String, ActivityCacheDTO> newCaches =new HashMap<>();
        List<CustomCondition> customConditions = new ArrayList<>();
        CustomCondition customCondition1 = new CustomCondition();
        customCondition1.setCustomKey("key1");
        customCondition1.setCustomValue("value1");
        customConditions.add(customCondition1);

        CustomCondition customCondition = new CustomCondition();
        customCondition.setCustomKey("key");
        customCondition.setCustomValue("value");
        customConditions.add(customCondition);

        List<String> list = new ArrayList<>();

        mallActivityController.filterActivityCouponByReceiveTime(newCaches, param);
    }

    @Test
    public void filterActivityCouponByReceiveTime(){

        QueryActivityMallParam param = new QueryActivityMallParam();
        param.setActivityType("01");
        param.setDomainCode("!");
        param.setTenantCode("!");
        param.setActivityName("活动12345");
        param.setActivityCode("1234567");
        param.setActivityOrgCode("011");
        param.setOrgCode("123456");
        param.setStoreOrgCode("123456");
        param.setReceiveStartTime("20220222222222");
        param.setReceiveEndTime("20231222222222");
        Map<String, ActivityCacheDTO> map = new HashMap<>();
        map.put("1234567",new ActivityCacheDTO());
        map.put("1",new ActivityCacheDTO());

        List<String> list = new ArrayList<>();
        list.add("1234567");

        Mockito.when(promoCouponReleaseService.queryActivityCodeByReceiveTime(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(list);

        mallActivityController.filterActivityCouponByReceiveTime(map, param);
    }


    @Test
    public void calcShoppingCart1(){

        CalcShoppingCartParam param = new CalcShoppingCartParam();
        param.setTenantCode("1");
        param.setDomainCode("1");
        param.setMemberCode("1");
        List<ShoppingCartStore> cartStoreList = new ArrayList<>();
        ShoppingCartStore store = new ShoppingCartStore();
        List<ShoppingCartItem> cartItemList = new ArrayList<>();
        ShoppingCartItem shoppingCartItem = new ShoppingCartItem();
        shoppingCartItem.setSkuCode("1");
        shoppingCartItem.setSelectionFlag("1");
        shoppingCartItem.setProductPrice(new BigDecimal(1));
        shoppingCartItem.setQuantity(1);
        cartItemList.add(shoppingCartItem);
        store.setCartItemList(cartItemList);
        cartStoreList.add(store);
        param.setCartStoreList(cartStoreList);

        Map<String, ActivityCacheDTO> activityCacheMap = new HashedMap<>();

        activityCacheMap.put("123456",new ActivityCacheDTO());
        when(activityCacheDomain.getActivityCacheMap(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(activityCacheMap);
        Result<List<CalcShoppingCartResult>> result = mallActivityController.calcShoppingCart(param);
        Assert.assertEquals(0,result.getData().size());
    }

    @Test
    public void filterCoupon(){
        FilterCouponParam param = new FilterCouponParam();
        param.setTenantCode("1");
        param.setDomainCode("1");
        param.setCouponCodes("1");

        when(shoppingCartDomain.filterCouponDomain(Mockito.anyString(),Mockito.anyString())).thenReturn(new ArrayList<>());
        Result<List<String>> listResult = mallActivityController.filterCoupon(param);
        Assert.assertEquals(0,listResult.getData().size());
    }
}
