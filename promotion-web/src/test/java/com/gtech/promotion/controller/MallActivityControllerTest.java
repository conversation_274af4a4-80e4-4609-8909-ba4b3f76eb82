package com.gtech.promotion.controller;

import com.github.pagehelper.PageInfo;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.promotion.component.activity.*;
import com.gtech.promotion.controller.activity.MallActivityController;
import com.gtech.promotion.dao.model.activity.*;
import com.gtech.promotion.dto.cache.ActivityCacheDTO;
import com.gtech.promotion.dto.in.activity.ShoppingCartActivity;
import com.gtech.promotion.dto.in.activity.ShoppingCartDTO;
import com.gtech.promotion.dto.out.activity.SkuActivityPriceDTO;
import com.gtech.promotion.dto.out.activity.TPromoActivityOutDTO;
import com.gtech.promotion.exception.PromotionException;
import com.gtech.promotion.service.activity.ActivityProductDetailService;
import com.gtech.promotion.vo.bean.Giveaway;
import com.gtech.promotion.vo.bean.ShoppingCartItem;
import com.gtech.promotion.vo.bean.ShoppingCartStore;
import com.gtech.promotion.vo.param.activity.*;
import com.gtech.promotion.vo.result.activity.*;
import org.apache.commons.collections4.map.HashedMap;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class MallActivityControllerTest {

    @InjectMocks
    private MallActivityController mallActivityController;

    @Mock
    private ActivityCacheDomain activityCacheDomain;


    @Mock
    private ActivityComponentDomain tPromoActivityDomain;

    @Mock
    private ActivityQueryDomain queryDomain;

    @Mock
    private ShoppingCartDomain shoppingCartDomain;

    @Mock
    private ProductDomain productDomain;

    @Mock
    private ActivityProductDetailService productDetailService;
//    @Mock
//    private ExecutorService promotionThreadPoolConfig;

    @Test(expected = NullPointerException.class)
    public void queryActivityListByProductList(){
        QueryActivityListByProductListParam param = new QueryActivityListByProductListParam();
        param.setDomainCode("!");
        param.setTenantCode("!");
        ArrayList<QueryActivityListByProductListParam.Product> products = new ArrayList<>();
        QueryActivityListByProductListParam.Product product = new QueryActivityListByProductListParam.Product();
        product.setSkuCode("!");
        product.setProductCode("!");
        products.add(product);
        QueryActivityListByProductListParam.Product product1 = new QueryActivityListByProductListParam.Product();
        product1.setSkuCode("!");
        product1.setProductCode("!");
        product1.setOrgCodes("!");
        products.add(product1);
        param.setProducts(products);
        Map<String, ActivityCacheDTO> activityCacheMap = new HashedMap<>();
        when(activityCacheDomain.getActivityCacheMap(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(activityCacheMap);
//        ExecutorService mock = mock(ExecutorService.class);
//        mock.execute(Mockito.any());
//        when(queryDomain.getActivityByProduct(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new ArrayList<>());
        when(productDetailService.queryListByActivityCodesAndProductCodes(Mockito.any(), Mockito.any())).thenReturn(new ArrayList<>());
        Result<List<QueryActivityListByProductListResult>> listResult = mallActivityController.queryActivityListByProductList(param);
        Assert.assertEquals(0, listResult.getData().size());

    }

    @Test
    public void calcSkuPromotionPrice(){
        CalcSkuPromotionPriceParam param = new CalcSkuPromotionPriceParam();
        param.setTenantCode("1");
        param.setDomainCode("1");
        param.setPrice(new BigDecimal(11));
        when(activityCacheDomain.getActivityCacheMap(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new HashMap<>());
        when(tPromoActivityDomain.activitySkuPrice(Mockito.any(), Mockito.any())).thenReturn(new SkuActivityPriceDTO());
        Result<CalcSkuPromotionPriceResult> result = mallActivityController.calcSkuPromotionPrice(param);
        Assert.assertNull(result.getData());
    }

    @Test
    public void queryActivityListByProduct_org_not_empty(){
        QueryActivityListByProductParam param = new QueryActivityListByProductParam();
        param.setTenantCode("1");
        param.setDomainCode("1");
        param.setProductCode("1341");
        param.setOrgCodes("431");
        Result<List<QueryActivityListByProductResult>> result = mallActivityController.queryActivityListByProduct(param);
        Assert.assertEquals(0,result.getData().size());
    }
    @Test
    public void queryActivityListByProduct_org_empty(){
        QueryActivityListByProductParam param = new QueryActivityListByProductParam();
        param.setTenantCode("1");
        param.setDomainCode("1");
        param.setProductCode("1341");
        Result<List<QueryActivityListByProductResult>> result = mallActivityController.queryActivityListByProduct(param);
        Assert.assertEquals(0,result.getData().size());
    }

    @Test
    public void queryProductListByActivity(){
        QueryProductListByActivityParam param = new QueryProductListByActivityParam();
        param.setTenantCode("1");
        param.setDomainCode("1");
        param.setActivityCode("1");
        param.setSeqNum(null);
        when(productDomain.queryActivityProductByCode(Mockito.any(), Mockito.any(), Mockito.any(),Mockito.anyInt(), Mockito.any())).thenReturn(null);
        Result<QueryProductListByActivityResult> result = mallActivityController.queryProductListByActivity(param);
        Assert.assertEquals(null,result.getData());
    }

    @Test
    public void calcShoppingCart(){

        CalcShoppingCartParam param = new CalcShoppingCartParam();
        param.setTenantCode("1");
        param.setDomainCode("1");
        param.setMemberCode("1");
        List<ShoppingCartStore> cartStoreList = new ArrayList<>();
        ShoppingCartStore store = new ShoppingCartStore();
         List<ShoppingCartItem> cartItemList = new ArrayList<>();
        ShoppingCartItem shoppingCartItem = new ShoppingCartItem();
        shoppingCartItem.setSkuCode("1");
        shoppingCartItem.setSelectionFlag("1");
        shoppingCartItem.setProductPrice(new BigDecimal(1));
        shoppingCartItem.setQuantity(1);
        cartItemList.add(shoppingCartItem);
        store.setCartItemList(cartItemList);
        cartStoreList.add(store);
        param.setCartStoreList(cartStoreList);
        when(activityCacheDomain.getActivityCacheMap(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new HashMap<>());
        Result<List<CalcShoppingCartResult>> result = mallActivityController.calcShoppingCart(param);
        Assert.assertEquals(0,result.getData().size());
    }



    @Test(expected = PromotionException.class)
    public void calcShoppingCart_coupons(){

        CalcShoppingCartParam param = new CalcShoppingCartParam();
        param.setTenantCode("1");
        param.setDomainCode("1");
        param.setMemberCode("1");
        List<ShoppingCartStore> cartStoreList = new ArrayList<>();
        ShoppingCartStore store = new ShoppingCartStore();
        List<ShoppingCartItem> cartItemList = new ArrayList<>();
        ShoppingCartItem shoppingCartItem = new ShoppingCartItem();
        shoppingCartItem.setSkuCode("1");
        shoppingCartItem.setSelectionFlag("1");
        shoppingCartItem.setProductPrice(new BigDecimal(1));
        shoppingCartItem.setQuantity(1);
        cartItemList.add(shoppingCartItem);
        store.setCartItemList(cartItemList);
        cartStoreList.add(store);
        param.setCartStoreList(cartStoreList);
        param.setCouponCodes("1");
        when(activityCacheDomain.getActivityCacheMap(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new HashMap<>());
        Result<List<CalcShoppingCartResult>> result = mallActivityController.calcShoppingCart(param);
        Assert.assertEquals(0,result.getData().size());
    }


    @Test
    public void queryActivityByShoppingCartProduct_cache_empty(){
        CalcShoppingCartParam param = new CalcShoppingCartParam();
        param.setTenantCode("1");
        param.setDomainCode("1");
        param.setMemberCode("1");
        List<ShoppingCartStore> cartStoreList = new ArrayList<>();
        ShoppingCartStore store = new ShoppingCartStore();
        store.setOrgCode("1");
        store.setStoreName("1");
        List<ShoppingCartItem> items = new ArrayList<>();
       ShoppingCartItem item = new com.gtech.promotion.vo.bean.ShoppingCartItem();
        item.setSelectionFlag("01");
        item.setProductPrice(new BigDecimal(10));
        item.setQuantity(1);
        item.setSkuCode("1");
        items.add(item);
        store.setCartItemList(items);
        cartStoreList.add(store);
        param.setCartStoreList(cartStoreList);
        ShoppingCartDTO shoppingCart = new ShoppingCartDTO();
        List<com.gtech.promotion.dto.in.activity.ShoppingCartItem>  products = new ArrayList<>();
        com.gtech.promotion.dto.in.activity.ShoppingCartItem shoppingCartItem = new com.gtech.promotion.dto.in.activity.ShoppingCartItem();
        List<ShoppingCartActivity> activityList =new ArrayList<>();
        ShoppingCartActivity activity = new ShoppingCartActivity();
        activityList.add(activity);
        shoppingCartItem.setUsedActivitys(activityList);
        products.add(shoppingCartItem);
        shoppingCart.setPromoProducts(products);
        Map<String, ActivityCacheDTO> activityCacheMap = new HashedMap<>();
        when(activityCacheDomain.getActivityCacheMap(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(activityCacheMap);
        Result<List<QueryActivityByProductListResult>> listResult = mallActivityController.queryActivityByShoppingCartProduct(param);
        Assert.assertNotNull(listResult);
    }

    @Test
    public void queryPromoListByStore(){
        QueryPromoListByStoreParam param = new QueryPromoListByStoreParam();
        param.setTenantCode("1");
        param.setDomainCode("1");
        param.setOrgCode("1");
        param.setLanguage("12");
        PageInfo<TPromoActivityOutDTO> pageInfo = new PageInfo<>();
        when(tPromoActivityDomain.queryPromoListByStore(Mockito.any())).thenReturn(pageInfo);
        PageResult<QueryPromoListByStoreResult> result = mallActivityController.queryPromoListByStore(param);
        Assert.assertEquals(0,result.getData().getList().size());
    }

    @Test
    public void queryActivityByShoppingCartProduct(){
        CalcShoppingCartParam param = new CalcShoppingCartParam();
        param.setTenantCode("1");
        param.setDomainCode("1");
        param.setMemberCode("1");
        List<ShoppingCartStore> cartStoreList = new ArrayList<>();
        ShoppingCartStore store = new ShoppingCartStore();
        store.setOrgCode("1");
        store.setStoreName("1");
        List<ShoppingCartItem> items = new ArrayList<>();
        ShoppingCartItem item = new com.gtech.promotion.vo.bean.ShoppingCartItem();
        item.setSelectionFlag("01");
        item.setProductPrice(new BigDecimal(10));
        item.setQuantity(1);
        item.setSkuCode("1");
        items.add(item);
        store.setCartItemList(items);
        cartStoreList.add(store);
        param.setCartStoreList(cartStoreList);
        ShoppingCartDTO shoppingCart = new ShoppingCartDTO();
        List<com.gtech.promotion.dto.in.activity.ShoppingCartItem>  products = new ArrayList<>();
        com.gtech.promotion.dto.in.activity.ShoppingCartItem shoppingCartItem = new com.gtech.promotion.dto.in.activity.ShoppingCartItem();
        ActivityItemResult result = new ActivityItemResult();
        List<ActivityItemResult> results = new ArrayList<>();
        results.add(result);
        Map<String,List<ActivityItemResult>> skuMap = new HashMap<>();
        skuMap.put("1",results);
        //活动编码-活动实体
        Map<String,ActivityModel> activityMap = new HashMap<>();
        ActivityModel activityModel1 = new ActivityModel();
        activityModel1.setActivityCode("1");
        activityMap.put("1",activityModel1);

        List<ShoppingCartActivity> usedActivitys =new ArrayList<>();
        ShoppingCartActivity activity = new ShoppingCartActivity();
        activity.setActivityModel(activityModel1);
        usedActivitys.add(activity);
        activityModel1.setActivityCode("1");
        activity.setActivityModel(activityModel1);
        usedActivitys.add(activity);
        shoppingCartItem.setUsedActivitys(usedActivitys);
        products.add(shoppingCartItem);
        shoppingCart.setPromoProducts(products);
        Map<String, ActivityCacheDTO> activityCacheMap = new HashedMap<>();
        ActivityCacheDTO activityCacheDTO = new ActivityCacheDTO();
        List<ActivityFunctionParamRankModel> rankModelList = new ArrayList<>();
        ActivityFunctionParamRankModel model = new ActivityFunctionParamRankModel();
        model.setId("1");
        rankModelList.add(model);
        activityCacheDTO.setPromoFuncRanks(rankModelList);
        List<FunctionParamModel> paramModels = new ArrayList<>();
        FunctionParamModel model1 = new FunctionParamModel();
        model1.setRankId("1");
        paramModels.add(model1);
        activityCacheDTO.setPromoFuncParams(paramModels);
        List<TPromoIncentiveLimitedVO> list = new ArrayList<>();
        activityCacheDTO.setIncentiveLimiteds(list);
        List<QualificationModel> qualificationModels = new ArrayList<>();
        activityCacheDTO.setQualificationModels(qualificationModels);
        List<Giveaway> giveaways = new ArrayList<>();
        activityCacheDTO.setGiveaways(giveaways);
        activityCacheMap.put("1",activityCacheDTO);
        when(activityCacheDomain.getActivityCacheMap(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(activityCacheMap);
        when(shoppingCartDomain.queryActivityByShoppingCartProduct(Mockito.any(), Mockito.any())).thenReturn(shoppingCart);
        Result<List<QueryActivityByProductListResult>> listResult = mallActivityController.queryActivityByShoppingCartProduct(param);
        Assert.assertNotNull(listResult);
    }

    @Test
    public void queryAfterDiscountPrice(){
        QueryAfterDiscountPriceParam param = new QueryAfterDiscountPriceParam();
        try {
            mallActivityController.queryAfterDiscountPrice(param);
        }catch (Exception e){}
        param.setTenantCode("test");
        try {
            mallActivityController.queryAfterDiscountPrice(param);
        }catch (Exception e){}
        List<QueryAfterDiscountItemParam> itemList = new ArrayList<>();
        QueryAfterDiscountItemParam afterDiscountItemParam = new QueryAfterDiscountItemParam();
        itemList.add(afterDiscountItemParam);
        param.setItemList(itemList);
        try {
            mallActivityController.queryAfterDiscountPrice(param);
        }catch (Exception e){}

        afterDiscountItemParam.setSalePrice(new BigDecimal("10"));
        try {
            mallActivityController.queryAfterDiscountPrice(param);
        }catch (Exception e){}

        afterDiscountItemParam.setSkuCode("testSku");
        Result<DiscountPriceResult> resultResult = new Result<>();
//        when(tPromoActivityDomain.queryAfterDiscountPrice(any())).thenReturn(resultResult);
        try {
            mallActivityController.queryAfterDiscountPrice(param);
        }catch (Exception e){}
    }
}
