package com.gtech.promotion.controller.mq;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.rocketmq.client.consumer.DefaultMQPushConsumer;
import com.gtech.basic.filecloud.api.model.ImportFileMessage;
import com.gtech.promotion.mq.MQConsumerBusiness;
import com.gtech.promotion.mq.MQConsumerFactory;
import com.gtech.promotion.pojo.MqEnums;
import com.gtech.promotion.service.common.ExcelService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * <AUTHOR>
 * @date 2021-09-18 13:56
 */
@RunWith(MockitoJUnitRunner.class)
public class MQConsumerBusinessTest {
    @InjectMocks
    private MQConsumerBusiness mqConsumerBusiness;
    @Mock
    private ExcelService excelService;

    @Test
    public void businessImpl(){
        mqConsumerBusiness.businessImpl(MqEnums.EXCEL_REQUEST_MQ,null);
    }

    @Test
    public void businessImpl1(){
        mqConsumerBusiness.businessImpl(MqEnums.EXCEL_RESULT_MQ,null);
    }

    @Test
    public void businessImpl2(){
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("subcategory","promotion.flashsale");
        mqConsumerBusiness.businessImpl(MqEnums.EXCEL_REQUEST_MQ,jsonObject.toJSONString());
    }

    @Test
    public void businessImpl3(){
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("subcategory","1");
        mqConsumerBusiness.businessImpl(MqEnums.EXCEL_REQUEST_MQ,jsonObject.toJSONString());
    }

    @Test
    public void businessImpl4(){
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("subcategory","promotion.flashsale");
        Mockito.when(excelService.importSku(Mockito.any())).thenReturn(null);
        mqConsumerBusiness.businessImpl(MqEnums.EXCEL_REQUEST_MQ,jsonObject.toJSONString());
    }

    @Test
    public void businessImpl5(){
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("subcategory","promotion.flashsale");
        ImportFileMessage<ImportFileMessage.Result> resultMessage = new ImportFileMessage<>();
        Mockito.when(excelService.importSku(Mockito.any())).thenReturn(resultMessage);
        mqConsumerBusiness.businessImpl(MqEnums.EXCEL_REQUEST_MQ,jsonObject.toJSONString());
    }

    @Test
    public void businessImpl6(){
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("subcategory","promotion.flashsale");
        Mockito.when(excelService.importSku(Mockito.any())).thenThrow(NullPointerException.class);
        mqConsumerBusiness.businessImpl(MqEnums.EXCEL_REQUEST_MQ,jsonObject.toJSONString());
    }

//    @Test
//    public void testMQConsumerFactory(){
//        MQConsumerFactory.getInstance();
//        DefaultMQPushConsumer consumer = new DefaultMQPushConsumer();
//        MQConsumerFactory mqConsumerFactory = new MQConsumerFactory();
//        mqConsumerFactory.addConsumer("titan_filecloud_imports_flashsale",consumer);
//        mqConsumerFactory.getConsumer();
//    }
}
