package com.gtech.promotion.controller.Group;

import com.gtech.commons.result.Result;
import com.gtech.promotion.component.activity.PromoGroupRelationDomain;
import com.gtech.promotion.controller.activity.GroupRelationController;
import com.gtech.promotion.vo.param.activity.ActivityGroupRelationQueryParam;
import com.gtech.promotion.vo.param.activity.GroupRelationParam;
import com.gtech.promotion.vo.param.activity.GroupSettingRelationParam;
import com.gtech.promotion.vo.result.activity.ActivityGroupExclusionResult;
import com.gtech.promotion.vo.result.activity.ActivityGroupRelationResult;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description :
 * <AUTHOR> gaojie.li
 * @Date : 2023/6/16 10:01
 */
@RunWith(MockitoJUnitRunner.class)
public class GroupRelationControllerTest {

    @InjectMocks
    private GroupRelationController groupController;



    @Mock
    private PromoGroupRelationDomain promoGroupRelationDomain;

    @Test
    public void queryAllActivityGroupRelation(){

        ActivityGroupRelationQueryParam param = new ActivityGroupRelationQueryParam();

        param.setTenantCode("1");
        param.setDomainCode("1");


        List<ActivityGroupRelationResult> relationResults = new ArrayList<>();

        ActivityGroupRelationResult relationResult = new ActivityGroupRelationResult();
        relationResult.setGroupCode("!");
        relationResult.setGroupName("!");
        relationResult.setGroupCode("1");
        relationResult.setPriority(1);
        relationResult.setTenantCode("!");
        relationResult.setType("02");
        List<ActivityGroupExclusionResult> results = new ArrayList<>();

        relationResult.setRelations(results);

        relationResults.add(relationResult);

        Mockito.when(promoGroupRelationDomain.queryAllActivityGroupRelation(Mockito.any())).thenReturn(relationResults);
        Result<List<ActivityGroupRelationResult>> listResult = groupController.queryAllActivityGroupRelation(param);
        Assert.assertEquals(1, listResult.getData().size());
    }

    @Test
    public void settingRelationActivityGroup(){

        GroupSettingRelationParam param = new GroupSettingRelationParam();

        param.setTenantCode("1");
        param.setDomainCode("1");
        List<GroupRelationParam> relationParams = new ArrayList<>();
        GroupRelationParam groupRelationParam = new GroupRelationParam();
        groupRelationParam.setGroupCodeA("1");
        groupRelationParam.setGroupCodeB("2");
        groupRelationParam.setRelation(2);
        relationParams.add(groupRelationParam);
        param.setRelations(relationParams);

        List<ActivityGroupRelationResult> relationResults = new ArrayList<>();
        ActivityGroupRelationResult relationResult = new ActivityGroupRelationResult();
        relationResult.setGroupCode("!");
        relationResult.setGroupName("!");
        relationResult.setGroupCode("1");
        relationResult.setPriority(1);
        relationResult.setTenantCode("!");
        relationResult.setType("02");
        List<ActivityGroupExclusionResult> results = new ArrayList<>();
        relationResult.setRelations(results);
        relationResults.add(relationResult);

        Mockito.when(promoGroupRelationDomain.settingActivityGroupRelation(Mockito.any())).thenReturn(1);
        Result<Integer> integerResult = groupController.settingRelationActivityGroup(param);
        Assert.assertEquals(1, integerResult.getData().intValue());
    }
}
